package cn.getech.ehm.client;

import cn.getech.ehm.dto.DemoClientDTO;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/3/3
 */
@FeignClient(name = "workbench-service",path = "/api/workbench-service")
@Validated
public interface DemoUserClient {

    /**
     * demoUser
     *
     * @param name
     * @return RestResponse<String>
     */
    @GetMapping(value = "/demoUser/hello")
    RestResponse<DemoClientDTO> getById(@RequestParam("name") @NotBlank String name);
}
