<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.JobStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ListResultMap" type="cn.getech.ehm.task.dto.job.JobStandardListDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="majors" property="majors" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="job_type" property="jobType" />
        <result column="remark" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.dto.job.JobStandardDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="equipment_category_id" property="equipmentCategoryId"/>
        <result column="equipment_category_name" property="equipmentCategoryName"/>
        <result column="equipment_location_id" property="equipmentLocationId"/>
        <result column="equipment_location_name" property="equipmentLocationName"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="majors" property="majors" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="job_type" property="jobType" />
        <result column="remark" property="remark" />
        <result column="stopped" property="stopped" />
    </resultMap>
    <select id="getPageList" resultType="cn.getech.ehm.task.dto.job.JobStandardListDto">
        SELECT
            standard.*,
            ei.name equipment_name,
            ei.category_id equipment_category_id,
            ec.name equipment_category_name,
            ei.location_id equipment_location_id,
            ei.name equipment_location_name
        FROM job_standard standard
        LEFT JOIN equipment_info ei ON ei.id = standard.equipment_id
        LEFT JOIN equipment_category ec ON ec.id = ei.category_id
        LEFT JOIN equipment_location el ON el.id = ei.location_id
        <where>
            standard.deleted = 0
            <if test="param.equipmentCategoryId != null and param.equipmentCategoryId != ''">
                AND ei.category_id IN (${param.equipmentCategoryId})
            </if>
            <if test="param.equipmentLocationId != null and param.equipmentLocationId != ''">
                AND ei.location_id IN (${param.equipmentLocationId})
            </if>
            <if test="null != param.name and '' != param.name">
                AND standard.name LIKE CONCAT("%",#{param.name}, "%")
            </if>
            <if test="null != param.major and '' != param.major">
                AND standard.majors LIKE CONCAT("%",#{param.major}, "%")
            </if>
            <if test="null != param.status">
                AND standard.status = #{param.status}
            </if>
            <if test="null != param.jobType and '' != param.jobType">
                AND standard.job_type = #{param.jobType}
            </if>
            <if test="null != param.taskType and '' != param.taskType">
                AND standard.task_type = #{param.taskType}
            </if>
            <if test="null != param.equipmentName and '' != param.equipmentName">
                AND ei.name LIKE CONCAT('%',#{param.equipmentName}, '%')
            </if>
            <if test="null != param.equipmentCode and '' != param.equipmentCode">
                AND ei.code LIKE CONCAT('%',#{param.equipmentCode}, '%')
            </if>
            <if test="null != param.equipmentId and '' != param.equipmentId">
                AND standard.equipment_id = #{param.equipmentId}
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>

    <select id="getDtoById" resultMap="BaseResultMap">
        SELECT
            standard.id,
            standard.name,
            standard.majors,
            standard.job_type,
            standard.remark,
            standard.stopped,
            standard.equipment_id,
            ei.name equipment_name,
            ei.category_id equipment_category_id,
            ec.name equipment_category_name,
            ei.location_id equipment_location_id,
            ei.name equipment_location_name
        FROM job_standard standard
        LEFT JOIN equipment_info ei ON ei.id = standard.equipment_id
        LEFT JOIN equipment_category ec ON ec.id = ei.category_id
        LEFT JOIN equipment_location el ON el.id = ei.location_id
        <where>
            standard.id = #{id}
        </where>
    </select>
</mapper>
