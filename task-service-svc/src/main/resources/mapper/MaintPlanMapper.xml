<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintPlanMapper">
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.dto.plan.MaintPlanDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="job_type" property="jobType" />
        <result column="repair_id" property="repairId"/>
        <result column="urgency" property="urgency"/>
        <result column="major" property="major" />
        <result column="trigger_type" property="triggerType" />
        <result column="inter_val" property="interVal" />
        <result column="advance_day" property="advanceDay" />
        <result column="period" property="period" />
        <result column="exec_val" property="execVal" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="month_exec_val" property="monthExecVal" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="expiry_time" property="expiryTime" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deadline_days" property="deadlineDays" />
        <result column="cbm_frequency" property="cbmFrequency" />
        <result column="enabled" property="enabled" />
        <result column="info_category_id" property="infoCategoryId" />
        <result column="info_location_id" property="infoLocationId" />
        <result column="equipment_ids" property="equipmentIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="standard_id" property="standardId" />
        <result column="standardName" property="standardName" />
        <result column="description" property="description" />
        <result column="festival_type" property="festivalType" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>
    </resultMap>
    <resultMap id="SynResultMap" type="cn.getech.ehm.task.dto.plan.SynMaintPlanDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="job_type" property="jobType" />
        <result column="repair_id" property="repairId"/>
        <result column="urgency" property="urgency"/>
        <result column="major" property="major" />
        <result column="trigger_type" property="triggerType" />
        <result column="inter_val" property="interVal" />
        <result column="advance_day" property="advanceDay" />
        <result column="period" property="period" />
        <result column="exec_val" property="execVal" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="month_exec_val" property="monthExecVal" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="expiry_time" property="expiryTime" />
        <result column="create_by" property="createBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="cbm_frequency" property="cbmFrequency" />
        <result column="info_category_id" property="infoCategoryId" />
        <result column="info_location_id" property="infoLocationId" />
        <result column="equipment_ids" property="equipmentIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="standard_id" property="standardId" />
        <result column="standardName" property="standardName" />
        <result column="festival_type" property="festivalType" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>
        <result column="deadline_days" property="deadlineDays" />
        <result column="task_type" property="taskType" />
    </resultMap>
    <resultMap id="ListResultMap" type="cn.getech.ehm.task.dto.plan.MaintPlanListDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="job_type" property="jobType" />
        <result column="urgency" property="urgency"/>
        <result column="major" property="major" />
        <result column="standard_id" property="standardId" />
        <result column="standardName" property="standardName" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="plan_maint_time" property="nextMaintTime" />
        <result column="repair_auto" property="repairAuto" />
    </resultMap>
    <select id="getPageList" resultType="cn.getech.ehm.task.dto.plan.MaintPlanListDto">
        SELECT plan.id, plan.name, plan.status,plan.job_type, plan.urgency,plan.major,plan.expiry_time,
            plan.process_instance_id,plan.standard_id, standard.name standardName,
            plan.repair_auto, plan.enabled,ei.name as equipmentName
        FROM maint_plan plan
        LEFT JOIN job_standard standard ON plan.standard_id = standard.id
        left join equipment_info ei on ei.id = plan.equipment_ids
        <where>
            plan.deleted = 0
            <if test="param.name != null and param.name != ''">
                AND (
                     plan.name like concat('%', #{param.name} , '%')
                )
            </if>
            <if test="param.jobType != null and param.jobType != ''">
                AND plan.job_type = #{param.jobType}
            </if>
            <if test="param.enabled != null">
                AND plan.enabled = #{param.enabled}
            </if>
            <if test="param.urgency != null and param.urgency != ''">
                AND plan.urgency = #{param.urgency}
            </if>
            <if test="param.major != null and param.major != ''">
                AND plan.major = #{param.major}
            </if>
            <if test="param.status != null">
                AND plan.status = #{param.status}
            </if>
            <if test="param.standardId != null and param.standardId != ''">
                AND plan.standard_id = #{param.standardId}
            </if>
            <if test="param.beginCreateTime != null">
                AND plan.create_time &gt;= #{param.beginCreateTime}
            </if>
            <if test="param.endCreateTime != null">
                AND plan.create_time &lt;= #{param.endCreateTime}
            </if>
            <if test="param.overtimeFlag != null and param.overtimeFlag">
                AND plan.expiry_time &lt;= now()
            </if>
            <if test="param.overtimeFlag != null and !param.overtimeFlag">
                AND plan.expiry_time &gt;= now()
            </if>
            <if test="param.planIds !=null and param.planIds.size()>0">
                AND plan.id IN
                <foreach collection="param.planIds" open="(" close=")" separator="," item="planId" index="index">
                    #{planId}
                </foreach>
            </if>
            <if test="param.equipmentName != null and param.equipmentName != ''">
                AND ei.name like concat('%', #{param.equipmentName} , '%')
            </if>

            <if test="param.equipmentCode != null and param.equipmentCode != ''">
                AND ei.code = #{param.equipmentCode}
            </if>
            <if test="param.sourceTaskType != null and param.sourceTaskType">
                AND plan.source_task_type = #{param.sourceTaskType}
            </if>
            <if test="param.equipmentCategoryId != null and param.equipmentCategoryId != ''">
                AND ei.category_id IN (${param.equipmentCategoryId})
            </if>
            <if test="param.equipmentLocationId != null and param.equipmentLocationId != ''">
                AND ei.location_id IN (${param.equipmentLocationId})
            </if>
        <if test="param.equipmentId != null and param.equipmentId != ''">
            and  (
            plan.equipment_ids LIKE concat('%', #{param.equipmentId} , '%')
            )
        </if>
<!--            <if test="param.equipmentLocationIds !=null and param.equipmentLocationIds.size()>0">-->
<!--                AND-->
<!--                <foreach collection="param.equipmentLocationIds" open="(" close=")" separator=" OR " item="locationId" index="index">-->
<!--                    plan.info_location_id LIKE #{locationId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="param.categoryIds !=null and param.categoryIds.size()>0">-->
<!--                AND-->
<!--                <foreach collection="param.categoryIds" open="(" close=")" separator=" OR " item="categoryId" index="index">-->
<!--                    plan.info_category_id LIKE #{categoryId}-->
<!--                </foreach>-->
<!--            </if>-->

        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="getMaintPlanDto" resultMap="BaseResultMap">
        SELECT plan.*, standard.name standardName
        FROM maint_plan plan
        LEFT JOIN job_standard standard ON plan.standard_id = standard.id
        WHERE plan.id = #{id}
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT count(*) FROM ${tableName} WHERE ${columnName} = #{value} AND deleted = 0
    </select>
    <select id="getSynListByIds" resultMap="SynResultMap">
        SELECT plan.*, standard.name standardName
        FROM maint_plan plan
        LEFT JOIN job_standard standard ON plan.standard_id = standard.id
        WHERE plan.deleted = 0
        AND plan.id IN
        <foreach collection="planIds" open="(" close=")" separator="," item="planId" index="index">
            #{planId}
        </foreach>
    </select>
    <select id="getSynById"  resultMap="SynResultMap">
        SELECT plan.*, standard.name standardName
        FROM maint_plan plan
        LEFT JOIN job_standard standard ON plan.standard_id = standard.id
        WHERE plan.deleted = 0
        AND plan.id = #{planId}
    </select>
    <select id="getUsedStandardIds" resultType="java.lang.String">
        SELECT distinct plan.standard_id FROM maint_plan plan
        WHERE plan.deleted = 0 AND plan.status != -1
        AND plan.standard_id IN
        <foreach collection="standardIds" open="(" close=")" separator="," item="standardId" index="index">
            #{standardId}
        </foreach>
    </select>
    <select id="getCbmPlanIds" resultType="java.lang.String">
        SELECT DISTINCT plan.id FROM maint_plan plan
        LEFT JOIN maint_plan_cbm cbm ON plan.id = cbm.plan_id
        WHERE plan.enabled = 1 AND status = 1
        AND plan.info_category_id = #{categoryId} AND plan.job_type = #{jobType}
        AND cbm.category_parameter_id = #{categoryParamId}
        AND (plan.info_location_id IS NULL OR plan.info_location_id = '' OR plan.info_location_id IN
        <foreach collection="locationIds" index="index" item="locationId" open="(" close=")" separator=",">
            #{locationId}
        </foreach>
        )
        AND (plan.equipment_ids IS NULL OR plan.equipment_ids = '' OR plan.equipment_ids LIKE CONCAT("%",#{equipmentId}, "%"))
    </select>
</mapper>
