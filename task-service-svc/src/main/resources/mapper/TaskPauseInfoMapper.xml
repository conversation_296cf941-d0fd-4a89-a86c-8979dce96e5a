<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.TaskPauseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.TaskPauseInfo">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="time_key" property="timeKey" />
        <result column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="task_code" property="taskCode" />
        <result column="pause_start_time" property="pauseStartTime" />
        <result column="pause_end_time" property="pauseEndTime" />
        <result column="pause_total" property="pauseTotal" />
        <result column="pause_cause" property="pauseCause" />
        <result column="pause_cause_remark" property="pauseCauseRemark" />
        <result column="operator" property="operator" />
        <result column="operator_uid" property="operatorUid" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        time_key, task_id, task_name, task_code, pause_start_time, pause_end_time, pause_total, pause_cause, pause_cause_remark, operator, operator_uid, deleted, tenant_id
    </sql>
    <select id="getTotalTime" resultType="cn.getech.ehm.task.dto.TaskPauseInfoCountDto">
        select
        count(1) as totalCount,
        sum(pause_total) as totalTime
        from task_pause_info
        where task_id = #{taskId}
    </select>

</mapper>
