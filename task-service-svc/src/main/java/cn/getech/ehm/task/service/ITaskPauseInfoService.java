package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.TaskPauseInfoCountDto;
import cn.getech.ehm.task.entity.TaskPauseInfo;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.TaskPauseInfoQueryParam;
import cn.getech.ehm.task.dto.TaskPauseInfoDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface ITaskPauseInfoService extends IBaseService<TaskPauseInfo> {

        /**
         * 分页查询，返回Dto
         *
         * @param taskPauseInfoQueryParam
         * @return
         */
        PageResult<TaskPauseInfoDto> pageDto(TaskPauseInfoQueryParam taskPauseInfoQueryParam);


        TaskPauseInfoCountDto getCount(TaskPauseInfoQueryParam taskPauseInfoQueryParam);
}