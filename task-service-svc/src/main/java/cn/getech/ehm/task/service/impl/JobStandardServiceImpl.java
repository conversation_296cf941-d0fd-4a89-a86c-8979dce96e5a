package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoDto;
import cn.getech.ehm.task.dto.job.*;
import cn.getech.ehm.task.entity.JobStandard;
import cn.getech.ehm.task.handler.FileGetHandler;
import cn.getech.ehm.task.mapper.JobStandardMapper;
import cn.getech.ehm.task.service.IJobStandardItemService;
import cn.getech.ehm.task.service.IJobStandardService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 作业标准imp
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class JobStandardServiceImpl extends BaseServiceImpl<JobStandardMapper, JobStandard> implements IJobStandardService {

    @Autowired
    private JobStandardMapper jobStandardMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IJobStandardItemService standardItemService;
    @Autowired
    private FileGetHandler fileGetHandler;

    @Override
    public PageResult<JobStandardListDto> pageList(JobStandardQueryParam queryParam) {
        Page<JobStandardQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), JobStandard.class, "standard"));
        if (StringUtils.isNotEmpty(queryParam.getEquipmentCategoryId())) {
            String categoryIdStrs = Optional.ofNullable(equipmentClient.getDtosIncludeChild(
                            new String[]{queryParam.getEquipmentCategoryId()}).getData()).orElseGet(ArrayList::new)
                    .stream().map(CategoryInfoDto::getId).collect(Collectors.joining("','"));
            queryParam.setEquipmentCategoryId("'" + categoryIdStrs + "'");
        }
        if (StringUtils.isNotEmpty(queryParam.getEquipmentLocationId())) {
            String locationIdStrs = Optional.ofNullable(
                            equipmentClient.getLocationChildIds(queryParam.getEquipmentLocationId()).getData())
                    .orElseGet(ArrayList::new).stream().collect(Collectors.joining("','"));
            queryParam.setEquipmentLocationId("'" + StringUtils.join(locationIdStrs, "','") + "'");
        }
        IPage<JobStandardListDto> result = jobStandardMapper.getPageList(page, queryParam);
        List<JobStandardListDto> records = result.getRecords();
        if(CollectionUtils.isNotEmpty(records)) {
            Map<String, DictionaryItemDto> majorMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> restResponse = baseServiceClient.getItemMapByCode("major");
            if(restResponse.isOk()){
                majorMap = restResponse.getData();
            }else{
                log.info("连接base-service获取专业类别字典表失败");
            }
            Map<String, DictionaryItemDto> jobTypeMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeRes = baseServiceClient.getItemMapByCode("job_type");
            if(jobTypeRes.isOk()){
                jobTypeMap = jobTypeRes.getData();
            }else{
                log.info("连接base-service获取作业类别字典表失败");
            }
            List<String> standardIds = records.stream().map(JobStandardListDto::getId).distinct().collect(Collectors.toList());
            Map<String, ItemCountDto> countDtoMap = standardItemService.getCountMap(standardIds);
            for(JobStandardListDto record : records){
                if(null != record.getMajors() && record.getMajors().length > 0){
                    record.setMajorNames(buildMajorNames(record.getMajors(), majorMap));
                }
                if(StringUtils.isNotBlank(record.getJobType())){
                    DictionaryItemDto dictionaryItemDto = jobTypeMap.get(record.getJobType());
                    record.setJobTypeName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                }
                ItemCountDto itemCountDto = countDtoMap.get(record.getId());
                if(null != itemCountDto){
                    record.setStandardTimeCount(itemCountDto.getStandardTimeCount());
                    record.setWorkingTimeCount(itemCountDto.getWorkingTimeCount());
                }
            }
        }

        return Optional.ofNullable(PageResult.<JobStandardListDto>builder()
                .records(records)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public PageResult<JobStandardDetailDto> detailList(JobStandardQueryParam queryParam){
        Page<JobStandardQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        IPage<JobStandardListDto> result = jobStandardMapper.getPageList(page, queryParam);

        List<JobStandardDetailDto> records = CopyDataUtil.copyList(result.getRecords(), JobStandardDetailDto.class);
        return Optional.ofNullable(PageResult.<JobStandardDetailDto>builder()
                .records(records)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    /**
     * 构造设备类型名称集合
     * @param infoCategoryIds
     * @return
     */
    private String buildCategoryNames(String[] infoCategoryIds, Map<String, String> categoryNameMap){
        List<String> names = new ArrayList<>();
        for(String categoryId : infoCategoryIds){
            String name = categoryNameMap.get(categoryId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            return StringUtils.join(names.toArray(), StringPool.COMMA);
        }
        return null;
    }

    /**
     * 构造设备位置名称集合
     * @param infoLocationIds
     * @return
     */
    private String buildLocationNames(String[] infoLocationIds, Map<String, String> locationNameMap){
        List<String> names = new ArrayList<>();
        for(String locationId : infoLocationIds){
            String name = locationNameMap.get(locationId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            return StringUtils.join(names.toArray(), StringPool.COMMA);
        }
        return null;
    }

    /**
     * 构造专业类别集合
     * @param majors
     * @return
     */
    private String buildMajorNames(String[] majors, Map<String, DictionaryItemDto> majorMap){
        List<String> names = new ArrayList<>();
        for(String major : majors){
            DictionaryItemDto dictionaryItemDto = majorMap.get(major);
            if(null != dictionaryItemDto){
                names.add(dictionaryItemDto.getName());
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            return StringUtils.join(names.toArray(), StringPool.COMMA);
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveByParam(JobStandardAddParam addParam) {
        if(check(null, addParam.getName())){
            log.error("名称已存在");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        JobStandard jobStandard = CopyDataUtil.copyObject(addParam, JobStandard.class);

        Boolean flag = save(jobStandard);

        //同步更新作业项目
        standardItemService.editItems(addParam.getItemDtos(), jobStandard.getId(), true);
        return flag;
    }

    private Boolean check(String id, String name){
        LambdaQueryWrapper<JobStandard> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(JobStandard::getId, id);
        }
        if(StringUtils.isNotBlank(name)){
            wrapper.eq(JobStandard::getName, name);
        }
        wrapper.eq(JobStandard::getDeleted, DeletedType.NO.getValue());
        return jobStandardMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    @Override
    public Boolean editByParam(JobStandardEditParam editParam) {
        if(check(editParam.getId(), editParam.getName())){
            log.error("名称已存在");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        JobStandard jobStandard = CopyDataUtil.copyObject(editParam, JobStandard.class);
        Boolean flag = updateById(jobStandard);
        //同步更新作业项目
        standardItemService.editItems(editParam.getItemDtos(), jobStandard.getId(), false);
        return flag;
    }

    @Override
    public Boolean deleteByIds(String[] ids) {
        LambdaUpdateWrapper<JobStandard> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(JobStandard::getId, ids);
        wrapper.set(JobStandard::getDeleted, DeletedType.YES.getValue());
        return update(wrapper);
    }

    @Override
    public JobStandardDto getDtoById(String id) {
        JobStandardDto jobStandardDto = jobStandardMapper.getDtoById(id);
        if(null != jobStandardDto){
            List<JobStandardItemDto> itemDtos = standardItemService.getListByStandardId(id);
            if (CollectionUtils.isNotEmpty(itemDtos)){
                itemDtos.stream().filter(item-> ArrayUtils.isNotEmpty(item.getFileIds())).forEach(detailDto->{
                    List<AttachmentClientDto> attachmentList = fileGetHandler.getAttachmentList(Arrays.asList(detailDto.getFileIds()));
                    detailDto.setFileInfoList(attachmentList);
                });
            }
            jobStandardDto.setItemDtos(itemDtos);
            jobStandardDto.setCountDto(buildCountDto(itemDtos));
        }
        return jobStandardDto;
    }

    @Override
    public ItemCountDto buildCountDto(List<JobStandardItemDto> itemDtos){
        ItemCountDto countDto = new ItemCountDto();
        if(CollectionUtils.isNotEmpty(itemDtos)) {
            List<String> largeCategories = new ArrayList<>();
            //大类跟小类集合键值对
            Map<String, List<String>> largerSubMap = new HashMap<>();

            BigDecimal standardTimeCount = new BigDecimal(0);
            BigDecimal workingTimeCount = new BigDecimal(0);
            for (JobStandardItemDto itemDto : itemDtos) {
                if(!largeCategories.contains(itemDto.getLargeCategory())) {
                    largeCategories.add(itemDto.getLargeCategory());
                }
                //不同大类可能有相同小类名称，只需要相同大类内小类去重，不同大类间需要多次统计
                List<String> subCategories = new ArrayList<>();
                if(largerSubMap.containsKey(itemDto.getLargeCategory())){
                    subCategories = largerSubMap.get(itemDto.getLargeCategory());
                }
                if(!subCategories.contains(itemDto.getSubCategory())) {
                    subCategories.add(itemDto.getSubCategory());
                    largerSubMap.put(itemDto.getLargeCategory(), subCategories);
                }

                standardTimeCount = standardTimeCount.add(null != itemDto.getStandardTime() ? itemDto.getStandardTime() : new BigDecimal(0));
                workingTimeCount = workingTimeCount.add(null != itemDto.getWorkingTime() ? itemDto.getWorkingTime() : new BigDecimal(0));
            }
            countDto.setLargeCategoryCount(largeCategories.size());
            List<String> allSubCategories = new ArrayList<>();
            for(Map.Entry<String, List<String>> entity : largerSubMap.entrySet()){
                allSubCategories.addAll(entity.getValue().stream().distinct().collect(Collectors.toList()));
            }
            countDto.setSubCategoryCount(allSubCategories.size());
            countDto.setContentCount(itemDtos.size());
            countDto.setStandardTimeCount(standardTimeCount);
            countDto.setWorkingTimeCount(workingTimeCount);
        }
        return countDto;
    }

    @Override
    public Boolean updateStatus(List<String> standardIds, Integer status){
        List<JobStandard> jobStandards = new ArrayList<>(standardIds.size());
        for(String standardId : standardIds) {
            JobStandard jobStandard = new JobStandard();
            jobStandard.setId(standardId);
            jobStandard.setStatus(status);
            jobStandards.add(jobStandard);
        }
        return this.updateBatchById(jobStandards);
    }
}
