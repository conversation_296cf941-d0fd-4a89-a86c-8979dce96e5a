package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.base.dto.SchedulePlanDetailQueryParam;
import cn.getech.ehm.base.dto.SchedulePlanDetailResult;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.task.dto.ParameterCategoryDto;
import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.dto.task.info.MaintTaskPlanAddDto;
import cn.getech.ehm.task.entity.MaintPlan;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.PlanTriggerTime;
import cn.getech.ehm.task.enums.CbmFrequencyType;
import cn.getech.ehm.task.enums.TaskJobType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.ActivitiHandler;
import cn.getech.ehm.task.mapper.MaintPlanMapper;
import cn.getech.ehm.task.service.*;
import cn.getech.poros.bpm.client.ProcessServiceClient;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.dto.BaseDto;
import cn.getech.poros.bpm.dto.task.TaskDto;
import cn.getech.poros.bpm.param.process.ProcessStartParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskOptionParam;
import cn.getech.poros.bpm.param.task.TaskRejectParam;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 维护计划 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintPlanServiceImpl extends BaseServiceImpl<MaintPlanMapper, MaintPlan> implements IMaintPlanService {
    @Autowired
    private MaintPlanMapper maintPlanMapper;
    @Autowired
    private IPlanEquipmentTimeService planEquipmentTimeService;
    @Autowired
    private ProcessServiceClient processServiceClient;
    @Autowired
    private TaskServiceClient taskServiceClient;
    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private ITriggerTimeService triggerTimeService;
    @Autowired
    private IJobStandardService jobStandardService;
    @Autowired
    private ActivitiHandler activitiHandler;
    @Autowired
    private IMaintPlanCbmService cbmService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;

    @Value("${flow.maint.plan:E0005}")
    private String planCode;

    @Override
    public PageResult<MaintPlanListDto> pageDto(MaintPlanQueryParam queryParam) {
        Page<MaintPlanQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), MaintPlan.class, "plan"));
        if (StringUtils.isNotEmpty(queryParam.getEquipmentCategoryId())) {
            String categoryIdStrs = Optional.ofNullable(equipmentClient.getDtosIncludeChild(
                            new String[]{queryParam.getEquipmentCategoryId()}).getData()).orElseGet(ArrayList::new)
                    .stream().map(CategoryInfoDto::getId).collect(Collectors.joining("','"));
            queryParam.setEquipmentCategoryId("'" + categoryIdStrs + "'");
        }
        if (StringUtils.isNotEmpty(queryParam.getEquipmentLocationId())) {
            String locationIdStrs = Optional.ofNullable(
                            equipmentClient.getLocationChildIds(queryParam.getEquipmentLocationId()).getData())
                    .orElseGet(ArrayList::new).stream().collect(Collectors.joining("','"));
            queryParam.setEquipmentLocationId("'" + StringUtils.join(locationIdStrs, "','") + "'");
        }
        Boolean searchResult = this.buildTriggerTimeSearch(queryParam);
        IPage<MaintPlanListDto> result = new Page<>();
        if (searchResult) {
            result = maintPlanMapper.getPageList(page, queryParam);
        }
        List<MaintPlanListDto> list = result.getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> planIds = list.stream().map(MaintPlanListDto::getId).distinct().collect(Collectors.toList());
            Map<String, Date> lastPlanDateMap = maintTaskService.getLastPlanDateMap(planIds);
            Map<String, Date> nextPlanDateMap = triggerTimeService.getNextPlanDateMap(planIds);
            Map<String, Integer> taskCountMap = maintTaskService.getTaskCountMap(planIds);
            Date now = new Date();
            for (MaintPlanListDto dto : list) {
                //审批状态需要过滤权限
                if (dto.getStatus() != null && (dto.getStatus() == StaticValue.TWO || dto.getStatus() == StaticValue.THREE)) {
                    dto.setActivityId(maintTaskService.getProcessTaskId(dto.getProcessInstanceId(), false));
                }
                dto.setLastMaintTime(lastPlanDateMap.get(dto.getId()));
                //审核通过，获取离当天最近维保日期
                dto.setNextMaintTime(null != nextPlanDateMap.get(dto.getId()) ? nextPlanDateMap.get(dto.getId()) : dto.getNextMaintTime());
                dto.setTaskCount(null != taskCountMap.get(dto.getId()) ? taskCountMap.get(dto.getId()) : 0);
                if (dto.getExpiryTime() != null && dto.getExpiryTime().before(now)) {
                    dto.setExpiryFlag(true);
                }
            }
        }

        return Optional.ofNullable(PageResult.<MaintPlanListDto>builder()
                        .records(result.getRecords())
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    public Boolean buildTriggerTimeSearch(MaintPlanQueryParam queryParam) {
        if (ObjectUtils.isNotEmpty(queryParam.getBeginTriggerTime()) && ObjectUtils.isNotEmpty(queryParam.getEndTriggerTime())) {
            List<PlanTriggerTime> list = triggerTimeService.list(new QueryWrapper<PlanTriggerTime>().lambda().ge(PlanTriggerTime::getPlanMaintTime, queryParam.getBeginTriggerTime()).le(PlanTriggerTime::getPlanMaintTime, queryParam.getEndTriggerTime()));
            if (CollectionUtils.isNotEmpty(list)) {
                Set<String> collect = list.stream().map(PlanTriggerTime::getPlanId).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(collect)) {
                    queryParam.setPlanIds(new ArrayList<String>(collect));
                    return true;
                }
            }
        } else {
            return true;
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String saveByParam(MaintPlanAddParam addParam) {
        MaintPlan maintPlan = CopyDataUtil.copyObject(addParam, MaintPlan.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintPlan);
        save(maintPlan);
        if (CollectionUtils.isNotEmpty(addParam.getTimeDtos())) {
            planEquipmentTimeService.saveOrUpdateDto(addParam.getTimeDtos(), maintPlan.getId(), true);
        }
        if (addParam.getJobType().equals(TaskJobType.CBM.getValue())) {
            cbmService.saveByParam(addParam.getCbmTriggerDtos(), maintPlan.getId(), true);
        }
        //更新作业标准使用标识
        jobStandardService.updateStatus(Arrays.asList(new String[]{addParam.getStandardId()}), StaticValue.ONE);
        return maintPlan.getId();
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(MaintPlanEditParam editParam) {
        MaintPlan maintPlan = CopyDataUtil.copyObject(editParam, MaintPlan.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintPlan);
        Boolean flag = updateById(maintPlan);
        planEquipmentTimeService.saveOrUpdateDto(editParam.getTimeDtos(), maintPlan.getId(), false);
        if (editParam.getJobType().equals(TaskJobType.CBM.getValue())) {
            cbmService.saveByParam(editParam.getCbmTriggerDtos(), maintPlan.getId(), false);
        }
        // 已发布的修改状态变为待发布,清空计划发单日期
        if (maintPlan.getStatus() == StaticValue.ONE) {
            /*maintPlan.setStatus(StaticValue.ZERO);
            List<String> planIds = new ArrayList<>();
            planIds.add(editParam.getId());
            triggerTimeService.deleteByPlanIdsNow(planIds);*/
            List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                    .eq(MaintTask::getSourceId, maintPlan.getId())
                    .ge(MaintTask::getPlanMaintTime, new Date())
                    .in(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue(), TaskStatusType.RECEIVING.getValue(), TaskStatusType.REPAIR_AUDIT.getValue()));
            list.stream().forEach(item -> {
                maintTaskService.markDeletedBatch(item.getId());
            });
            planEquipmentTimeService.createTriggerTime(maintPlan.getId());
        }

        return flag;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean release(String id) {

        //获取对象计划单信息以及维保计划信息
        SynMaintPlanDto synMaintPlanDto = this.getSynById(id);
        if (null != synMaintPlanDto) {
            List<String> planIds = new ArrayList<>();
            planIds.add(id);
            Map<String, List<SynPlanEquipmentTimeDto>> planEquipmentTimeMap = planEquipmentTimeService.getMapByPlanIds(planIds);
            List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos = planEquipmentTimeMap.get(id);
            Map<String, DictionaryItemDto> jobTypeMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeRes = baseServiceClient.getItemMapByCode("job_type");
            if (jobTypeRes.isOk()) {
                jobTypeMap = jobTypeRes.getData();
            } else {
                log.info("连接base-service获取作业类别字典表失败");
            }
            if (CollectionUtils.isNotEmpty(planEquipmentTimeDtos)) {
                DictionaryItemDto dictionaryItemDto = jobTypeMap.get(synMaintPlanDto.getJobType());
                String jobTypeName = "";
                if (null != dictionaryItemDto) {
                    jobTypeName = dictionaryItemDto.getName();
                }
                List<MaintTaskPlanAddDto> maintTaskPlanAddDtos = createTaskOrder(synMaintPlanDto);
                if (CollectionUtils.isNotEmpty(maintTaskPlanAddDtos)) {
                    for (SynPlanEquipmentTimeDto timeDto : planEquipmentTimeDtos) {
                        TriggerTimeDto triggerTimeDto = CopyDataUtil.copyObject(timeDto, TriggerTimeDto.class);
                        //拼接日期
                        Calendar planCalendar = Calendar.getInstance();
                        planCalendar.setTime(new Date());
                        Calendar timeCalendar = Calendar.getInstance();
                        timeCalendar.setTime(timeDto.getPlanTime());
                        planCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
                        planCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
                        planCalendar.set(Calendar.SECOND, planCalendar.getActualMinimum(Calendar.SECOND));
                        triggerTimeDto.setPlanMaintTime(planCalendar.getTime());

                        for (MaintTaskPlanAddDto addDto : maintTaskPlanAddDtos) {
                            MaintTaskPlanAddDto newDto = CopyDataUtil.copyObject(addDto, MaintTaskPlanAddDto.class);
                            String name = newDto.getName() + jobTypeName + "工单";
                            newDto.setName(name);
                            this.buildTeamPerson(triggerTimeDto, newDto);
                            maintTaskService.savePlanTask(newDto);
                        }
                    }
                }

            }
        } else {
            log.error("没有待释放的计划");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }
        return true;
    }

    /**
     * 获取位置、类型下设备id集合
     *
     * @param locationId
     * @param categoryId
     * @return
     */
    @Override
    public List<String> getEquipmentIds(String locationId, String[] categoryId) {
        List<String> equipmentIds = new ArrayList<>();
        EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
        param.setCategoryIds(Arrays.asList(categoryId));
        param.setLocationId(locationId);
        RestResponse<List<String>> listRestResponse = equipmentClient.getEquipmentIdsByParamNonAuth(param);
        if (!listRestResponse.isOk()) {
            log.error("远程调用equipment-service出错");
        } else {
            equipmentIds = listRestResponse.getData();
        }
        return equipmentIds;
    }

    /**
     * 首次释放/定时器生成触发类型为周期的计划生成工单
     */
    @Override
    public List<MaintTaskPlanAddDto> createTaskOrder(SynMaintPlanDto synMaintPlanDto) {
        List<String> equipmentIds = new ArrayList<>();
        if (null == synMaintPlanDto.getEquipmentIds() || synMaintPlanDto.getEquipmentIds().length <= 0) {
            //没有设备id，为根据类型或位置取全部设备
            List<String> peEquipmentIds = this.getEquipmentIds(synMaintPlanDto.getInfoLocationId(), synMaintPlanDto.getInfoCategoryId());
            if (CollectionUtils.isNotEmpty(peEquipmentIds)) {
                equipmentIds.addAll(peEquipmentIds);
                synMaintPlanDto.setEquipmentIds(peEquipmentIds.toArray(new String[peEquipmentIds.size()]));
            }
        } else {
            equipmentIds.addAll(Arrays.asList(synMaintPlanDto.getEquipmentIds()));
        }
        if (CollectionUtils.isNotEmpty(equipmentIds)) {
            return buildTaskDtos(synMaintPlanDto);
        }
        return new ArrayList<>();
    }

    /**
     * 以上次日期生成工单
     */
    @Override
    public List<MaintTaskPlanAddDto> createLastTaskOrder(SynMaintPlanDto synMaintPlanDto, List<String> equipmentIds) {

        if (CollectionUtils.isNotEmpty(equipmentIds)) {
            synMaintPlanDto.setEquipmentIds(equipmentIds.toArray(new String[equipmentIds.size()]));
            return buildTaskDtos(synMaintPlanDto);
        }
        return null;
    }

    private List<MaintTaskPlanAddDto> buildTaskDtos(SynMaintPlanDto synMaintPlanDto) {
        List<MaintTaskPlanAddDto> taskAddDtos = new ArrayList<>();
        //量太大需要分批次查询
        String[] equipmentId = synMaintPlanDto.getEquipmentIds();

        RestResponse<Map<String, EquipmentListDto>> res = equipmentClient.getListByIds(equipmentId);
        Map<String, EquipmentListDto> infoMap = new HashMap<>();
        if (res.isOk()) {
            infoMap = res.getData();
        } else {
            log.info("连接equipment-service获取设备信息失败");
        }
        if (null != synMaintPlanDto.getEquipmentIds() && synMaintPlanDto.getEquipmentIds().length > 0) {
            for (String infoId : synMaintPlanDto.getEquipmentIds()) {
                EquipmentListDto equipmentListDto = infoMap.get(infoId);
                if (null != equipmentListDto) {
                    taskAddDtos.add(this.buildTaskAddDto(synMaintPlanDto, equipmentListDto));

                } else {
                    log.error("未找到对应设备");
                }
            }
        }
        return taskAddDtos;
    }

    private MaintTaskPlanAddDto buildTaskAddDto(SynMaintPlanDto synMaintPlanDto, EquipmentListDto equipmentListDto) {
        MaintTaskPlanAddDto taskPlanAddDto = CopyDataUtil.copyObject(synMaintPlanDto, MaintTaskPlanAddDto.class);
        taskPlanAddDto.setName(synMaintPlanDto.getName() + StringPool.DASH + equipmentListDto.getEquipmentName());
        taskPlanAddDto.setEquipmentId(equipmentListDto.getEquipmentId());
        taskPlanAddDto.setSourceId(synMaintPlanDto.getId());
        taskPlanAddDto.setSourceName(synMaintPlanDto.getName());
        taskPlanAddDto.setContent(synMaintPlanDto.getStandardName());
        taskPlanAddDto.setStandardId(synMaintPlanDto.getStandardId());
        taskPlanAddDto.setDeadlineDays(synMaintPlanDto.getDeadlineDays());
        taskPlanAddDto.setPlanTaskType(synMaintPlanDto.getSourceTaskType());
        if (synMaintPlanDto.getSourceTaskType() != null) {
            taskPlanAddDto.setPlanJobType(synMaintPlanDto.getJobType());
        }
        //先设置为设备班组人员
        taskPlanAddDto.setTeamIds(equipmentListDto.getTeamIds());
        taskPlanAddDto.setStaffIds(equipmentListDto.getMaintainerIds());
        return taskPlanAddDto;
    }

    /**
     * 构造维护班组维护人员
     *
     * @param dto
     * @param addDto
     * @return
     */
    @Override
    public void buildTeamPerson(TriggerTimeDto dto, MaintTaskPlanAddDto addDto) {
        addDto.setPlanMaintTime(dto.getPlanMaintTime());
        if (null != addDto.getDeadlineDays()) {
            addDto.setTaskDeadlineDate(DateUtil.offset(addDto.getPlanMaintTime(), DateField.HOUR, addDto.getDeadlineDays()));
        }
        if (dto.getPersonStrategy() == 0) {
            addDto.setFreeTask(0);
        } else if (dto.getPersonStrategy() == 1) {
            addDto.setTeamIds(dto.getTeamIds());
            addDto.setStaffIds(dto.getMaintainerIds());
            addDto.setFreeTask(0);
        } else if (dto.getPersonStrategy() == 2) {
            addDto.setFreeTask(1);
        } else if (dto.getPersonStrategy() == 3) {
            SchedulePlanDetailQueryParam param = new SchedulePlanDetailQueryParam();
            param.setSearchTime(dto.getPlanMaintTime() != null ? dto.getPlanMaintTime() : new Date());
            param.setMajor(addDto.getMajor());
            RestResponse<SchedulePlanDetailResult> scheduleMaintTeam = baseServiceClient.getScheduleMaintTeam(param);
            if (scheduleMaintTeam.isOk()) {
                SchedulePlanDetailResult data = scheduleMaintTeam.getData();
                String maintTeamId = data.getMaintTeamId();
                String[] memberIds = data.getMaintTeam().getMemberIds();
                addDto.setTeamIds(new String[]{maintTeamId});
                addDto.setStaffIds(memberIds);
            }
            addDto.setFreeTask(0);
        }
    }

    public void getStaffByScheduleDate(MaintTask maintTask) {
        SchedulePlanDetailQueryParam param = new SchedulePlanDetailQueryParam();
        param.setSearchTime(maintTask.getPlanMaintTime() != null ? maintTask.getPlanMaintTime() : new Date());
        param.setMajor(maintTask.getMajor());
        RestResponse<SchedulePlanDetailResult> scheduleMaintTeam = baseServiceClient.getScheduleMaintTeam(param);
        if (scheduleMaintTeam.isOk()) {
            SchedulePlanDetailResult data = scheduleMaintTeam.getData();
            String maintTeamId = data.getMaintTeamId();
            String[] memberIds = data.getMaintTeam().getMemberIds();
            maintTask.setTeamIds(new String[]{maintTeamId});
            maintTask.setStaffIds(memberIds);
        } else {
            //此时因为已经填充默认模式下的人，所以无需做处理
        }
    }

    @Override
    public Boolean markEnabled(PlanEnableEditParam param) {
        List<MaintPlan> planList = this.listByIds(param.getIdList());
        boolean result = false;
        if (CollectionUtils.isNotEmpty(planList)) {
            List<MaintPlan> publishPlanList = planList.stream().filter(plan -> plan.getStatus() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(publishPlanList)) {
                publishPlanList.forEach(plan -> plan.setEnabled(param.getEnabled()));
                result = this.updateBatchById(publishPlanList);
            }
        }
        return result;
    }

    @Override
    public PageResult<MaintPlanAppDto> planPageList(String equipmentId) {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean removeByIds(String[] ids) {
        Assert.notEmpty(ResultCode.PARAM_VALID_ERROR, ids);
        // 删除主表
        LambdaUpdateWrapper<MaintPlan> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(MaintPlan::getId, ids);
        wrapper.set(MaintPlan::getDeleted, DeletedType.YES.getValue());
        this.update(wrapper);
        //关联维保对象日期表不删除，为了找回记录

        //删除关联出单日期表
        triggerTimeService.deleteByPlanIds(Arrays.asList(ids));

        //对应维保对象作业标准id
        LambdaQueryWrapper<MaintPlan> selectWrapper = Wrappers.lambdaQuery();
        selectWrapper.in(MaintPlan::getId, ids);
        selectWrapper.select(MaintPlan::getId, MaintPlan::getStandardId, MaintPlan::getProcessInstanceId);
        List<MaintPlan> maintPlans = maintPlanMapper.selectList(selectWrapper);
        List<String> peStandardIds = maintPlans.stream().map(MaintPlan::getStandardId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(peStandardIds)) {
            //删除完之后还在使用的作业标准id
            List<String> usedStandardIds = getUsedStandardIds(peStandardIds);
            if (CollectionUtils.isNotEmpty(usedStandardIds)) {
                peStandardIds.removeAll(usedStandardIds);
            }
            if (CollectionUtils.isNotEmpty(peStandardIds)) {
                //未使用的作业标准id修改状态
                jobStandardService.updateStatus(peStandardIds, StaticValue.TWO);
            }
        }
        //删除对应工作流
        List<String> processInstanceIds = maintPlans.stream().filter(dto -> StringUtils.isNotBlank(dto.getProcessInstanceId())).map(MaintPlan::getProcessInstanceId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(processInstanceIds)) {
            processInstanceIds.stream().forEach(item -> activitiHandler.deleteByProcessId(item));
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean publish(String id) {
        MaintPlan maintPlan = (MaintPlan) this.getById(id);
        ProcessStartParam processStartParam = new ProcessStartParam();
        processStartParam.setProcessDefinitionKey(planCode);
        processStartParam.setProcessInstanceName(maintPlan.getName());
        processStartParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        processStartParam.setLevel(StaticValue.ONE);
        RestResponse<String> restResponse = processServiceClient.startProcess(processStartParam);
        if (log.isDebugEnabled()) {
            log.debug("启动维护工单流程参数{}响应{}", JSON.toJSONString(processStartParam), JSON.toJSONString(restResponse));
        }
        if (restResponse.isOk()) {
            LambdaUpdateWrapper<MaintPlan> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(MaintPlan::getStatus, StaticValue.TWO);
            wrapper.set(MaintPlan::getProcessInstanceId, restResponse.getData());
            wrapper.eq(MaintPlan::getId, id);
            return this.update(wrapper);
        } else {
            log.error("启动流程实例失败," + restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Override
    public Integer getCount(String tableName, String columnName, String value) {
        return maintPlanMapper.getCount(tableName, columnName, value);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean submitProcessTask(PlanAuditParam auditParam) {
        PlanSubmitType submitType = PlanSubmitType.valueOf(auditParam.getSubmitType());
        switch (submitType) {
            case SUBMIT:
                return this.publish(auditParam.getPlanId());
            case CONFIRM:
                return this.confirm(auditParam.getActivityId(), auditParam.getPlanId(), auditParam.getRemark());
            case REJECT:
                return this.reject(auditParam.getActivityId(), auditParam.getPlanId(), auditParam.getRemark());
            case AGAIN_SUBMIT:
                return this.againSubmit(auditParam.getActivityId(), auditParam.getPlanId(), auditParam.getRemark());
            case TRASH:
                return this.trashTask(auditParam.getActivityId(), auditParam.getPlanId(), auditParam.getRemark());
            default:
                log.error("未知的流程任务提交类型");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
    }

    /**
     * 审核
     *
     * @param taskId
     * @param planId
     * @param comment
     */
    private Boolean confirm(String taskId, String planId, String comment) {
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskId);
        taskCompleteParam.setComment(comment);
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk()) {
            log.error("启动流程实例失败," + completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        // 审核同意, 更新计划为已发布
        LambdaUpdateWrapper<MaintPlan> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(MaintPlan::getStatus, StaticValue.ONE);
        wrapper.set(MaintPlan::getEnabled, StaticValue.ONE);
        wrapper.eq(MaintPlan::getId, planId);
        this.update(wrapper);

        //构造计划发单日期
        planEquipmentTimeService.createTriggerTime(planId);
        return true;
    }

    /**
     * 驳回
     *
     * @param taskId
     * @param planId
     * @param comment
     */
    private Boolean reject(String taskId, String planId, String comment) {
        // 发起任务驳回流程
        TaskRejectParam taskRejectParam = new TaskRejectParam();
        taskRejectParam.setTaskId(taskId);
        taskRejectParam.setComment(comment);
        RestResponse<Object> rejectTask = taskServiceClient.rejectTask(taskRejectParam);
        log.info("流程节点驳回任务提交参数{}响应{}", JSON.toJSONString(taskRejectParam), JSON.toJSONString(rejectTask));
        if (!rejectTask.isOk()) {
            log.error("启动流程实例失败," + rejectTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        // 驳回
        LambdaUpdateWrapper<MaintPlan> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(MaintPlan::getStatus, StaticValue.THREE);
        wrapper.eq(MaintPlan::getId, planId);
        return this.update(wrapper);
    }

    private Boolean againSubmit(String taskId, String planId, String comment) {
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskId);
        taskCompleteParam.setComment(comment);
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk()) {
            log.error("启动流程实例失败," + completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        MaintPlan maintPlan = new MaintPlan();
        maintPlan.setId(planId);
        //更新计划为审核中
        maintPlan.setStatus(StaticValue.TWO);
        return this.updateById(maintPlan);
    }


    public boolean trashTask(String taskId, String planId, String comment) {
        MaintPlan maintPlan = new MaintPlan();
        maintPlan.setId(planId);

        if (StringUtils.isNotBlank(taskId)) {
            TaskOptionParam taskOptionParam = new TaskOptionParam();
            taskOptionParam.setTaskId(taskId);
            taskOptionParam.setComment(comment);
            RestResponse<Object> trashTask = taskServiceClient.trashTask(taskOptionParam);
            log.info("工作流返回消息->" + JSONObject.toJSON(trashTask));
            if (trashTask.isOk()) {
                maintPlan.setStatus(StaticValue.MINUS_ONE);
            }
        } else {
            maintPlan.setStatus(StaticValue.MINUS_ONE);
        }
        return maintPlanMapper.updateById(maintPlan) > StaticValue.ONE;
    }

    @Override
    public Map<String, SynMaintPlanDto> getSynListByIds(List<String> planIds) {
        Map<String, SynMaintPlanDto> map = new HashMap<>();
        List<SynMaintPlanDto> synMaintPlanDtos = maintPlanMapper.getSynListByIds(planIds);
        if (CollectionUtils.isNotEmpty(synMaintPlanDtos)) {
            return synMaintPlanDtos.stream().collect(Collectors.toMap(SynMaintPlanDto::getId, v -> v, (v1, v2) -> v1));
        }
        return map;
    }

    @Override
    public SynMaintPlanDto getSynById(String planId) {
        return maintPlanMapper.getSynById(planId);
    }

    @Override
    public MaintPlanDto getMaintPlanDtoByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<MaintPlan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MaintPlan::getProcessInstanceId, processInstanceId);
        List<MaintPlan> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list) && list.size() == 1) {
            return CopyDataUtil.copyObject(list.get(0), MaintPlanDto.class);
        }
        return new MaintPlanDto();
    }

    @Override
    public MaintPlanDto getDtoById(String id) {
        MaintPlanDto maintPlanDto = maintPlanMapper.getMaintPlanDto(id);

        if (null != maintPlanDto) {
            //审批状态需要过滤权限
            if (maintPlanDto.getStatus() == StaticValue.TWO || maintPlanDto.getStatus() == StaticValue.THREE) {
                maintPlanDto.setActivityId(maintTaskService.getProcessTaskId(maintPlanDto.getProcessInstanceId(), false));
            }

            String[] uids = new String[]{maintPlanDto.getCreateBy(), maintPlanDto.getUpdateBy()};
            Map<String, String> uidNameMap = new HashMap<>();
            RestResponse<Map<String, String>> restResponse = porosSecStaffClient.getMap(StringUtils.join(uids, com.baomidou.mybatisplus.core.toolkit.StringPool.COMMA));
            if (restResponse.isOk()) {
                uidNameMap = restResponse.getData();
            } else {
                log.info("连接porosSecStaffClient失败");
            }
            maintPlanDto.setCreateUserName(uidNameMap.get(maintPlanDto.getCreateBy()));
            maintPlanDto.setUpdateUserName(uidNameMap.get(maintPlanDto.getUpdateBy()));
            maintPlanDto.setTimeDtos(planEquipmentTimeService.getListByPlanId(id));
            if (maintPlanDto.getJobType().equals(TaskJobType.CBM.getValue())) {
                maintPlanDto.setCbmTriggerDtos(cbmService.getCbmDetail(maintPlanDto.getId()));
            }

            Map<String, String> categoryNameMap = new HashMap<>();
            if (ArrayUtils.isNotEmpty(maintPlanDto.getInfoCategoryId())) {
                RestResponse<Map<String, String>> categoryRes = equipmentClient.getCategoryMapByIds(maintPlanDto.getInfoCategoryId());
                if (categoryRes.isOk()) {
                    categoryNameMap = categoryRes.getData();
                } else {
                    log.error("获取设备类型失败");
                }
            }
            Map<String, String> locationNameMap = new HashMap<>();
            if (StringUtils.isNotBlank(maintPlanDto.getInfoLocationId())) {
                RestResponse<Map<String, String>> locationRes = equipmentClient.getLocationMapByIds(new String[]{maintPlanDto.getInfoLocationId()});
                if (locationRes.isOk()) {
                    locationNameMap = locationRes.getData();
                } else {
                    log.error("获取设备位置失败");
                }
            }
            Map<String, EquipmentListDto> equipmentMap = new HashMap<>();
            if (null != maintPlanDto.getEquipmentIds() && maintPlanDto.getEquipmentIds().length > 0) {
                RestResponse<Map<String, EquipmentListDto>> equipmentRes = equipmentClient.getListByIds(maintPlanDto.getEquipmentIds());
                if (equipmentRes.isOk()) {
                    equipmentMap = equipmentRes.getData();

                } else {
                    log.error("获取设备信息失败");
                }
            }
            maintPlanDto.setInfoCategoryName(categoryNameMap.get(maintPlanDto.getInfoCategoryId()));
            maintPlanDto.setInfoLocationName(locationNameMap.get(maintPlanDto.getInfoLocationId()));
            if (null != maintPlanDto.getEquipmentIds() && maintPlanDto.getEquipmentIds().length > 0) {
                buildEquipmentNames(maintPlanDto.getEquipmentIds(), equipmentMap, maintPlanDto);
            }
        }
        return maintPlanDto;
    }

    /**
     * 构造设备类型名称集合
     *
     * @param equipmentIds
     * @return
     */
    private void buildEquipmentNames(String[] equipmentIds, Map<String, EquipmentListDto> equipmentMap, MaintPlanDto maintPlanDto) {
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for (String equipmentId : equipmentIds) {
            EquipmentListDto equipmentListDto = equipmentMap.get(equipmentId);
            if (null != equipmentListDto) {
                names.add(equipmentListDto.getEquipmentName() + StringPool.SLASH + equipmentListDto.getEquipmentCode());
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(equipmentListDto.getEquipmentId());
                detailDto.setName(equipmentListDto.getEquipmentName());
                detailDto.setCode(equipmentListDto.getEquipmentCode());
                detailDtos.add(detailDto);
            }
        }
        if (CollectionUtils.isNotEmpty(names)) {
            //拼接找到的名称
            maintPlanDto.setEquipmentNames(StringUtils.join(names.toArray(), StringPool.COMMA));
        }
        //无值也初始化
        maintPlanDto.setEquipmentDtos(detailDtos);
    }

    @Override
    public List<String> getUsedStandardIds(List<String> standardIds) {
        return maintPlanMapper.getUsedStandardIds(standardIds);
    }

    public PageResult<MaintPlanDto> getTodoOfPlan(MaintPlanQueryParam param) {
        PageResult<MaintPlanDto> pageResult = new PageResult();
        BaseDto<TaskDto> todoList = activitiHandler.getTodoList(new Page(param.getPageNo(), param.getLimit()), planCode);
        if (todoList != null && CollectionUtils.isNotEmpty(todoList.getRecords())) {
            List<String> collect = todoList.getRecords().stream().filter(item -> item != null).filter(item -> item.getProcessInstanceDTO() != null).map(item -> item.getProcessInstanceDTO().getProcessInstanceId()).collect(Collectors.toList());
            if (collect.size() == 0) {
                return pageResult;
            }
            List<MaintPlan> maintPlanList = this.list(new QueryWrapper<MaintPlan>().lambda().in(MaintPlan::getProcessInstanceId, collect).orderByDesc(MaintPlan::getUpdateTime));
            pageResult.setTotal(todoList.getTotal());
            pageResult.setRecords(CopyDataUtil.copyList(maintPlanList, MaintPlanDto.class));
        }
        return pageResult;
    }

    @Override
    public Boolean createCbmOrder(ParameterCategoryDto dto) {

        executorService.execute(() -> {
            UserBaseInfo baseInfo = JSONObject.parseObject("{\"uid\":\"" + dto.getCreateBy() + "\"," +
                    "\"tenantId\":\"" + dto.getTenantId() + "\"}", UserBaseInfo.class);
            UserContextHolder.getContext().setUserBaseInfo(baseInfo);

            //获取设备
            EquipmentListDto equipmentListDto = null;
            RestResponse<Map<String, EquipmentListDto>> res = equipmentClient.getListByIds(new String[]{dto.getEquipmentId()});
            if (res.isOk()) {
                equipmentListDto = res.getData().get(dto.getEquipmentId());
            } else {
                log.info("连接equipment-service获取设备信息失败");
            }
            if (null != equipmentListDto) {
                //维保对象位置需过滤父级
                String[] locationIds = equipmentListDto.getLocationLayerCode().split(StringPool.SLASH);
                String categoryId = equipmentListDto.getCategoryId();
                List<String> planIds = maintPlanMapper.getCbmPlanIds(locationIds, categoryId, dto.getEquipmentId(), TaskJobType.CBM.getValue(), dto.getCategoryParamId());
                if (CollectionUtils.isNotEmpty(planIds)) {
                    //获取cbm计划单对应触发器
                    log.info("-----iot推数触发cbm计划单");
                    List<String> createTaskPlanIds = cbmService.satisfyTriggerPlanId(planIds, dto.getCategoryParamId(), dto.getAllParamValueMap());
                    if (CollectionUtils.isNotEmpty(createTaskPlanIds)) {
                        this.saveCbmTask(createTaskPlanIds, equipmentListDto);
                    }
                }
            }
        });
        return true;
    }

    private void saveCbmTask(List<String> planIds, EquipmentListDto equipmentListDto) {
        Map<String, SynMaintPlanDto> planDtoMap = this.getSynListByIds(planIds);
        Map<String, List<SynPlanEquipmentTimeDto>> planEquipmentTimeMap = planEquipmentTimeService.getMapByPlanIds(planIds);
        List<MaintTaskPlanAddDto> maintTaskPlanAddDtos = new ArrayList<>();
        for (Map.Entry<String, SynMaintPlanDto> entity : planDtoMap.entrySet()) {
            //cbm人员只有一组
            List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos = planEquipmentTimeMap.get(entity.getKey());
            if (CollectionUtils.isNotEmpty(planEquipmentTimeDtos)) {
                //判断该维保计划内的设备是否在周期内已经生成工单
                String key = entity.getKey() + StringPool.COLON + equipmentListDto.getEquipmentId();
                Object keyValue = redisTemplate.opsForValue().get(key);
                if (null == keyValue) {
                    maintTaskPlanAddDtos.add(this.buildTaskAddDto(entity.getValue(), equipmentListDto));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(maintTaskPlanAddDtos)) {
            for (MaintTaskPlanAddDto addDto : maintTaskPlanAddDtos) {
                List<SynPlanEquipmentTimeDto> synPlanEquipmentTimeDtos = planEquipmentTimeMap.get(addDto.getSourceId());

                if (CollectionUtils.isEmpty(synPlanEquipmentTimeDtos)) {
                    //未查询到对应维护人员
                    continue;
                }
                TriggerTimeDto triggerTimeDto = CopyDataUtil.copyObject(synPlanEquipmentTimeDtos.get(0), TriggerTimeDto.class);
                triggerTimeDto.setPlanMaintTime(new Date());

                String name = addDto.getName() + TaskJobType.CBM.getName() + "工单";
                addDto.setName(name);
                this.buildTeamPerson(triggerTimeDto, addDto);
                maintTaskService.savePlanTask(addDto);
                String key = addDto.getSourceId() + StringPool.COLON + equipmentListDto.getEquipmentId();
                if (addDto.getCbmFrequency() != CbmFrequencyType.ZERO.getValue()) {
                    //非0小时间隔需要标记
                    redisTemplate.opsForValue().set(key, true, CbmFrequencyType.getHourByValue(addDto.getCbmFrequency()), TimeUnit.HOURS);
                }
            }
        }
    }

    public void markDeletedBatch(String id) {
        MaintPlan byId = (MaintPlan) this.getById(id);
        if (byId != null) {
            List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda().select(MaintTask::getId, MaintTask::getProcessInstanceId).eq(MaintTask::getSourceId, id));
            List<String> idList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<String> processInstanceIdList = list.stream().filter(item -> StringUtils.isNotBlank(item.getProcessInstanceId())).map(item -> item.getProcessInstanceId()).collect(Collectors.toList());
            maintTaskService.removeByIds(idList);
            processInstanceIdList.stream().forEach(item -> {
                activitiHandler.deleteByProcessId(item);
            });

        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("该计划不存在"));
        }
    }

    @Override
    public String getStatisticsOfPlanEndCount(String type) {
        //1 1周内临近2今日到期3过期
        LambdaQueryWrapper<MaintPlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintPlan::getStatus, StaticValue.ONE);
        wrapper.eq(MaintPlan::getEnabled, StaticValue.ONE);
        wrapper.eq(MaintPlan::getDeleted, DeletedType.NO.getValue());
        if (type.equals("1")) {
            Date beginDate = DateUtil.beginOfDay(new Date());
            Date endDate = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), 6));
            wrapper.between(MaintPlan::getExpiryTime, beginDate, endDate);
        } else if (type.equals("2")) {
            Date beginDate = DateUtil.beginOfDay(new Date());
            Date endDate = DateUtil.endOfDay(new Date());
            wrapper.between(MaintPlan::getExpiryTime, beginDate, endDate);
        } else if (type.equals("3")) {
            wrapper.le(MaintPlan::getExpiryTime, new Date());
        } else {
            wrapper.eq(MaintPlan::getId, "-1");
        }
        return maintPlanMapper.selectCount(wrapper).toString();
    }

    public MaintPlanQueryParam buildPlanByEquipmentId(MaintPlanQueryParam param) {
        RestResponse<EquipmentInfoDto> equipmentInfo = equipmentClient.getEquipmentInfo(param.getEquipmentId());
        if (!equipmentInfo.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("设备不存在"));
        }
        EquipmentInfoDto data = equipmentInfo.getData();
        String[] categoryLayer = data.getCategoryLayer().split("/");
        String[] locationLayer = data.getLocationLayer().split("/");
        param.setCategoryLayer(categoryLayer);
        param.setLocationLayer(locationLayer);
        return param;
    }
}
