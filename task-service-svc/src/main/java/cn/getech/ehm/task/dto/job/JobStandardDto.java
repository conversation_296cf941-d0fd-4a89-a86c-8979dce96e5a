package cn.getech.ehm.task.dto.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 作业标准
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardDto", description = "作业标准dto")
public class JobStandardDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id集")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String equipmentCategoryName;

    @ApiModelProperty(value = "设备位置id")
    private String equipmentLocationId;

    @ApiModelProperty(value = "设备位置名称")
    private String equipmentLocationName;

    @ApiModelProperty(value = "专业类别集合")
    private String[] majors;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "是否发布")
    private Boolean published;

    @ApiModelProperty(value = "是否停机")
    private Boolean stopped;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "作业项目集合")
    private List<JobStandardItemDto> itemDtos;

    @ApiModelProperty(value = "作业项目统计")
    private ItemCountDto countDto;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    private String equipmentName;

    @ApiModelProperty(value = "适用工单类型")
    private String taskType;
}