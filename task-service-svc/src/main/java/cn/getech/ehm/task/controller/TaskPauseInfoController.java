package cn.getech.ehm.task.controller;


import cn.getech.ehm.task.dto.TaskPauseInfoCountDto;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.annotation.Permission;
import cn.getech.poros.framework.common.api.PageResult;
import org.apache.commons.collections4.CollectionUtils;
import cn.getech.ehm.task.dto.TaskPauseInfoQueryParam;
import cn.getech.ehm.task.dto.TaskPauseInfoDto;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.task.service.ITaskPauseInfoService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单暂停记录表控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@RestController
@RequestMapping("/taskPauseInfo")
@Api(tags = "工单暂停记录表服务接口")
public class TaskPauseInfoController {

    @Autowired
    private ITaskPauseInfoService taskPauseInfoService;

    /**
     * 分页获取工单暂停记录表列表
     */
    @ApiOperation("分页获取工单暂停记录表列表")
    @GetMapping("/list")
    //@Permission("task:pause:info:list")
    public RestResponse<PageResult<TaskPauseInfoDto>> pageList(@Valid TaskPauseInfoQueryParam taskPauseInfoQueryParam){
        return RestResponse.ok(taskPauseInfoService.pageDto(taskPauseInfoQueryParam));
    }

    @ApiOperation("获取总数和总时间")
    @GetMapping("/getCount")
    //@Permission("task:pause:info:list")
    public RestResponse<TaskPauseInfoCountDto> getCount(@Valid TaskPauseInfoQueryParam taskPauseInfoQueryParam){
        return RestResponse.ok(taskPauseInfoService.getCount(taskPauseInfoQueryParam));
    }

}
