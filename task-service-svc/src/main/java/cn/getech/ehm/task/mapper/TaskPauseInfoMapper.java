package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.TaskPauseInfoCountDto;
import cn.getech.ehm.task.dto.TaskPauseInfoQueryParam;
import cn.getech.ehm.task.entity.TaskPauseInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface TaskPauseInfoMapper extends BaseMapper<TaskPauseInfo> {

    TaskPauseInfoCountDto getTotalTime(@Param("taskId") String taskId);
}
