package cn.getech.ehm.task.dto;


import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "TaskPauseInfoCountDto", description = "返回数据模型")
public class TaskPauseInfoCountDto {


    @ApiModelProperty(value = "总次数")
    private Integer totalCount;

    @ApiModelProperty(value = "总时长")
    private BigDecimal totalTime;
}
