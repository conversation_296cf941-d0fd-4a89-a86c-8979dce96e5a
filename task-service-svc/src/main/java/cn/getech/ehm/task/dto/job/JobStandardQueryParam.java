package cn.getech.ehm.task.dto.job;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Map;

/**
 * 作业标准查询参数
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "JobStandardQueryParam", description = "作业标准查询参数")
public class JobStandardQueryParam extends PageParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "设备位置id")
    private String equipmentLocationId;

    @ApiModelProperty(value = "专业类别")
    private String major;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "使用状态1使用中2未使用")
    private Integer status;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "适用工单类型")
    private String taskType;

}
