package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.TaskPauseInfo;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  TaskPauseInfoParamMapper{



    /**
     * 实体转换为Dto
     * @param taskPauseInfo
     * @return
     */
    TaskPauseInfoDto entity2Dto(TaskPauseInfo taskPauseInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<TaskPauseInfoDto> pageEntity2Dto(PageResult<TaskPauseInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<TaskPauseInfo> dtoList2Entity(List<TaskPauseInfoDto> rows);

}
