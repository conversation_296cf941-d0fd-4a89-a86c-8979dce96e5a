package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.task.dto.TaskPauseInfoCountDto;
import cn.getech.ehm.task.entity.TaskPauseInfo;
import cn.getech.ehm.task.mapper.TaskPauseInfoMapper;
import cn.getech.ehm.task.service.ITaskPauseInfoService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.task.dto.TaskPauseInfoQueryParam;
import cn.getech.ehm.task.dto.TaskPauseInfoParamMapper;
import cn.getech.ehm.task.dto.TaskPauseInfoDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import cn.hutool.core.util.ArrayUtil;



/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@Service
public class TaskPauseInfoServiceImpl extends BaseServiceImpl<TaskPauseInfoMapper, TaskPauseInfo> implements ITaskPauseInfoService {

    @Autowired
    private TaskPauseInfoParamMapper taskPauseInfoParamMapper;
    @Autowired
    private TaskPauseInfoMapper taskPaInfoMapper;

    @Override
    public PageResult<TaskPauseInfoDto> pageDto(TaskPauseInfoQueryParam taskPauseInfoQueryParam) {
        Wrapper<TaskPauseInfo> wrapper = getPageSearchWrapper(taskPauseInfoQueryParam);
        PageResult<TaskPauseInfoDto> result = taskPauseInfoParamMapper.pageEntity2Dto(page(taskPauseInfoQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public TaskPauseInfoCountDto getCount(TaskPauseInfoQueryParam taskPauseInfoQueryParam) {
        return taskPaInfoMapper.getTotalTime(taskPauseInfoQueryParam.getTaskId());
    }

    private Wrapper<TaskPauseInfo> getPageSearchWrapper(TaskPauseInfoQueryParam taskPauseInfoQueryParam) {
        LambdaQueryWrapper<TaskPauseInfo> wrapper = Wrappers.<TaskPauseInfo>lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(taskPauseInfoQueryParam.getTaskId()),
                     TaskPauseInfo::getTaskId, taskPauseInfoQueryParam.getTaskId());
        if(BaseEntity.class.isAssignableFrom(TaskPauseInfo.class)){
            wrapper.orderByDesc(TaskPauseInfo::getUpdateTime,TaskPauseInfo::getCreateTime);
        }
        return wrapper;
    }
}
