package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "TaskPauseInfoDto", description = "返回数据模型")
public class TaskPauseInfoDto{

    @ApiModelProperty(value = "时间key")
    @Excel(name="时间key",cellType = Excel.ColumnType.STRING )
    private String timeKey;

    @ApiModelProperty(value = "工单id")
    @Excel(name="工单id",cellType = Excel.ColumnType.STRING )
    private String taskId;

    @ApiModelProperty(value = "工单名称")
    @Excel(name="工单名称",cellType = Excel.ColumnType.STRING )
    private String taskName;

    @ApiModelProperty(value = "工单编码")
    @Excel(name="工单编码",cellType = Excel.ColumnType.STRING )
    private String taskCode;

    @ApiModelProperty(value = "暂停开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date pauseStartTime;

    @ApiModelProperty(value = "暂停结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date pauseEndTime;

    @ApiModelProperty(value = "暂停时长/小时")
    @Excel(name="暂停时长/小时",cellType = Excel.ColumnType.STRING )
    private BigDecimal pauseTotal;

    @ApiModelProperty(value = "暂停原因")
    @Excel(name="暂停原因",cellType = Excel.ColumnType.STRING )
    private String pauseCause;

    @ApiModelProperty(value = "暂停原因描述")
    @Excel(name="暂停原因描述",cellType = Excel.ColumnType.STRING )
    private String pauseCauseRemark;

    @ApiModelProperty(value = "操作人")
    @Excel(name="操作人",cellType = Excel.ColumnType.STRING )
    private String operator;

    @ApiModelProperty(value = "操作人uid")
    @Excel(name="操作人uid",cellType = Excel.ColumnType.STRING )
    private String operatorUid;

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name="createBy",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "updateBy")
    @Excel(name="updateBy",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

}