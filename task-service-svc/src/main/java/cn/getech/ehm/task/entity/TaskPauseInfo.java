package cn.getech.ehm.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("task_pause_info")
public class TaskPauseInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 时间key
     */
    @TableField("time_key")
    private String timeKey;

    /**
     * 工单id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 工单名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 工单编码
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 暂停开始时间
     */
    @TableField("pause_start_time")
    private Date pauseStartTime;

    /**
     * 暂停结束时间
     */
    @TableField("pause_end_time")
    private Date pauseEndTime;

    /**
     * 暂停时长/小时
     */
    @TableField("pause_total")
    private BigDecimal pauseTotal;

    /**
     * 暂停原因
     */
    @TableField("pause_cause")
    private String pauseCause;

    /**
     * 暂停原因描述
     */
    @TableField("pause_cause_remark")
    private String pauseCauseRemark;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作人uid
     */
    @TableField("operator_uid")
    private String operatorUid;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;


}
