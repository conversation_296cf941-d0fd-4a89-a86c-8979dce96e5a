package cn.getech.ehm.task.dto.job;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 作业标准列表
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardListDto", description = "作业标准列表")
public class JobStandardListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id集")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String equipmentCategoryName;

    @ApiModelProperty(value = "设备位置id")
    private String equipmentLocationId;

    @ApiModelProperty(value = "设备位置名称")
    private String equipmentLocationName;

    @ApiModelProperty(value = "专业类别集合")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] majors;

    @ApiModelProperty(value = "专业类别名称")
    private String majorNames;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "作业类别名称")
    private String jobTypeName;

    @ApiModelProperty(value = "发布状态")
    private Boolean published;

    @ApiModelProperty(value = "使用状态1使用中2未使用")
    private Integer status;

    @ApiModelProperty(value = "标准工时统计")
    private BigDecimal standardTimeCount;

    @ApiModelProperty(value = "作业时间统计")
    private BigDecimal workingTimeCount;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "适用工单类型")
    private String taskType;


    @ApiModelProperty(value = "是否停机")
    private Boolean stopped;
}