package cn.getech.ehm.knowledge.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 * 新闻资讯信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-05
 */
@Data
@ApiModel(value = "NewsInfoDto", description = "新闻资讯信息表返回数据模型")
public class NewsInfoDto {


    @ApiModelProperty(value = " 全局id ")
    @Excel(name=" 全局id ",cellType = Excel.ColumnType.STRING)
    private String id;


    @ApiModelProperty(value = " 图文标题 ")
    @Excel(name=" 图文标题 ",cellType = Excel.ColumnType.STRING)
    private String title;


    @ApiModelProperty(value = " 分类ID ")
    @Excel(name=" 分类ID ",cellType = Excel.ColumnType.STRING)
    private String categoryId;


    @ApiModelProperty(value = " 摘要概述 ")
    @Excel(name=" 摘要概述 ",cellType = Excel.ColumnType.STRING)
    private String summary;


    @ApiModelProperty(value = " 图文内容 ")
    @Excel(name=" 图文内容 ",cellType = Excel.ColumnType.STRING)
    private String content;


    @ApiModelProperty(value = " 发布时间 ")
    @Excel(name=" 发布时间 ",cellType = Excel.ColumnType.STRING)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;


    @ApiModelProperty(value = " 排序序号 ")
    @Excel(name=" 排序序号 ",cellType = Excel.ColumnType.STRING)
    private Integer sortNumber;


    @ApiModelProperty(value = " 浏览量 ")
    @Excel(name=" 浏览量 ",cellType = Excel.ColumnType.STRING)
    private Integer scanCount;


    @ApiModelProperty(value = "分类名")
    @Excel(name="分类名称",cellType = Excel.ColumnType.STRING )
    private String categoryName;

    @ApiModelProperty(value = "是否收藏。true-是，false-不是")
    private Boolean isFavorite;

}