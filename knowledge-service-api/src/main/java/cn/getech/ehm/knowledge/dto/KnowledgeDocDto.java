package cn.getech.ehm.knowledge.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 知识学习库 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudyKnowledgeDocDto", description = "知识学习库返回数据模型")
public class KnowledgeDocDto {

    @ApiModelProperty(value = "名称")
    @Excel(name="名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "描述")
    @Excel(name="描述",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "附件ID")
    private String attachId;

    @ApiModelProperty(value = "知识文档分类ID")
    private String categoryId;

    @ApiModelProperty(value = "分类名 A-B-C")
    private String categoryName;

    @ApiModelProperty(value = "是否收藏。true-是，false-不是")
    private Boolean isFavorite;

}