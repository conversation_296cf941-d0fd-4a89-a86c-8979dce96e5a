package cn.getech.ehm.knowledge.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <pre>
 * 课程信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Data
@ApiModel(value = "CourseInfoDto", description = "课程信息表返回数据模型")
public class CourseInfoDto {

    @ApiModelProperty(value = "全局id。")
    @Excel(name="全局id。",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "标题。")
    @Excel(name="标题。",cellType = Excel.ColumnType.STRING)
    private String title;


    @ApiModelProperty(value = "分类id。")
    @Excel(name="分类id。",cellType = Excel.ColumnType.STRING)
    private String categoryId;

    @ApiModelProperty(value = "封面图片id。")
    @Excel(name="封面图片id。",cellType = Excel.ColumnType.STRING)
    private String imgId;

    @ApiModelProperty(value = "封面图片url。")
    @Excel(name="封面图片url。",cellType = Excel.ColumnType.STRING)
    private String imgUrl;

    @ApiModelProperty(value = "视频id。")
    @Excel(name="视频id。",cellType = Excel.ColumnType.STRING)
    private String videoId;

    @ApiModelProperty(value = "播放地址。")
    @Excel(name="播放地址。",cellType = Excel.ColumnType.STRING)
    private String playUrl;

    @ApiModelProperty(value = "下载地址。")
    @Excel(name="下载地址。",cellType = Excel.ColumnType.STRING)
    private String downloadUrl;

    @ApiModelProperty(value = "视频时长。单位：秒")
    @Excel(name="视频时长。",cellType = Excel.ColumnType.STRING)
    private Integer duration;

    @ApiModelProperty(value = "学习人数。")
    @Excel(name="学习人数。",cellType = Excel.ColumnType.STRING)
    private Integer learningCount;

    @ApiModelProperty(value = "点赞数。")
    @Excel(name="点赞数。",cellType = Excel.ColumnType.STRING)
    private Integer likeCount;

    @ApiModelProperty(value = "评论数。")
    @Excel(name="评论数。",cellType = Excel.ColumnType.STRING)
    private Integer commentCount;

    @ApiModelProperty(value = "备注信息。")
    @Excel(name="备注信息。",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "分类名")
    @Excel(name="分类名称",cellType = Excel.ColumnType.STRING )
    private String categoryName;

    @ApiModelProperty(value = "是否点赞。true-是，false-不是")
    private Boolean isLike;

    @ApiModelProperty(value = "是否收藏。true-是，false-不是")
    private Boolean isFavorite;

    @ApiModelProperty(value = "指定开放的客户ID列表")
    private List<String> uids;

    @ApiModelProperty(value = "是否为公开课程")
    private Boolean isPublic;

    @ApiModelProperty(value = "视频文件名")
    private String videoFileName;


}