package cn.getech.ehm.knowledge.client;

import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.knowledge.dto.KnowledgeDto;
import cn.getech.ehm.knowledge.dto.KnowledgeQueryParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 知识文档模块
 * <AUTHOR>
 */
@FeignClient(name = "knowledge-service",path = "/api/knowledge-service")
public interface KnowledgeClient {

    @PostMapping("/appKnowledge/listKnowledge")
    RestResponse<KnowledgeDto> listKnowledge(@RequestBody KnowledgeQueryParam queryParam);


    @GetMapping("/courseInfo/getCourseCount")
    RestResponse<Integer> getCourseCount();

    @GetMapping("/courseInfo/getPlayCount")
    RestResponse<Integer> getPlayCount();
}
