package cn.getech.ehm.knowledge.client;

import cn.getech.ehm.knowledge.dto.DemoClientDto;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/3/3
 */
@FeignClient(name = "kjs-service-study",path = "/api/kjs-service-study")
public interface DemoUserClient {

    /**
     * demoUser
     *
     * @param name
     * @return RestResponse<String>
     */
    @GetMapping(value = "/demo/hello")
    RestResponse<DemoClientDto> getById(@RequestParam("name") @NotBlank String name);
}
