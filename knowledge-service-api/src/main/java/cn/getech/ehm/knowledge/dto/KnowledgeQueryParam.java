package cn.getech.ehm.knowledge.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "查找 知识文档，新闻资讯，课程信息", description = "查找 知识文档，新闻资讯，课程信息")
public class KnowledgeQueryParam extends PageParam {

    @ApiModelProperty(value = "类型。1-新闻，2-课程，3-文档分类")
    private Integer type = 0;

}
