package cn.getech.ehm.knowledge.client;

import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 知识新闻模块
 * <AUTHOR>
 */
@FeignClient(name = "knowledge-service",path = "/api/knowledge-service")
public interface NewsClient {

    @GetMapping(value = "/appNewsInfo/{id}")
    RestResponse<NewsInfoDto> get(@PathVariable(value="id") String id);

}
