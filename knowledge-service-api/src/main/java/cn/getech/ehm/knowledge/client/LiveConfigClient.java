package cn.getech.ehm.knowledge.client;

import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 直播配置客户端
 * <AUTHOR>
 */
@FeignClient(name = "knowledge-service",path = "/api/knowledge-service")
public interface LiveConfigClient {

    /**
     * 判断用户直播观看权限
     * @param uid
     * @return
     */
    @GetMapping("/liveAuthorizationConfig/checkAuthorization")
    RestResponse<Boolean> checkAuthorization(@RequestParam String uid);
}
