package cn.getech.ehm.scada.controller;

import cn.getech.ehm.scada.dto.definition.DefinitionAddParam;
import cn.getech.ehm.scada.dto.definition.DefinitionDto;
import cn.getech.ehm.scada.dto.definition.DefinitionEditParam;
import cn.getech.ehm.scada.dto.definition.DefinitionQueryParam;
import cn.getech.ehm.scada.service.DefinitionService;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import javax.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * <AUTHOR> 
 */
@RestController
@RequestMapping("/definition")
@Api(tags = "组态定义接口")
public class DefinitionController {

    @Autowired
    private DefinitionService definitionService;

    /**
     * 分页获取组态定义列表
     */
    @ApiOperation("分页获取组态定义列表")
    @GetMapping("/page")
    public RestResponse<PageResult<DefinitionDto>> page(@Valid DefinitionQueryParam definitionQueryParam){
        return RestResponse.ok(definitionService.pageDto(definitionQueryParam));
    }

    /**
     * 新增组态定义
     */
    @ApiOperation("新增组态定义")
    @AuditLog(title = "组态定义",desc = "新增组态定义",businessType = BusinessType.INSERT)
    @PostMapping
    public RestResponse<String> add(@RequestBody @Valid DefinitionAddParam definitionAddParam) {
        return RestResponse.ok(definitionService.saveByParam(definitionAddParam));
    }

    /**
     * 修改组态定义
     */
    @ApiOperation(value="修改组态定义")
    @AuditLog(title = "组态定义",desc = "修改组态定义",businessType = BusinessType.UPDATE)
    @PutMapping
    public RestResponse<Boolean> update(@RequestBody @Valid DefinitionEditParam definitionEditParam) {
        return RestResponse.ok(definitionService.updateByParam(definitionEditParam));
    }

    /**
     * 根据ids删除组态定义
     */
    @ApiOperation(value="根据ids删除组态定义")
    @AuditLog(title = "组态定义",desc = "组态定义",businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public RestResponse<Boolean> deleteByIds(@RequestBody @NotEmpty List<String> ids) {
        return RestResponse.ok(definitionService.deleteByIds(ids));
    }

    /**
     * 根据id获取组态定义
     */
    @ApiOperation(value = "根据id获取组态定义")
    @GetMapping(value = "/{id}")
    public RestResponse<DefinitionDto> get(@PathVariable String id) {
        return RestResponse.ok(definitionService.getDtoById(id));
    }

    /**
     * 根据设备id获取组态定义
     */
    @ApiOperation(value = "根据设备id获取组态定义")
    @GetMapping(value = "/getByEquipmentId")
    public RestResponse<DefinitionDto> getByEquipmentId(@RequestParam("equipmentId") String equipmentId) {
        return RestResponse.ok(definitionService.getByEquipmentId(equipmentId));
    }

    /**
     * 判断设备是否被使用
     */
    @ApiOperation(value = "判断设备是否被使用")
    @PostMapping(value = "/checkInfoIdsUsed")
    public RestResponse<List<String>> checkInfoIdsUsed(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(definitionService.checkInfoIdsUsed(equipmentIds));
    }
}
