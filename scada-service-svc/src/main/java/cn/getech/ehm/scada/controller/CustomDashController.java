package cn.getech.ehm.scada.controller;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.scada.dto.dash.CustomDashDto;
import cn.getech.ehm.scada.entity.CustomDash;
import cn.getech.ehm.scada.service.CustomDashService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customDash")
@Api(tags = "自定义大屏配置接口")
public class CustomDashController {

    @Autowired
    private CustomDashService customDashService;

    @ApiOperation("编辑大屏配置")
    @PostMapping("/edit")
    public RestResponse<CustomDashDto> saveOrUpdate(@RequestBody CustomDashDto dto) {
        return RestResponse.ok(customDashService.saveOrUpdateByParam(dto));
    }

    @ApiOperation("初始化大屏配置")
    @PostMapping("/init")
    public RestResponse<Boolean> initDashConfig(@RequestBody List<CustomDashDto> dtos) {
        List<CustomDash> customDashList = CopyDataUtil.copyList(dtos, CustomDash.class);
        return RestResponse.ok(customDashService.saveBatch(customDashList));
    }

    @ApiOperation(value="获取自定义大屏配置列表")
    @GetMapping("/getListByTag")
    public RestResponse<List<CustomDashDto>> getListByTag(@RequestParam String tag) {
        return RestResponse.ok(customDashService.getListByTag(tag));
    }
}
