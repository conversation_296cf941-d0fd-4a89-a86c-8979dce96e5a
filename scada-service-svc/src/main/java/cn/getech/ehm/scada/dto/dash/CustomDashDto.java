package cn.getech.ehm.scada.dto.dash;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自定义Dash
 * <AUTHOR>
 * @date 2023-02-24 18:50:04
 **/
@Data
@ApiModel(value = "CustomDashDto", description = "自定义Dash")
public class CustomDashDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "页面标签")
    private String tag;

    @ApiModelProperty(value = "页面组件编码")
    private String code;

    @ApiModelProperty(value = "页面组件名称")
    private String name;

    @ApiModelProperty(value = "组件datakey")
    private String dataKey;

    @ApiModelProperty("组件历史最新值")
    private Object value;

}