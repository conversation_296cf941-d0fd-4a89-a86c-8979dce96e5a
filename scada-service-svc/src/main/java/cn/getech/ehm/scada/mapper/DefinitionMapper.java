package cn.getech.ehm.scada.mapper;

import cn.getech.ehm.scada.dto.definition.DefinitionDto;
import cn.getech.ehm.scada.entity.Definition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 组态定义Mapper
 * <AUTHOR>
 */
@Repository
public interface DefinitionMapper extends BaseMapper<Definition> {

    /**
     * 根据设备id查询，转dto
     * @return
     */
    DefinitionDto getByEquipmentId(@Param("equipmentId") String equipmentId);
}
