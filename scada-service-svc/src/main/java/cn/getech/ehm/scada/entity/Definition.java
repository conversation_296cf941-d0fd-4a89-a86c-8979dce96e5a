package cn.getech.ehm.scada.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.JdbcType;

/**
 * 组态定义
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("scada_definition")
public class Definition extends BaseEntity {

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态
     * 0:未发布
     * 1:已发布
     */
    @TableField("status")
    private Integer status;

    @TableField("content")
    private String content;

    /**
     * 是否删除
     * (0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

}
