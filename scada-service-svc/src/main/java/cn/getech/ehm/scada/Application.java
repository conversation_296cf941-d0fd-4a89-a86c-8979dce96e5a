package cn.getech.ehm.scada;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @date 2021-07-18 14:23:12
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = "cn.getech")
@EnableFeignClients(basePackages = "cn.getech")
@MapperScan("cn.getech.ehm.scada.mapper")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
