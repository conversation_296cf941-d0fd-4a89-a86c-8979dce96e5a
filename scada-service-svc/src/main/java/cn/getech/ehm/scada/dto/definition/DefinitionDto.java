package cn.getech.ehm.scada.dto.definition;

import cn.getech.ehm.iot.dto.parameter.ParameterDataDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 组态定义数据模型
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "DefinitionDto", description = "组态定义数据模型")
public class DefinitionDto {

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("设备id")
    private String equipmentId;

    @ApiModelProperty("设备名称")
    private String equipmentName;

    @ApiModelProperty("设备编码")
    private String equipmentCode;

    @ApiModelProperty("设备位置名称")
    private String equipmentLocationName;

    @ApiModelProperty("设备类型名称")
    private String equipmentCategoryName;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("上次参数值(key:测点参数id,value:double)")
    private List<ParameterDataDto> latestValueMap = Lists.newArrayList();
}