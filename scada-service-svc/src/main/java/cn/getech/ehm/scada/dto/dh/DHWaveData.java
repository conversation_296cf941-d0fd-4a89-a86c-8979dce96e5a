package cn.getech.ehm.scada.dto.dh;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2023-05-06 17:25:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DHWaveData implements Serializable {

    /**
     * 设备ID
     */
    private int deviceId;

    /**
     * 通道ID
     */
    private int channelId;

    /**
     * 采样频率
     */
    private int sampleFreq;

    /**
     * 采样时间
     */
    private long sampleTime;

    /**
     * 数据位数
     */
    private long pos;

    /**
     * 原始波形数据
     */
    private float[] data;

}
