package cn.getech.ehm.scada.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.scada.dto.datakey.ScadaDataKeyDto;
import cn.getech.ehm.scada.entity.ScadaDataKey;
import cn.getech.ehm.scada.mapper.DataKeyMapper;
import cn.getech.ehm.scada.service.DataKeyService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataKeyServiceImpl extends BaseServiceImpl<DataKeyMapper, ScadaDataKey> implements DataKeyService {

    @Autowired
    private DataKeyMapper dataKeyMapper;

    @Override
    public Boolean saveByDto(ScadaDataKeyDto dto) {
        ScadaDataKey scadaDataKey = CopyDataUtil.copyObject(dto, ScadaDataKey.class);
        return save(scadaDataKey);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean editByDto(ScadaDataKeyDto dto) {
        this.deleteByDto(dto);
        return this.saveByDto(dto);
    }

    @Override
    public Boolean deleteByDto(ScadaDataKeyDto dto) {
        LambdaQueryWrapper<ScadaDataKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ScadaDataKey::getDefinitionId, dto.getDefinitionId());
        //编辑时候删除旧dataKey
        String infoParamId = StringUtils.isNotBlank(dto.getOldInfoParamId()) ? dto.getOldInfoParamId() : dto.getInfoParamId();
        wrapper.eq(ScadaDataKey::getInfoParamId, infoParamId);
        wrapper.eq(ScadaDataKey::getIdentifierId, dto.getIdentifierId());
        return dataKeyMapper.delete(wrapper) > 0;
    }

    @Override
    public Boolean checkUsed(String infoParamId) {
        LambdaQueryWrapper<ScadaDataKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ScadaDataKey::getInfoParamId, infoParamId);
        wrapper.select(ScadaDataKey::getId);
        return dataKeyMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Boolean checkLibraryUsed(String libraryId){
        LambdaQueryWrapper<ScadaDataKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ScadaDataKey::getIdentifierId, libraryId);
        return dataKeyMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<String> getMeasuringParamIds(String definitionId){
        LambdaQueryWrapper<ScadaDataKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ScadaDataKey::getDefinitionId, definitionId);
        return dataKeyMapper.selectList(wrapper).stream().map(ScadaDataKey::getInfoParamId).collect(Collectors.toList());
    }
}
