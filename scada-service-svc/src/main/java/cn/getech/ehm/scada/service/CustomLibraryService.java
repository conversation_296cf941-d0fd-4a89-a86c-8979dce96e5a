package cn.getech.ehm.scada.service;

import cn.getech.ehm.scada.dto.library.CustomLibraryDto;
import cn.getech.ehm.scada.entity.CustomLibrary;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 自定义组态图片库接口
 * <AUTHOR>
 */
public interface CustomLibraryService extends IBaseService<CustomLibrary> {

    /**
     * 新增或编辑
     * @param dto
     * @return
     */
    Boolean saveOrUpdateByParam(CustomLibraryDto dto);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 获取组态库列表
     * @return
     */
    List<CustomLibraryDto> getList();
}