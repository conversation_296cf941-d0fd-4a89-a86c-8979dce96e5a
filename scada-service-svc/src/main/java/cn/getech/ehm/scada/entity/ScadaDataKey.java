package cn.getech.ehm.scada.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组态绑定数据源
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("scada_datakey")
public class ScadaDataKey extends BaseEntity {

    /**
     * 组态id
     */
    @TableField("definition_id")
    private String definitionId;

    /**
     * 组件id
     */
    @TableField("identifier_id")
    private String identifierId;

    /**
     * 设备参数关联表id
     */
    @TableField("info_param_id")
    private String infoParamId;
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

}
