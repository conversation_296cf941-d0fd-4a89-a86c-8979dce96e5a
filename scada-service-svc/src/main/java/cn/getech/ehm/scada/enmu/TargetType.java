package cn.getech.ehm.scada.enmu;

import java.util.HashMap;
import java.util.Map;

/**
 * 指标枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TargetType {
    HIGHACCLONGWAVE(1, "HighAccLongWave", "长波形"),
    HIGHACCWAVE(11, "HighAccWave", "高频加速度"),
    LOWACCWAVE(12, "LowAccWave", "低频加速度"),
    VELWAVE(15, "VelWave", "速度"),
    TEMP(100, "Temp", "温度"),
    LOWACCTOTAL(101, "LowAccTotal", "低频总值"),
    VELTOTAL(107, "VelTotal", "速度总值"),
    HIGHACCTOTAL(112, "HighAccTotal", "高频总值");

    TargetType(int code, String value, String name) {
        this.value = value;
        this.name = name;
        this.code = code;
    }

    public static String getNameByValue(String value){
        switch (value){
            case "HighAccLongWave":     return TargetType.HIGHACCLONGWAVE.name;
            case "HighAccWave":         return TargetType.HIGHACCWAVE.name;
            case "LowAccWave":          return TargetType.LOWACCWAVE.name;
            case "VelWave":             return TargetType.VELWAVE.name;
            case "Temp":                return TargetType.TEMP.name;
            case "LowAccTotal":         return TargetType.LOWACCTOTAL.name;
            case "VelTotal":            return TargetType.VELTOTAL.name;
            case "HighAccTotal":        return TargetType.HIGHACCTOTAL.name;
        }
        return null;
    }

    public static Map<String, String> getMapByNodeType(String nodeType){
        Map<String, String> map = new HashMap<>();
        map.put(TargetType.LOWACCWAVE.value, TargetType.LOWACCWAVE.name);
        map.put(TargetType.VELWAVE.value, TargetType.VELWAVE.name);
        map.put(TargetType.LOWACCTOTAL.value, TargetType.LOWACCTOTAL.name);
        map.put(TargetType.VELTOTAL.value, TargetType.VELTOTAL.name);
        if(nodeType.equals("21")){
            //驱动V
            map.put(TargetType.HIGHACCLONGWAVE.value, TargetType.HIGHACCLONGWAVE.name);
            map.put(TargetType.HIGHACCWAVE.value, TargetType.HIGHACCWAVE.name);
            map.put(TargetType.HIGHACCTOTAL.value, TargetType.HIGHACCTOTAL.name);
        }
        return map;
    }

    private int code;

    private String value;

    private String name;

    public int getCode() { return code; }

    public void setCode(int code) { this.code = code; }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
