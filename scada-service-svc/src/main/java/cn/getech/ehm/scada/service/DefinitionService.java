package cn.getech.ehm.scada.service;

import cn.getech.ehm.scada.dto.definition.DefinitionAddParam;
import cn.getech.ehm.scada.dto.definition.DefinitionDto;
import cn.getech.ehm.scada.dto.definition.DefinitionEditParam;
import cn.getech.ehm.scada.dto.definition.DefinitionQueryParam;
import cn.getech.ehm.scada.entity.Definition;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 组态定义接口
 * <AUTHOR>
 */
public interface DefinitionService extends IBaseService<Definition> {

    /**
     * 分页查询
     * @param definitionQueryParam
     * @return
     */
    PageResult<DefinitionDto> pageDto(DefinitionQueryParam definitionQueryParam);

    /**
     * 新增
     * @param definitionAddParam
     * @return
     */
    String saveByParam(DefinitionAddParam definitionAddParam);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    DefinitionDto getDtoById(String id);

    /**
     * 根据设备id查询，转dto
     * @return
     */
    DefinitionDto getByEquipmentId(String equipmentId);

    /**
     * 更新
     * @param definitionEditParam
     */
    boolean updateByParam(DefinitionEditParam definitionEditParam);

    /**
     * 根据id集合删除规则模板
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<String> ids);

    /**
     * 判断设备是否绑定
     * @return
     */
    Boolean checkUsed(String equipmentId);

    /**
     * 校验是否使用
     * @param equipmentIds
     * @return
     */
    List<String> checkInfoIdsUsed(String[] equipmentIds);
}