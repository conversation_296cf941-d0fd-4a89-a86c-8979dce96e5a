package cn.getech.ehm.scada.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.BuildInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.parameter.ParameterDataDto;
import cn.getech.ehm.scada.dto.definition.DefinitionAddParam;
import cn.getech.ehm.scada.dto.definition.DefinitionDto;
import cn.getech.ehm.scada.dto.definition.DefinitionEditParam;
import cn.getech.ehm.scada.dto.definition.DefinitionQueryParam;
import cn.getech.ehm.scada.entity.Definition;
import cn.getech.ehm.scada.mapper.DefinitionMapper;
import cn.getech.ehm.scada.service.DataKeyService;
import cn.getech.ehm.scada.service.DefinitionService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DefinitionServiceImpl extends BaseServiceImpl<DefinitionMapper, Definition> implements DefinitionService {

    @Autowired
    private DefinitionMapper definitionMapper;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private DataKeyService dataKeyService;
    @Autowired
    private ParameterClient parameterClient;

    @Override
    public PageResult<DefinitionDto> pageDto(DefinitionQueryParam definitionQueryParam) {
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if(!authRes.isOk()){
            log.error("获取设备权限失败");
            return new PageResult(Collections.emptyList(), StaticValue.ZERO);
        }else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())) {
                return new PageResult(Collections.emptyList(), StaticValue.ZERO);
            }
            LambdaQueryWrapper<Definition> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(StringUtils.isNotBlank(definitionQueryParam.getKeyword()), Definition::getName, definitionQueryParam.getKeyword())
                    .or()
                    .like(StringUtils.isNotBlank(definitionQueryParam.getKeyword()), Definition::getDescription, definitionQueryParam.getKeyword());
            queryWrapper.eq(Definition::getDeleted, StaticValue.ZERO);
            queryWrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), Definition::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
            queryWrapper.orderByDesc(Definition::getCreateTime, Definition::getName);
            IPage<Definition> page = page(new Page(definitionQueryParam.getPageNo(), definitionQueryParam.getLimit()), queryWrapper);
            List<DefinitionDto> definitionDtos = page.getRecords().stream().map(definition -> {
                DefinitionDto definitionDto = CopyDataUtil.copyObject(definition, DefinitionDto.class);
                RestResponse<EquipmentInfoDto> restResponse = equipmentClient.getEquipmentInfo(definition.getEquipmentId());
                if (restResponse.isOk()) {
                    EquipmentInfoDto equipmentInfoDto = restResponse.getData();
                    if (null != equipmentInfoDto) {
                        definitionDto.setEquipmentName(equipmentInfoDto.getName());
                        definitionDto.setEquipmentCode(equipmentInfoDto.getCode());
                        definitionDto.setEquipmentLocationName(equipmentInfoDto.getLocationName());
                        definitionDto.setEquipmentCategoryName(equipmentInfoDto.getCategoryName());
                    }
                }
                return definitionDto;
            }).collect(Collectors.toList());
            return new PageResult<>(definitionDtos, page.getTotal());
        }
    }

    @Override
    public String saveByParam(DefinitionAddParam definitionAddParam) {

        //todo 设备可以被多个绑定
        DefinitionDto definitionDto = definitionMapper.getByEquipmentId(definitionAddParam.getEquipmentId());
        if(null != definitionDto){
            throw new GlobalServiceException(GlobalResultMessage.of("该设备已被组态:" + definitionDto.getName() + "绑定"));
        }

        Definition definition = CopyDataUtil.copyObject(definitionAddParam, Definition.class);
        save(definition);
        return definition.getId();
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public DefinitionDto getDtoById(String id) {
        DefinitionDto definitionDto =  CopyDataUtil.copyObject(getById(id), DefinitionDto.class);
        if(null != definitionDto) {
            buildEquipment(definitionDto);
            List<String> measuringParamIds = dataKeyService.getMeasuringParamIds(definitionDto.getId());
            if(CollectionUtils.isNotEmpty(measuringParamIds)){
                RestResponse<List<ParameterDataDto>> restResponse = parameterClient.getLatestValueMap(measuringParamIds.toArray(new String[measuringParamIds.size()]));
                definitionDto.setLatestValueMap(restResponse.isOk() ? restResponse.getData() : new ArrayList<>(0));
            }
        }
        return definitionDto;
    }

    private DefinitionDto buildEquipment(DefinitionDto definitionDto){
        if(StringUtils.isNotBlank(definitionDto.getEquipmentId())) {
            RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(new String[]{definitionDto.getEquipmentId()});
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用equipment-service出错");
            } else {
                Map<String, EquipmentListDto> stringListMap = listRestResponse.getData();
                EquipmentListDto equipment = stringListMap.get(definitionDto.getEquipmentId());
                definitionDto.setEquipmentName(null != equipment ? equipment.getEquipmentName() : null);
                definitionDto.setEquipmentCode(null != equipment ? equipment.getEquipmentCode() : null);
            }
        }
        return definitionDto;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public DefinitionDto getByEquipmentId(String equipmentId){
        DefinitionDto definitionDto = definitionMapper.getByEquipmentId(equipmentId);
        if(null != definitionDto){
            buildEquipment(definitionDto);
        }
        return definitionDto;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean updateByParam(DefinitionEditParam definitionEditParam) {
        Definition definition = CopyDataUtil.copyObject(definitionEditParam, Definition.class);
        return updateById(definition);
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public Boolean deleteByIds(List<String> ids) {
        LambdaUpdateWrapper<Definition> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(Definition::getId, ids);
        wrapper.set(Definition::getDeleted, StaticValue.ONE);
        return update(wrapper);
    }

    @Override
    public Boolean checkUsed(String equipmentId){
        LambdaQueryWrapper<Definition> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Definition::getEquipmentId, equipmentId);
        wrapper.eq(Definition::getDeleted, DeletedType.NO.getValue());
        return definitionMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<String> checkInfoIdsUsed(String[] equipmentIds){
        LambdaQueryWrapper<Definition> wrapper = Wrappers.lambdaQuery();
        wrapper.in(Definition::getEquipmentId, equipmentIds);
        wrapper.eq(Definition::getDeleted, DeletedType.NO.getValue());
        wrapper.select(Definition::getEquipmentId);
        return definitionMapper.selectList(wrapper).stream().map(Definition::getEquipmentId).distinct().collect(Collectors.toList());
    }
}
