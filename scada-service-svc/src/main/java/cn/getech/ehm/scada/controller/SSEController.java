package cn.getech.ehm.scada.controller;

import cn.getech.ehm.scada.service.SseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @time 2023-02-14 17:03:03
 **/
@RestController
@RequestMapping("/sse")
public class SSEController {

    @Autowired
    private SseService sseService;

    @GetMapping("/subscribe")
    public SseEmitter subscribe(@RequestParam String id) {
        return sseService.subscribe(id);
    }

    @PostMapping("/push")
    public String push(@RequestParam String id, @RequestBody Object content) {
        return sseService.push(id, content);
    }

    @GetMapping("/unsubscribe")
    public String unsubscribe(@RequestParam String id) {
        return sseService.unsubscribe(id);
    }
}

