package cn.getech.ehm.scada.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.scada.dto.library.CustomLibraryDto;
import cn.getech.ehm.scada.entity.CustomLibrary;
import cn.getech.ehm.scada.mapper.CustomLibraryMapper;
import cn.getech.ehm.scada.service.CustomLibraryService;
import cn.getech.ehm.scada.service.DataKeyService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomLibraryServiceImpl extends BaseServiceImpl<CustomLibraryMapper, CustomLibrary> implements CustomLibraryService {

    @Autowired
    private DataKeyService dataKeyService;
    @Autowired
    private CustomLibraryMapper customLibraryMapper;

    @Override
    public Boolean saveOrUpdateByParam(CustomLibraryDto dto) {
        CustomLibrary customLibrary = CopyDataUtil.copyObject(dto, CustomLibrary.class);
        return saveOrUpdate(customLibrary);
    }

    @Override
    public Boolean deleteById(String id) {
        Boolean flag = dataKeyService.checkLibraryUsed(id);
        if(flag){
            throw new GlobalServiceException(GlobalResultMessage.of("该组态图片被引用"));
        }
        return removeById(id);
    }

    @Override
    public List<CustomLibraryDto> getList() {
        List<CustomLibraryDto> customLibraryDtos = CopyDataUtil.copyList(customLibraryMapper.selectList(null), CustomLibraryDto.class);
        return CollectionUtils.isNotEmpty(customLibraryDtos) ? customLibraryDtos : Collections.EMPTY_LIST;
    }
}
