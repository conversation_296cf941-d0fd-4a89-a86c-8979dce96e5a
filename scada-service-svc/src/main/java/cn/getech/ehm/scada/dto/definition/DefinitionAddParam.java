package cn.getech.ehm.scada.dto.definition;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Definition新增", description = "组态定义新增参数")
public class DefinitionAddParam extends ApiParam {

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "设备id", required = true)
    @NotBlank(message = "设备不能为空")
    private String equipmentId;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("状态")
    private Integer status;

}