package cn.getech.ehm.scada.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.parameter.ParameterDataDto;
import cn.getech.ehm.scada.dto.dash.CustomDashDto;
import cn.getech.ehm.scada.entity.CustomDash;
import cn.getech.ehm.scada.mapper.CustomDashMapper;
import cn.getech.ehm.scada.service.CustomDashService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.IdGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomDashServiceImpl extends BaseServiceImpl<CustomDashMapper, CustomDash> implements CustomDashService {

    @Value("${iot.data.latest.enabled:false}")
    private boolean enabledLatest;

    @Autowired
    private CustomDashMapper customDashMapper;

    @Autowired
    private ParameterClient iotClient;

    @Override
    public CustomDashDto saveOrUpdateByParam(CustomDashDto dto) {
        if (null == dto.getId()) {
            dto.setId(IdGenerator.getIdStr());
        }
        CustomDash customDash = CopyDataUtil.copyObject(dto, CustomDash.class);
        saveOrUpdate(customDash);
        return dto;
    }

    @Override
    public Boolean deleteById(String id) {
        return removeById(id);
    }

    @Override
    public List<CustomDashDto> getListByTag(String tag) {
        List<CustomDash> customDashList = customDashMapper.selectList(new LambdaQueryWrapper<CustomDash>().eq(CustomDash::getTag, tag));
        if (enabledLatest) {
            String[] keys = customDashList.stream().map(CustomDash::getDataKey).toArray(String[]::new);
            RestResponse<List<ParameterDataDto>> restResponse = iotClient.getLatestValueMapByKeys(keys);
            if (restResponse.isSuccess() && restResponse.getData().size() == keys.length) {
                List<ParameterDataDto> parameterDataDtoList = restResponse.getData();
                List<CustomDashDto> customDashDtoList = customDashList.stream().map(
                      customDash -> {
                        int index = customDashList.indexOf(customDash);
                        ParameterDataDto parameterDataDto = parameterDataDtoList.get(index);
                        CustomDashDto customDashDto = new CustomDashDto();
                        customDashDto.setTag(customDashDto.getTag());
                        customDashDto.setId(customDash.getId());
                        customDashDto.setCode(customDash.getCode());
                        customDashDto.setName(customDash.getName());
                        customDashDto.setDataKey(customDash.getDataKey());
                        customDashDto.setValue(parameterDataDto.getValue());
                        return customDashDto;
                      })
                  .collect(Collectors.toList());
                return customDashDtoList;
            }
        }
        return CopyDataUtil.copyList(customDashList, CustomDashDto.class);
    }
}
