package cn.getech.ehm.scada.service;

import cn.getech.ehm.scada.dto.dash.CustomDashDto;
import cn.getech.ehm.scada.dto.library.CustomLibraryDto;
import cn.getech.ehm.scada.entity.CustomDash;
import cn.getech.ehm.scada.entity.CustomLibrary;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 自定义Dash
 * <AUTHOR>
 */
public interface CustomDashService extends IBaseService<CustomDash> {

    /**
     * 新增或编辑
     * @param dto
     * @return
     */
    CustomDashDto saveOrUpdateByParam(CustomDashDto dto);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 获取组态库列表
     * @return
     */
    List<CustomDashDto> getListByTag(String tag);

}