package cn.getech.ehm.scada.dto.definition;

import cn.getech.ehm.scada.entity.Definition;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 * 参数定义 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-09-14
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface DefinitionParamMapper{

    /**
     * 新增参数转换为实体
     * @param definitionAddParam
     * @return
     */
    Definition addParam2Entity(DefinitionAddParam definitionAddParam);

    /**
     * 编辑参数转换为实体
     * @param definitionEditParam
     * @return
     */
    Definition editParam2Entity(DefinitionEditParam definitionEditParam);

    /**
     * 实体转换为Dto
     * @param definition
     * @return
     */
    DefinitionDto entity2Dto(Definition definition);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<DefinitionDto> pageEntity2Dto(PageResult<Definition> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<Definition> dtoList2Entity(List<DefinitionDto> rows);

    /**
     * entity集合转dto集合
     * @param list
     * @return
     */
    List<DefinitionDto> entityList2DtoList(List<Definition> list);

}
