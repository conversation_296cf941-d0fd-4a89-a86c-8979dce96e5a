package cn.getech.ehm.scada.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义大屏
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("scada_custom_dash")
public class CustomDash extends BaseEntity {

    @TableField("tag")
    private String tag;

    /**
     * 前端code
     */
    @TableField("code")
    private String code;

    @TableField("name")
    private String name;

    /**
     * 数据源datakey
     */
    @TableField("data_key")
    private String dataKey;

}
