package cn.getech.ehm.scada.dto.datakey;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 组态数据源模型
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "ScadaDataKeyDto", description = "组态定义数据模型")
public class ScadaDataKeyDto {

    @ApiModelProperty(value = "组态id", required = true)
    @NotBlank(message = "组态id不能为空")
    private String definitionId;

    @ApiModelProperty(value = "设备参数关联表id", required = true)
    @NotBlank(message = "设备参数关联表id不能为空")
    private String infoParamId;

    @ApiModelProperty(value = "组件id", required = true)
    @NotBlank(message = "组件id不能为空")
    private String identifierId;

    @ApiModelProperty("旧设备参数关联表id")
    private String oldInfoParamId;

}