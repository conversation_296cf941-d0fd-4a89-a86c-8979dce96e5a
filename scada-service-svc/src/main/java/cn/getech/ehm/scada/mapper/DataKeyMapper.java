package cn.getech.ehm.scada.mapper;

import cn.getech.ehm.scada.entity.ScadaDataKey;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组态数据源定义Mapper
 * <AUTHOR>
 */
@Repository
public interface DataKeyMapper extends BaseMapper<ScadaDataKey> {
    /**
     * 获取在用的dataKey
     * @return
     */
    @SqlParser(filter = true)
    List<String> getAllDataKey();
}
