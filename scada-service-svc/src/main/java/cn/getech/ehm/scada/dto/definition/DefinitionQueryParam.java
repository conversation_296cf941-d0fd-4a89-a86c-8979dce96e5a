package cn.getech.ehm.scada.dto.definition;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Definition查询", description = "组态定义查询参数")
public class DefinitionQueryParam extends PageParam {

    @ApiModelProperty(value = "状态")
    private Integer status;

}
