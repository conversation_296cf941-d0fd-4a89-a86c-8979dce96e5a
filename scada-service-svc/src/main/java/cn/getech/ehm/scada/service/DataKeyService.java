package cn.getech.ehm.scada.service;

import cn.getech.ehm.scada.dto.datakey.ScadaDataKeyDto;
import cn.getech.ehm.scada.entity.ScadaDataKey;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 组态数据源定义接口
 * <AUTHOR>
 */
public interface DataKeyService extends IBaseService<ScadaDataKey> {

    /**
     * 新增
     * @param dto
     * @return
     */
    Boolean saveByDto(ScadaDataKeyDto dto);

    /**
     * 修改
     * @param dto
     * @return
     */
    Boolean editByDto(ScadaDataKeyDto dto);

    /**
     * 删除
     * @param dto
     * @return
     */
    Boolean deleteByDto(ScadaDataKeyDto dto);

    /**
     * 判断参数是否绑定
     * @return
     */
    Boolean checkUsed(String infoParamId);

    /**
     * 校验自定义图片是否使用
     * @param libraryId
     * @return
     */
    Boolean checkLibraryUsed(String libraryId);

    /**
     * 获取设备测点参数关联表id
     * @param definitionId
     * @return
     */
    List<String> getMeasuringParamIds(String definitionId);
}