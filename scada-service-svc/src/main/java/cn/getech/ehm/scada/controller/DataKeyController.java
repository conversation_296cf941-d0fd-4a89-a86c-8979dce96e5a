package cn.getech.ehm.scada.controller;

import cn.getech.ehm.scada.dto.datakey.ScadaDataKeyDto;
import cn.getech.ehm.scada.service.DataKeyService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataKey")
@Api(tags = "组态数据源定义接口")
public class DataKeyController {

    @Autowired
    private DataKeyService dataKeyService;

    @ApiOperation("新增组态数据源")
    @PostMapping
    public RestResponse<Boolean> add(@RequestBody @Valid ScadaDataKeyDto dto) {
        return RestResponse.ok(dataKeyService.saveByDto(dto));
    }

    @ApiOperation(value="修改组态数据源")
    @PutMapping
    public RestResponse<Boolean> update(@RequestBody @Valid ScadaDataKeyDto dto) {
        return RestResponse.ok(dataKeyService.editByDto(dto));
    }

    @ApiOperation(value="解绑数据源")
    @PostMapping("/delete")
    public RestResponse<Boolean> deleteByIds(@RequestBody @Valid ScadaDataKeyDto dto) {
        return RestResponse.ok(dataKeyService.deleteByDto(dto));
    }
}
