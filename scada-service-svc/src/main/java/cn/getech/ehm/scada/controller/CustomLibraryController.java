package cn.getech.ehm.scada.controller;

import cn.getech.ehm.scada.dto.library.CustomLibraryDto;
import cn.getech.ehm.scada.service.CustomLibraryService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customLibrary")
@Api(tags = "自定义组态图片库接口")
public class CustomLibraryController {

    @Autowired
    private CustomLibraryService customLibraryService;

    @ApiOperation("新增修改组态图片库")
    @PostMapping("/edit")
    public RestResponse<Boolean> update(@RequestBody CustomLibraryDto dto) {
        return RestResponse.ok(customLibraryService.saveOrUpdateByParam(dto));
    }

    @ApiOperation(value="删除组态数据源")
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> delete(@PathVariable String id) {
        return RestResponse.ok(customLibraryService.deleteById(id));
    }

    @ApiOperation(value="获取自定义组态库列表")
    @PostMapping("/getList")
    public RestResponse<List<CustomLibraryDto>> getList() {
        return RestResponse.ok(customLibraryService.getList());
    }
}
