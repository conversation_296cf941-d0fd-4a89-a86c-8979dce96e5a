package cn.getech.ehm.scada.service.impl;

import cn.getech.ehm.scada.service.SseService;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @time 2023-02-22 10:51:04
 **/
@Service
public class SseServiceImpl implements SseService {

    private static Map<String, SseEmitter> sseCache = new ConcurrentHashMap<>();

    @Override
    public boolean has(String id) {
        return sseCache.containsKey(id);
    }

    @Override
    public SseEmitter subscribe(String id){
        SseEmitter sseEmitter = new SseEmitter(3600_000L);
        sseCache.put(id, sseEmitter);
        sseEmitter.onTimeout(() -> sseCache.remove(id));
        sseEmitter.onCompletion(() -> System.out.println("subscribe completed..."));
        return sseEmitter;
    }

    @Override
    public String push(String id, Object content) {
        SseEmitter sseEmitter = sseCache.get(id);
        if (sseEmitter != null) {
            try {
                sseEmitter.send(content);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return "success";
    }

    @Override
    public String unsubscribe(String id) {
        SseEmitter sseEmitter = sseCache.get(id);
        if (sseEmitter != null) {
            sseEmitter.complete();
            sseCache.remove(id);
        }
        return "over";
    }

}
