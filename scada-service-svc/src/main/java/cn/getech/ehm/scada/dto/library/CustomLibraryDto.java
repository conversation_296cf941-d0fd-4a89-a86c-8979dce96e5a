package cn.getech.ehm.scada.dto.library;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 自定义组态图片库
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "CustomLibraryDto", description = "自定义组态图片库")
public class CustomLibraryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "路径")
    private String url;

    @ApiModelProperty("文件后缀")
    private String fileType;

    @ApiModelProperty("文件大小(w*h)")
    private String fileLength;

}