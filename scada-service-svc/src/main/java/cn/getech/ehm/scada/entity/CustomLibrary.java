package cn.getech.ehm.scada.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义组态图片库
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("scada_custom_library")
public class CustomLibrary extends BaseEntity {

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 图片路径
     */
    @TableField("url")
    private String url;

    /**
     * 图片后缀
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件大小
     */
    @TableField("file_length")
    private String fileLength;
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

}
