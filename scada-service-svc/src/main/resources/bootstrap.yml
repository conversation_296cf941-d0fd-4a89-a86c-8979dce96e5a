server:
  port: 8030
  servlet:
    context-path: /api/scada-service
  compression:
    enabled: true

management:
  server:
    port: 8031
  health:
    redis:
      enabled: false
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

spring:
  application:
    name: scada-service
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: ${NACOS_CONFIG_SERVER_ADDR:nacos.poros-platform:8848}
        group: ${NACOS_CONFIG_GROUP:POROS_PLATFORM}
        file-extension: yaml
        shared-configs[0]:
          dataId: poros-common.yaml
          group: ${NACOS_CONFIG_GROUP:POROS_PLATFORM}
          refresh: true
        namespace: ${NACOS_CONFIG_NAMESPACE:public}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
      discovery:
        enabled: true
        server-addr: ${NACOS_DISCOVERY_SERVER_ADDR:nacos.poros-platform:8848}
        group: ${NACOS_DISCOVERY_GROUP:DEFAULT_GROUP}
        metadata: {"service.desc": "scada-service"}
        namespace: ${NACOS_DISCOVERY_NAMESPACE:public}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
    sentinel:
      #取消Sentinel控制台懒加载
      #eager: true
      enabled: false
      transport:
        dashboard: sentinel.poros-platform:8080
        port: 8032
      datasource:
        ds1:
          nacos:
            server-addr: nacos.poros3-platform:8848
            dataId: iot-service-rule.json
            groupId: POROS_PLATFORM
            ruleType: flow
            dataType: json

logging:
  level:
    com.alibaba.nacos.client.*: WARN
