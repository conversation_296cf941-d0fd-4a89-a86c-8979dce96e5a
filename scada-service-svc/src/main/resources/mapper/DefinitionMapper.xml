<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.scada.mapper.DefinitionMapper">
    <select id="getByEquipmentId" resultType="cn.getech.ehm.scada.dto.definition.DefinitionDto">
        SELECT definition.id,definition.equipment_id equipmentId,definition.name,
        definition.description,definition.content,definition.status
        FROM scada_definition definition
        WHERE definition.deleted = 0 AND definition.equipment_id = #{equipmentId}
    </select>
</mapper>
