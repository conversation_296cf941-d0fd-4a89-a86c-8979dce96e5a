<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.scada.mapper.DataKeyMapper">
    <select id="getAllDataKey" resultType="java.lang.String">
        SELECT distinct data_key FROM scada_datakey datakey
        LEFT JOIN scada_definition def ON datakey.definition_id = def.id
        WHERE def.deleted = 0
    </select>
</mapper>
