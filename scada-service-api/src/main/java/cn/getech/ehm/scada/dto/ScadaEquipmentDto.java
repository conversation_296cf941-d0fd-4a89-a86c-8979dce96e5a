package cn.getech.ehm.scada.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <pre>
 * 设备表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "ScadaEquipmentDto", description = "设备表返回数据模型")
public class ScadaEquipmentDto {
    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    @Excel(name="设备编码",cellType = Excel.ColumnType.STRING )
    private String code;

    @ApiModelProperty(value = "名称")
    @Excel(name="设备名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    @Excel(name="客户",cellType = Excel.ColumnType.STRING )
    private String customerName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    @Excel(name="设备类型",cellType = Excel.ColumnType.STRING )
    private String categoryName;

    @ApiModelProperty(value = "设备位置id")
    private String locationId;

    @ApiModelProperty(value = "设备位置名称")
    @Excel(name="位置",cellType = Excel.ColumnType.STRING )
    private String locationName;

    @ApiModelProperty(value = "规格型号")
    @Excel(name="规格型号",cellType = Excel.ColumnType.STRING )
    private String specification;

    @ApiModelProperty(value = "出厂编号")
    @Excel(name="出厂编号",cellType = Excel.ColumnType.STRING )
    private String factoryCode;

    @ApiModelProperty(value = "设备责任人ID")
    private String dutyId;

    @ApiModelProperty(value = "责任人")
    @Excel(name="责任人",cellType = Excel.ColumnType.STRING )
    private String principal;

    @ApiModelProperty(value = "重要程度(1 A:关键设备 2 B:重要设备 3 C:一般设备 4 D:次要设备)")
//    @Excel(name="重要程度",cellType = Excel.ColumnType.STRING, readConverterExp = "1=A:关键设备,2=B:重要设备,3=C:一般设备,4=D:次要设备")
    private Integer importance;

    @ApiModelProperty(value = "重要程度")
    @Excel(name="重要程度",cellType = Excel.ColumnType.STRING )
    private String importanceStr;

    @ApiModelProperty(value = "资产编码")
    @Excel(name="资产编码",cellType = Excel.ColumnType.STRING )
    private String assetCode;

    @ApiModelProperty(value = "维护班组名称列表")
    @Excel(name="维护班组",cellType = Excel.ColumnType.STRING )
    private String maintTeamNames;

    @ApiModelProperty(value = "维护人员姓名列表")
    @Excel(name="维护人员",cellType = Excel.ColumnType.STRING )
    private String maintainerNames;

    @ApiModelProperty(value = "供应商Id")
    private String supplierId;

    @ApiModelProperty(value = "制造商Id")
    private String manufacturerId;

    @ApiModelProperty(value = "供应商名称")
    @Excel(name="厂商",cellType = Excel.ColumnType.STRING )
    private String supplierName;

    @ApiModelProperty(value = "制造商名称")
    private String manufacturerName;

    @ApiModelProperty(value = "资产状态(0闲置1正常2故障3失效4限用5禁用6借出7报废8变卖)")
//    @Excel(name="状态",cellType = Excel.ColumnType.STRING, readConverterExp = "0=闲置,1=正常,2=故障,3=失效,4=限用,5=禁用,6=借出,7=报废,8=变卖")
    private Integer status;

    @ApiModelProperty(value = "资产状态")
    @Excel(name="状态",cellType = Excel.ColumnType.STRING )
    private String statusStr;

    @ApiModelProperty(value = "状态说明")
    @Excel(name="状态说明",cellType = Excel.ColumnType.STRING )
    private String statusRemark;

    @ApiModelProperty(value = "维护班组id列表")
    private List<String> maintTeamIds;

    @ApiModelProperty(value = "维护人员id列表")
    private List<String> maintainerIds;

    @ApiModelProperty(value = "系统平台(0:TRICON 1:TSXPLUS 2:TRIDENT)")
    private Integer platform;

    @ApiModelProperty(value = "设备更多属性")
    @Excel(name="设备更多属性",cellType = Excel.ColumnType.STRING, width = 64.00)
    private String property;

    @ApiModelProperty(value = "备注")
//    @Excel(name="备注",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "出厂日期")
    @Excel(name="出厂日期",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date productionTime;

    @ApiModelProperty(value = "安装日期")
    @Excel(name="安装日期",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date installDate;

    @ApiModelProperty(value = "质保日期")
    @Excel(name="质保日期",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date warrantyTime;

    @ApiModelProperty(value = "二维码附件id")
    private String qrCodeId;

    @ApiModelProperty(value = "设备二维码base64字符串,供前端显示和打印")
    private String base64;

    @ApiModelProperty(value = "文档附件id列表")
    private String docIds;

    @ApiModelProperty(value = "创建人")
    @Excel(name="创建人",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @Excel(name="创建时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    @Excel(name="修改人",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    @Excel(name="修改时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "校准日期")
    private String calibrationDate;

    @ApiModelProperty(value = "校准周期")
    private Integer intervalPeriod;

    @ApiModelProperty(value = "有效日期")
    private String effectiveDate;

    @ApiModelProperty(value = "组态url")
    private String configureUrl;

    @ApiModelProperty(value = "图片ids")
    private String picIds;

    @ApiModelProperty(value = "在线状态(0在线1故障2离线)")
    private Integer onlineStatus;
}