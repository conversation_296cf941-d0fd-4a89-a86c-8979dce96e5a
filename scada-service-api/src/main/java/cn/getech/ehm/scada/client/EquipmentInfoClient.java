package cn.getech.ehm.scada.client;

import cn.getech.ehm.scada.dto.ScadaEquipmentDto;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2021-05-06 14:19:50
 **/
@FeignClient(name = "equipment-service",  path = "/api/equipment-service/equipmentInfo")
public interface EquipmentInfoClient {

    /**
     * 获取设备详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    RestResponse<ScadaEquipmentDto> equipmentInfo(@PathVariable("id") String id);

}
