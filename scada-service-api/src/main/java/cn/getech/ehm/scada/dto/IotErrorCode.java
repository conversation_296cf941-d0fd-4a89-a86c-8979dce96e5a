package cn.getech.ehm.scada.dto;

import cn.getech.poros.framework.common.api.IResultCode;

/**
 * <AUTHOR>
 * @date 2020/3/17
 */
public enum IotErrorCode implements IResultCode {

    REMOTE_SERVICE_INVOKE_FAIL(10099,"远程服务调用失败"),
    USER_NOT_FOUND(10002,"用户没有找到");

    private int code;

    private String msg;

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    private IotErrorCode(final int code, final String msg) {
        this.code = code;
        this.msg = msg;
    }

}
