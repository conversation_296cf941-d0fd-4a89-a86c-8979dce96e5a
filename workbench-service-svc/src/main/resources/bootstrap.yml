server:
  port: 8030
  servlet:
    context-path: /api/workbench-service

management:
  endpoint:
    health:
      show-details: ALWAYS
  server:
    port: 8031
  endpoints:
    web:
      exposure:
        include: '*'
  health:
    sentinel:
      enabled: false

spring:
  application:
    name: workbench-service
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: ${NACOS_CONFIG_SERVER_ADDR:nacos.poros-platform:8848}
        group: ${NACOS_CONFIG_GROUP:POROS_PLATFORM}
        file-extension: yaml
        shared-configs[0]:
          dataId: poros-common.yaml
          group: ${NACOS_CONFIG_GROUP:POROS_PLATFORM}
          refresh: true
      discovery:
        enabled: true
        server-addr: ${NACOS_DISCOVERY_SERVER_ADDR:nacos.poros-platform:8848}
        group: ${NACOS_DISCOVERY_GROUP:DEFAULT_GROUP}
        metadata: {"service.desc": "workbench-service"}
    sentinel:
      #取消Sentinel控制台懒加载
      #eager: true
      enabled: false
      transport:
        dashboard: sentinel.poros-platform:8848
        port: 8032
      datasource:
        ds1:
          nacos:
            server-addr: nacos.poros-platform:8848
            dataId: workbench-service-rule.json
            groupId: POROS_PLATFORM
            ruleType: flow
            dataType: json
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000