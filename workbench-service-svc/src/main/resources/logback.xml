<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="AuditLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖workbench-service-svc‖%tid‖AUDIT_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="LoginLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖workbench-service-svc‖%tid‖LOGIN_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="BizLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖workbench-service-svc‖%tid‖BIZ_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="audit_log" level="info" additivity="false">
        <appender-ref ref="AuditLogAppender"/>
    </logger>

    <logger name="login_log" level="info" additivity="false">
        <appender-ref ref="LoginLogAppender"/>
    </logger>

    <root level="info">
        <appender-ref ref="BizLogAppender"/>
    </root>
    <logger name="com.alibaba.nacos.client.config.impl.ClientWorker" level="off"/>
</configuration>