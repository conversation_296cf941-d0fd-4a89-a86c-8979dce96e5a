<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.workbench.mapper.GeneralStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.workbench.entity.GeneralStatistics">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="module_main_type" property="moduleMainType" />
        <result column="module_item_type" property="moduleItemType" />
        <result column="module_role_type" property="moduleRoleType" />
        <result column="date" property="date" />
        <result column="content_key" property="contentKey" />
        <result column="content_value" property="contentValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        remark,
        deleted, module_main_type, module_item_type, module_role_type, date, content_key, content_value
    </sql>

</mapper>
