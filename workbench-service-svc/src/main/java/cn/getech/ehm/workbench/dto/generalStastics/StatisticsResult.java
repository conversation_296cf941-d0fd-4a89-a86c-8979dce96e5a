package cn.getech.ehm.workbench.dto.generalStastics;

import cn.getech.ehm.workbench.entity.GeneralStatistics;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "StatisticsResult", description = "返回数据模型")
public class StatisticsResult {
    @ApiModelProperty(value = "数据计算规则")
    public String resultDealType;

    @ApiModelProperty(value = "二级类型")
    private String moduleItemType;

    @ApiModelProperty(value = "统计结果value")
    public String resultValue;

    @ApiModelProperty(value = "统计结果key")
    public String resultKey;

    @ApiModelProperty(value = "来源数据")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public List<GeneralStatistics> sourceData;
}
