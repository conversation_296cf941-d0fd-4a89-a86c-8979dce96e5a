package cn.getech.ehm.workbench.dto.moduleCusInfo;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 模块自定义信息 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ModuleCusInfo新增", description = "模块自定义信息新增参数")
public class ModuleCusInfoAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String remark;
}