package cn.getech.ehm.workbench.dto.generalStastics;

import cn.getech.poros.framework.common.param.PageParam;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GeneralStatistics查询", description = "查询参数")
public class GeneralStatisticsQueryParam extends PageParam {

    /**
     * 模块类型
     */
    @TableField("module_main_type")
    private String moduleMainType;

    /**
     * 统计项类型
     */
    @TableField("module_item_type")
    private String moduleItemType;

    /**
     * 统计角色维度
     */
    @TableField("module_role_type")
    private String moduleRoleType;

    /**
     * 关联日期
     */
    @TableField("date")
    private Date date;

    /**
     * 数据key
     */
    @TableField("content_key")
    private String contentKey;

    /**
     * 数据value
     */
    @TableField("content_value")
    private String contentValue;
}
