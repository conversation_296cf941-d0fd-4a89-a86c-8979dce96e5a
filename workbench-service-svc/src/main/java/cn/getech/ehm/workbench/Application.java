package cn.getech.ehm.workbench;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = "cn.getech.*")
@EnableFeignClients(basePackages = "cn.getech.*")
@MapperScan("cn.getech.ehm.**.mapper")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
