package cn.getech.ehm.workbench.controller;

import cn.getech.ehm.workbench.dto.workbench.WorkBenchDto;
import cn.getech.ehm.workbench.dto.workbench.WorkBenchUnit;
import cn.getech.ehm.workbench.service.impl.WorkBenchService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController("/workbench")
@Api(value = "工作台接口", tags = "工作台接口")
public class WorkBenchController {
    private final static String TODAY_TASK = "今日任务概览";
    private final static String TODAY_PARTS = "今日备件概览";
    private final static String MAINT_TASK = "工单概览";
    private final static String WARNING_TASK = "工单预警";
    private final static String WARNING_EQUIPMENT = "特种设备检验预警";
    private final static String WARNING_PERSON = "安全人员证书检验预警";
    private final static String WARNING_PART = "备件预警";
    private final static String WARNING_DEFECTT = "缺陷预警";
    private final static String WARNING_PLAN = "维保计划预警";
    private final static String REMAINING_LIFE = "设备寿命预警";
    private final static String TEST = "预警_维保";
    @Autowired
    private WorkBenchService workBenchService;

    @ApiOperation("今日任务概览")
    @GetMapping("/count/todayTask")
    public RestResponse todayCount() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.TODAY_TASK);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(TODAY_TASK).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("今日备件概览")
    @GetMapping("/count/todayParts")
    public RestResponse todayParts() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.TODAY_PARTS);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(TODAY_PARTS).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("工单概览")
    @GetMapping("/count/taskMaint")
    public RestResponse taskMaint() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.MAINT_TASK);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(MAINT_TASK).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-设备")
    @GetMapping("/count/warning/equipment")
    public RestResponse warningOfEquipment() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_EQUIPMENT);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_EQUIPMENT).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-工单/缺陷")
    @GetMapping("/count/warning/task")
    public RestResponse warningOfTask() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_TASK);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_TASK).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-备件")
    @GetMapping("/count/warning/part")
    public RestResponse warningOfPart() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_PART);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_PART).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-缺陷")
    @GetMapping("/count/warning/defect")
    public RestResponse warningOfDefect() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_DEFECTT);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_DEFECTT).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-安全人员")
    @GetMapping("/count/warning/person")
    public RestResponse warningOfPerson() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_PERSON);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_PERSON).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-维保计划")
    @GetMapping("/count/warning/plan")
    public RestResponse warningOfPlan() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.WARNING_PLAN);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(WARNING_PLAN).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("预警总览-设备寿命预警")
    @GetMapping("/count/warning/remainingLife")
    public RestResponse remainingLife() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.REMAINING_LIFE);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(REMAINING_LIFE).build();
        return RestResponse.ok(workBenchDto);
    }

    @ApiOperation("测试用")
    @GetMapping("/count/test")
    public RestResponse test() {
        List<WorkBenchUnit> workBenchUnits = workBenchService.generateUnitList(WorkBenchService.TEST);
        WorkBenchDto workBenchDto = WorkBenchDto.builder().workBenchUnitList(workBenchUnits).moduleName(TEST).build();
        return RestResponse.ok(workBenchDto);
    }
}
