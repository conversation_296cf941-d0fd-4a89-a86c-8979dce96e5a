package cn.getech.ehm.workbench.dto.generalStastics;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;

import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@ApiModel(value = "GeneralStatisticsDto", description = "返回数据模型")
public class GeneralStatisticsDto {

    @ApiModelProperty(value = "")
    @Excel(name = "", cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "remark")
    @Excel(name = "remark", cellType = Excel.ColumnType.STRING)
    private String remark;

    @ApiModelProperty(value = "一级类型")
    private String moduleMainType;

    @ApiModelProperty(value = "二级类型")
    private String moduleItemType;

    @ApiModelProperty(value = "角色类型")
    private String moduleRoleType;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    @ApiModelProperty(value = "key值")
    private String contentKey;

    @ApiModelProperty(value = "value值")
    private String contentValue;

}