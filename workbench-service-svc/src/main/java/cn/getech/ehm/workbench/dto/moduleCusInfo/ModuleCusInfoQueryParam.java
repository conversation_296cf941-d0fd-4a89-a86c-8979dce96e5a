package cn.getech.ehm.workbench.dto.moduleCusInfo;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 模块自定义信息 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ModuleCusInfo查询", description = "模块自定义信息查询参数")
public class ModuleCusInfoQueryParam extends PageParam {

}
