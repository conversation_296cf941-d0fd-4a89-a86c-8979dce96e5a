package cn.getech.ehm.workbench.dto.generalStastics;

import cn.getech.poros.framework.common.param.PageParam;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@ApiModel(value = "GeneralStatisticsQueryParamNoPage", description = "查询参数")
public class GeneralStatisticsQueryParamNoPage {

    /**
     * 模块类型
     */
    @TableField("module_main_type")
    @NotEmpty(message = "模块类型不能为空")
    private String moduleMainType;

    /**
     * 统计项类型
     */
    @TableField("module_item_type")
    private String moduleItemType;

    /**
     * 统计角色维度
     */
    @TableField("module_role_type")
    @NotEmpty(message = "统计角色维度不能为空")
    private String moduleRoleType;

}
