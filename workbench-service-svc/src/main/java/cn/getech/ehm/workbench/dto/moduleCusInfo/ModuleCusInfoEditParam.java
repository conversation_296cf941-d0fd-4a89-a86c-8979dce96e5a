package cn.getech.ehm.workbench.dto.moduleCusInfo;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 模块自定义信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ModuleCusInfo编辑", description = "模块自定义信息编辑参数")
public class ModuleCusInfoEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String remark;

    /**
     * 是否显示
     */
    @TableField("is_show")
    private Integer isShow;

    /**
     * 排序信息
     */
    @TableField("sort_num")
    private Integer sortNum;

    /**
     * 模块id
     */
    @TableField("module_id")
    private String moduleId;

}
