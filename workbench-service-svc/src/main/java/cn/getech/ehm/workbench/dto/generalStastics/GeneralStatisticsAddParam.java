package cn.getech.ehm.workbench.dto.generalStastics;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GeneralStatistics新增", description = "新增参数")
public class GeneralStatisticsAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String remark;

    /**
     * 模块类型
     */
    @TableField("module_main_type")
    private String moduleMainType;

    /**
     * 统计项类型
     */
    @TableField("module_item_type")
    private String moduleItemType;

    /**
     * 统计角色维度
     */
    @TableField("module_role_type")
    private String moduleRoleType;

    /**
     * 关联日期
     */
    @TableField("date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 数据key
     */
    @TableField("content_key")
    private String contentKey;

    /**
     * 数据value
     */
    @TableField("content_value")
    private String contentValue;
}