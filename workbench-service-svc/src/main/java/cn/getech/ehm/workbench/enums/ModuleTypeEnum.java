package cn.getech.ehm.workbench.enums;

import com.google.common.collect.Maps;

import java.util.Map;

public enum ModuleTypeEnum {
    MAINTAIN_ORDER(1, "保养工单"),
    EC_ORDER(2, "点检工单"),
    EC_MANAGE(3, "点检管理"),
    EQUIPMENT_MANAGE(4, "设备管理"),
    MAINTAIN_MANAGE(5, "保养管理"),
    PART_MANAGE(6, "备件管理"),
    MENU(7, "快捷菜单入口"),
    OPERATION_DATA(8, "运营数据");

    ModuleTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Map<Integer,ModuleTypeEnum> getAll(){
        Map<Integer,ModuleTypeEnum> map = Maps.newHashMap();
        for(ModuleTypeEnum moduleTypeEnum : ModuleTypeEnum.values()){
            map.put(moduleTypeEnum.getValue(),moduleTypeEnum);
        }
        return map;
    }

//    public static String getNameByValue(Integer type) {
//        switch (type) {
//            case 0:
//                return ModuleTypeEnum.ORDER_TASK.getName();
//        }
//        return "";
//    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
