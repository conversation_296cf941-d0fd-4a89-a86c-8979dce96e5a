package cn.getech.ehm.workbench.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 模块自定义信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wb_module_cus_info")
public class ModuleCusInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 是否显示
     */
    @TableField("is_show")
    private Integer isShow;

    /**
     * 排序信息
     */
    @TableField("sort_num")
    private Integer sortNum;

    /**
     * 模块id
     */
    @TableField("module_id")
    private String moduleId;


}
