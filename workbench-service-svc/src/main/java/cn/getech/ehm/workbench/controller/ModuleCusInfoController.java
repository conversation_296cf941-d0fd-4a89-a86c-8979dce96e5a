package cn.getech.ehm.workbench.controller;


import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoEditParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleResult;
import cn.getech.ehm.workbench.entity.ModuleCusInfo;
import cn.getech.ehm.workbench.enums.ModuleTypeEnum;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.getech.poros.framework.common.api.RestResponse;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.getech.ehm.workbench.service.IModuleCusInfoService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 首页自定义控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RestController
@RequestMapping("/moduleCusInfo")
@Api(tags = "首页自定义服务接口")
public class ModuleCusInfoController {

    @Autowired
    private IModuleCusInfoService moduleCusInfoService;


    @GetMapping("/get/mine")
    @ApiOperation("获取我的工作台")
    public RestResponse<List<ModuleResult>> getMine() {
        String userId = PorosContextHolder.getCurrentUser().getUid();
        Map<Integer, ModuleTypeEnum> enumMap = ModuleTypeEnum.getAll();
        List<ModuleResult> moduleCusInfos = Lists.newArrayList();
        List<ModuleCusInfo> list = moduleCusInfoService.list(new QueryWrapper<ModuleCusInfo>().lambda()
                .eq(ModuleCusInfo::getCreateBy, userId)
                .orderByAsc(ModuleCusInfo::getSortNum));
        Map<String, List<ModuleCusInfo>> collect = list.stream().collect(Collectors.groupingBy(ModuleCusInfo::getModuleId));
        for (Integer key : enumMap.keySet()) {
            ModuleTypeEnum moduleTypeEnum = enumMap.get(key);
            List<ModuleCusInfo> temp = collect.get("" + key);
            if (temp != null) {
                ModuleCusInfo moduleCusInfo = temp.get(0);
                moduleCusInfos.add(ModuleResult.builder().isShow(moduleCusInfo.getIsShow()).orderNum(moduleCusInfo.getSortNum()).moduleId(moduleCusInfo.getModuleId()).build());
            } else {
                moduleCusInfos.add(ModuleResult.builder().isShow(1).orderNum(moduleTypeEnum.getValue()).moduleId("" + moduleTypeEnum.getValue()).build());
            }
        }

        return RestResponse.ok(moduleCusInfos.stream().sorted(Comparator.comparing(item -> item.getOrderNum())).collect(Collectors.toList()));
    }

    @PostMapping("/edit")
    @ApiOperation("编辑我的工作台")
    public void updateModuleCusInfo(@RequestBody ModuleCusInfoEditParam param) {
        ModuleCusInfo moduleCusInfo = (ModuleCusInfo) moduleCusInfoService.getOne(new QueryWrapper<ModuleCusInfo>().lambda()
                .eq(ModuleCusInfo::getModuleId, param.getModuleId())
                .eq(ModuleCusInfo::getCreateBy, PorosContextHolder.getCurrentUser().getUid()), false);
        if (moduleCusInfo == null) {
            moduleCusInfo = new ModuleCusInfo();
            moduleCusInfo.setModuleId(param.getModuleId());
        }
        moduleCusInfo.setIsShow(param.getIsShow());
        moduleCusInfo.setSortNum(param.getSortNum());
        moduleCusInfoService.saveOrUpdate(moduleCusInfo);
    }

}
