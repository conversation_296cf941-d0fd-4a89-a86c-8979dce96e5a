package cn.getech.ehm.workbench.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 线程池配置
 * <AUTHOR>
 */
@Configuration
public class ExecutorConfig {

    @Bean
    public ExecutorService executorService() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("new-index-thread-%d").build();
        ExecutorService pool = new ThreadPoolExecutor(20, 50, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(50), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        return pool;
    }


}
