package cn.getech.ehm.workbench.service.impl;

import cn.getech.ehm.workbench.dto.generalStastics.*;
import cn.getech.ehm.workbench.entity.GeneralStatistics;
import cn.getech.ehm.workbench.mapper.GeneralStatisticsMapper;
import cn.getech.ehm.workbench.service.IGeneralStatisticsService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.math.BigDecimal;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Slf4j
@Service
public class GeneralStatisticsServiceImpl extends BaseServiceImpl<GeneralStatisticsMapper, GeneralStatistics> implements IGeneralStatisticsService {


//    @Override
//    public PageResult<GeneralStatisticsDto> pageDto(GeneralStatisticsQueryParam generalStatisticsQueryParam) {
//        Wrapper<GeneralStatistics> wrapper = getPageSearchWrapper(generalStatisticsQueryParam);
//        PageResult<GeneralStatisticsDto> result = generalStatisticsParamMapper.pageEntity2Dto(page(generalStatisticsQueryParam, wrapper));
//
//        return Optional.ofNullable(result).orElse(new PageResult<>());
//    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParamNoDate(GeneralStatisticsAddParam generalStatisticsAddParam) {
        GeneralStatistics generalStatistics = BeanUtil.copyProperties(generalStatisticsAddParam, GeneralStatistics.class);
        this.remove(new QueryWrapper<GeneralStatistics>().lambda()
                .eq(GeneralStatistics::getModuleMainType, generalStatistics.getModuleMainType())
                .eq(GeneralStatistics::getModuleItemType, generalStatistics.getModuleItemType())
                .eq(GeneralStatistics::getContentKey, generalStatistics.getContentKey())
                .eq(GeneralStatistics::getModuleRoleType, generalStatistics.getModuleRoleType()));
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, generalStatistics);
        return save(generalStatistics);
    }

    public boolean saveByParamWithDate(GeneralStatisticsAddParam generalStatisticsAddParam) {
        GeneralStatistics generalStatistics = BeanUtil.copyProperties(generalStatisticsAddParam, GeneralStatistics.class);
        this.remove(new QueryWrapper<GeneralStatistics>().lambda()
                .eq(GeneralStatistics::getModuleMainType, generalStatistics.getModuleMainType())
                .eq(GeneralStatistics::getModuleItemType, generalStatistics.getModuleItemType())
                .eq(GeneralStatistics::getModuleRoleType, generalStatistics.getModuleRoleType())
                .eq(GeneralStatistics::getContentKey, generalStatistics.getContentKey())
                .eq(GeneralStatistics::getDate, generalStatistics.getDate()));
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, generalStatistics);
        return save(generalStatistics);
    }

//    @SuppressWarnings("unchecked")
//    @Override
//    public boolean updateByParam(GeneralStatisticsEditParam generalStatisticsEditParam) {
//        GeneralStatistics generalStatistics = generalStatisticsParamMapper.editParam2Entity(generalStatisticsEditParam);
//        Assert.notNull(ResultCode.PARAM_VALID_ERROR, generalStatistics);
//        return updateById(generalStatistics);
//    }
//
//
//    @Override
//    public GeneralStatisticsDto getDtoById(Long id) {
//        return generalStatisticsParamMapper.entity2Dto((GeneralStatistics) this.getById(id));
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public boolean saveDtoBatch(List<GeneralStatisticsDto> rows) {
//        return saveBatch(generalStatisticsParamMapper.dtoList2Entity(rows));
//    }

    private Wrapper<GeneralStatistics> getPageSearchWrapper(GeneralStatisticsQueryParam generalStatisticsQueryParam) {
        LambdaQueryWrapper<GeneralStatistics> wrapper = Wrappers.<GeneralStatistics>lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(GeneralStatistics.class)) {
            wrapper.orderByDesc(GeneralStatistics::getUpdateTime, GeneralStatistics::getCreateTime);
        }
        return wrapper;
    }

    public List<GeneralStatistics> getResultType1(String mainType, String roleType) {
        GeneralStatisticsQueryParamNoPage param = new GeneralStatisticsQueryParamNoPage();
        param.setModuleMainType(mainType);
        param.setModuleRoleType(roleType);
        return this.getByMainType(param);
    }

    public List<GeneralStatistics> getByMainType(GeneralStatisticsQueryParamNoPage param) {
        return this.list(new QueryWrapper<GeneralStatistics>().lambda().eq(GeneralStatistics::getModuleMainType, param.getModuleMainType()).eq(GeneralStatistics::getModuleRoleType, param.getModuleRoleType()));
    }

    public List<GeneralStatistics> getResultType2(GeneralStatisticsQueryParamNoPageWithDate param) {
        return this.getByMainTypeAndDate(param);
    }

    public Map<String, List<GeneralStatistics>> convertToMapByModuleItemType(List<GeneralStatistics> byMainTypeAndDate) {
        return byMainTypeAndDate.stream().collect(Collectors.groupingBy(item -> item.getModuleItemType()));
    }

    public Map<String, Map<String, List<GeneralStatistics>>> convertToMapByModuleItemTypeAndContentKey(List<GeneralStatistics> byMainTypeAndDate) {
        Map<String, Map<String, List<GeneralStatistics>>> collect = byMainTypeAndDate.stream().collect(Collectors.groupingBy(item -> item.getContentKey(), Collectors.groupingBy(item -> item.getModuleItemType())));
        return collect;
    }

    public List<GeneralStatistics> getByMainTypeAndDate(GeneralStatisticsQueryParamNoPageWithDate param) {
        return this.list(new QueryWrapper<GeneralStatistics>().lambda()
                .eq(GeneralStatistics::getModuleMainType, param.getModuleMainType())
                .eq(GeneralStatistics::getModuleRoleType, param.getModuleRoleType())
                .ge(GeneralStatistics::getDate, param.getStartTime())
                .le(GeneralStatistics::getDate, param.getEndTime())
                .orderByDesc(GeneralStatistics::getDate));
    }

    public List<StatisticsResult> getResultTypeOfOther(String moduleItemType, String[] realModuleItemType, Map<String, Map<String, List<GeneralStatistics>>> map) {
        List<StatisticsResult> result = Lists.newArrayList();

        for (String contentKey : map.keySet()) {
            Map<String, List<GeneralStatistics>> itemTypeMap = map.get(contentKey);
            List<GeneralStatistics> sourceList = Lists.newArrayList();
            for (String temp : realModuleItemType) {
                List<GeneralStatistics> list = itemTypeMap.get(temp);
                if (list != null) {
                    for (GeneralStatistics generalStatistics : list) {
                        sourceList.add(generalStatistics);
                    }
                }
            }
            if (sourceList.size() > 0) {
                result.add(StatisticsResult.builder()
                        .resultValue(contentKey)
                        .resultDealType("柱状图")
                        .sourceData(sourceList)
                        .moduleItemType(moduleItemType)
                        .build());
            }
        }

        return result;
    }

    public StatisticsResult getResultTypeOfPercentNum(String
                                                              moduleItemType, Map<String, List<GeneralStatistics>> totalResult, String key1, String key2) {
        List<GeneralStatistics> generalStatistics0 = totalResult.get(key1);
        List<GeneralStatistics> generalStatistics1 = totalResult.get(key2);
        List<GeneralStatistics> sourceList = Lists.newArrayList();


        final BigDecimal[] result1 = {new BigDecimal("0")};
        if (generalStatistics0 != null) {
            generalStatistics0.stream().filter(item -> StringUtils.isNotBlank(item.getContentValue())).map(GeneralStatistics::getContentValue).forEach(item -> {
                result1[0] = result1[0].add(new BigDecimal(item));
            });
        }


        final BigDecimal[] result2 = {new BigDecimal("0")};
        if (generalStatistics0 != null) {
            generalStatistics1.stream().filter(item -> StringUtils.isNotBlank(item.getContentValue())).map(GeneralStatistics::getContentValue).forEach(item -> {
                result2[0] = result2[0].add(new BigDecimal(item));
            });
        }

        if (result1[0].compareTo(new BigDecimal("0")) == 0) {
            return StatisticsResult.builder().resultValue("0").resultDealType("未统计").moduleItemType(moduleItemType).sourceData(sourceList).build();
        }
        if (result2[0].compareTo(new BigDecimal("0")) == 0) {
            return StatisticsResult.builder().resultValue("0").resultDealType("未统计").moduleItemType(moduleItemType).sourceData(sourceList).build();
        }

        sourceList.addAll(generalStatistics0);
        sourceList.addAll(generalStatistics1);
        return StatisticsResult.builder()
                .resultValue(result1[0].divide(result2[0], 2, BigDecimal.ROUND_HALF_UP).toString())
                .resultDealType("百分比数据")
                .sourceData(sourceList)
                .moduleItemType(moduleItemType)
                .build();
    }

    public StatisticsResult getResultTypeOfLineChart(String moduleItemType, Map<String, List<GeneralStatistics>> totalResult, String key) {
        List<GeneralStatistics> sourceList = Lists.newArrayList();
        List<GeneralStatistics> generalStatistics = totalResult.get(key);
        if (generalStatistics != null) {
            sourceList.addAll(generalStatistics);
        }
        return StatisticsResult.builder()
                .resultKey("")
                .resultValue("0")
                .resultDealType("折线图")
                .sourceData(sourceList)
                .moduleItemType(moduleItemType)
                .build();
    }

    public List<StatisticsResult> getResultTypeOfNormal(List<GeneralStatistics> list) {
        List<StatisticsResult> result = Lists.newArrayList();
        for (GeneralStatistics generalStatistics : list) {
            List<GeneralStatistics> sourceList = Lists.newArrayList();
            sourceList.add(generalStatistics);
            result.add(StatisticsResult.builder()
                    .resultKey(generalStatistics.getContentValue())
                    .resultValue(generalStatistics.getContentValue())
                    .resultDealType("普通数据")
                    .sourceData(sourceList)
                    .moduleItemType(generalStatistics.getModuleItemType())
                    .build());
        }
        return result;
    }

    public List<StatisticsResult> getResultTypeOfCompare(Map<String, Map<String, List<GeneralStatistics>>> nowMonthMap, Map<String, Map<String, List<GeneralStatistics>>> lastMonthMap, String moduleItemType) {
        List<StatisticsResult> result = Lists.newArrayList();

        if (nowMonthMap != null) {
            for (String key : nowMonthMap.keySet()) {
                Map<String, List<GeneralStatistics>> nowMap = nowMonthMap.get(key);
                Map<String, List<GeneralStatistics>> lastMap = lastMonthMap.get(key);
                if (nowMap != null && lastMap != null) {
                    List<GeneralStatistics> nowList = nowMap.get(moduleItemType);
                    List<GeneralStatistics> lastList = lastMap.get(moduleItemType);
                    if (nowList == null && lastList == null) {
                        continue;
                    }
                    List<GeneralStatistics> sourceList = Lists.newArrayList();
                    sourceList.addAll(nowList);
                    sourceList.addAll(lastList);
                    String resultValue = "0";
                    if (new BigDecimal(nowList.get(0).getContentValue()).compareTo(new BigDecimal(lastList.get(0).getContentValue())) >= 0) {
                        resultValue = nowList.get(0).getContentValue();
                    } else {
                        resultValue = lastList.get(0).getContentValue();
                    }
                    result.add(StatisticsResult.builder()
                            .resultValue(resultValue)
                            .resultKey(key)
                            .resultDealType("对比数据")
                            .sourceData(sourceList)
                            .moduleItemType(moduleItemType)
                            .build());
                }
            }
            return result;
        } else {
            return result;
        }
    }
}
