package cn.getech.ehm.workbench.enums;

import cn.getech.ehm.task.enums.DefectStatusEnum;
import cn.getech.ehm.task.enums.TaskOperationType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.enums.TaskType;
import com.google.common.collect.Lists;

import java.util.List;

public enum WorkBenchModuleEnum {


    MODULE_1(1, "故障工单数", "getWorkbenchStatisticsOfMaintTask", 1, "todayTask", "task", generate("" + TaskType.BREAKDOWN.getValue(), " ", "1")),
    MODULE_2(2, "维保工单数", "getWorkbenchStatisticsOfMaintTask", 2, "todayTask", "task", generate("" + TaskType.PLAN.getValue(), " ", "1")),
    MODULE_3(3, "点检工单数", "getWorkbenchStatisticsOfMaintTask", 3, "todayTask", "task", generate("" + TaskType.EC.getValue(), " ", "1")),
    MODULE_4(4, "缺陷工单数", "getWorkbenchStatisticsOfMaintTask", 4, "todayTask", "task", generate("" + TaskType.DEFECT.getValue(), " ", "1")),
//    MODULE_5(5, "待处理缺陷数", "getCountOfStatistics", 5, "todayTask", "task", generate("" + DefectStatusEnum.NODEAL.getValue(), "" + DefectStatusEnum.DEALING.getValue())),

    MODULE_6(6, "备件使用数", "test", 6, "todayParts", "part", generate()),
    MODULE_7(7, "今日入库备件", "getCount", 7, "todayParts", "part", generate("0", "1", "0")),
    MODULE_8(8, "今日出库备件", "getCount", 8, "todayParts", "part", generate("1", "1", "0")),
    MODULE_9(9, "今日调拨备件", "getCount", 9, "todayParts", "part", generate("2", "1", "0")),
    MODULE_10(10, "备件到期数", "test", 10, "todayParts", "part", generate()),

    MODULE_11(11, "待派单", "getWorkbenchStatisticsOfMaintTask", 11, "maintTask", "task", generate(" ", "" + TaskStatusType.DISPATCH.getValue(), "0")),
    MODULE_12(12, "待审批", "getWorkbenchStatisticsOfMaintTask", 12, "maintTask", "task", generate(" ", "" + TaskStatusType.REPAIR_AUDIT.getValue(), "0")),
    MODULE_13(13, "待接单", "getWorkbenchStatisticsOfMaintTask", 13, "maintTask", "task", generate(" ", "" + TaskStatusType.RECEIVING.getValue(), "0")),
    MODULE_14(14, "待执行", "getWorkbenchStatisticsOfMaintTask", 14, "maintTask", "task", generate(" ", "" + TaskStatusType.HANDLE.getValue(), "0")),
    MODULE_15(15, "安全确认", "getWorkbenchStatisticsOfMaintTask", 15, "maintTask", "task", generate(" ", "" + TaskStatusType.CONFIRM.getValue(), "0")),
    MODULE_16(16, "执行中", "getWorkbenchStatisticsOfMaintTask", 16, "maintTask", "task", generate(" ", "" + TaskStatusType.PROCESSING.getValue(), "0")),
    MODULE_17(17, "待验收", "getWorkbenchStatisticsOfMaintTask", 17, "maintTask", "task", generate(" ", "" + TaskStatusType.CHECK_ACCEPT.getValue(), "0")),
    MODULE_18(18, "已关闭", "getWorkbenchStatisticsOfMaintTask", 18, "maintTask", "task", generate(" ", "" + TaskStatusType.CLOSED.getValue(), "0")),
    MODULE_19(19, "异常关闭", "getWorkbenchStatisticsOfMaintTask", 19, "maintTask", "task", generate(" ", "" + TaskStatusType.EXCEPTION_CLOSED.getValue(), "0")),

    MODULE_20(20, "库存预警", "getCountOfStockMainRelation", 20, "warningofPart", "part", generate(" "," "," ")),
    MODULE_21(21, "库龄预警", "getWarnNum", 21, "warningofPart", "part", generate("3")),
    MODULE_22(22, "寿命预警", "getHealNumCount", 22, "warningofPart", "part", generate("")),

    MODULE_23(23, "工单超时", "getWorkbenchStatisticsOfMaintTaskOverTime", 23, "warningofTask", "task", generate("" + TaskOperationType.ANY_OVERTIME_TASK.getValue())),
    //MODULE_24(24, "接单超时", "getWorkbenchStatisticsOfMaintTaskOverTime", 24, "warningofTask", "task", generate("" + TaskOperationType.REC_TASK.getValue())),
    MODULE_25(25, "工单超限", "getWorkbenchStatisticsOfMaintTaskOverTime", 25, "warningofTask", "task", generate("" + TaskOperationType.DEAL_TASK.getValue())),

    MODULE_26(26, "处理超期", "getCountOfStatisticsOverTime", 26, "warningofDefect", "task", generate("" + DefectStatusEnum.DEALING.getValue(), "" + DefectStatusEnum.NODEAL.getValue())),

    MODULE_27(27, "2月内临近", "getSpecialWarningCountOfNext", 27, "warningofEquipment", "equipment", generate("" + 60)),
    MODULE_28(28, "1月内临近", "getSpecialWarningCountOfNext", 28, "warningofEquipment", "equipment", generate("" + 30)),
    MODULE_29(29, "检验数量", "getSpecialWarningCountOfOverRemind", 29, "warningofEquipment", "equipment", generate("")),
    MODULE_30(30, "到期数量", "getSpecialWarningCountOfOverNext", 30, "warningofEquipment", "equipment", generate("")),

    MODULE_31(31, "2月内临近", "getSpecialWarningPersonCountOfNext", 31, "warningofPerson", "equipment", generate("" + 60)),
    MODULE_32(32, "1月内临近", "getSpecialWarningPersonCountOfNext", 32, "warningofPerson", "equipment", generate("" + 30)),
    MODULE_33(33, "复审数量", "getSpecialWarningPersonCountOfOverRemind", 33, "warningofPerson", "equipment", generate("")),
    MODULE_34(34, "到期数量", "getSpecialWarningPersonCountOfOverNext", 34, "warningofPerson", "equipment", generate("")),

    MODULE_35(35, "1周内临近", "getStatisticsOfPlanEndCount", 35, "warningofPlan", "task", generate("1")),
    MODULE_36(36, "今日到期", "getStatisticsOfPlanEndCount", 36, "warningofPlan", "task", generate("2")),
    MODULE_37(37, "过期数量", "getStatisticsOfPlanEndCount", 37, "warningofPlan", "task", generate("3")),

    MODULE_38(38, "不足1月", "getStatisticsOfRemainingLifeCount", 38, "remainingLife", "equipment", generate("1")),
    MODULE_39(39, "不足3月", "getStatisticsOfRemainingLifeCount", 39, "remainingLife", "equipment", generate("3")),
    MODULE_40(40, "不足6月", "getStatisticsOfRemainingLifeCount", 40, "remainingLife", "equipment", generate("6")),


    MODULE_999(999, "测试专用", "getSpecialWarningCountOfNext", 999, "test", "test", generate(""));

    public static List<WorkBenchModuleEnum> getBySceneCode(String sceneCode) {
        List<WorkBenchModuleEnum> list = Lists.newArrayList();
        for (WorkBenchModuleEnum workBenchModuleEnum : WorkBenchModuleEnum.values()) {
            if (workBenchModuleEnum.getSceneCode().equals(sceneCode)) {
                list.add(workBenchModuleEnum);
            }
        }
        return list;
    }

    public static String generate(String... args) {
        return String.join(",", args);
    }

    WorkBenchModuleEnum(Integer id, String name, String methodName, Integer sort, String sceneCode, String moudleName, String methodParam) {
        this.id = id;
        this.name = name;
        this.methodName = methodName;
        this.sort = sort;
        this.sceneCode = sceneCode;
        this.moduleName = moudleName;
        this.methodParam = methodParam;
    }

    private Integer id;

    private String name;

    private String methodName;

    private Integer sort;

    private String sceneCode;

    private String moduleName;

    private String methodParam;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getMethodParam() {
        return methodParam;
    }

    public void setMethodParam(String methodParam) {
        this.methodParam = methodParam;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
}
