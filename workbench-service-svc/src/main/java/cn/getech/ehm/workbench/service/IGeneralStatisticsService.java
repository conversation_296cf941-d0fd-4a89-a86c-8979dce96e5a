package cn.getech.ehm.workbench.service;

import cn.getech.ehm.workbench.dto.generalStastics.GeneralStatisticsAddParam;
import cn.getech.ehm.workbench.dto.generalStastics.GeneralStatisticsQueryParamNoPage;
import cn.getech.ehm.workbench.dto.generalStastics.GeneralStatisticsQueryParamNoPageWithDate;
import cn.getech.ehm.workbench.dto.generalStastics.StatisticsResult;
import cn.getech.ehm.workbench.entity.GeneralStatistics;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IGeneralStatisticsService extends IBaseService<GeneralStatistics> {

//    /**
//     * 分页查询，返回Dto
//     *
//     * @param generalStatisticsQueryParam
//     * @return
//     */
//    PageResult<GeneralStatisticsDto> pageDto(GeneralStatisticsQueryParam generalStatisticsQueryParam);

    /**
     * 保存
     *
     * @param generalStatisticsAddParam
     * @return
     */
    boolean saveByParamNoDate(GeneralStatisticsAddParam generalStatisticsAddParam);

    boolean saveByParamWithDate(GeneralStatisticsAddParam generalStatisticsAddParam);

//    /**
//     * 根据id查询，转dto
//     *
//     * @param id
//     * @return
//     */
//    GeneralStatisticsDto getDtoById(Long id);
//
//    /**
//     * 批量保存
//     *
//     * @param rows
//     */
//    boolean saveDtoBatch(List<GeneralStatisticsDto> rows);
//
//    /**
//     * 更新
//     *
//     * @param generalStatisticsEditParam
//     */
//    boolean updateByParam(GeneralStatisticsEditParam generalStatisticsEditParam);

    public List<GeneralStatistics> getResultType1(String mainType, String roleType);

    public List<GeneralStatistics> getResultType2(GeneralStatisticsQueryParamNoPageWithDate param);

    public Map<String, List<GeneralStatistics>> convertToMapByModuleItemType(List<GeneralStatistics> byMainTypeAndDate);

    public Map<String, Map<String, List<GeneralStatistics>>> convertToMapByModuleItemTypeAndContentKey(List<GeneralStatistics> byMainTypeAndDate);

    public List<GeneralStatistics> getByMainType(GeneralStatisticsQueryParamNoPage param);

    public List<GeneralStatistics> getByMainTypeAndDate(GeneralStatisticsQueryParamNoPageWithDate param);

    public StatisticsResult getResultTypeOfPercentNum(String moduleItemType, Map<String, List<GeneralStatistics>> totalResult, String key1, String key2);

    public StatisticsResult getResultTypeOfLineChart(String moduleItemType, Map<String, List<GeneralStatistics>> totalResult, String key);

    public List<StatisticsResult> getResultTypeOfOther(String moduleItemType, String[] realModuleItemType, Map<String, Map<String, List<GeneralStatistics>>> map);

    public List<StatisticsResult> getResultTypeOfNormal(List<GeneralStatistics> list);

    public List<StatisticsResult> getResultTypeOfCompare(Map<String, Map<String, List<GeneralStatistics>>> nowMonthMap, Map<String, Map<String, List<GeneralStatistics>>> lastMonthMap, String moduleItemType);
}