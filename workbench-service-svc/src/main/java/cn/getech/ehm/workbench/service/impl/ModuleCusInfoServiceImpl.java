package cn.getech.ehm.workbench.service.impl;

import cn.getech.ehm.workbench.entity.ModuleCusInfo;
import cn.getech.ehm.workbench.mapper.ModuleCusInfoMapper;
import cn.getech.ehm.workbench.service.IModuleCusInfoService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoQueryParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoAddParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoEditParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoParamMapper;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 * 模块自定义信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Slf4j
@Service
public class ModuleCusInfoServiceImpl extends BaseServiceImpl<ModuleCusInfoMapper, ModuleCusInfo> implements IModuleCusInfoService {

    @Autowired
    private ModuleCusInfoParamMapper moduleCusInfoParamMapper;

    @Override
    public PageResult<ModuleCusInfoDto> pageDto(ModuleCusInfoQueryParam moduleCusInfoQueryParam) {
        Wrapper<ModuleCusInfo> wrapper = getPageSearchWrapper(moduleCusInfoQueryParam);
        PageResult<ModuleCusInfoDto> result = moduleCusInfoParamMapper.pageEntity2Dto(page(moduleCusInfoQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(ModuleCusInfoAddParam moduleCusInfoAddParam) {
        ModuleCusInfo moduleCusInfo = moduleCusInfoParamMapper.addParam2Entity(moduleCusInfoAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,moduleCusInfo);
        return save(moduleCusInfo);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(ModuleCusInfoEditParam moduleCusInfoEditParam) {
        ModuleCusInfo moduleCusInfo = moduleCusInfoParamMapper.editParam2Entity(moduleCusInfoEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,moduleCusInfo);
        return updateById(moduleCusInfo);
    }


    @Override
    public ModuleCusInfoDto getDtoById(Long id) {
        return moduleCusInfoParamMapper.entity2Dto((ModuleCusInfo) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<ModuleCusInfoDto> rows) {
        return saveBatch(moduleCusInfoParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<ModuleCusInfo> getPageSearchWrapper(ModuleCusInfoQueryParam moduleCusInfoQueryParam) {
        LambdaQueryWrapper<ModuleCusInfo> wrapper = Wrappers.<ModuleCusInfo>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(ModuleCusInfo.class)){
            wrapper.orderByDesc(ModuleCusInfo::getUpdateTime,ModuleCusInfo::getCreateTime);
        }
        return wrapper;
    }
}
