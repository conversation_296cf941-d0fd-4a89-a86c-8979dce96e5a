package cn.getech.ehm.workbench.service;

import cn.getech.ehm.workbench.entity.ModuleCusInfo;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoQueryParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoAddParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoEditParam;
import cn.getech.ehm.workbench.dto.moduleCusInfo.ModuleCusInfoDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 模块自定义信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IModuleCusInfoService extends IBaseService<ModuleCusInfo> {

        /**
         * 分页查询，返回Dto
         *
         * @param moduleCusInfoQueryParam
         * @return
         */
        PageResult<ModuleCusInfoDto> pageDto(ModuleCusInfoQueryParam moduleCusInfoQueryParam);

        /**
         * 保存
         * @param moduleCusInfoAddParam
         * @return
         */
        boolean saveByParam(ModuleCusInfoAddParam moduleCusInfoAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        ModuleCusInfoDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<ModuleCusInfoDto> rows);

        /**
         * 更新
         * @param moduleCusInfoEditParam
         */
        boolean updateByParam(ModuleCusInfoEditParam moduleCusInfoEditParam);
}