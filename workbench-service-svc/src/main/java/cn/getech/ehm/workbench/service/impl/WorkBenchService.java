package cn.getech.ehm.workbench.service.impl;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.SpringContextUtils;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.part.client.PartNewClient;
import cn.getech.ehm.task.client.TaskClient;
import cn.getech.ehm.workbench.dto.workbench.WorkBenchUnit;
import cn.getech.ehm.workbench.enums.WorkBenchModuleEnum;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.utils.ReflectUtils;
import cn.getech.poros.framework.common.utils.StringUtils;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.support.FeignUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkBenchService {
    public final static String TODAY_TASK = "todayTask";
    public final static String TODAY_PARTS = "todayParts";
    public final static String MAINT_TASK = "maintTask";
    public final static String WARNING_TASK = "warningofTask";
    public final static String WARNING_EQUIPMENT = "warningofEquipment";
    public final static String WARNING_PART = "warningofPart";
    public final static String WARNING_PERSON = "warningofPerson";
    public final static String WARNING_DEFECTT = "warningofDefect";
    public final static String WARNING_PLAN = "warningofPlan";
    public final static String REMAINING_LIFE = "remainingLife";
    public final static String TEST = "test";

    public final static String NOTICE_SERVICE_ERROR = "服务维护中";
    public final static String NOTICE_NO_COUNT = "暂无";

    @Autowired
    private PartNewClient partFeignClient;
    @Autowired
    private TaskClient taskclient;
    @Autowired
    ExecutorService executorService;
    @Autowired
    private EquipmentClient equipmentClient;

    @Autowired
    RedisTemplate redisTemplate;

    @SneakyThrows
    public List<WorkBenchUnit> generateUnitList(String sceneCode) {
        List<WorkBenchUnit> list = Lists.newArrayList();
        List<WorkBenchModuleEnum> bySceneCode = WorkBenchModuleEnum.getBySceneCode(sceneCode);
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        List<CompletableFuture<WorkBenchUnit>> collect = bySceneCode.stream().map(temp -> {
            return CompletableFuture.supplyAsync(() -> {
                String value = this.getValueByMethod(temp.getModuleName(), currentUser, temp.getMethodName(), temp.getMethodParam());
                WorkBenchUnit unit = WorkBenchUnit.builder().showValue(value).remark(temp.getName()).sortNum(temp.getSort()).build();
                return unit;
            }, executorService).exceptionally(ex -> {
                ex.printStackTrace();
                return WorkBenchUnit.builder().showValue("计算有误").remark(temp.getName()).sortNum(temp.getSort()).build();
            });
        }).collect(Collectors.toList());
        CompletableFuture.allOf(collect.toArray(new CompletableFuture[collect.size()])).join();
        for (CompletableFuture<WorkBenchUnit> completableFuture : collect) {
            list.add(completableFuture.get());
        }
        return list.stream().sorted((o1, o2) -> o1.getSortNum().compareTo(o2.getSortNum())).collect(Collectors.toList());
    }


    public Object getFeignClient(String moudleName) {
        Map map = Maps.newHashMap();
        map.put("task", taskclient);
        map.put("part", partFeignClient);
        map.put("equipment", equipmentClient);
        map.put("test", taskclient);
        return map.get(moudleName);
    }

    private String getValueByMethod(String moudleName, UserBaseInfo currentUser, String method, String methodParam) {
        if (method.equals("test")) {
            return NOTICE_NO_COUNT;
        }
        UserContextHolder.getContext().setUserBaseInfo(currentUser);
        try {
            String cache = this.getCache(moudleName, currentUser.getUid(), method, methodParam);
            if (StringUtils.isNotEmpty(cache) && !cache.equals(NOTICE_SERVICE_ERROR)) {
                return cache;
            }
            Object feignClient = this.getFeignClient(moudleName);
            RestResponse result = ReflectUtil.invoke(feignClient, method, methodParam);
            log.info("调用feign:{},获取到结果：{}", method, result);
            //String result = ReflectUtil.invoke(workBenchService, method, methodParam);
            if (result != null && result.isOk()) {
                String s = "" + ((result.getData() != null) ? result.getData().toString() : "");
                this.setCache(moudleName, currentUser.getUid(), method, methodParam, s);
                return s;
            } else {
                return this.getCache(moudleName, currentUser.getUid(), method, methodParam);
            }

        } catch (Exception e) {
//            e.printStackTrace();
        }
        return this.getCache(moudleName, currentUser.getUid(), method, methodParam);
    }

    public void setCache(String moudleName, String uid, String method, String methodParam, String value) {
        try {
            redisTemplate.opsForValue().set(moudleName + uid + method + methodParam, value, 10, TimeUnit.MINUTES);
        } catch (Exception e) {

        }
    }

    private String getCache(String moudleName, String uid, String method, String methodParam) {
        try {
            Object o = redisTemplate.opsForValue().get(moudleName + uid + method + methodParam);
            if (o != null) {
                return o.toString();
            } else {
                return NOTICE_SERVICE_ERROR;
            }
        } catch (Exception e) {

        }
        return NOTICE_SERVICE_ERROR;
    }

    private String test(Object[] methodParam) {
        return NOTICE_NO_COUNT;
    }


    public void setPoros(Object[] methodParam) {
        UserBaseInfo currentUser = (UserBaseInfo) methodParam[methodParam.length - 1];
        UserContextHolder.getContext().setUserBaseInfo(currentUser);
    }


}
