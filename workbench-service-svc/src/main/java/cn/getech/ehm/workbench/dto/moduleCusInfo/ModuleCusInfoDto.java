package cn.getech.ehm.workbench.dto.moduleCusInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 模块自定义信息 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@ApiModel(value = "ModuleCusInfoDto", description = "模块自定义信息返回数据模型")
public class ModuleCusInfoDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

}