package cn.getech.ehm.workbench.enums;

public enum StatisticsRoleTypeEnum {
    NORMAL(0, "一般员工"),
    MANAGER_NORMAL(1, "厂区管理者"),
    MANAGER_SITE(2, "site管理者"),
    MANAGER_CORP(3, "公司管理者");

    StatisticsRoleTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

//    public static String getNameByValue(Integer type) {
//        switch (type) {
//            case 0:
//                return ModuleTypeEnum.ORDER_TASK.getName();
//        }
//        return "";
//    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
