package cn.getech.ehm.workbench.controller;


import cn.getech.ehm.workbench.dto.generalStastics.GeneralStatisticsAddParam;
import cn.getech.ehm.workbench.dto.generalStastics.GeneralStatisticsQueryParamNoPageWithDate;
import cn.getech.ehm.workbench.dto.generalStastics.StatisticsResult;
import cn.getech.ehm.workbench.entity.GeneralStatistics;
import cn.getech.ehm.workbench.enums.ModuleTypeEnum;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import cn.getech.poros.framework.common.api.RestResponse;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.getech.ehm.workbench.service.IGeneralStatisticsService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 通用统计控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RestController
@RequestMapping("/generalStatistics")
@Api(tags = "通用统计服务接口")
public class GeneralStatisticsController {

    @Autowired
    private IGeneralStatisticsService generalStatisticsService;

    /**
     * 新增通用统计
     */
    @ApiOperation("新增统计数据-无时间")
    @AuditLog(title = "通用统计", desc = "新增通用统计", businessType = BusinessType.INSERT)
    @PostMapping("/add/noDate")
    //@Permission("general:statistics:update")
    public RestResponse<Boolean> addNoDate(@RequestBody @Valid GeneralStatisticsAddParam generalStatisticsAddParam) {
        return RestResponse.ok(generalStatisticsService.saveByParamNoDate(generalStatisticsAddParam));
    }

    @ApiOperation("新增统计数据-带时间")
    @AuditLog(title = "通用统计", desc = "新增通用统计", businessType = BusinessType.INSERT)
    @PostMapping("/add/withDate")
    //@Permission("general:statistics:update")
    public RestResponse<Boolean> addWithDate(@RequestBody @Valid GeneralStatisticsAddParam generalStatisticsAddParam) {
        return RestResponse.ok(generalStatisticsService.saveByParamWithDate(generalStatisticsAddParam));
    }


//    @PostMapping("/get/by/mainType")
//    @ApiOperation("根据一级类型获取无时间范围统计数据")
//    public RestResponse getByMainType(@RequestBody @Valid GeneralStatisticsQueryParamNoPage param) {
//        return RestResponse.ok(generalStatisticsService.getByMainType(param));
//    }
//
//    @PostMapping("/get/by/mainType/date")
//    @ApiOperation("根据一级类型获取有时间范围统计数据")
//    public RestResponse getByMainTypeAndDate(@RequestBody @Valid GeneralStatisticsQueryParamNoPageWithDate param) {
//        return RestResponse.ok(generalStatisticsService.getByMainTypeAndDate(param));
//    }

    @GetMapping("/get/by/type/type1")
    @ApiOperation("获取保养工单统计数据")
    public RestResponse<List<StatisticsResult>> getType1(String roleType) {
        List<GeneralStatistics> resultType1 = generalStatisticsService.getResultType1("" + ModuleTypeEnum.MAINTAIN_ORDER.getValue(), roleType);
        return RestResponse.ok(generalStatisticsService.getResultTypeOfNormal(resultType1));
    }

    @GetMapping("/get/by/type/type2")
    @ApiOperation("获取点检工单统计数据")
    public RestResponse<List<StatisticsResult>> getType2(String roleType) {
        List<GeneralStatistics> resultType1 = generalStatisticsService.getResultType1("" + ModuleTypeEnum.EC_ORDER.getValue(), roleType);
        return RestResponse.ok(generalStatisticsService.getResultTypeOfNormal(resultType1));
    }

    @PostMapping("/get/by/type/type3")
    @ApiOperation("获取点检管理统计数据")
    public RestResponse<List<StatisticsResult>> getType3(@RequestBody @Valid GeneralStatisticsQueryParamNoPageWithDate param) {
        param.setModuleMainType("" + ModuleTypeEnum.EC_MANAGE.getValue());
        Map<String, List<GeneralStatistics>> totalResult = generalStatisticsService.convertToMapByModuleItemType(generalStatisticsService.getResultType2(param));
        List<StatisticsResult> result = Lists.newArrayList();
        StatisticsResult result1 = generalStatisticsService.getResultTypeOfPercentNum("3.1", totalResult, "3.1.1", "3.1.2");
        result.add(result1);
        StatisticsResult result2 = generalStatisticsService.getResultTypeOfPercentNum("3.2", totalResult, "3.2.1", "3.2.2");
        result.add(result2);
        StatisticsResult result3 = generalStatisticsService.getResultTypeOfLineChart("3.3", totalResult, "3.3");
        result.add(result3);
        return RestResponse.ok(result);
    }

    @PostMapping("/get/by/type/type4")
    @ApiOperation("获取设备管理统计数据")
    public RestResponse<List<StatisticsResult>> getType4(String roleType) {
        Map<String, Map<String, List<GeneralStatistics>>> map = generalStatisticsService.convertToMapByModuleItemTypeAndContentKey(generalStatisticsService.getResultType1("" + ModuleTypeEnum.EQUIPMENT_MANAGE.getValue(), roleType));
        List<StatisticsResult> result = Lists.newArrayList();
        List<StatisticsResult> result1 = generalStatisticsService.getResultTypeOfOther("4.1", new String[]{"4.1.1"}, map);
        result.addAll(result1);
        List<StatisticsResult> result2 = generalStatisticsService.getResultTypeOfOther("4.2", new String[]{"4.2.1"}, map);
        result.addAll(result2);
        List<StatisticsResult> result3 = generalStatisticsService.getResultTypeOfOther("4.3", new String[]{"4.3.1"}, map);
        result.addAll(result3);
        return RestResponse.ok(result);
    }

    @PostMapping("/get/by/type/type5")
    @ApiOperation("获取保养管理统计数据")
    public RestResponse<List<StatisticsResult>> getType5(@RequestBody @Valid GeneralStatisticsQueryParamNoPageWithDate param) {
        param.setModuleMainType("" + ModuleTypeEnum.MAINTAIN_MANAGE.getValue());
        Map<String, List<GeneralStatistics>> totalResult = generalStatisticsService.convertToMapByModuleItemType(generalStatisticsService.getResultType2(param));
        List<StatisticsResult> result = Lists.newArrayList();
        StatisticsResult result1 = generalStatisticsService.getResultTypeOfPercentNum("5.1", totalResult, "5.1.1", "5.1.2");
        result.add(result1);
        StatisticsResult result2 = generalStatisticsService.getResultTypeOfPercentNum("5.2", totalResult, "5.2.1", "5.2.2");
        result.add(result2);
        StatisticsResult result3 = generalStatisticsService.getResultTypeOfPercentNum("5.3", totalResult, "5.3.1", "5.3.2");
        result.add(result3);
        StatisticsResult result4 = generalStatisticsService.getResultTypeOfLineChart("5.4", totalResult, "5.4");
        result.add(result4);
        return RestResponse.ok(result);
    }

    @PostMapping("/get/by/type/type6")
    @ApiOperation("获取备件管理统计数据")
    public RestResponse<List<StatisticsResult>> getType6(@RequestBody @Valid GeneralStatisticsQueryParamNoPageWithDate param) {
        param.setModuleMainType("" + ModuleTypeEnum.PART_MANAGE.getValue());
        Map<String, Map<String, List<GeneralStatistics>>> map = generalStatisticsService.convertToMapByModuleItemTypeAndContentKey(generalStatisticsService.getResultType2(param));
        List result = Lists.newArrayList();
        List<StatisticsResult> resultTypeOfOther1 = generalStatisticsService.getResultTypeOfOther("6.1", new String[]{"6.1.1", "6.1.2", "6.1.3"}, map);
        result.addAll(resultTypeOfOther1);
        List<StatisticsResult> resultTypeOfOther2 = generalStatisticsService.getResultTypeOfOther("6.2", new String[]{"6.2.1", "6.2.2", "6.2.3"}, map);
        result.addAll(resultTypeOfOther2);
        List<StatisticsResult> resultTypeOfOther3 = generalStatisticsService.getResultTypeOfOther("6.3", new String[]{"6.3.1", "6.3.2", "6.3.3"}, map);
        result.addAll(resultTypeOfOther3);
        List<StatisticsResult> resultTypeOfOther4 = generalStatisticsService.getResultTypeOfOther("6.4", new String[]{"6.4.1", "6.4.2", "6.4.3"}, map);
        result.addAll(resultTypeOfOther4);
        return RestResponse.ok(result);
    }

    @PostMapping("/get/by/type/type8")
    @ApiOperation("获取运营数据统计数据")
    public RestResponse<List<StatisticsResult>> getType8(String roleType) {
        GeneralStatisticsQueryParamNoPageWithDate param = new GeneralStatisticsQueryParamNoPageWithDate();
        param.setModuleRoleType(roleType);
        param.setModuleMainType("" + ModuleTypeEnum.OPERATION_DATA.getValue());
        Date now = new Date();
        param.setStartTime(DateUtil.beginOfMonth(now));
        param.setEndTime(DateUtil.endOfMonth(now));
        List<GeneralStatistics> resultType2 = generalStatisticsService.getResultType2(param);
        Map<String, Map<String, List<GeneralStatistics>>> nowMonthMap = generalStatisticsService.convertToMapByModuleItemTypeAndContentKey(resultType2.stream().sorted(Comparator.comparing(GeneralStatistics::getContentValue).reversed()).collect(Collectors.toList()));
        Date lastMonth = DateUtil.offset(now, DateField.MONTH, -1);
        param.setStartTime(DateUtil.beginOfMonth(lastMonth));
        param.setEndTime(DateUtil.endOfMonth(lastMonth));
        Map<String, Map<String, List<GeneralStatistics>>> lastMonethMap = generalStatisticsService.convertToMapByModuleItemTypeAndContentKey((generalStatisticsService.getResultType2(param)));


        List<StatisticsResult> result = Lists.newArrayList();
        List<StatisticsResult> resultTypeOfOther1 = generalStatisticsService.getResultTypeOfCompare(nowMonthMap, lastMonethMap, "8.1");
        result.addAll(resultTypeOfOther1);
        List<StatisticsResult> resultTypeOfOther2 = generalStatisticsService.getResultTypeOfCompare(nowMonthMap, lastMonethMap, "8.2");
        result.addAll(resultTypeOfOther2);
        List<StatisticsResult> resultTypeOfOther3 = generalStatisticsService.getResultTypeOfCompare(nowMonthMap, lastMonethMap, "8.3");
        result.addAll(resultTypeOfOther3);
        return RestResponse.ok(result.stream().sorted(Comparator.comparing(StatisticsResult::getResultValue).reversed()).collect(Collectors.toList()));
    }


}
