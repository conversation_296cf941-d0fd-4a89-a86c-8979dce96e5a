package cn.getech.ehm.workbench.dto.moduleCusInfo;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.workbench.entity.ModuleCusInfo;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 模块自定义信息 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  ModuleCusInfoParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param moduleCusInfoAddParam
     * @return
     */
    ModuleCusInfo addParam2Entity(ModuleCusInfoAddParam moduleCusInfoAddParam);

    /**
     * 编辑参数转换为实体
     * @param moduleCusInfoEditParam
     * @return
     */
    ModuleCusInfo editParam2Entity(ModuleCusInfoEditParam moduleCusInfoEditParam);

    /**
     * 实体转换为Dto
     * @param moduleCusInfo
     * @return
     */
    ModuleCusInfoDto entity2Dto(ModuleCusInfo moduleCusInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<ModuleCusInfoDto> pageEntity2Dto(PageResult<ModuleCusInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<ModuleCusInfo> dtoList2Entity(List<ModuleCusInfoDto> rows);

}
