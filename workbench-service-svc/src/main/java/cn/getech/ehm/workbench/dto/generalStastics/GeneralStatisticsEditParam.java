package cn.getech.ehm.workbench.dto.generalStastics;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GeneralStatistics编辑", description = "编辑参数")
public class GeneralStatisticsEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String remark;

}
