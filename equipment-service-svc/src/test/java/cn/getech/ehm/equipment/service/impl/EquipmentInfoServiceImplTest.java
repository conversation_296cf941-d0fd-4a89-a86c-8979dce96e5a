package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.equipment.Application;
import cn.getech.ehm.equipment.entity.EquipmentInfoProp;
import cn.getech.ehm.equipment.mapper.EquipmentInfoPropMapper;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
public class EquipmentInfoServiceImplTest {
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private EquipmentInfoPropMapper equipmentInfoPropMapper;

    @Test
    public void test() {
        String[] partIds = {"9ae1efd4f4658faa1f26w8e992da5435","111"};
        List<String> strings = equipmentInfoService.checkPartUsed(partIds);
        log.info(strings.toString());
    }

    @Test
    public void test2() {
        String[] partIds = {"9ae1efd4f4658faa1f26w8e992da5435","111"};
        //List<EquipmentInfoProp> list = equipmentInfoPropMapper.getCalibrationDateListByEquipmentIds(partIds);
        //log.info(list.toString());
    }


}