2022/3/22 轴承相关
ALTER TABLE iot_bearing_db ADD tenant_id varchar(32) COMMENT '租户id' DEFAULT 'ehm_t1' AFTER remark;
ALTER TABLE iot_bearing_db ADD deleted tinyint(3) COMMENT '是否删除' DEFAULT 0 AFTER tenant_id;
ALTER TABLE iot_bearing_factory ADD tenant_id varchar(32) COMMENT '租户id' DEFAULT 'ehm_t1' AFTER remark;
ALTER TABLE iot_bearing_factory ADD deleted tinyint(3) COMMENT '是否删除' DEFAULT 0 AFTER tenant_id;
ALTER TABLE iot_bearing_model ADD tenant_id varchar(32) COMMENT '租户id' DEFAULT 'ehm_t1' AFTER remark;
ALTER TABLE iot_bearing_model ADD deleted tinyint(3) COMMENT '是否删除' DEFAULT 0 AFTER tenant_id;

CREATE TABLE `spare_parts_category` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `type` tinyint NOT NULL COMMENT '类型(1部件2参数)',
  `maintain_param` tinyint NOT NULL DEFAULT '1' COMMENT '是否维护运行参数',
  `base_library` tinyint DEFAULT NULL COMMENT '基础库类型',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  `deleted` tinyint(1) unsigned zerofill NOT NULL DEFAULT '0' COMMENT '是否删除(0false1true)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='零部件类型';

CREATE TABLE `running_parameter` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `spare_parts_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '零部件id',
  `code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `unit` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `source_type` tinyint NOT NULL DEFAULT '1' COMMENT '数据来源(1录入2基础库3计算)',
  `create_param` tinyint DEFAULT NULL COMMENT '是否生成参数表(0否1是)',
  `bearing_param` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基础库参数',
  `expression` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '脚本',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='零部件运行参数';

CREATE TABLE `running_parameter_variable` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `running_parameter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '零部件id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `source_type` tinyint NOT NULL DEFAULT '1' COMMENT '来源(1录入2参数)',
  `value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '值(来源参数为运行参数id,否则为输入)',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='零部件运行参数变量';

ALTER TABLE iot_parameter_definition ADD type tinyint DEFAULT '1' COMMENT '参数类型' AFTER fixed;

CREATE TABLE `iot_param_definition_variable` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parameter_definition_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数定义id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `source_type` tinyint NOT NULL DEFAULT '1' COMMENT '来源(1录入2参数)',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参数定义变量';
