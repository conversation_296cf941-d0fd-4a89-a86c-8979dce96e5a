SET
FOREIGN_KEY_CHECKS=0;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `category_id`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `model_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '型号' AFTER `factory_name`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `life_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用寿命' AFTER `unit_name`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `life_time_unit` tinyint NULL DEFAULT NULL COMMENT '使用寿命单位1小时2日3月4年' AFTER `life_time`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `source_type` tinyint NULL DEFAULT 0 COMMENT '数据来源0默认手动添加' AFTER `life_time_unit`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `importance_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '重要性类型SABC' AFTER `source_type`;

ALTER TABLE `ehm`.`part_info`
    ADD COLUMN `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `importance_type`;

ALTER TABLE `ehm`.`part_info` MODIFY COLUMN `specification` varchar (64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规格' AFTER `model_info`;

ALTER TABLE `ehm`.`part_info` MODIFY COLUMN `unit` varchar (10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '计量单位' AFTER `specification`;

CREATE TABLE `ehm`.`part_inventory_order`
(
    `id`                          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `code`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单号',
    `stock_id`                    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '盘点仓库id',
    `stock_name`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_code`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `status`                      tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
    `reject_reason`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '驳回原因',
    `tenant_id`                   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户ID',
    `deleted`                     tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `remark`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `create_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`                 datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`                 datetime                                                     NOT NULL COMMENT '更新时间',
    `process_user`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `process_instance_id`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '流程实例ID',
    `activity_id`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '流程任务id',
    `name`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '盘点名称',
    `opt_type`                    tinyint NULL DEFAULT NULL COMMENT '盘点类型',
    `opt_user_id`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '盘点人id',
    `opt_user_name`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `start_time`                  datetime NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`                    datetime NULL DEFAULT NULL COMMENT '结束时间',
    `number_result`               tinyint NULL DEFAULT NULL COMMENT '数量状态',
    `number_result_value`         decimal(10, 4) NULL DEFAULT NULL COMMENT '数量结果',
    `number_result_diff`          decimal(10, 4) NULL DEFAULT NULL COMMENT '数量差值',
    `prime_result`                tinyint NULL DEFAULT NULL COMMENT '成本状态',
    `prime_result_value`          decimal(10, 4) NULL DEFAULT NULL COMMENT '成本结果',
    `prime_result_diff`           decimal(10, 4) NULL DEFAULT NULL COMMENT '成本差值',
    `service_type`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_status`                tinyint NULL DEFAULT NULL,
    `create_user_name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `history_number_result_value` decimal(10, 4) NULL DEFAULT NULL,
    `history_prime_result_value`  decimal(10, 4) NULL DEFAULT NULL,
    `inventory_status`            tinyint NULL DEFAULT 0,
    `stock_manager_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_manager_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `inventory_finished_date`     datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '盘点单' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_inventory_order_detail`
(
    `id`                          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `inventory_order_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调拨单id',
    `inventory_order_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '盘点单名',
    `inventory_order_code`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调拨单code',
    `part_id`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件id',
    `part_code`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件编码',
    `part_name`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_brand_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_specification`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_model`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_type_id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_type_name`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_price`                  decimal(10, 2) NULL DEFAULT NULL,
    `part_manage_type`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `sn_code`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `num_stock`                   decimal(10, 4) NULL DEFAULT NULL COMMENT '库存数量',
    `num_actual`                  decimal(10, 4) UNSIGNED NULL DEFAULT NULL COMMENT '实际数量',
    `history_prime_result_value`  decimal(10, 4) NULL DEFAULT NULL,
    `tenant_id`                   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户ID',
    `deleted`                     tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `remark`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `create_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`                 datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`                 datetime                                                     NOT NULL COMMENT '更新时间',
    `stock_main_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库id',
    `stock_main_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库名',
    `stock_item_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设id',
    `stock_item_code`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '库存下设名',
    `number_result`               tinyint NULL DEFAULT NULL COMMENT '数量状态',
    `number_result_value`         decimal(10, 4) NULL DEFAULT NULL COMMENT '数量结果',
    `number_result_diff`          decimal(10, 4) NULL DEFAULT NULL COMMENT '数量差值',
    `prime_result`                tinyint NULL DEFAULT NULL COMMENT '成本状态',
    `prime_result_value`          decimal(10, 4) NULL DEFAULT NULL COMMENT '成本结果',
    `prime_result_diff`           decimal(10, 4) NULL DEFAULT NULL COMMENT '成本差值',
    `opt_type`                    tinyint NULL DEFAULT NULL,
    `service_type`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `part_stock_item_relation_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'part_stock_item_relation id',
    `stock_item_parent_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_parent_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_parent_code`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_ancestor_id`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_ancestor_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_ancestor_code`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `inventory_finished_date`     datetime NULL DEFAULT NULL COMMENT '盘点时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '盘点单明细' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_sn_info`
(
    `id`                          varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`                   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
    `deleted`                     tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `remark`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `create_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`                 datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`                 datetime                                                     NOT NULL COMMENT '更新时间',
    `sn_code`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编码',
    `stock_item_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下属id',
    `stock_level_type`            tinyint NULL DEFAULT NULL COMMENT '仓库下属层级',
    `stock_main_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库id',
    `part_stock_item_relation_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联part_stock_item_relation_id',
    `part_id`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件id',
    `part_type_id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型id',
    `part_name`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件名',
    `part_type_name`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '备件SN表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_stock_item_relation`
(
    `id`                       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
    `deleted`                  tinyint NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `create_by`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人标识',
    `create_time`              datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人标识',
    `update_time`              datetime NULL DEFAULT NULL COMMENT '更新时间',
    `stock_id`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库id',
    `stock_name`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联库存名',
    `stock_code`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联库存code',
    `stock_parent_id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_parent_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_parent_code`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_ancestor_id`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_ancestor_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_ancestor_code`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库下设id',
    `stock_item_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联库存下设名',
    `stock_item_code`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_level_type`    tinyint NULL DEFAULT NULL COMMENT '关联仓库下设层级类型',
    `num`                      decimal(10, 4) NULL DEFAULT NULL COMMENT '库存数量',
    `part_id`                  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件id',
    `part_name`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件名',
    `part_code`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件code',
    `part_type_id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型id',
    `part_type_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型名',
    `part_brand_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件品牌',
    `part_specification`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件规格',
    `part_model_info`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件型号',
    `part_price`               decimal(10, 4) NULL DEFAULT NULL COMMENT '备件价格',
    `remark`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `sn_code`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'sn码',
    `manage_type`              tinyint NULL DEFAULT NULL COMMENT '1SN管理2批量管理',
    `stock_item_parent_id`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库下设父id',
    `stock_item_parent_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库下设父name',
    `stock_item_parent_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_ancestor_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库下设祖id',
    `stock_item_ancestor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库下设祖name',
    `stock_item_ancestor_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '备件与仓库下设关联表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_stock_main_relation`
(
    `id`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
    `deleted`      tinyint NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `create_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人标识',
    `create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人标识',
    `update_time`  datetime NULL DEFAULT NULL COMMENT '更新时间',
    `stock_id`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库id',
    `part_id`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件id',
    `part_type_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型id',
    `min_stock`    decimal(10, 4) NULL DEFAULT NULL COMMENT '最小库存',
    `max_stock`    decimal(10, 4) NULL DEFAULT NULL COMMENT '最大库存',
    `total_stock`  decimal(10, 4) NULL DEFAULT NULL COMMENT '目前该备件库存',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `stock_status` tinyint NULL DEFAULT NULL COMMENT '库存状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '备件与仓库下设关联表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_transfer_order`
(
    `id`                        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `code`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单号',
    `opt_type`                  tinyint UNSIGNED NOT NULL COMMENT '操作类型',
    `from_stock_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调出仓库name',
    `from_stock_id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调出仓库id',
    `from_dept_id`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调出部门',
    `from_dept_name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调出部门名',
    `from_user_id`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '出库人id',
    `from_user_name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `to_stock_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调入仓库name',
    `to_stock_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调入仓库id',
    `to_dept_id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调入部门',
    `to_dept_name`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调入部门名',
    `to_user_id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '入库人id',
    `to_user_name`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `send_stock_user_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '送库人id',
    `send_stock_user_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `status`                    tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态',
    `reject_reason`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '驳回原因',
    `category`                  int UNSIGNED NOT NULL COMMENT '备件种类',
    `total_qty`                 decimal(10, 4) UNSIGNED NOT NULL COMMENT '备件总数量',
    `process_instance_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '流程实例ID',
    `process_user`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '审批人列表',
    `activity_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `tenant_id`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户ID',
    `deleted`                   tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `create_by`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`               datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`               datetime                                                     NOT NULL COMMENT '更新时间',
    `name`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调拨单名称',
    `service_type`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型(出库类型下设，入库类型下设)',
    `opt_user_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '入库人id',
    `opt_user_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `opt_dept_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '入库部门id',
    `opt_dept_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `transfer_type`             tinyint NULL DEFAULT NULL COMMENT '借还类别',
    `pre_back_time`             datetime NULL DEFAULT NULL COMMENT '预计归还时间',
    `real_back_time`            datetime NULL DEFAULT NULL COMMENT '实际归还时间',
    `lend_time`                 datetime NULL DEFAULT NULL COMMENT '借出时间',
    `lend_content`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '借用用途',
    `lend_transfer_order_id`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调拨关联调拨单id',
    `apply_content`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_status`              tinyint NULL DEFAULT NULL COMMENT '仓库状态',
    `create_user_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `transfer_finished_date`    datetime NULL DEFAULT NULL COMMENT '单结束时间',
    `out_stock_time`            datetime NULL DEFAULT NULL COMMENT '领用时间',
    `in_stock_time`             datetime NULL DEFAULT NULL COMMENT '入库时间',
    `stock_manager_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_manager_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `source_stock_manager_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `source_stock_manager_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '调拨单' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`part_transfer_order_detail`
(
    `id`                              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `transfer_order_id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调拨单id',
    `transfer_order_code`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调拨单code',
    `transfer_order_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '调拨单名',
    `part_id`                         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备件id',
    `part_code`                       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备件编码',
    `part_name`                       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备件名称',
    `part_brand_name`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件品牌名',
    `part_specification`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件规格',
    `part_model`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规格型号',
    `part_type_id`                    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型id',
    `part_type_name`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备件类型名',
    `part_price`                      decimal(10, 2) NULL DEFAULT NULL COMMENT '备件单价',
    `part_manage_type`                tinyint NULL DEFAULT NULL COMMENT '备件管理方式',
    `sn_code`                         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SN号',
    `num_pre`                         decimal(10, 4) UNSIGNED NULL DEFAULT NULL COMMENT '数量',
    `num_actual`                      decimal(10, 4) NULL DEFAULT NULL COMMENT '实际操作数量',
    `num_actual_in`                   decimal(10, 4) NULL DEFAULT NULL,
    `num_stock_real`                  decimal(10, 4) NULL DEFAULT NULL COMMENT '此次操作后实时库存',
    `tenant_id`                       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户ID',
    `deleted`                         tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `remark`                          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `create_by`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`                     datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`                     datetime                                                     NOT NULL COMMENT '更新时间',
    `stock_item_id`                   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设id',
    `stock_item_name`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设名字',
    `stock_item_code`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_parent_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设父id',
    `stock_item_parent_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设父name',
    `stock_item_parent_code`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_item_ancestor_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设祖id',
    `stock_item_ancestor_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库下设祖name',
    `stock_item_ancestor_code`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `stock_main_id`                   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库id',
    `stock_main_name`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库名',
    `opt_type`                        tinyint NULL DEFAULT NULL COMMENT '操作类型',
    `service_type`                    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型',
    `source_stock_main_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库id',
    `source_stock_main_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库name',
    `source_stock_item_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设id',
    `source_stock_item_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设name',
    `source_stock_item_code`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `source_stock_item_parent_id`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设父id',
    `source_stock_item_parent_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设父name',
    `source_stock_item_parent_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `source_stock_item_ancestor_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设祖id',
    `source_stock_item_ancestor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原仓库下设祖name',
    `source_stock_item_ancestor_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '调拨单明细' ROW_FORMAT = Dynamic;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `dept_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联组织id' AFTER `update_time`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `dept_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联组织code' AFTER `dept_id`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `dept_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `dept_code`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `stock_main_type` tinyint NULL DEFAULT NULL COMMENT '仓库分类(大类)，如备件仓,当前默认为备件' AFTER `dept_name`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `stock_item_type` tinyint NULL DEFAULT NULL COMMENT '仓库类型(小类)，如存储仓' AFTER `stock_main_type`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `stock_level_type` tinyint NULL DEFAULT NULL COMMENT '仓库层级类型,如工厂/装置/车间层级' AFTER `stock_item_type`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `manager_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库管理员userId' AFTER `stock_level_type`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `manager_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓管名' AFTER `manager_user_id`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `charger_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人userId' AFTER `manager_user_name`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `charger_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人name' AFTER `charger_user_id`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `location_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '仓库位置描述' AFTER `charger_user_name`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父仓id' AFTER `location_remark`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `parent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父仓名' AFTER `parent_id`;

ALTER TABLE `ehm`.`stock_info`
    ADD COLUMN `create_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `parent_name`;

ALTER TABLE `ehm`.`stock_info` MODIFY COLUMN `location` varchar (255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置' AFTER `name`;

ALTER TABLE `ehm`.`stock_info` MODIFY COLUMN `status` tinyint UNSIGNED NOT NULL COMMENT '状态(0停用1启用)' AFTER `location`;

CREATE TABLE `ehm`.`stock_item_info`
(
    `id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `code`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编号',
    `name`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
    `tenant_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `deleted`     tinyint NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人标识',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人标识',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `stock_id`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联仓库id',
    `level_type`  tinyint NULL DEFAULT NULL COMMENT '层级类型',
    `parent_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '仓库下设信息表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`stock_item_relation`
(
    `ancestor`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `descendant` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`ancestor`, `descendant`) USING BTREE,
    INDEX        `idx1`(`ancestor`) USING BTREE,
    INDEX        `idx2`(`descendant`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '仓库下设关系表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`stock_main_relation`
(
    `ancestor`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `descendant` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`ancestor`, `descendant`) USING BTREE,
    INDEX        `idx1`(`ancestor`) USING BTREE,
    INDEX        `idx2`(`descendant`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '仓库关系表' ROW_FORMAT = Dynamic;

CREATE TABLE `ehm`.`stock_service_config`
(
    `id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `tenant_id`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `deleted`           tinyint                                                      NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
    `create_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
    `create_time`       datetime                                                     NOT NULL COMMENT '创建时间',
    `update_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
    `update_time`       datetime                                                     NOT NULL COMMENT '更新时间',
    `scene_code`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务领域',
    `service_classify`  tinyint NULL DEFAULT NULL COMMENT '业务分类',
    `service_type_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型名',
    `order_status`      tinyint NULL DEFAULT NULL COMMENT '是否关联工单',
    `status`            tinyint NULL DEFAULT NULL COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '仓库业务配置' ROW_FORMAT = Dynamic;

INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('025ce1ef4fe687249273e0e2324c8e83', 'geek', NULL, 0, 'hey', '2022-04-18 15:23:03', 'hey', '2022-04-18 15:23:03', 'stock', 2, '外部调拨', NULL, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('15b91b8a3bfa5ba9f4a7c437d59d0d95', 'geek', NULL, 0, 'hey', '2022-04-18 15:24:25', 'hey', '2022-04-22 10:33:42', 'stock', 1, '出库-禁用', 0, 0);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('15f70cd1791a6022be2e2527e6cbe310', 'geek', NULL, 0, 'hey', '2022-04-18 15:22:50', 'hey', '2022-04-18 15:22:50', 'stock', 2, '内部调拨', NULL, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('2c0251cc1e1d480390417c485b5ab560', 'geek', NULL, 0, 'hey', '2022-04-18 15:22:07', 'hey', '2022-04-18 15:22:07', 'stock', 1, '领用出库', 0, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('406c5de79c6e49d36b6c037b80c14869', 'geek', NULL, 0, 'hey', '2022-04-18 15:22:37', 'hey', '2022-04-18 15:22:37', 'stock', 1, '废料出库', 0, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('43c77378839a6d3428417a5651525f6f', 'geek', NULL, 0, 'hey', '2022-04-18 15:21:36', 'hey', '2022-04-18 15:21:36', 'stock', 0, '入库-禁用', 0, 0);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('5c574acf0a95883a3074aa946723172c', 'geek', NULL, 0, 'hey', '2022-04-18 15:23:56', 'hey', '2022-04-18 15:23:56', 'stock', 3, '盘点-禁用', NULL, 0);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('6591290a0c305b773f10f6b41f30a68f', 'geek', NULL, 0, 'hey', '2022-04-18 15:08:57', 'hey', '2022-04-18 15:08:57', 'stock', 0, '退还入库', 1, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('7b3a27ea5ded09fb4c20d7735c720a3c', 'geek', NULL, 0, 'hey', '2022-04-18 15:23:34', 'hey', '2022-04-18 15:23:34', 'stock', 3, '定期盘点', NULL, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('7c055cb21e70464f2571432d335a8185', 'geek', NULL, 0, 'hey', '2022-04-18 15:08:37', 'hey', '2022-04-18 15:08:37', 'stock', 0, '采购入库', 0, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('831ca51ec0c22aa40f345b8d6b12087d', 'geek', NULL, 0, 'hey', '2022-04-18 15:24:08', 'hey', '2022-04-22 10:33:39', 'stock', 2, '调拨-禁用', NULL, 0);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('90a33d43ca516407ffb916ac64d1ec07', 'geek', NULL, 0, 'hey', '2022-04-18 15:08:27', 'hey', '2022-04-18 15:08:27', 'stock', 0, '领用入库', 0, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('9598f445af571bee6e416b62c5ca5689', 'geek', NULL, 0, 'hey', '2022-04-18 15:23:22', 'hey', '2022-04-18 15:23:22', 'stock', 3, '专项盘点', NULL, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('ecf4f93a91a6b898c6fc69b6ba16c00e', 'geek', NULL, 0, 'admin', '2022-04-28 15:13:17', 'admin', '2022-04-28 15:13:17', 'stock', 0, '采购入库', 0, 1);
INSERT INTO `ehm`.`stock_service_config`(`id`, `tenant_id`, `remark`, `deleted`, `create_by`, `create_time`, `update_by`, `update_time`, `scene_code`, `service_classify`, `service_type_name`, `order_status`, `status`) VALUES ('f91b36260688c6c222ba7e4a10836452', 'geek', NULL, 0, 'hey', '2022-04-18 15:22:18', 'hey', '2022-04-18 15:22:18', 'stock', 1, '维修出库', 1, 1);


DROP TABLE `ehm`.`equipment_maint_team_rel`;

DROP TABLE `ehm`.`equipment_maintainer_rel`;

DROP TABLE `ehm`.`inventory_order`;

DROP TABLE `ehm`.`inventory_order_detail`;

DROP TABLE `ehm`.`task_group`;

DROP TABLE `ehm`.`task_item`;

DROP TABLE `ehm`.`transfer_order`;

DROP TABLE `ehm`.`transfer_order_detail`;

ALTER TABLE `ehm`.`maint_task` ADD COLUMN `task_deadline_date` datetime NULL DEFAULT NULL COMMENT '工单截止时间' AFTER `rec_task_deadline_date`;

ALTER TABLE `ehm`.`part_info` MODIFY COLUMN `min_stock` decimal(10, 3) UNSIGNED NULL DEFAULT 0.000 COMMENT '最小库存' AFTER `category_name`;

ALTER TABLE `ehm`.`part_info` MODIFY COLUMN `max_stock` decimal(10, 3) UNSIGNED NULL DEFAULT 0.000 COMMENT '最大库存' AFTER `min_stock`;

ALTER TABLE `ehm`.`part_info` MODIFY COLUMN `current_stock` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '当前库存' AFTER `doc_ids`;

INSERT INTO `ehm`.`dictionary`(`id`, `name`, `code`, `type`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('03f14bbdb438408a98a64a0a372c9c13', '备件_备件使用寿命单位', 'part_part_lifeunit', 1, '', 'geek', 'hey', '2022-04-14 10:12:02', 'hey', '2022-04-14 10:12:20');
INSERT INTO `ehm`.`dictionary`(`id`, `name`, `code`, `type`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('c1a2c6de0888a1c26daef02331c101cb', '备件_备件重要性', 'part_part_importance', 1, '', 'geek', 'hey', '2022-04-14 10:11:46', 'hey', '2022-04-14 10:11:46');
INSERT INTO `ehm`.`dictionary`(`id`, `name`, `code`, `type`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('67a29572992b6dc2f06e4f2ad3f39cf9', '备件_备件单位', 'part_part_unit', 1, '', 'geek', 'hey', '2022-04-14 10:11:35', 'hey', '2022-04-14 10:11:35');
INSERT INTO `ehm`.`dictionary`(`id`, `name`, `code`, `type`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('c07bfd1890d60284c45644efe6fd501c', '备件_备件来源', 'part_source_type', 1, '', 'geek', 'hey', '2022-04-14 10:11:23', 'hey', '2022-04-14 10:11:23');
INSERT INTO `ehm`.`dictionary`(`id`, `name`, `code`, `type`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('db69cb1bb2fd55b964d7165c75ac4761', '备件_仓库类型', 'stock_item_type', 1, '', 'geek', 'hey', '2022-04-14 10:10:21', 'hey', '2022-04-14 10:10:21');


INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('0f494fba3b3e40712f501dd4990a6aa5', '03f14bbdb438408a98a64a0a372c9c13', '年', '3', '', NULL, 3, 1, '', 'geek', 'hey', '2022-04-14 11:50:57', 'hey', '2022-04-14 11:50:57');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('427eaa4555b42c7ea0e3bdc097b97c7a', '03f14bbdb438408a98a64a0a372c9c13', '月', '2', '', NULL, 2, 1, '', 'geek', 'hey', '2022-04-14 11:50:15', 'hey', '2022-04-14 11:50:15');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('81181d2accf8fbf374b38091b0257ed8', '67a29572992b6dc2f06e4f2ad3f39cf9', '米', '2', '', NULL, 2, 1, '', 'geek', 'hey', '2022-04-14 10:26:03', 'hey', '2022-04-14 10:26:03');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('d1e25ea714e5452a90bba614dae837a1', '67a29572992b6dc2f06e4f2ad3f39cf9', '台', '1', '', NULL, 1, 1, '', 'geek', 'hey', '2022-04-14 10:25:46', 'hey', '2022-04-14 10:25:46');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('6c9d47333dac1750f5625459e5444737', '03f14bbdb438408a98a64a0a372c9c13', '日', '1', '', NULL, 1, 1, '', 'geek', 'hey', '2022-04-14 10:25:11', 'hey', '2022-04-14 11:50:06');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('f98f33337f1d9d7fdf41868412f8820f', 'db69cb1bb2fd55b964d7165c75ac4761', '公共仓', '1', '', NULL, 1, 1, '', 'geek', 'hey', '2022-04-14 10:21:33', 'hey', '2022-04-14 10:21:33');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('54c9d76e4b50e99454dd3f64b42e0e8c', '03f14bbdb438408a98a64a0a372c9c13', '小时', '0', '', NULL, 0, 1, '', 'geek', 'hey', '2022-04-14 10:13:55', 'hey', '2022-04-14 10:25:29');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('27e632d9aa779aabce95d0350c352f90', 'c1a2c6de0888a1c26daef02331c101cb', 'C', '3', '', NULL, 3, 0, '', 'geek', 'hey', '2022-04-14 10:13:46', 'hey', '2022-04-14 10:13:46');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('f9a98d705f88bac537e4f691d8236f58', 'c1a2c6de0888a1c26daef02331c101cb', 'B', '2', '', NULL, 2, 0, '', 'geek', 'hey', '2022-04-14 10:13:41', 'hey', '2022-04-14 10:13:41');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('8a4e55f524b1aa4b10ef75d1b96aa80c', 'c1a2c6de0888a1c26daef02331c101cb', 'A', '1', '', NULL, 1, 0, '', 'geek', 'hey', '2022-04-14 10:13:36', 'hey', '2022-04-14 10:13:36');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('fb0efe9b5008775433da345793a516f8', 'c1a2c6de0888a1c26daef02331c101cb', 'S', '0', '', NULL, 0, 0, '', 'geek', 'hey', '2022-04-14 10:13:29', 'hey', '2022-04-14 10:13:29');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('7d3d6634a1ba1b1882955b3c20798be6', '67a29572992b6dc2f06e4f2ad3f39cf9', '个', '0', '', NULL, 0, 0, '', 'geek', 'hey', '2022-04-14 10:13:15', 'hey', '2022-04-14 10:13:15');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('c3d3ac9c80c9023fb2b95bfc552882f3', 'c07bfd1890d60284c45644efe6fd501c', '手动添加', '0', '', NULL, 0, 0, '', 'geek', 'hey', '2022-04-14 10:12:39', 'hey', '2022-04-14 10:12:39');
INSERT INTO `ehm`.`dictionary_item`(`id`, `dictionary_id`, `name`, `value`, `label`, `color`, `sort`, `enable`, `remark`, `tenant_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('493708721545d4572cdf199527b70a23', 'db69cb1bb2fd55b964d7165c75ac4761', '存储仓', '0', '', NULL, 0, 0, '', 'geek', 'hey', '2022-04-14 10:10:31', 'hey', '2022-04-14 10:10:31');

SET
FOREIGN_KEY_CHECKS=1;