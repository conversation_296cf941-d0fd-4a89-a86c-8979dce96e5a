2022/4/6 部件测点

CREATE TABLE `equipment_category_structure` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备类型id',
  `spare_parts_category_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '零部件id',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
  `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父节点id',
  `type` tinyint DEFAULT NULL COMMENT '类型(1部件2零件)',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备类型结构';

ALTER TABLE iot_category_point DROP COLUMN  structure_id;
ALTER TABLE iot_category_point DROP COLUMN  parameter_ids;

CREATE TABLE `iot_category_point_param` (
`id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
`name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '测点参数名称',
`category_point_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '测点id',
`parameter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数id',
`sort` int DEFAULT NULL COMMENT '排序',
`create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
`create_time` datetime DEFAULT NULL,
`update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
`update_time` datetime DEFAULT NULL,
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
`tenant_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备类型测点参数表';


CREATE TABLE `equipment_structure` (
`id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
`equipment_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备id',
`spare_parts_category_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '零部件类型id',
`relation_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联id(轴承id、齿轮id)',
`name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
`parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父节点id',
`type` tinyint NOT NULL COMMENT '类型(1部件2零件)',
`base_library` tinyint DEFAULT NULL COMMENT '基础库类型',
`monitored` tinyint NOT NULL DEFAULT '1' COMMENT '是否监测',
`source_type` tinyint NOT NULL DEFAULT '1' COMMENT '添加来源(1手动2设备类型)',
`brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '品牌',
`model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '型号',
`sort` int NOT NULL DEFAULT '0' COMMENT '排序',
`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户id',
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
`create_time` datetime NOT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
 `update_time` datetime NOT NULL COMMENT '更新时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备结构';

CREATE TABLE `equipment_structure_parameter` (
`id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
`structure_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备零部件id',
`running_parameter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '零部件id',
`code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '编码',
`name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
`unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
`value` double(20,5) DEFAULT NULL COMMENT '对应值',
`source_type` tinyint NOT NULL DEFAULT '1' COMMENT '数据来源(1录入2基础库3计算)',
  `create_param` tinyint DEFAULT NULL COMMENT '是否生成参数表(0否1是)',
  `bearing_param` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基础库参数',
  `expression` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '脚本',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='零部件运行参数';

CREATE TABLE `equipment_structure_variable` (
`id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
`structure_parameter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '零部件参数id',
`name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
`source_type` tinyint NOT NULL DEFAULT '1' COMMENT '来源(1录入2参数)',
`value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '值(来源参数为运行参数id,否则为输入)',
`sort` int DEFAULT NULL COMMENT '排序',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
`create_time` datetime NOT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
`update_time` datetime NOT NULL COMMENT '更新时间',
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备部件运行参数变量';

ALTER TABLE equipment_info ADD max_rate int(11) DEFAULT NULL COMMENT '转速最大范围' AFTER maintainer_ids;
ALTER TABLE equipment_info ADD min_rate int(11) DEFAULT NULL COMMENT '转速最小范围' AFTER max_rate;

ALTER TABLE iot_equipment_point ADD type tinyint(3) DEFAULT 1 COMMENT '类型(1设备2部件)' AFTER structure_id;
ALTER TABLE iot_measuring_parameter DROP COLUMN  min_speed;
ALTER TABLE iot_measuring_parameter DROP COLUMN  max_speed;
ALTER TABLE iot_measuring_parameter ADD name varchar(100) DEFAULT NULL COMMENT '参数名称' AFTER measuring_id;
ALTER TABLE iot_measuring_parameter ADD monitored tinyint NOT NULL DEFAULT '1' COMMENT '是否监测' AFTER parameter_id;
ALTER TABLE iot_measuring_parameter ADD sort int(11) DEFAULT 20 COMMENT '排序' AFTER rule_id;
ALTER TABLE iot_measuring_parameter ADD collection_time datetime DEFAULT NULL COMMENT '采集时间' AFTER latest_value;
ALTER TABLE iot_measuring_parameter ADD expression varchar(100) DEFAULT NULL COMMENT '虚拟参数表达式' AFTER collection_time;
ALTER TABLE iot_measuring_parameter ADD is_virtual tinyint(1) DEFAULT '0' COMMENT '是否是虚拟参数' AFTER expression;
ALTER TABLE iot_measuring_parameter DROP COLUMN  node_name;
ALTER TABLE iot_measuring_parameter ADD alarm_threshold_type tinyint DEFAULT NULL COMMENT '告警阈值方式(1人工2自动)' AFTER rule_id;
ALTER TABLE iot_measuring_parameter ADD enable_alarm tinyint DEFAULT '0' COMMENT '是否启动告警' AFTER alarm_threshold_type;
ALTER TABLE iot_measuring_parameter MODIFY column latest_value double(20,5) DEFAULT NULL COMMENT '最新值';
ALTER TABLE iot_measuring_parameter CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE equipment_spare_parts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

CREATE TABLE `iot_measuring_parameter_variable` (
`id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
`measuring_parameter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数定义id',
`name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
`source_type` tinyint NOT NULL DEFAULT '1' COMMENT '来源(1录入2参数)',
`value` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '值',
`sort` int DEFAULT NULL COMMENT '排序',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人标识',
`create_time` datetime NOT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人标识',
`update_time` datetime NOT NULL COMMENT '更新时间',
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='测点参数变量';


//告警
ALTER TABLE iot_rule_template ADD init tinyint DEFAULT '0' COMMENT '是否初始化' AFTER expression;
ALTER TABLE iot_rule_result ADD alarm_mode varchar(255) DEFAULT NULL COMMENT '通知方式' AFTER rule_id;

ALTER TABLE iot_info_param_result ADD alarm_mode varchar(255) DEFAULT NULL COMMENT '通知方式' AFTER warn_action;

ALTER TABLE iot_mars_detail ADD cc_flag bit(1) DEFAULT b'0' COMMENT '倒谱是否生成文件成功' AFTER flac_flag;