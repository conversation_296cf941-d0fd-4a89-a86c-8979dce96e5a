需修改table_schema对应库名

DELIMITER $$
DROP PROCEDURE
IF EXISTS pro_AddColumn;
CREATE PROCEDURE pro_AddColumn () BEGIN
	IF NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_type' ) THEN
			ALTER TABLE manual_repair ADD fault_type VARCHAR ( 32 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '故障类别' AFTER equipment_id;
	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'major' ) THEN
			ALTER TABLE manual_repair ADD major VARCHAR ( 32 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '专业' AFTER fault_type;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'urgency' ) THEN
			ALTER TABLE manual_repair ADD urgency VARCHAR ( 32 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急程度' AFTER major;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'influence' ) THEN
			ALTER TABLE manual_repair ADD influence VARCHAR ( 32 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '影响程度' AFTER urgency;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_phenomenon_ids' ) THEN
			ALTER TABLE manual_repair ADD fault_phenomenon_ids VARCHAR ( 500 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '故障现象ids' AFTER influence;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_phenomenon_remark' ) THEN
			ALTER TABLE manual_repair ADD fault_phenomenon_remark text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '故障现象扩展' AFTER fault_phenomenon_ids;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_reason_ids' ) THEN
			ALTER TABLE manual_repair ADD fault_reason_ids VARCHAR ( 500 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '故障原因ids' AFTER fault_phenomenon_remark;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_reason_remark' ) THEN
			ALTER TABLE manual_repair ADD fault_reason_remark text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '故障原因扩展' AFTER fault_reason_ids;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_measures_ids' ) THEN
			ALTER TABLE manual_repair ADD fault_measures_ids VARCHAR ( 500 ) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理措施ids' AFTER fault_reason_remark;

	END IF;
	IF
		NOT EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'fault_measures_remark' ) THEN
			ALTER TABLE manual_repair ADD fault_measures_remark text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理措施扩展' AFTER fault_measures_ids;

	END IF;
	IF
		EXISTS ( SELECT 1 FROM information_schema.COLUMNS WHERE table_schema = 'ehm_1.3' AND table_name = 'manual_repair' AND COLUMN_NAME = 'priority' ) THEN
			ALTER TABLE manual_repair DROP priority;
		END IF;

END $$
DELIMITER ;
CALL pro_AddColumn;
DROP PROCEDURE pro_AddColumn;


更新子设备类型
update equipment_info set type = 2 where type = 3


2021/11/19 其他配置修改为是否校验设备、是否特种设备
INSERT INTO `ehm`.`form_configuration`(`id`, `field_name`, `show_field_name`, `name`, `displayed`, `displayed_fixed`, `config_type`, `label_col`, `wraper_col`, `required`, `custom_zh`, `custom_us`, `group_key`, `config_option`, `form_name`, `config_json`, `sort`, `tenant_id`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`) VALUES (replace(uuid(),'-',''), 'specialInfo', 'specialInfo', '是否特种设备', 1, 0, 'Switch', 8, 16, 0, null, null, 'baseInfo', null, 'equipment',null, 9, 'geek', NULL, 'admin', '2021-10-13 11:17:07', 'hey', '2021-11-09 15:33:38', 0);

INSERT INTO `ehm`.`form_configuration`(`id`, `field_name`, `show_field_name`, `name`, `displayed`, `displayed_fixed`, `config_type`, `label_col`, `wraper_col`, `required`, `custom_zh`, `custom_us`, `group_key`, `config_option`, `form_name`, `config_json`, `sort`, `tenant_id`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`) VALUES (replace(uuid(),'-',''), 'check', 'check', '是否需要校验', 1, 0, 'Switch', 8, 16, 0, null, null, 'baseInfo', null, 'equipment',null, 10, 'geek', NULL, 'admin', '2021-10-13 11:17:07', 'hey', '2021-11-09 15:33:38', 0);

ALTER TABLE equipment_info ADD special_info tinyint(3) COMMENT '是否特种设备' DEFAULT 0 AFTER brand;
ALTER TABLE equipment_info ADD checked tinyint(3) COMMENT '是否需要校验' DEFAULT 0 AFTER specialInfo;
ALTER TABLE equipment_info DROP other_config