-- -----------------------------------------设备中心begin-------------------------------------------
CREATE TABLE `equipment` (
                             `id` bigint unsigned NOT NULL,
                             `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '编码',
                             `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
                             `equipment_location_id` bigint unsigned NOT NULL COMMENT '设备位置id',
                             `equipment_location_name` varchar(100) NOT NULL COMMENT '设备位置名称',
                             `equipment_type_id` bigint unsigned NOT NULL COMMENT '设备类型id',
                             `equipment_type_name` varchar(100) NOT NULL COMMENT '设备类型名称',
                             `specification` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '型号规格',
                             `importance` tinyint unsigned NOT NULL COMMENT '重要度(1 A:关键设备 2 B:重要设备 3 C:一般设备 D:次要设备)',
                             `supplier_id` bigint unsigned NULL DEFAULT NULL COMMENT '供应商id',
                             `manufacturer_id` bigint unsigned NULL DEFAULT NULL COMMENT '制造商id',
                             `status` tinyint unsigned NOT NULL COMMENT '资产状态(0在用1闲置2停用3报废4变卖)',
                             `principal` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备负责人',
                             `production_time` datetime NULL DEFAULT NULL COMMENT '出厂日期',
                             `install_date` datetime NULL DEFAULT NULL COMMENT '安装日期',
                             `warranty_time` datetime NULL DEFAULT NULL COMMENT '质保日期',
                             `or_code_attar_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '二维码附件id列表',
                             `doc_code_attar_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文档附件id列表',
                             `property` json  DEFAULT NULL COMMENT '设备更多属性',
                             `location_layer_code` varchar(64)  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备位置层级编码(每层3位数字)',
                             `type_layer_code` varchar(64)  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备类型层级编码(每层3位数字)',
                             `tenant_id` bigint unsigned NULL COMMENT '租户id',
                             `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                             `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                             `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                             `update_time` datetime NOT NULL COMMENT '更新时间',
                             PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备表';

CREATE TABLE `equipment_maint_team_rel` (
                                            `id` bigint unsigned NOT NULL,
                                            `equipment_id` bigint unsigned NOT NULL COMMENT '设备ID',
                                            `team_id` bigint unsigned NOT NULL COMMENT '维护班组ID',
                                            `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                            `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                            `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                            `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                            `update_time` datetime NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备维护班组关联表';

CREATE TABLE `equipment_maintainer_rel` (
                                            `id` bigint unsigned NOT NULL,
                                            `equipment_id` bigint unsigned NOT NULL COMMENT '设备ID',
                                            `maintainer_id` bigint unsigned NOT NULL COMMENT '维护人员ID',
                                            `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                            `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                            `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                            `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                            `update_time` datetime NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备维护人员关联表';

CREATE TABLE `equipment_location` (
                                      `id` bigint unsigned NOT NULL,
                                      `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备位置名称',
                                      `parent_id` bigint unsigned NOT NULL COMMENT '上级节点',
                                      `localtion_type` tinyint unsigned NOT NULL COMMENT '位置类型(0工厂1车间2线体3其他)',
                                      `province` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省编码',
                                      `city` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市编码',
                                      `area` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区编码',
                                      `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
                                      `longitude` decimal(20,17) NULL COMMENT '经度',
                                      `latitude` decimal(20,17) NULL COMMENT '纬度',
                                      `layer_code` varchar(64)  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '层级编码(每层3位数字)',
                                      `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                      `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                      `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                      `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                      `update_time` datetime NOT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备位置';

CREATE TABLE `equipment_data_permission` (
                                             `id` bigint unsigned NOT NULL,
                                             `equipment_location_id` bigint unsigned NOT NULL COMMENT '设备位置ID',
                                             `org_id` bigint unsigned NOT NULL COMMENT '组织机构ID',
                                             `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                             `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                             `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                             `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                             `update_time` datetime NOT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备数据权限表';

CREATE TABLE `equipment_type` (
                                  `id` bigint unsigned NOT NULL,
                                  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型编码',
                                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型名称',
                                  `parent_id` bigint unsigned NOT NULL COMMENT '上级节点',
                                  `pic_attr_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外观图片附件id列表',
                                  `doc_attr_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文档附件id列表',
                                  `layer_code` varchar(64)  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '层级编码(每层3位数字)',
                                  `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                  `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                  `update_time` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备类型';

CREATE TABLE `equipment_type_prop` (
                                       `id` bigint unsigned NOT NULL,
                                       `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '属性名称',
                                       `prop_type` tinyint unsigned NOT NULL COMMENT '属性类型(0文本1数值2选项)',
                                       `define` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '属性定义',
                                       `default_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '默认值',
                                       `equipment_type_id` bigint unsigned NOT NULL COMMENT '设备类型id',
                                       `sort` int unsigned NOT NULL COMMENT '排序',
                                       `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                       `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                       `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                       `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                       `update_time` datetime NOT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备类型扩展属性';

CREATE TABLE `equipment_type_part_rel` (
                                           `id` bigint unsigned NOT NULL,
                                           `equipment_type_id` bigint unsigned NOT NULL COMMENT '设备类型id',
                                           `spare_part_id` bigint unsigned NOT NULL COMMENT '备件id',
                                           `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                           `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                           `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                           `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                           `update_time` datetime NOT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备类型备件关联表';

CREATE TABLE `equipment_part_rel` (
                                      `id` bigint unsigned NOT NULL,
                                      `equipment_id` bigint unsigned NOT NULL COMMENT '设备id',
                                      `spare_part_id` bigint unsigned NOT NULL COMMENT '备件id',
                                      `tenant_id` bigint unsigned NULL COMMENT '租户id',
                                      `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                                      `deleted` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '是否删除(0false1true)',
                                      `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人标识',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人标识',
                                      `update_time` datetime NOT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT = '设备备件关联表';
-- ---------------------------------------设备中心end---------------------------------------------