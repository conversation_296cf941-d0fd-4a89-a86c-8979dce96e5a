<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentInfoSpecialMapper">
    <select id="getPageList" resultType="cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto">
        SELECT special.id, special.equipment_id equipmentId, special.structure_id structureId,
        special.certificate_no certificateNo, special.registration_code registrationCode,special.inspection_date inspectionDate,
        special.next_inspection_date nextInspectionDate,special.report_no reportNo,
        special.inspection_org inspectionOrg, special.issuing_authority issuingAuthority,special.use_org useOrg,special.duty_user_id,special.duty_user_name,special.deadline_days
        FROM equipment_info_special special
        LEFT JOIN equipment_info info ON special.equipment_id = info.id
        LEFT JOIN equipment_structure structure ON special.structure_id = structure.id
        <where>
            <if test="param.type != null">
                <choose>
                    <when test="param.type == 1 and null != param.categoryId and '' != param.categoryId">
                        AND info.category_id = #{param.categoryId}
                        AND special.structure_id IS NULL
                    </when>
                    <when test="param.type == 2 and param.partIds != null and param.partIds.size() > 0">
                        AND structure.part_id IN
                        <foreach collection="param.partIds" open="(" close=")" separator="," item="partId" index="index">
                            #{partId}
                        </foreach>
                    </when>
                </choose>
            </if>
            <if test="null != param.certificateNo and '' != param.certificateNo">
                AND special.certificate_no LIKE CONCAT("%",#{param.certificateNo}, "%")
            </if>
            <if test="null != param.registrationCode and '' != param.registrationCode">
                AND special.registration_code LIKE CONCAT("%",#{param.registrationCode}, "%")
            </if>
            <if test="null != param.beginInspectionDate and null != param.endInspectionDate">
                AND special.inspection_date BETWEEN #{param.beginInspectionDate} AND #{param.endInspectionDate}
            </if>
            <if test="null != param.beginNextDate and null != param.endNextDate">
                AND special.next_inspection_date BETWEEN #{param.beginNextDate} AND #{param.endNextDate}
            </if>
            <if test="null != param.reportNo and '' != param.reportNo">
                AND special.report_no LIKE CONCAT("%",#{param.reportNo}, "%")
            </if>
            <if test="null != param.inspectionOrg and '' != param.inspectionOrg">
                AND special.inspection_org LIKE CONCAT("%",#{param.inspectionOrg}, "%")
            </if>
            <if test="null != param.issuingAuthority and '' != param.issuingAuthority">
                AND special.issuing_authority LIKE CONCAT("%",#{param.issuingAuthority}, "%")
            </if>
            <if test="null != param.useOrg and '' != param.useOrg">
                AND special.use_org LIKE CONCAT("%",#{param.useOrg}, "%")
            </if>
            <if test="param.isOverTime">
                AND (
                    (DATE_FORMAT(NOW(),'%Y-%m-%d') &gt; DATE_SUB( special.next_inspection_date, INTERVAL special.deadline_days DAY ))
                        or
                    (DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_FORMAT(special.next_inspection_date,'%Y-%m-%d'))
                    )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND special.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="getExportSpecialEq" resultType="cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto">
        SELECT special.id, special.equipment_id equipmentId, special.structure_id structureId,
        special.certificate_no certificateNo, special.registration_code registrationCode,special.inspection_date inspectionDate,
        special.next_inspection_date nextInspectionDate,special.report_no reportNo,
        special.inspection_org inspectionOrg, special.issuing_authority issuingAuthority,special.use_org useOrg,special.duty_user_id,special.duty_user_name,special.deadline_days
        FROM equipment_info_special special
        LEFT JOIN equipment_info info ON special.equipment_id = info.id
        LEFT JOIN equipment_structure structure ON special.structure_id = structure.id
        <where>
            1=1
            <if test="null != param.categoryId and '' != param.categoryId">
                AND info.category_id = #{param.categoryId}
                AND special.structure_id IS NULL
            </if>
            <if test="null != param.beginNextDate and null != param.endNextDate">
                AND DATE_FORMAT(special.next_inspection_date, '%Y-%m-%d')  BETWEEN DATE_FORMAT(#{param.beginNextDate}, '%Y-%m-%d')
                AND DATE_FORMAT(#{param.endNextDate}, '%Y-%m-%d')
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                AND info.parent_id IN
                <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                    #{parentId}
                </foreach>
            </if>
        </where>
        ORDER BY special.update_time DESC
    </select>
    <select id="getStructureList" resultType="cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto">
        SELECT special.id, special.equipment_id equipmentId, special.structure_id structureId,structure.part_id structurePartId,
        structure.location structureLocation, structureParent.part_id structureParentPartId,
        special.certificate_no certificateNo, special.registration_code registrationCode,special.inspection_date inspectionDate,
        special.next_inspection_date nextInspectionDate,special.report_no reportNo,structure.specification,
        special.inspection_org inspectionOrg, special.issuing_authority issuingAuthority,special.use_org useOrg
        FROM equipment_info_special special
        LEFT JOIN equipment_info info ON special.equipment_id = info.id
        LEFT JOIN equipment_structure structure ON special.structure_id = structure.id
        LEFT JOIN equipment_structure structureParent ON structure.parent_id = structureParent.id
        <where>
            structure.part_id IN
            <foreach collection="param.partIds" open="(" close=")" separator="," item="partId" index="index">
                #{partId}
            </foreach>
        </where>
        ORDER BY special.update_time DESC
    </select>
    <select id="getEquipmentCategoryIds" resultType="java.lang.String">
        SELECT DISTINCT info.category_id FROM equipment_info_special special
        LEFT JOIN equipment_info info ON special.equipment_id = info.id
        WHERE special.structure_id IS NULL
    </select>
    <select id="getStructurePartIds" resultType="java.lang.String">
        SELECT DISTINCT structure.part_id FROM equipment_info_special special
        LEFT JOIN equipment_structure structure ON special.structure_id = structure.id
        WHERE special.structure_id IS NOT NULL
    </select>
</mapper>
