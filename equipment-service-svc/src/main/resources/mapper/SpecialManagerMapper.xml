<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.SpecialManagerMapper">
    <select id="getPageList" resultType="cn.getech.ehm.equipment.dto.manager.SpecialManagerDto">
        SELECT id,name,certificate_no,job_item,issue_date,review_date,certificate_validity,
            issuing_authority,employer,workshop,doc_ids,id_number,certificate_name,certificate_type,deadline_days
        FROM equipment_special_manager
        <where>
            <if test="null != param.name and '' != param.name">
                AND name LIKE CONCAT("%",#{param.name}, "%")
            </if>
            <if test="null != param.keyword and '' != param.keyword">
                AND name LIKE CONCAT("%",#{param.keyword}, "%")
            </if>
            <if test="null != param.certificateNo and '' != param.certificateNo">
                AND certificate_no LIKE CONCAT("%",#{param.certificateNo}, "%")
            </if>
            <if test="null != param.jobItem and '' != param.jobItem">
                AND job_item = #{param.jobItem}
            </if>
            <if test="null != param.beginIssueDate and null != param.endIssueDate">
                AND issue_date BETWEEN #{param.beginIssueDate} AND #{param.endIssueDate}
            </if>
            <if test="null != param.beginReviewDate and null != param.endReviewDate">
                AND review_date BETWEEN #{param.beginReviewDate} AND #{param.endReviewDate}
            </if>
            <if test="null != param.beginCertificateValidity and null != param.endCertificateValidity">
                AND certificate_validity BETWEEN #{param.beginCertificateValidity} AND #{param.endCertificateValidity}
            </if>
            <if test="null != param.issuingAuthority and '' != param.issuingAuthority">
                AND issuing_authority LIKE CONCAT("%",#{param.issuingAuthority}, "%")
            </if>
            <if test="null != param.employer and '' != param.employer">
                AND employer = #{param.employer}
            </if>
            <if test="param.isOverTime">
                AND (
                (DATE_FORMAT(NOW(),'%Y-%m-%d') &gt; DATE_SUB( certificate_validity, INTERVAL deadline_days DAY ))
                or
                (DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_FORMAT(certificate_validity,'%Y-%m-%d'))
                )
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
</mapper>
