<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentStructureParameterMapper">
    <select id="getList" resultType="cn.getech.ehm.equipment.dto.info.EquipmentStructureParameterDto">
        SELECT id,structure_id, name,unit,param_type,source_type,code,value,expression
        FROM equipment_structure_parameter
        WHERE
            structure_id IN
            <foreach collection="structureIds" item="structureId" open="(" separator="," close=")">
                #{structureId}
            </foreach>
            <if test="null != paramTypes">
                AND param_type IN
                <foreach collection="paramTypes" item="paramType" open="(" separator="," close=")">
                    #{paramType}
                </foreach>
            </if>
        ORDER BY sort ASC
    </select>
</mapper>
