<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.InfoCategoryStructureMapper">
    <select id="getListByCategoryId" resultType="cn.getech.ehm.equipment.dto.category.CategoryStructureDto">
        SELECT structure.id,structure.spare_parts_category_id, structure.name, structure.type,
        structure.parent_id,spc.name sparePartsCategoryName,structure.category_id,structure.sort,
        structure.part_id, spc.basic_library_id,library.name basicLibraryName
        FROM equipment_category_structure structure
        LEFT JOIN spare_parts_category spc ON structure.spare_parts_category_id = spc.id
        LEFT JOIN basic_library library ON spc.basic_library_id = library.id
        WHERE structure.category_id = #{categoryId}
        <if test="null != copyStructureIds and copyStructureIds.size()>0">
            AND (structure.id IN
            <foreach collection="copyStructureIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            OR structure.parent_id IN
            <foreach collection="copyStructureIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        ORDER BY structure.sort ASC
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort), 0) FROM equipment_category_structure WHERE category_id = #{categoryId}
    </select>
</mapper>
