<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentCategory">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="special_info" property="specialInfo" />
        <result column="checked" property="checked" />
        <result column="pic_ids" property="picIds" />
        <result column="doc_ids" property="docIds" />
        <result column="layer_code" property="layerCode" />
        <result column="fixed" property="fixed" />
        <result column="prefix" property="prefix" />
        <result column="type" property="type" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        category.id,
        category.create_by,
        category.create_time,
        category.update_by,
        category.update_time,
        category.code,
        category.name,
        category.special_info,
        category.checked,
        category.fixed,
        category.type,
        category.prefix,
        category.parent_id,
        category.pic_ids,
        category.doc_ids,
        category.layer_code,
        category.tenant_id,
        category.oee_open,
        category.status_param_open
    </sql>
    <select id="getLayerCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT layer_code FROM equipment_category WHERE id = #{id} AND deleted = 0
    </select>
    <select id="getListByLayerCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_category category
        WHERE deleted = 0 AND layer_code LIKE CONCAT(#{layerCode}, '%')
    </select>
    <update id="deleteById" parameterType="java.lang.String">
        UPDATE equipment_category SET deleted = 1 WHERE id = #{id}
    </update>
    <select id="getById" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.category.EquipmentCategoryDto">
        SELECT
        <include refid="Base_Column_List"/>,parent.name parentName
        FROM equipment_category category
        LEFT JOIN equipment_category parent ON category.parent_id = parent.id
        WHERE category.id = #{id}
    </select>
    <select id="getListByParentId" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.category.EquipmentCategoryTreeDto">
        SELECT id,name,code,layer_code,fixed,type,interval_period intervalPeriod FROM equipment_category WHERE deleted = 0 AND parent_id = #{parentId}
        ORDER BY name ASC,create_time ASC
    </select>
    <select id="getListByParentCode" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.category.EquipmentCategoryTreeDto">
        SELECT id,name,code,layer_code,fixed,type,interval_period intervalPeriod FROM equipment_category WHERE deleted = 0 AND parent_id =
        (SELECT id FROM equipment_category WHERE code = #{parentCode})
                ORDER BY create_time ASC
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT count(*) FROM ${tableName} WHERE ${columnName} = #{value} AND deleted = 0
    </select>
    <insert id="saveDto" parameterType="cn.getech.ehm.equipment.entity.EquipmentCategory">
        insert into equipment_category(id,code,name,parent_id,layer_code,tenant_id,fixed,type,prefix,deleted,create_by,create_time,update_by,update_time)
        values(#{entity.id},#{entity.code},#{entity.name},#{entity.parentId},#{entity.layerCode}
        ,#{entity.tenantId},#{entity.fixed},#{entity.type},#{entity.prefix},#{entity.deleted}
        ,#{entity.createBy},#{entity.createTime},#{entity.updateBy},#{entity.updateTime})
    </insert>
    <select id="checkInit" parameterType="java.lang.String" resultType="java.lang.Integer">
        select ifnull(count(id),0) from equipment_category category
        where category.deleted = 0
        and category.tenant_id = #{tenantId}
        and category.code in ('SCSB','RQ','YQYB','GJGZ')
    </select>
    <insert id="importCW">
        INSERT INTO equipment_category(id,name,parent_id,code,layer_code,deleted,tenant_id,
        create_by,create_time,update_by,update_time)
        <foreach collection="entities" open="VALUES" close=";" index="index" item="entity" separator=",">
            (#{entity.id},#{entity.name},#{entity.parentId},#{entity.code},#{entity.layerCode},
            #{entity.deleted},#{entity.tenantId},#{entity.createBy},#{entity.createTime},
            #{entity.updateBy},#{entity.updateTime})
        </foreach>
    </insert>
    <update id="updateByCodes">
        UPDATE equipment_category SET deleted = 1
        WHERE tenant_id = #{tenantId} AND code IN
        <foreach collection="codes" open="(" close=")" separator="," item="code" index="index">
            #{code}
        </foreach>
    </update>
</mapper>
