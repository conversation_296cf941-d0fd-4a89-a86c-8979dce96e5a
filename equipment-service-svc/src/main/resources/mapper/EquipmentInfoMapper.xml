<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="InfoDtoMap" type="cn.getech.ehm.equipment.dto.info.EquipmentInfoDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="categoryName" property="categoryName" />
        <result column="locationId" property="locationId" />
        <result column="locationName" property="locationName" />
        <result column="type" property="type" />
        <result column="item_no" property="itemNo" />
        <result column="parent_id" property="parentId" />
        <result column="parentName" property="parentName" />
        <result column="specification" property="specification" />
        <result column="completion_time" property="completionTime" />
        <result column="importance" property="importance" />
        <result column="brand" property="brand" />
        <result column="alternate_field_x" property="alternateFieldX" />
        <result column="alternate_field_Y" property="alternateFieldY" />
        <result column="special_info" property="specialInfo" />
        <result column="checked" property="checked" />
        <result column="check_date" property="checkDate" />
        <result column="check_user" property="checkUser" />
        <result column="check_status" property="checkStatus" />
        <result column="networked" property="networked" />
        <result column="monitored" property="monitored" />
        <result column="running_status" property="runningStatus" />
        <result column="iot_status" property="iotStatus" />
        <result column="health_status" property="healthStatus" />
        <result column="manufacturer_id" property="manufacturerId" />
        <result column="supplier_id" property="supplierId" />
        <result column="design_company_id" property="designCompanyId" />
        <result column="install_company_id" property="installCompanyId" />
        <result column="factory_code" property="factoryCode" />
        <result column="production_time" property="productionTime" />
        <result column="design_period" property="designPeriod" />
        <result column="warranty_period" property="warrantyPeriod" />
        <result column="asset_code" property="assetCode" />
        <result column="asset_status" property="assetStatus" />
        <result column="cost_center" property="costCenter" />
        <result column="source" property="source" />
        <result column="original_asset" property="originalAsset" />
        <result column="net_asset" property="netAsset" />
        <result column="manage_depart" property="manageDepart" />
        <result column="manage_principal" property="managePrincipal" />
        <result column="use_depart" property="useDepart" />
        <result column="use_principal" property="usePrincipal" />
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="pic_id" property="picId" />
        <result column="doc_ids" property="docIds" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="position_tag_id" property="positionTagId" />
    </resultMap>
    <resultMap id="ListDtoMap" type="cn.getech.ehm.equipment.dto.info.EquipmentInfoListDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="categoryName" property="categoryName" />
        <result column="locationId" property="locationId" />
        <result column="locationName" property="locationName" />
        <result column="type" property="type" />
        <result column="item_no" property="itemNo" />
        <result column="parent_id" property="parentId" />
        <result column="parentName" property="parentName" />
        <result column="specification" property="specification" />
        <result column="completion_time" property="completionTime" />
        <result column="importance" property="importance" />
        <result column="brand" property="brand" />
    </resultMap>
    <resultMap id="excelDtoMap" type="cn.getech.ehm.equipment.dto.info.EquipmentSpecialExcel">
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="categoryName" property="categoryName" />
        <result column="type" property="type" />
        <result column="item_no" property="itemNo" />
        <result column="parent_id" property="parentId" />
        <result column="parentName" property="parentName" />
        <result column="specification" property="specification" />
        <result column="completion_time" property="completionTime" />
        <result column="importance" property="importance" />
        <result column="brand" property="brand" />
        <result column="alternate_field_x" property="alternateFieldX" />
        <result column="alternate_field_Y" property="alternateFieldY" />
        <result column="special_info" property="specialInfo" />
        <result column="checked" property="checked" />
        <result column="check_date" property="checkDate" />
        <result column="check_user" property="checkUser" />
        <result column="check_status" property="checkStatus" />
        <result column="networked" property="networked" />
        <result column="monitored" property="monitored" />
        <result column="running_status" property="runningStatus" />
        <result column="health_status" property="healthStatus" />
        <result column="iot_status" property="iotStatus" />
        <result column="manufacturer_id" property="manufacturerId" />
        <result column="supplier_id" property="supplierId" />
        <result column="design_company_id" property="designCompanyId" />
        <result column="install_company_id" property="installCompanyId" />
        <result column="factory_code" property="factoryCode" />
        <result column="production_time" property="productionTime" />
        <result column="design_period" property="designPeriod" />
        <result column="warranty_period" property="warrantyPeriod" />
        <result column="asset_code" property="assetCode" />
        <result column="asset_status" property="assetStatus" />
        <result column="cost_center" property="costCenter" />
        <result column="source" property="source" />
        <result column="original_asset" property="originalAsset" />
        <result column="net_asset" property="netAsset" />
        <result column="manage_depart" property="manageDepart" />
        <result column="manage_principal" property="managePrincipal" />
        <result column="use_depart" property="useDepart" />
        <result column="use_principal" property="usePrincipal" />
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="certificate_no" property="certificateNo" />
        <result column="registration_code" property="registrationCode" />
        <result column="inspection_date" property="inspectionDate" />
        <result column="next_inspection_date" property="nextInspectionDate" />
        <result column="report_no" property="reportNo" />
        <result column="inspection_org" property="inspectionOrg" />
        <result column="issuing_authority" property="issuingAuthority" />
        <result column="use_org" property="useOrg" />
    </resultMap>
    <resultMap id="DetailDtoMap" type="cn.getech.ehm.equipment.dto.EquipmentListDto">
        <result column="id" property="equipmentId" />
        <result column="code" property="equipmentCode" />
        <result column="name" property="equipmentName" />
        <result column="item_no" property="equipmentItemNo" />
        <result column="category_id" property="categoryId" />
        <result column="categoryName" property="categoryName" />
        <result column="type" property="equipmentType" />
        <result column="parent_id" property="parentId" />
        <result column="parentName" property="parentName" />
        <result column="running_status" property="runningStatus" />
        <result column="specification" property="specification" />
        <result column="location_id" property="locationId" />
        <result column="locationName" property="locationName" />
        <result column="production_time" property="productionTime" />
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="pic_id" property="picId" />
        <result column="manage_principal" property="managePrincipal" />
        <result column="use_principal" property="usePrincipal" />
    </resultMap>
    <select id="getCountByLocationId" resultType="java.lang.Integer" parameterType="list">
        SELECT IFNULL(COUNT(id),0) FROM equipment_info WHERE deleted = 0
        AND parent_id in
        <foreach collection="equipmentLocationIds" index="index" item="equipmentLocationId" open="(" close=")" separator=",">
            #{equipmentLocationId}
        </foreach>
        <if test="null != customerInfoIds and customerInfoIds.size() > 0">
            AND id in
            <foreach collection="customerInfoIds" index="index" item="customerInfoId" open="(" close=")" separator=",">
                #{customerInfoId}
            </foreach>

        </if>
    </select>
    <select id="getCountByTypeId" resultType="java.lang.Integer" parameterType="list">
        SELECT IFNULL(COUNT(id),0) FROM equipment_info WHERE deleted = 0
        AND category_id in
        <foreach collection="equipmentTypeIds" index="index" item="equipmentTypeId" open="(" close=")" separator=",">
            #{equipmentTypeId}
        </foreach>
        <if test="null != customerInfoIds and customerInfoIds.size() > 0">
            AND id in
            <foreach collection="customerInfoIds" index="index" item="customerInfoId" open="(" close=")" separator=",">
                #{customerInfoId}
            </foreach>

        </if>
    </select>

    <select id="getListByIds" parameterType="java.util.List" resultMap="DetailDtoMap">
        SELECT info.id, info.code, info.name, info.specification,info.item_no,
        info.production_time,info.parent_id, location.name parentName,info.location_id,location.layer_code locationLayerCode,
        location.name locationName,info.category_id,category.name categoryName, info.pic_id, info.type,info.team_ids,info.maintainer_ids,
        info.manage_principal,info.use_principal,info.running_status,info.importance
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE info.id IN
        <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="getTreeListByParentId" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.info.LazyEquipmentTreeDto">
        SELECT id,name,parent_id parentId,sort, case type when 1 then 11 when 2 then 12 else 12 end type,
               category_id categoryId
        FROM equipment_info
        <where>
            deleted = 0 AND parent_id = #{parentId}
            <if test="null != keyword and '' != keyword">
                AND name LIKE CONCAT("%",#{param.keyword}, "%")
            </if>
            <if test="null != equipmentIds and equipmentIds.size() > 0">
                AND id IN
                <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY sort desc,name ASC,update_time DESC
    </select>
    <select id="getAllTreeList" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto">
        SELECT id,name,parent_id parentId,category_id categoryId,
               case type when 1 then 11 when 2 then 12 else 12 end type
        FROM equipment_info
        WHERE deleted = 0
        <if test="null != equipmentIds and equipmentIds.size() > 0">
            AND id IN
            <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>
        ORDER BY sort desc,name ASC,update_time DESC
    </select>
    <select id="listTreeDetailDto" resultType="cn.getech.ehm.equipment.dto.info.EquipmentTreeDetailDto">
        SELECT info.id id, info.code code, info.name name,info.type,location.id locationId,category.id categoryId,
            location.name locationName, category.name categoryName, info.parent_id parentId,
            info.layer_code layerCode,info.specification,info.importance,info.running_status runningStatus,
            info.item_no itemNo ,info.manage_depart manageDepart,info.use_depart useDepart,info.pic_id picId
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            AND info.type = 1
            <if test="param.equipmentName != null and param.equipmentName != ''">
                AND info.name LIKE concat('%', #{param.equipmentName} , '%')
            </if>
            <if test="param.equipmentCode != null and param.equipmentCode != ''">
                AND info.code LIKE concat('%', #{param.equipmentCode} , '%')
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="null != param.noEquipmentIds and param.noEquipmentIds.size() > 0">
                AND info.id NOT IN (
                <foreach collection="param.noEquipmentIds" index="index" item="noEquipmentId" separator=",">
                    #{noEquipmentId}
                </foreach>
                )
            </if>
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND info.id IN (
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" separator=",">
                    #{equipmentId}
                </foreach>
                )
            </if>
            AND info.deleted = 0
        </where>
        ORDER BY info.update_time DESC, info.create_time DESC
    </select>
    <select id="secondaryInfoList" resultType="cn.getech.ehm.equipment.dto.info.EquipmentTreeDetailDto">
        SELECT info.id id, info.code code, info.name name,info.type,location.id locationId,category.id categoryId,
        location.name locationName, category.name categoryName, info.parent_id parentId,info.pic_id picId,
        info.layer_code layerCode,info.specification,info.importance,info.running_status runningStatus
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            AND info.type != 1
            <if test="parentId != null and parentId != ''">
                AND info.parent_id = #{parentId}
            </if>
            AND info.deleted = 0
        </where>
        ORDER BY info.update_time DESC, info.create_time DESC
    </select>
    <select id="getAppPageList" resultType="cn.getech.ehm.equipment.dto.info.EquipmentInfoAppDetailDto">
        SELECT info.id, info.code, info.name,info.item_no itemNo,info.parent_id parentId,
        location.name parentName, location.id locationId, location.name locationName,
        category.name categoryName, category.id categoryId, info.pic_id picId,
        info.asset_status assetStatus,info.running_status runningStatus,info.type,info.manage_principal managePrincipal,
        info.use_principal userPrincipal,info.manage_depart manageDepart,info.use_depart useDepart,info.iot_status iotStatus        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.advancedSearch == false and (null == param.parentEquipmentId or '' == param.parentEquipmentId)">
                AND info.type = 1
            </if>
            <if test="null != param.parentEquipmentId and '' != param.parentEquipmentId">
                AND info.parent_id = #{param.parentEquipmentId}
            </if>
            <if test="null != param.runningStatus">
                AND info.running_status = #{param.runningStatus}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.locationIds != null and param.locationIds.size() > 0">
                AND (
                <foreach collection="param.locationIds" index="index" item="locationId" separator=" OR ">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>
            <if test="null != param.iotStatus">
                AND info.iot_status = #{param.iotStatus}
            </if>
        </where>
        ORDER BY info.update_time DESC
    </select>

    <select id="getPageList" resultType="cn.getech.ehm.equipment.dto.info.EquipmentInfoListDto">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName, location.id locationId,location.name locationName,
        info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,
        info.alternate_field_y, info.create_by, info.create_time, info.update_by, info.update_time,
        info.health_index, info.health_index_update_time, info.remaining_life, info.remaining_life_update_time,
        info.iot_status
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.oeeFlag == true">
                AND category.oee_open = 1
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>
            <if test="null != param.specialInfo">
                AND info.special_info = #{param.specialInfo}
            </if>
            <if test="null != param.beginCompletionTime and null != param.endCompletionTime">
                AND (info.completion_time between #{param.beginCompletionTime} AND #{param.endCompletionTime})
            </if>
            <if test="null != param.importance and '' != param.importance">
                AND info.importance = #{param.importance}
            </if>
            <if test="null != param.brand and '' != param.brand">
                AND info.brand LIKE CONCAT('%',#{param.brand}, '%')
            </if>
            <if test="null != param.runningStatus and param.runningStatus.size() > 0">
                AND info.running_status IN
                <foreach collection="param.runningStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="null != param.healthStatus and param.healthStatus.size() > 0">
                AND info.health_status IN
                <foreach collection="param.healthStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="null != param.iotStatus and param.iotStatus.size() > 0">
                AND info.iot_status IN
                <foreach collection="param.iotStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                <choose>
                    <when test="null != param.parentType and param.parentType == 1">
                        AND info.location_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND info.parent_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="param.locationIds != null and param.locationIds.size() > 0">
                AND info.location_id IN
                <foreach collection="param.locationIds" index="index" item="parentId" open="(" close=")" separator=",">
                    #{parentId}
                </foreach>
            </if>
            <if test="param.remainingLifeOperationName != null and '' != param.remainingLifeOperationName and null != param.remainingLifeValue">
                AND info.remaining_life ${param.remainingLifeOperationName} #{param.remainingLifeValue}
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="getAllListByParam" resultMap="ListDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName, location.id locationId,location.name locationName,
        info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,
        info.alternate_field_y, info.create_by, info.create_time, info.update_by, info.update_time
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>
            <if test="null != param.specialInfo">
                AND info.special_info = #{param.specialInfo}
            </if>
            <if test="null != param.beginCompletionTime and null != param.endCompletionTime">
                AND (info.completion_time between #{param.beginCompletionTime} AND #{param.endCompletionTime})
            </if>
            <if test="null != param.importance and '' != param.importance">
                AND info.importance = #{param.importance}
            </if>
            <if test="null != param.brand and '' != param.brand">
                AND info.brand LIKE CONCAT('%',#{param.brand}, '%')
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                <choose>
                    <when test="null != param.parentType and param.parentType == 1">
                        AND info.location_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND info.parent_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="detailList" resultType="cn.getech.ehm.equipment.dto.info.EquipmentInfoDetailDto">
        SELECT info.id, info.code, info.name,info.type, info.parent_id, info.item_no, info.specification,
        location.name parentName, location.id locationId,location.name locationName,info.category_id, category.name categoryName
        FROM equipment_info info
        LEFT JOIN equipment_location location ON info.location_id = location.id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>

            <if test="param.locationId != null and '' != param.locationId">
                <choose>
                    <when test="null != param.containLocationChild and false == param.containLocationChild">
                        AND location.id = #{param.locationId}
                    </when>
                    <otherwise>
                        AND location.layer_code LIKE concat('%', #{param.locationId} , '%')
                    </otherwise>
                </choose>
            </if>
            <if test="param.categoryId != null and '' != param.categoryId">
                <choose>
                    <when test="null != param.containCategoryChild and false == param.containCategoryChild">
                        AND category.id = #{param.categoryId}
                    </when>
                    <otherwise>
                        AND category.layer_code LIKE concat('%', #{param.categoryId} , '%')
                    </otherwise>
                </choose>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                <choose>
                    <when test=" false == param.containCategoryChild">
                        AND category.id in (
                        <foreach collection="param.categoryIds" index="index" item="categoryId" separator=",">
                            #{categoryId}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        AND (
                        <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                            category.layer_code LIKE concat('%',#{categoryId} , '%')
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY info.update_time DESC
    </select>
    <select id="getRunningStatus" resultType="cn.getech.ehm.equipment.entity.EquipmentInfo">
        SELECT info.id, info.running_status
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>
            <if test="null != param.beginCompletionTime and null != param.endCompletionTime">
                AND (info.completion_time between #{param.beginCompletionTime} AND #{param.endCompletionTime})
            </if>
            <if test="null != param.importance and '' != param.importance">
                AND info.importance = #{param.importance}
            </if>
            <if test="null != param.brand and '' != param.brand">
                AND info.brand LIKE CONCAT('%',#{param.brand}, '%')
            </if>
            <if test="null != param.runningStatus and param.runningStatus.size() > 0">
                AND info.running_status IN
                <foreach collection="param.runningStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                <choose>
                    <when test="null != param.type and param.type == 1">
                        AND info.location_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND info.parent_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
        </where>
    </select>
    <select id="getInfoIdsByName" resultType="java.lang.String">
        SELECT id FROM equipment_info
        WHERE delete = 0
        <if test="null != equipmentName and '' != equipmentName">
             AND name LIKE concat('%', #{equipmentName} , '%')
        </if>
        <if test="null != equipmentIds and equipmentIds.size() > 0">
             AND id IN
            <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>
    </select>
    <select id="getEquipmentListByCategoryId" resultType="cn.getech.ehm.equipment.entity.EquipmentInfo">
        SELECT * FROM equipment_info WHERE category_id = #{categoryId}
    </select>

    <select id="getExportList" resultMap="excelDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName, info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,info.iot_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,
        info.alternate_field_y, info.create_by, info.create_time, info.update_by, info.update_time
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="null != param.categoryId and '' != param.categoryId">
                AND info.category_id = #{param.categoryId}
            </if>
            <if test="null != param.locationId and '' != param.locationId">
                AND location.layer_code LIKE concat('%', #{param.locationId} , '%')
            </if>
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY info.update_time DESC
    </select>
    <select id="getSpecialExportList" resultMap="excelDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName, info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,
        info.alternate_field_y, info.create_by, info.create_time, info.update_by, info.update_time,
        special.certificate_no, special.registration_code, special.inspection_date, special.next_inspection_date,
        special.report_no, special.inspection_org, special.issuing_authority, special.use_org
        FROM equipment_info_special special
        LEFT JOIN equipment_info info ON info.id = special.equipment_id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            AND info.category_id = #{param.categoryId}
            AND special.structure_id IS NULL
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY info.update_time DESC
    </select>
    <select id="getEquipmentStatistics" resultType="cn.getech.ehm.equipment.dto.EquipmentStatisticsDto">
        SELECT info.id equipmentId, info.code equipmentCode, info.name equipmentName, category.id categoryId,
        category.name categoryName, category.code categoryCode, category.layer_code categoryLayerCode,
        location.id locationId, location.name locationName, location.layer_code locationLayerCode,
        info.`status` , info.type,info.manufacturer_id
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            AND info.deleted = 0
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND info.category_id IN
                <foreach collection="param.categoryIds" index="index" open="(" close=")" item="categoryId" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="null != param.locationIds and param.locationIds.size() > 0">
                AND info.location_id IN
                <foreach collection="param.locationIds" index="index" open="(" close=")" item="locationId" separator=",">
                    #{locationId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getEquipmentSummary" resultType="cn.getech.ehm.equipment.dto.EquipmentSummaryDto">
        SELECT info.id equipmentId, info.code equipmentCode, info.name equipmentName, category.id categoryId,
        category.name categoryName, category.code categoryCode, category.layer_code categoryLayerCode,info.item_no equipmentItemNo,
        location.id locationId, location.name locationName, location.layer_code locationLayerCode,info.health_status healthStatus,
        info.asset_status assetStatus,info.iot_status iotStatus, info.running_status runningStatus,info.param_stop_enable paramStopEnable,
        info.health_index,info.remaining_life,info.max_rate,info.min_rate
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="searchDto.keyword != null and searchDto.keyword != ''">
                AND (
                info.name LIKE concat('%', #{searchDto.keyword} , '%')
                OR info.code LIKE concat('%', #{searchDto.keyword} , '%')
                OR info.item_no LIKE concat('%', #{searchDto.keyword} , '%')
                )
            </if>
            <if test="null != searchDto.runningStatus">
                AND info.running_status = #{searchDto.runningStatus}
            </if>
            <if test="null != searchDto.equipmentId and '' != searchDto.equipmentId">
                AND info.id = #{searchDto.equipmentId}
            </if>
            <if test="searchDto.equipmentIds != null and searchDto.equipmentIds.size() > 0">
                AND info.id IN(
                <foreach collection="searchDto.equipmentIds" index="index" item="equipmentId" separator=",">
                    #{equipmentId}
                </foreach>
                )
            </if>
            <if test="null != searchDto.categoryId and '' != searchDto.categoryId">
                <choose>
                    <when test="null != searchDto.containCategoryChild and false == searchDto.containCategoryChild">
                        AND category.id = #{searchDto.categoryId}
                    </when>
                    <otherwise>
                        AND category.layer_code LIKE concat('%', #{searchDto.categoryId} , '%')
                    </otherwise>
                </choose>
            </if>
            <if test="null != searchDto.locationId and '' != searchDto.locationId">
                AND location.layer_code LIKE concat('%',#{searchDto.locationId},'%')
            </if>
            <if test="searchDto.categoryIds != null and searchDto.categoryIds.size() > 0">
                AND info.category_id IN
                <foreach collection="searchDto.categoryIds" index="index" item="categoryId" separator="," open="(" close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="searchDto.locationIds != null and searchDto.locationIds.size() > 0">
                AND info.location_id IN
                <foreach collection="searchDto.locationIds" index="index" item="locationId" separator="," open="(" close=")">
                    #{locationId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getDtoById" parameterType="java.lang.String" resultMap="InfoDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName, location.id locationId, location.name locationName,
        info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,info.iot_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,info.pic_id,
        info.doc_ids,info.alternate_field_y, info.pc_studio_url, info.app_studio_url, info.create_by, info.create_time, info.update_by, info.update_time,
        info.position_tag_id
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE
            info.deleted = 0
            AND info.id = #{id}
    </select>
    <select id="getDtoByPositionTagId" parameterType="java.lang.String" resultMap="InfoDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
               info.parent_id, location.name parentName, location.id locationId, location.name locationName,
               info.item_no, info.specification, info.completion_time, info.importance,
               info.brand, info.special_info, info.checked, info.check_date, info.check_user, info.check_status,
               info.networked,info.monitored, info.running_status,info.health_status,info.iot_status,
               info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
               info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
               info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
               info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,info.pic_id,
               info.doc_ids,info.alternate_field_y, info.pc_studio_url, info.app_studio_url, info.create_by, info.create_time, info.update_by, info.update_time
        FROM equipment_info info
                 LEFT JOIN equipment_category category ON info.category_id = category.id
                 LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE
            info.deleted = 0
          AND info.position_tag_id = #{id}
    </select>
    <select id="getBomDtoById" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.info.BomParentDto">
        SELECT id,name,parent_id parentId, case type when 1 then 11 when 2 then 12 else 12 end type FROM equipment_info
        <where>
            deleted = 0 AND id = #{id}
        </where>
        ORDER BY update_time DESC
    </select>
    <select id="getAppDtoById" parameterType="java.lang.String" resultMap="InfoDtoMap">
        SELECT info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id, location.name parentName,location.id locationId,location.name locationName,
         info.item_no, info.specification, info.completion_time, info.importance,
        info.brand, info.special_info, info.checked,info.check_date, info.check_user, info.check_status,
        info.networked,info.monitored, info.running_status,info.health_status,info.iot_status,
        info.manufacturer_id, info.supplier_id, info.design_company_id, info.install_company_id, info.factory_code,
        info.production_time, info.design_period, info.warranty_period, info.asset_code, info.asset_status,
        info.cost_center, info.source, info.original_asset, info.net_asset, info.manage_depart, info.manage_principal,
        info.use_depart, info.use_principal, info.maintainer_ids, info.team_ids, info.alternate_field_x,info.pic_id,
        info.alternate_field_y, info.create_by, info.create_time, info.update_by, info.update_time
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE
            info.deleted = 0 AND category.deleted = 0
            AND info.id = #{id}
    </select>
    <update id="updateOnlineStatus">
        UPDATE equipment_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="online_status =case" suffix="end,">
                <foreach collection="params" item="param" index="index">
                    <if test="param.onlineStatus!=null">
                        when iot_equipment_id=#{param.deviceId} then #{param.onlineStatus}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="params" separator="or" item="param" index="index" >
            iot_equipment_id=#{param.deviceId}
        </foreach>
    </update>
    <select id="equipmentCount" resultType="java.lang.Integer">
        SELECT count(id) FROM equipment_info info
        <where>
            <if test="null != param.type and param.type == 1">
                AND info.iot_equipment_id IS NOT NULL
                AND trim(info.iot_equipment_id) != ''
                AND info.online_status != 2
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.beginTime != null and param.endTime != null">
                AND info.create_time between #{param.beginTime} and #{param.endTime}
            </if>
        </where>
    </select>

    <select id="equipmentAllCount" resultType="java.lang.Integer">
        SELECT count(1) FROM equipment_info info where deleted = 0
    </select>

    <select id="equipmentTotalCount" resultType="java.lang.Integer">
        SELECT count(1) FROM equipment_info where 1=1
    </select>

    <select id="equipmentJoinCount" resultType="java.lang.Integer">
        SELECT count(1) FROM equipment_info info
        <where>
            info.iot_equipment_id IS NOT NULL
            AND trim(info.iot_equipment_id) != ''
            AND (online_status = 0 or online_status = 1)
        </where>
    </select>
    <select id="getDocByInfoId" resultType="cn.getech.ehm.equipment.dto.EquipmentDocDto">
        select info.id, info.code, info.name, info.category_id, category.name categoryName, info.type,
        info.parent_id parentId, location.name parentName,info.doc_ids infoDocIds, category.doc_ids commonDocIds
        from equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        where info.id = #{equipmentId}
    </select>
    <select id="getLayerCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT layer_code FROM equipment_info WHERE id = #{id} AND deleted = 0
    </select>
    <select id="getLocationLayerCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT location.layer_code FROM equipment_info info
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE info.id = #{id} AND info.deleted = 0
    </select>
    <select id="getLocationLayerCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT location.layer_code FROM equipment_info info
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE info.id = #{id} AND info.deleted = 0
    </select>
    <select id="getListByLayerCode" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.info.EquipmentBomTreeDto">
        SELECT
            info.id, info.name, info.code, category.name type, info.type equipmentType,
            info.parent_id parentId, info.layer_code layerCode, info.specification modelName
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        WHERE info.deleted = 0 AND info.layer_code LIKE CONCAT(#{layerCode}, '%')
        ORDER BY info.create_time ASC
    </select>
    <select id="getMainInfoIdByLocation" resultType="java.lang.String">
        SELECT info.id FROM equipment_info info
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            AND info.type = 1
            AND info.deleted = 0 AND location.deleted = 0
            <if test="locationIds != null and locationIds.size() > 0">
                AND (
                <foreach collection="locationIds" index="index" item="locationId" separator=" OR ">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>

        </where>
    </select>
    <select id="getEquipmentIdsByParam" resultType="java.lang.String">
        SELECT distinct info.id id
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="param.equipmentId != null and param.equipmentId != ''">
                AND info.id = #{param.equipmentId}
            </if>
            <if test="param.equipmentName != null and param.equipmentName != ''">
                AND info.name LIKE concat('%', #{param.equipmentName} , '%')
            </if>
            <if test="param.equipmentCode != null and param.equipmentCode != ''">
                AND info.code LIKE concat('%', #{param.equipmentCode} , '%')
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0 and param.containCategoryChild">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%',#{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0 and !param.containCategoryChild">
                AND category.id in (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=",">
                    #{categoryId}
                </foreach>
                )
            </if>
            <if test="null != param.locationIds and param.locationIds.size() > 0">
                AND (
                <foreach collection="param.locationIds" index="index" item="locationId" separator="OR">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="haveChildInfoNames" resultType="java.lang.String">
        SELECT DISTINCT parent.name FROM equipment_info info
        LEFT JOIN equipment_info parent ON info.parent_id = parent.id
        WHERE info.deleted = 0
        AND info.parent_id IN (
        <foreach collection="equipmentIds" index="index" item="equipmentId" separator=",">
            #{equipmentId}
        </foreach>
        )
    </select>
    <select id="getSpecialListByIds" parameterType="java.util.List" resultType="cn.getech.ehm.equipment.dto.info.SpecialEquipmentDto">
        SELECT info.id equipmentId, info.code equipmentCode, info.name equipmentName, info.parent_id parentId,
        location.name parentName,category.id categoryId,category.name categoryName, info.type equipmentType,
        info.item_no equipmentItemNo
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE info.id IN
        <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="getMaxCode" resultType="java.lang.String">
        SELECT ifnull(max(code), concat(#{categoryCode}, '0000')) FROM equipment_info WHERE code LIKE concat(#{categoryCode} , '%')
    </select>
    <update id="updateByCodes">
        UPDATE equipment_info SET deleted = 1
        WHERE tenant_id = #{tenantId} AND code IN
        <foreach collection="codes" open="(" close=")" separator="," item="code" index="index">
            #{code}
        </foreach>
    </update>
    <select id="getEquipmentRateOfUtilization" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from equipment_info info
                                 left join equipment_location location on info.location_id = location.id
        where  info.running_status=0 AND location.layer_code like CONCAT( '%', #{locationId}, '%' )
    </select>
    <select id="getEquipmentRateOfUtilizationAll" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from equipment_info info
                                 left join equipment_location location on info.location_id = location.id
        where location.layer_code like CONCAT( '%', #{locationId}, '%' )
    </select>

    <select id="getLocationName" parameterType="java.lang.String" resultType="java.lang.String">
        select name from equipment_location where id=#{locationId}
    </select>

    <select id="getRunningStatusList" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.entity.EquipmentInfo">
        SELECT info.id,info.name,location.id,location.layer_code,info.running_status from equipment_info info
        LEFT JOIN equipment_location location on info.location_id = location.id
                                                     WHERE
             location.layer_code LIKE  CONCAT( '%', #{locationId}, '%' ) AND info.deleted=0
    </select>
    <select id="getWranCount"  resultType="java.lang.Integer">
        SELECT COUNT(1) FROM equipment_info WHERE iot_status NOT IN (0,-1,-2)
    </select>
    <select id="getOeeInfoPageList" resultType="cn.getech.ehm.equipment.dto.OeeDetailListDto">
        SELECT info.id equipmentId, info.name equipmentName, info.category_id categoryId,info.running_status runningStatus,
               category.name categoryName, location.id locationId,location.name locationName
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0 AND category.oee_open = 1
            <if test="param.categoryIds != null and param.categoryIds.length > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.locationIds != null and param.locationIds.length > 0">
                AND (
                <foreach collection="param.locationIds" index="index" item="locationId" separator=" OR ">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY info.create_time DESC
    </select>
    <select id="getOeeInfoList" resultType="cn.getech.ehm.equipment.dto.OeeDetailListDto">
        SELECT info.id equipmentId, info.name equipmentName, info.category_id categoryId,info.running_status runningStatus,
        category.name categoryName, location.id locationId,location.name locationName
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0 AND category.oee_open = 1
            <if test="param.categoryIds != null and param.categoryIds.length > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.locationIds != null and param.locationIds.length > 0">
                AND (
                <foreach collection="param.locationIds" index="index" item="locationId" separator=" OR ">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY info.create_time DESC
    </select>
    <select id="getOeeOpenInfoIds" resultType="java.lang.String">
        SELECT distinct info.id
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        WHERE info.deleted = 0 AND category.oee_open = 1
    </select>

    <select id="getHealthStatus" resultType="cn.getech.ehm.equipment.entity.EquipmentInfo">
        SELECT info.id, info.health_status
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>
            <if test="null != param.beginCompletionTime and null != param.endCompletionTime">
                AND (info.completion_time between #{param.beginCompletionTime} AND #{param.endCompletionTime})
            </if>
            <if test="null != param.importance and '' != param.importance">
                AND info.importance = #{param.importance}
            </if>
            <if test="null != param.brand and '' != param.brand">
                AND info.brand LIKE CONCAT('%',#{param.brand}, '%')
            </if>
            <if test="null != param.runningStatus and param.runningStatus.size() > 0">
                AND info.running_status IN
                <foreach collection="param.runningStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                <choose>
                    <when test="null != param.type and param.type == 1">
                        AND info.location_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND info.parent_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
        </where>
    </select>

    <select id="getIotStatus" resultType="cn.getech.ehm.equipment.entity.EquipmentInfo">
        SELECT info.id, info.iot_status
        FROM equipment_info info
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.deleted = 0
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                OR info.item_no LIKE concat('%', #{param.keyword} , '%')
                OR info.specification LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.code and '' != param.code">
                AND info.code LIKE CONCAT('%',#{param.code}, '%')
            </if>
            <if test="null != param.name and '' != param.name">
                AND info.name LIKE CONCAT('%',#{param.name}, '%')
            </if>
            <if test="null != param.itemNo and '' != param.itemNo">
                AND info.item_no LIKE CONCAT('%',#{param.itemNo}, '%')
            </if>
            <if test="null != param.specification and '' != param.specification">
                AND info.specification LIKE CONCAT('%',#{param.specification}, '%')
            </if>
            <if test="null != param.beginCompletionTime and null != param.endCompletionTime">
                AND (info.completion_time between #{param.beginCompletionTime} AND #{param.endCompletionTime})
            </if>
            <if test="null != param.importance and '' != param.importance">
                AND info.importance = #{param.importance}
            </if>
            <if test="null != param.brand and '' != param.brand">
                AND info.brand LIKE CONCAT('%',#{param.brand}, '%')
            </if>
            <if test="null != param.runningStatus and param.runningStatus.size() > 0">
                AND info.running_status IN
                <foreach collection="param.runningStatus" index="index" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.parentIds != null and param.parentIds.size() > 0">
                <choose>
                    <when test="null != param.type and param.type == 1">
                        AND info.location_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND info.parent_id IN
                        <foreach collection="param.parentIds" index="index" item="parentId" open="(" close=")" separator=",">
                            #{parentId}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
        </where>
    </select>
</mapper>

