<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.BasicLibraryFieldMapper">
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT ifnull(max(sort),0) FROM basic_library_field WHERE basic_library_id = #{basicLibraryId}
    </select>
    <select id="getDefaultFields" resultType="cn.getech.ehm.equipment.entity.bearing.BasicLibraryField">
        SELECT * FROM basic_library_field WHERE tenant_id = 'geek'
    </select>
</mapper>
