<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.SparePartsMapper">
    <select id="getMaxSort" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT ifnull(max(sort),0) FROM equipment_spare_parts WHERE equipment_id = #{equipmentId}
    </select>
</mapper>
