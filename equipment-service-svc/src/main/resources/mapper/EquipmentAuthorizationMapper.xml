<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentAuthorizationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentAuthorization">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
        <result column="code" property="code" />
        <result column="code_path" property="codePath" />
        <result column="org_type" property="orgType" />
        <result column="parent_code" property="parentCode" />
        <result column="auth_type" property="authType" />
        <result column="equipment_ids" property="equipmentIds" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        code, code_path, org_type, parent_code, auth_type, equipment_ids
    </sql>

</mapper>
