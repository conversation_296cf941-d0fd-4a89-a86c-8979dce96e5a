<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.BearingFactoryMapper">
    <select id="checkNameExits" resultType="java.lang.Integer">
        SELECT count(0) FROM bearing_factory WHERE deleted = 0
        <if test="null != id and '' != id">
            AND id != #{id}
        </if>
        AND lower(name) = lower(#{name})
    </select>
    <select id="getDefaultFactory" resultType="cn.getech.ehm.equipment.entity.bearing.BearingFactory">
        SELECT * FROM bearing_factory WHERE tenant_id = 'geek'
    </select>
</mapper>
