<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.DiagnosisReportV2Mapper">
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportV2ListDto">
        SELECT report.*
        FROM equipment_diagnosis_report report
        <where>
            AND (report.status = 2
                <if test="null != param.currentUid and '' != param.currentUid">
                    OR (report.status = 1 AND report.create_by = #{param.currentUid})
                </if>
                <if test="null != param.auditPerson and true == param.auditPerson">
                    OR (report.status = 1 AND report.type = 2)
                </if>
            )
            <if test="null != param.nameOrCode and '' != param.nameOrCode">
                AND (report.name LIKE CONCAT("%",#{param.nameOrCode}, "%")
                OR report.code LIKE CONCAT("%",#{param.nameOrCode}, "%"))
            </if>
            <if test="null != param.type and '' != param.type">
                AND report.type = #{param.type}
            </if>
            <if test="null != param.status and '' != param.status">
                AND report.status = #{param.status}
            </if>
        </where>
        ORDER BY report.update_time DESC
    </select>
    <select id="getMaxCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT max(code) FROM equipment_diagnosis_report WHERE code LIKE concat(#{prex}, "%");
    </select>
</mapper>
