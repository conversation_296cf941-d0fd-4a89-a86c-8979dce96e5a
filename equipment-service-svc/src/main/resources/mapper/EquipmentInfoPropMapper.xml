<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentInfoPropMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentInfoProp">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
        <result column="name" property="name" />
        <result column="prop_type" property="propType" />
        <result column="define" property="define" />
        <result column="default_value" property="defaultValue" />
        <result column="equipment_id" property="equipmentId" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        `name`, prop_type,  define, default_value, equipment_id,  tenant_id
    </sql>

    <select id="getCalibrationDateByEquipmentId" resultType="java.lang.String">
        SELECT default_value FROM equipment_info_prop WHERE equipment_id = #{equipmentId} AND `name` = '校准日期'
    </select>

    <select id="getListByEquipmentIds"
            resultType="cn.getech.ehm.equipment.entity.EquipmentInfoProp">
        SELECT category.id id, info.equipment_id equipmentId, category.name name, category.prop_type propType,
        category.define define, IFNULL(info.default_value, category.default_value) defaultValue, category.fixed fixed
        FROM equipment_category_prop category
        LEFT JOIN equipment_info_prop info ON category.id = info.category_prop_id
        WHERE info.equipment_id IN
        <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        ORDER BY category.sort
    </select>

    <select id="getListByInfoIds" resultType="cn.getech.ehm.equipment.dto.info.EquipmentPropDetailDto">
        SELECT  infoProp.id, infoProp.equipment_id equipmentId, infoProp.category_prop_id categoryPropId, infoProp.name, infoProp.group_name groupName,
        infoProp.prop_type propType, infoProp.define, ifnull(infoProp.default_value, "") defaultValue, infoProp.fixed, infoProp.sort
        FROM equipment_info_prop infoProp
        WHERE infoProp.equipment_id IN
        <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        ORDER BY infoProp.sort ASC
    </select>
</mapper>
