<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.CalibrationTaskOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.CalibrationTaskOrder">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
        <result column="code" property="code" />
        <result column="current_equipment_id" property="currentEquipmentId" />
        <result column="ref_calibration_id" property="refCalibrationId" />
        <result column="plan_calibrate_date" property="planCalibrateDate" />
        <result column="actualCalibrate_date" property="actualCalibrateDate" />
        <result column="status" property="status" />
        <result column="result" property="result" />
        <result column="type" property="type" />
        <result column="attachment_id" property="attachmentId" />
        <result column="certificate_no" property="certificateNo" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        code,
        current_equipment_id,
        ref_calibration_id,
        plan_calibrate_date,
        actual_calibrate_date,
        status,
        result,
        type,
        attachment_id,
        certificate_no
    </sql>

    <select id="pageDto" resultType="cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderPageDto">
        SELECT calibration.id,calibration.code,calibration.equipment_id, equipment.code equipmentCode,
            equipment.name equipmentName,location.name equipmentLocation,calibration.plan_calibrate_date planCalibrateDate,
            calibration.actual_calibrate_date actualCalibrateDate,calibration.status,calibration.result,
            calibration.type
        FROM calibration_task_order calibration
        LEFT JOIN equipment_info equipment ON calibration.equipment_id = equipment.id
        LEFT JOIN equipment_location location ON equipment.location_id = location.id
        <where>
                calibration.deleted = 0 AND calibration.display_date &lt;= #{queryParam.currentDate}
            <if test="null != queryParam.keyword and '' != queryParam.keyword">
                AND (
                equipment.name LIKE concat('%', #{queryParam.keyword} , '%')
                OR equipment.code LIKE concat('%', #{queryParam.keyword} , '%')
                )
            </if>
            <if test="null != queryParam.status">
                AND calibration.status = #{queryParam.status}
            </if>
            <if test="null != queryParam.result">
                AND calibration.result = #{queryParam.result}
            </if>

            <if test="null != queryParam.innerCode and '' != queryParam.innerCode">
                AND calibration.code LIKE concat('%' , #{queryParam.innerCode}, '%')
            </if>
            <if test="null != queryParam.innerEquipmentCode and '' != queryParam.innerEquipmentCode">
                AND equipment.code LIKE concat('%' , #{queryParam.innerEquipmentCode}, '%')
            </if>
            <if test="null != queryParam.innerEquipmentName and '' != queryParam.innerEquipmentName">
                AND equipment.name LIKE concat('%' , #{queryParam.innerEquipmentName}, '%')
            </if>
            <if test="null != queryParam.innerStartPlanCalibrateDate and null != queryParam.innerEndPlanCalibrateDate">
                AND calibration.plan_calibrate_date BETWEEN #{queryParam.innerStartPlanCalibrateDate} AND #{queryParam.innerEndPlanCalibrateDate}
            </if>

            <if test="null != queryParam.innerStartCountdown and null != queryParam.innerEndCountdown">
                AND DATEDIFF(calibration.plan_calibrate_date, CURRENT_TIME) BETWEEN #{queryParam.innerStartCountdown} AND #{queryParam.innerEndCountdown}
            </if>
            <if test="null != queryParam.innerStartActualCalibrateDate and null != queryParam.innerEndActualCalibrateDate">
                AND calibration.actual_calibrate_date BETWEEN #{queryParam.innerStartActualCalibrateDate} AND #{queryParam.innerEndActualCalibrateDate}
            </if>
            <if test="null != queryParam.innerStatus and '' != queryParam.innerStatus">
                AND FIND_IN_SET(calibration.status, #{queryParam.innerStatus})
            </if>
            <if test="null != queryParam.innerResults and '' != queryParam.innerResults">
                AND FIND_IN_SET(calibration.result, #{queryParam.innerResults})
            </if>

            <if test="null != queryParam.equipmentIds and queryParam.equipmentIds.size != 0">
                AND calibration.equipment_id in
                <foreach collection="queryParam.equipmentIds" index="index" item="equipmentId" separator="," close=")" open="(">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        <if test="null != queryParam.sortValue and '' != queryParam.sortValue">
            ${queryParam.sortValue}
        </if>
    </select>

    <select id="getDtoById" resultType="cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderDto">
        SELECT calibration.id, calibration.code,calibration.equipment_id equipmentId,equipment.code equipmentCode,
            equipment.name equipmentName,equipment.category_id equipmentCategoryId,location.name equipmentLocation,
            equipment.specification equipmentSpecification,equipment.manufacturer_id equipmentManufacturerId,
            equipment.factory_code equipmentFactoryCode,calibration.ref_calibration_id refCalibrationId,
            calibration.plan_calibrate_date planCalibrateDate,calibration.actual_calibrate_date actualCalibrateDate,
            calibration.status,calibration.result,calibration.type,calibration.attachment_id attachmentId,
            calibration.certificate_no certificateNo,calibration.remark
        FROM calibration_task_order calibration
        LEFT JOIN equipment_info equipment ON calibration.equipment_id = equipment.id
        LEFT JOIN equipment_location location ON equipment.location_id = location.id
        WHERE calibration.id = #{id}
    </select>

    <select id="getMaxCode" resultType="java.lang.String">
        SELECT max(code) FROM calibration_task_order
        WHERE code LIKE concat(#{year} ,'%')
    </select>
    <select id="getEquipmentIds" resultType="java.lang.String">
        SELECT calibration.equipment_id equipmentId FROM calibration_task_order calibration
        WHERE calibration.type = 1 AND calibration.status = 1 AND calibration.result != 5
    </select>
</mapper>
