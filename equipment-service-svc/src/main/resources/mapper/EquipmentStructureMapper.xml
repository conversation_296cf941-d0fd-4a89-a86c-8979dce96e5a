<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentStructureMapper">
    <select id="getListByEquipmentId" resultType="cn.getech.ehm.equipment.dto.EquipmentStructureDto">
        SELECT structure.*,category.name sparePartsCategoryName,library.name basicLibraryName
        FROM equipment_structure structure
        LEFT JOIN spare_parts_category category ON structure.spare_parts_category_id = category.id
        LEFT JOIN basic_library library ON structure.basic_library_id = library.id
        WHERE structure.equipment_id = #{equipmentId}
        <if test="null != keyword and '' != keyword">
            AND structure.name LIKE concat('%' , #{keyword}, '%')
        </if>
        ORDER BY structure.sort ASC
    </select>
    <select id="getListByStructureId" resultType="cn.getech.ehm.equipment.dto.EquipmentStructureDto">
        SELECT structure.id,structure.equipment_id, structure.spare_parts_category_id,category.name sparePartsCategoryName,
        structure.basic_library_id,structure.name,structure.parent_id,structure.type,structure.monitored,structure.source_type,
        structure.brand, structure.model,structure.relation_id
        FROM equipment_structure structure
        LEFT JOIN spare_parts_category category ON structure.spare_parts_category_id = category.id
        WHERE structure.id IN
        <foreach collection="structureIds" index="index" item="structureId" open="(" close=")" separator=",">
            #{structureId}
        </foreach>
        ORDER BY structure.sort ASC
    </select>
    <select id="getFeatureStructures" resultType="cn.getech.ehm.equipment.dto.FeatureParameterDto">
        SELECT structure.id, structure.name ,CASE structure.type WHEN 1 THEN 2 WHEN 2 THEN 3 END type
        FROM equipment_structure structure
        LEFT JOIN equipment_structure parent ON structure.parent_id = parent.id
        WHERE structure.equipment_id = #{equipmentId}
        <if test="null != structureId and '' != structureId">
            AND (structure.id = #{structureId} OR structure.parent_id = #{structureId})
        </if>
        AND (structure.monitored = 1 OR parent.monitored = 1)
        ORDER BY structure.sort ASC
    </select>
    <select id="getSpecialListByIds" resultType="cn.getech.ehm.equipment.dto.info.SpecialStructureDto">
        SELECT structure.id structureId,structure.part_id partId,structure.location,
        structure.parent_id parentId,parent.part_id parentPartId
        FROM equipment_structure structure
        LEFT JOIN equipment_structure parent ON structure.parent_id = parent.id
        WHERE structure.id IN
        <foreach collection="structureIds" index="index" item="structureId" open="(" close=")" separator=",">
            #{structureId}
        </foreach>
        ORDER BY structure.sort ASC
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort), 0) FROM equipment_structure WHERE equipment_id = #{equipmentId}
    </select>
</mapper>
