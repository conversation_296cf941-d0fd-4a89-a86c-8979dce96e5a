<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.BasicLibraryDetailMapper">
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.bearing.BasicLibraryDetailDto">
        SELECT libraryDetail.id,libraryDetail.basic_library_id, libraryDetail.factory_id factoryId, factory.name factoryName, libraryDetail.model_id modelId,
        model.name modelName, libraryDetail.detail
        FROM basic_library_detail libraryDetail
        LEFT JOIN bearing_factory factory ON libraryDetail.factory_id = factory.id
        LEFT JOIN bearing_model model ON libraryDetail.model_id = model.id
        <where>
            libraryDetail.basic_library_id = #{param.basicLibraryId}
            <if test="null != param.factoryId and '' != param.factoryId">
                AND factory.id = #{param.factoryId}
            </if>
            <if test="null != param.factoryName and '' != param.factoryName">
                AND factory.name LIKE concat('%', #{param.factoryName} , '%')
            </if>
            <if test="null != param.modelId and '' != param.modelId">
                AND model.id = #{param.modelId}
            </if>
            <if test="null != param.modelName and '' != param.modelName">
                AND model.name LIKE concat('%', #{param.modelName} , '%')
            </if>
        </where>
        ORDER BY libraryDetail.sort ASC
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT ifnull(max(sort),0) FROM basic_library_detail WHERE basic_library_id = #{basicLibraryId}
    </select>
    <select id="getDefaultDetails" resultType="cn.getech.ehm.equipment.entity.bearing.BasicLibraryDetail">
        SELECT * FROM basic_library_detail WHERE tenant_id = 'geek'
    </select>
</mapper>
