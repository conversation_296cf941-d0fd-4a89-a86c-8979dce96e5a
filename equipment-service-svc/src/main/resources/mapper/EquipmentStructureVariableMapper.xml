<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentStructureVariableMapper">
    <select id="getList" resultType="cn.getech.ehm.equipment.dto.info.StructureParameterVariableDto">
        SELECT variable.id, variable.structure_parameter_id,variable.name,variable.source_type,
        variable.value,IFNULL(param.name, variable.value) valueName, variable.remark
        FROM equipment_structure_variable variable
        LEFT JOIN equipment_structure_parameter param ON variable.value = param.id
        WHERE variable.structure_parameter_id IN
        <foreach collection="structureParameterIds" open="(" close=")" separator="," item="structureParameterId" index="index">
            #{structureParameterId}
        </foreach>
        ORDER BY variable.sort ASC
    </select>
    <select id="getVariableValue" resultType="cn.getech.ehm.equipment.dto.info.ParameterVariableValueDto">
        SELECT variable.id, variable.name,variable.source_type,
        variable.value,param.value paramValue
        FROM equipment_structure_variable variable
        LEFT JOIN equipment_structure_parameter param ON variable.value = param.id
        WHERE variable.structure_parameter_id = #{structureParameterId}
        ORDER BY variable.sort ASC
    </select>
</mapper>
