<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentChangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentChange">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="equipment_ids" property="equipmentIds" />
        <result column="type" property="type" />
        <result column="duty_id" property="dutyId" />
        <result column="duty_name" property="dutyName" />
        <result column="duty_contact" property="dutyContact" />
        <result column="duty_dept" property="dutyDept" />
        <result column="change_time" property="changeTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        equipment_ids, type, duty_id, duty_name, duty_contact, duty_dept, change_time, tenant_id, deleted
    </sql>

    <select id="getEquipmentChangeList" resultType="cn.getech.ehm.equipment.dto.change.EquipmentChangeDto">
        SELECT `change`.type type, location.`name` locationName, `change`.duty_name dutyName,
        `change`.duty_contact dutyContact,`change`.duty_dept dutyDept, `change`.change_time changeTime, `change`.status
        FROM equipment_change `change`
        LEFT JOIN equipment_location location ON `change`.location_id = location.id
        WHERE equipment_ids like concat('%',#{equipmentId},'%')
        AND status = 1 <!-- 审批通过的 -->
        ORDER BY `change`.change_time DESC
    </select>

</mapper>
