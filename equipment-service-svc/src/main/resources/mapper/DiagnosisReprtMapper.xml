<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.DiagnosisReportMapper">
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportListDto">
        SELECT report.id,report.name,report.code,report.equipment_id,equipment.name equipmentName,
               location.id locationId,location.name locationName,report.create_by,report.update_time
        FROM iot_diagnosis_report report
        LEFT JOIN equipment_info equipment ON report.equipment_id = equipment.id
        LEFT JOIN equipment_location location ON equipment.location_id = location.id
        <where>
            1 = 1
            <if test="null != param.nameOrCode and '' != param.nameOrCode">
                AND (report.name LIKE CONCAT("%",#{param.nameOrCode}, "%")
                OR report.code LIKE CONCAT("%",#{param.nameOrCode}, "%"))
            </if>
            <if test="null != param.equipmentName and '' != param.equipmentName">
                AND equipment.name LIKE CONCAT("%",#{param.equipmentName}, "%")
            </if>
            <if test="null != param.treeId and '' != param.treeId">
                AND (equipment.layer_code LIKE CONCAT("%",#{param.treeId}, "%")
                OR location.layer_code LIKE CONCAT("%",#{param.treeId}, "%"))
            </if>
        </where>
        ORDER BY report.update_time DESC
    </select>
    <select id="getDtoById" resultType="cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportDto">
        SELECT report.id,report.name,report.code,report.equipment_id,equipment.name equipmentName,
               equipment.code equipmentCode,location.id locationId,location.name locationName,
               category.id categoryId,category.name categoryName,equipment.pic_id equipmentPicIds,
               report.analyze_pic_info,report.analyze_info,report.analyze_reason,report.handling_suggestions,
               report.create_by,report.create_time
        FROM iot_diagnosis_report report
        LEFT JOIN equipment_info equipment ON report.equipment_id = equipment.id
        LEFT JOIN equipment_location location ON equipment.location_id = location.id
        LEFT JOIN equipment_category category ON equipment.category_id = category.id
        WHERE report.id = #{id}
    </select>
</mapper>
