<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.RunningParameterVariableMapper">
    <select id="getList" resultType="cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto">
        SELECT variable.id, variable.running_parameter_id,variable.name,variable.source_type,
        variable.value,IFNULL(param.name, variable.value) valueName, variable.remark,variable.sort
        FROM running_parameter_variable variable
        LEFT JOIN running_parameter param ON variable.value = param.id
        WHERE variable.running_parameter_id IN
        <foreach collection="runningParameterIds" open="(" close=")" separator="," item="runningParameterId" index="index">
            #{runningParameterId}
        </foreach>
        ORDER BY variable.sort ASC
    </select>
</mapper>
