<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentChangeNewMapper">

    <select id="getDtoById" resultType="cn.getech.ehm.equipment.dto.change.EquipmentChangeNewDto">
        SELECT changeNew.*,info.name equipmentName,info.code equipmentCode
        FROM equipment_change_new changeNew
        LEFT JOIN equipment_info info ON changeNew.equipment_id = info.id
        WHERE changeNew.id = #{id}
    </select>
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.change.EquipmentChangeNewDto">
        SELECT changeNew.*,info.name equipmentName,info.code equipmentCode
        FROM equipment_change_new changeNew
                 LEFT JOIN equipment_info info ON changeNew.equipment_id = info.id
        <where>
            changeNew.deleted = 0
            <if test="null != param.keyword and '' != param.keyword">
                AND (info.name LIKE CONCAT('%', #{param.keyword}, '%')
                  OR info.CODE LIKE CONCAT('%', #{param.keyword}, '%'))
            </if>
            <if test="null != param.startDate and null != param.endDate">
                AND changeNew.create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>
            <if test="null != param.optUserNames and param.optUserNames.size() > 0">
                AND changeNew.create_user_name IN
                <foreach collection="param.optUserNames" index="index" item="optUserName" open="(" close=")" separator=",">
                    #{optUserName}
                </foreach>
            </if>
            <if test="null != param.uidList and param.uidList.size() > 0">
                AND changeNew.create_by IN
                <foreach collection="param.uidList" index="index" item="uid" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
            </if>
            <if test="null != param.type">
                AND changeNew.type = #{param.type}
            </if>
            <if test="null != param.startChangeDate and null != param.endChangeDate">
                AND changeNew.change_date BETWEEN #{param.startChangeDate} AND #{param.endChangeDate}
            </if>
            <if test="null != param.isMyTodo and param.isMyTodo == true">
                AND (changeNew.status = 0 AND changeNew.process_user LIKE CONCAT ('%', #{param.currentUid}, '%'))
            </if>
            <if test="null != param.id and '' != param.id">
                AND changeNew.id = #{param.id}
            </if>
            <if test="null != param.equipmentId and '' != param.equipmentId">
                AND changeNew.equipment_id = #{param.equipmentId}
            </if>
            <if test="null != param.status">
                AND changeNew.status = #{param.status}
            </if>
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND changeNew.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="null != param.equipmentId and '' != param.equipmentId">
                ORDER BY changeNew.change_date DESC
            </when>
            <otherwise>
                ORDER BY changeNew.status ASC, changeNew.update_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="getExportList" resultType="cn.getech.ehm.equipment.dto.change.EquipmentChangeExcelDto">
        SELECT changeNew.*,info.name equipmentName,info.code equipmentCode
        FROM equipment_change_new changeNew
        LEFT JOIN equipment_info info ON changeNew.equipment_id = info.id
        <where>
            changeNew.deleted = 0
            <if test="null != param.startDate and null != param.endDate">
                AND changeNew.create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>
            <if test="null != param.optUserNames and param.optUserNames.size() > 0">
                AND changeNew.create_user_name IN
                <foreach collection="param.optUserNames" index="index" item="optUserName" open="(" close=")" separator=",">
                    #{optUserName}
                </foreach>
            </if>
            <if test="null != param.uidList and param.uidList.size() > 0">
                AND changeNew.create_by IN
                <foreach collection="param.uidList" index="index" item="uid" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
            </if>
            <if test="null != param.type">
                AND changeNew.type = #{param.type}
            </if>
            <if test="null != param.equipmentId and '' != param.equipmentId">
                AND changeNew.equipment_id = #{param.equipmentId}
            </if>
            <if test="null != param.status">
                AND changeNew.status = #{param.status}
            </if>
            <if test="null != param.equipmentIds and param.equipmentIds.size() > 0">
                AND changeNew.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        ORDER BY changeNew.status ASC, changeNew.update_time DESC
    </select>
</mapper>
