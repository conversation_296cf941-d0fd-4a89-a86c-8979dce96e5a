<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentCategoryPropMapper">

    <insert id="saveDto" parameterType="cn.getech.ehm.equipment.entity.EquipmentCategoryProp">
        insert into equipment_category_prop(id,name,prop_type,fixed,equipment_category_id,tenant_id,sort,create_by,create_time,update_by,update_time)
        values(#{info.id},#{info.name},#{info.propType},#{info.fixed},#{info.equipmentCategoryId},#{info.tenantId},#{info.sort}
        ,#{info.createBy},#{info.createTime},#{info.updateBy},#{info.updateTime})
    </insert>
    <select id="getDetailListByCategoryIds" resultType="cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto">
        SELECT id,id categoryPropId, group_name groupName, name, prop_type propType,define, ifnull(default_value,"") defaultValue,
        fixed,sort, exported,equipment_category_id categoryId,concat(group_name , '-' , name) allName
        FROM equipment_category_prop
        WHERE equipment_category_id IN
        <foreach collection="categoryIds" open="(" close=")" separator="," item="categoryId" index="index">
            #{categoryId}
        </foreach>
        <if test="fixed == true">
            AND fixed = 1
        </if>
        <if test="exported == true">
            AND exported = 1
        </if>
        ORDER BY sort ASC
    </select>
    <select id="getNameListByCategoryId" resultType="java.lang.String">
        SELECT concat(group_name , '-' , name)
        FROM equipment_category_prop
        WHERE equipment_category_id = #{categoryId}
        <if test="exported == true">
            AND exported = 1
        </if>
        ORDER BY sort ASC
    </select>
</mapper>
