<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.DiagnosisReportV2DetailMapper">
    <select id="getListByReportId" resultType="cn.getech.ehm.equipment.dto.diagnosis.ReportV2DetailDetailDto">
        SELECT detail.*, info.code equipmentCode, info.name equipmentName,info.health_status,
               location.id locationId, location.name locationName,category.id categoryId,category.name categoryName
        FROM equipment_diagnosis_report_detail detail
        LEFT JOIN equipment_info info ON detail.equipment_id = info.id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE detail.report_id = #{reportId}
        ORDER BY detail.sort ASC
    </select>
</mapper>
