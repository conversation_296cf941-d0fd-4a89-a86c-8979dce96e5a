<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentWarnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentWarn">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
        <result column="iot_equipment_id" property="iotEquipmentId" />
        <result column="level" property="level" />
        <result column="status" property="status" />
        <result column="warn_time" property="warnTime" />
        <result column="repair_time" property="repairTime" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        iot_equipment_id,
        level,
        status,
        warn_time,
        repair_time,
        tenant_id
    </sql>
    <insert id="saveIotBatch" parameterType="java.util.List" >
        INSERT INTO equipment_warn(id, iot_equipment_id, level, status, warn_time,
        remark, tenant_id, create_by, create_time, update_by, update_time, frame, fixture)
        <foreach collection="params" open="VALUES" close=";" index="index" item="param" separator=",">
            (#{param.id},#{param.iotEquipmentId},#{param.level},#{param.status},
            #{param.warnTime},#{param.remark},#{param.tenantId},#{param.createBy},#{param.createTime},
            #{param.updateBy},#{param.updateTime},#{param.frame},#{param.fixture})
        </foreach>
    </insert>
    <update id="updateIotBatch" parameterType="java.util.List">
            UPDATE equipment_warn
            <trim prefix="set" suffixOverrides=",">
                <trim prefix="status =case" suffix="end,">
                    <foreach collection="params" item="param" index="index">
                        <if test="param.status!=null">
                            when id=#{param.id} then #{param.status}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="repair_time =case" suffix="end,">
                    <foreach collection="params" item="param" index="index">
                        <if test="param.repairTime!=null">
                            when id=#{param.id} then #{param.repairTime}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="update_time =case" suffix="end,">
                    <foreach collection="params" item="param" index="index">
                        <if test="param.updateTime!=null">
                            when id=#{param.id} then #{param.updateTime}
                        </if>
                    </foreach>
                </trim>
            </trim>
            where
            <foreach collection="params" separator="or" item="param" index="index" >
                id=#{param.id}
            </foreach>
    </update>

    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.warn.EquipmentWarnDto">
        SELECT warn.id, info.id equipmentId, info.name equipmentName, info.code equipmentCode,
               warn.frame,warn.fixture,
        warn.iot_equipment_id iotEquipmentId,category.id categoryId, category.name categoryName,
        warn.level, warn.status, warn.warn_time, warn.repair_time,warn.remark
        FROM equipment_warn warn
        LEFT JOIN equipment_info info ON warn.iot_equipment_id = info.iot_equipment_id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        <where>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN (
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" separator=" , ">
                    #{equipmentId}
                </foreach>
                )
            </if>
            <if test="param.level != null and param.level != ''">
                AND FIND_IN_SET(warn.level, #{param.level})
            </if>
            <if test="null != param.equipmentCode and '' != param.equipmentCode">
                AND info.code LIKE CONCAT('%',#{param.equipmentCode}, '%')
            </if>
            <if test="null != param.equipmentName and '' != param.equipmentName">
                AND info.name LIKE CONCAT('%',#{param.equipmentName}, '%')
            </if>
            <!--<if test="null != param.customerName and '' != param.customerName">
                AND info.customer_name LIKE CONCAT('%',#{param.customerName}, '%')
            </if>-->
            <if test="null != param.fixture and '' != param.fixture">
                AND warn.fixture LIKE CONCAT('%',#{param.fixture}, '%')
            </if>
            <if test="null != param.frame and '' != param.frame">
                AND warn.frame LIKE CONCAT('%',#{param.frame}, '%')
            </if>
            <if test="param.categoryId != null and param.categoryId != '' and param.categoryId != '0'.toString()">
                ${param.categoryId}
            </if>
            <if test="null != param.remark and '' != param.remark">
                AND warn.remark LIKE CONCAT('%',#{param.remark}, '%')
            </if>
            <if test="param.status != null">
                AND warn.status = #{param.status}
            </if>

            <if test="null != param.innerEquipmentName and '' != param.innerEquipmentName">
                AND info.name LIKE CONCAT('%',#{param.innerEquipmentName}, '%')
            </if>

            <!--<if test="null != param.innerCustomerName and '' != param.innerCustomerName">
                AND info.customer_name LIKE CONCAT('%',#{param.innerCustomerName}, '%')
            </if>-->

            <if test="param.innerStatus != null and param.innerStatus != ''">
                AND FIND_IN_SET(warn.status, #{param.innerStatus})
            </if>
            <if test="null != param.beginTime and null != param.endTime">
                AND warn.warn_time BETWEEN #{param.beginTime} AND #{param.endTime}
            </if>
            <if test="null != param.innerBeginTime and null != param.innerEndTime">
                AND warn.warn_time BETWEEN #{param.innerBeginTime} AND #{param.innerEndTime}
            </if>
            <if test="null != param.beginRepairTime and null != param.endRepairTime">
                AND warn.repair_time BETWEEN #{param.beginRepairTime} AND #{param.endRepairTime}
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>

    <select id="getList" resultType="cn.getech.ehm.equipment.dto.warn.EquipmentWarnDto">
        SELECT warn.id, info.id equipmentId, info.name equipmentName, info.code equipmentCode,
        warn.iot_equipment_id iotEquipmentId,category.id categoryId, category.name categoryName, location.id locationId,
        location.name locationName, warn.level, warn.status, warn.warn_time, warn.repair_time,warn.remark
        FROM equipment_warn warn
        LEFT JOIN equipment_info info ON warn.iot_equipment_id = info.iot_equipment_id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN (
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" separator=" , ">
                    #{equipmentId}
                </foreach>
                )
            </if>
            <if test="null != param.status">
                AND warn.status = #{param.status}
            </if>
        </where>
        ORDER BY warn.create_time DESC
    </select>
    <select id="appPageList" resultType="cn.getech.ehm.equipment.dto.warn.EquipmentWarnAppDto">
        SELECT warn.id, info.id equipmentId, info.name equipmentName, info.code equipmentCode,
        category.name categoryName, category.pic_ids picIds,location.name locationName,
        warn.level, warn.status, warn.warn_time, warn.repair_time,warn.remark
        FROM equipment_warn warn
        LEFT JOIN equipment_info info ON warn.iot_equipment_id = info.iot_equipment_id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        <where>
            info.id IS NOT NULL
            <if test="param.keyword != null and param.keyword != ''">
                AND (
                info.name LIKE concat('%', #{param.keyword} , '%')
                OR info.code LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.equipmentId and ''!= param.equipmentId">
                AND info.id = #{param.equipmentId}
            </if>
            <if test="param.status != null">
                AND warn.status = #{param.status}
            </if>
            <if test="param.categoryIds != null and param.categoryIds.size() > 0">
                AND (
                <foreach collection="param.categoryIds" index="index" item="categoryId" separator=" OR ">
                    category.layer_code LIKE concat('%', #{categoryId} , '%')
                </foreach>
                )
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND info.id IN (
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" separator=" , ">
                    #{equipmentId}
                </foreach>
                )
            </if>
            <if test="param.locationIds != null and param.locationIds.size() > 0">
                AND (
                <foreach collection="param.locationIds" index="index" item="locationId" separator=" OR ">
                    location.layer_code LIKE concat('%', #{locationId} , '%')
                </foreach>
                )
            </if>
        </where>
        ORDER BY warn.create_time DESC
    </select>
    <select id="getWarnById" resultType="cn.getech.ehm.equipment.dto.warn.EquipmentWarnAppDto">
        SELECT warn.id, info.id equipmentId, info.name equipmentName, info.code equipmentCode,category.pic_ids picIds,
        warn.iot_equipment_id iotEquipmentId,category.id categoryId, category.name categoryName, location.id locationId,
        location.name locationName, warn.level, warn.status, warn.warn_time, warn.repair_time,warn.remark
        FROM equipment_warn warn
        LEFT JOIN equipment_info info ON warn.iot_equipment_id = info.iot_equipment_id
        LEFT JOIN equipment_category category ON info.category_id = category.id
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE warn.id = #{id}
    </select>

    <select id="getWarnListByEquipmentId" resultType="cn.getech.ehm.equipment.dto.warn.EquipmentWarnDto">
        SELECT warn.warn_time,warn.warn_time as warnTimeString, warn.repair_time,warn.repair_time as repairTimeString,warn.remark
        FROM equipment_warn warn, equipment_info e
        where ( warn.LEVEL = 2 or warn.LEVEL=1 )
        AND warn.iot_equipment_id = e.iot_equipment_id and e.id=#{equipmentId}
        <if test="beginTime != null and endTime != null">
            AND warn.create_time between #{beginTime} and #{endTime}
        </if>
        order by warn.warn_time
    </select>

</mapper>
