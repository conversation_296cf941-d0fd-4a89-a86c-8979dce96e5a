<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.SparePartsCategoryMapper">
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryDto">
        SELECT category.*, basic.name basicLibraryName
        FROM spare_parts_category category
        LEFT JOIN basic_library basic ON basic.id = category.basic_library_id
        <where>
            category.deleted = 0 AND category.type = #{param.type}
            <if test="param.name != null and param.name != ''">
                AND category.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
        </where>
        ORDER BY category.create_time DESC
    </select>
</mapper>
