<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.BearingDbMapper">
    <select id="pageList" resultType="cn.getech.ehm.equipment.dto.bearing.BearingDbDto">
        SELECT bearing.id, bearing.factory_id factoryId, factory.name factoryName, bearing.model_id modelId,
            model.name modelName, bearing.nb, bearing.bpfo, bearing.bpfi, bearing.fifi, bearing.fifo, bearing.bsf,
            bearing.contact_angle contactAngle
        FROM bearing_db bearing
        LEFT JOIN bearing_factory factory ON bearing.factory_id = factory.id
        LEFT JOIN bearing_model model ON bearing.model_id = model.id
        <where>
            bearing.deleted = 0
            <if test="null != param.factoryId and '' != param.factoryId">
                AND factory.id = #{param.factoryId}
            </if>
            <if test="null != param.factoryName and '' != param.factoryName">
                AND factory.name LIKE concat('%', #{param.factoryName} , '%')
            </if>
            <if test="null != param.modelId and '' != param.modelId">
                AND model.id = #{param.modelId}
            </if>
            <if test="null != param.modelName and '' != param.modelName">
                AND model.name LIKE concat('%', #{param.modelName} , '%')
            </if>
        </where>
        ORDER BY bearing.create_time DESC
    </select>
    <select id="getDtoById" resultType="cn.getech.ehm.equipment.dto.bearing.BearingDbDto">
        SELECT bearing.id, bearing.factory_id factoryId, factory.name factoryName, bearing.model_id modelId,
        model.name modelName, bearing.nb, bearing.bpfo, bearing.bpfi, bearing.fifi, bearing.fifo, bearing.bsf,
        bearing.contact_angle contactAngle
        FROM bearing_db bearing
        LEFT JOIN bearing_factory factory ON bearing.factory_id = factory.id
        LEFT JOIN bearing_model model ON bearing.model_id = model.id
        WHERE bearing.id = #{id}
    </select>
    <select id="mdbBearingList" resultType="cn.getech.ehm.equipment.dto.bearing.MdbBearingDbDto">
        SELECT * FROM bearingdb
        LEFT JOIN bearingfactory ON bearingfactory.FactoryID = bearingdb.FactoryID
    </select>
    <select id="getExcelList" resultType="cn.getech.ehm.equipment.dto.bearing.BearingDbExcelDto">
        SELECT factory.name factoryName, model.name modelName, bearing.nb, bearing.bpfo,
        bearing.bpfi, bearing.fifi, bearing.fifo, bearing.bsf,bearing.contact_angle contactAngle
        FROM bearing_db bearing
        LEFT JOIN bearing_factory factory ON bearing.factory_id = factory.id
        LEFT JOIN bearing_model model ON bearing.model_id = model.id
        <where>
            bearing.deleted = 0
            <if test="null != param.factoryId and '' != param.factoryId">
                AND factory.id = #{param.factoryId}
            </if>
            <if test="null != param.factoryName and '' != param.factoryName">
                AND factory.name LIKE concat('%', #{param.factoryName} , '%')
            </if>
            <if test="null != param.modelId and '' != param.modelId">
                AND model.id = #{param.modelId}
            </if>
            <if test="null != param.modelName and '' != param.modelName">
                AND model.name LIKE concat('%', #{param.modelName} , '%')
            </if>
        </where>
        ORDER BY bearing.id ASC
    </select>
    <select id="getDefaultDb" resultType="cn.getech.ehm.equipment.entity.bearing.BearingDb">
        SELECT * FROM bearing_db WHERE tenant_id = 'geek'
    </select>
    <select id="getDefaultModel" resultType="cn.getech.ehm.equipment.entity.bearing.BearingModel">
        SELECT * FROM bearing_model WHERE tenant_id = 'geek'
    </select>
    <select id="getDefaultFactory" resultType="cn.getech.ehm.equipment.entity.bearing.BearingFactory">
        SELECT * FROM bearing_factory WHERE tenant_id = 'geek'
    </select>
</mapper>
