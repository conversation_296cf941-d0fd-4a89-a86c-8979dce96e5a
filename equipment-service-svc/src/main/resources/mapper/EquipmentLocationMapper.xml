<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.EquipmentLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.equipment.entity.EquipmentLocation">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="type" property="type" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="layer_code" property="layerCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="pic_id" property="picId" />
        <result column="status_remark" property="statusRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        location.id,
        location.remark,
        location.create_by,
        location.create_time,
        location.update_by,
        location.update_time,
        location.pic_id,
        location.name,
        location.parent_id,
        location.type,
        location.province,
        location.city,
        location.area,
        location.address,
        location.longitude,
        location.latitude,
        location.layer_code,
        location.tenant_id,
        location.status_remark
    </sql>
    <select id="getLayerCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT layer_code FROM equipment_location WHERE id = #{id} AND deleted = 0
    </select>
    <select id="getListByLayerCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM equipment_location location
        WHERE deleted = 0 AND layer_code LIKE CONCAT(#{layerCode}, '%')
    </select>
    <update id="deleteById" parameterType="java.lang.String">
        UPDATE equipment_location SET deleted = 1 WHERE id = #{id}
    </update>
    <select id="getById" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.location.EquipmentLocationDto">
        SELECT
            <include refid="Base_Column_List"/>,parent.name parentName
        FROM equipment_location location
        LEFT JOIN equipment_location parent ON location.parent_id = parent.id
        WHERE location.id = #{id}
    </select>
    <select id="getListByParentId" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.location.EquipmentLocationTreeDto">
        SELECT id,name,layer_code layerCode,type,type as location_type,sort,parent_id FROM equipment_location
        <where>
            deleted = 0 AND parent_id = #{parentId}
            <if test="null != locationIds and locationIds.size() > 0">
                AND id IN
                <foreach collection="locationIds" index="index" item="locationId" open="(" close=")" separator=",">
                    #{locationId}
                </foreach>
            </if>
            <if test="null != keyword and '' != keyword">
                AND name LIKE CONCAT("%",#{param.keyword}, "%")
            </if>
        </where>
        ORDER BY sort desc,name ASC,create_time ASC
    </select>
    <select id="getTreeListByParentId" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto">
        SELECT id,name, parent_id parentId, type as locationType, 1 as type FROM equipment_location
        <where>
            deleted = 0 AND parent_id = #{parentId}
        </where>
        ORDER BY create_time ASC
    </select>
    <select id="getAllTreeList" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto">
        SELECT id,name, parent_id parentId, type as locationType, 1 as type FROM equipment_location
        WHERE deleted = 0
        <if test="null != locationIds and locationIds.size() > 0">
            AND id IN
            <foreach collection="locationIds" index="index" item="locationId" open="(" close=")" separator=",">
                #{locationId}
            </foreach>
        </if>
        ORDER BY sort desc,name ASC,create_time ASC
    </select>
    <select id="getBomDtoById" parameterType="java.lang.String" resultType="cn.getech.ehm.equipment.dto.info.BomParentDto">
        SELECT id,name, parent_id parentId, 1 as type FROM equipment_location
        <where>
            deleted = 0 AND id = #{id}
        </where>
        ORDER BY create_time ASC
    </select>
    <select id="getLocationIdByInfoIds" resultType="java.lang.String">
        SELECT distinct location.layer_code FROM equipment_location location
        LEFT JOIN equipment_info info ON location.id = info.parent_id
        <where>
            info.deleted = 0 AND location.deleted = 0
            AND info.id IN
            <foreach collection="param.equipmentIds" open="(" close=")" separator="," item="equipmentId" index="index">
                #{equipmentId}
            </foreach>
        </where>
    </select>
    <insert id="importCW">
        INSERT INTO equipment_location(id,name,parent_id,type,layer_code,deleted,tenant_id,
        create_by,create_time,update_by,update_time)
        <foreach collection="entities" open="VALUES" close=";" index="index" item="entity" separator=",">
            (#{entity.id},#{entity.name},#{entity.parentId},#{entity.type},#{entity.layerCode},
            #{entity.deleted},#{entity.tenantId},#{entity.createBy},#{entity.createTime},
            #{entity.updateBy},#{entity.updateTime})
        </foreach>
    </insert>
    <select id="getTenantRoot" resultType="cn.getech.ehm.equipment.entity.EquipmentLocation">
        SELECT id, tenant_id FROM equipment_location
        WHERE deleted = 0 AND parent_id = '0'
        AND tenant_id IN
        <foreach collection="tenantIds" open="(" close=")" separator="," item="tenantId" index="index">
            #{tenantId}
        </foreach>
    </select>
    <update id="updateByCodes">
        UPDATE equipment_location SET deleted = 1
        WHERE tenant_id = #{tenantId} AND code IN
        <foreach collection="codes" open="(" close=")" separator="," item="code" index="index">
            #{code}
        </foreach>
    </update>
    <select id="getInfoIdListByLocationId" resultType="cn.getech.ehm.equipment.dto.overview.CardAtlasCountDto">
        SELECT info.id equipmentId, location.id locationId, location.name locationName,
               substr(location.layer_code, 1, #{length}) locationLayerCode,info.running_status runningStatus, info.iot_status warnStatus
        FROM equipment_info info
        LEFT JOIN equipment_location location ON info.location_id = location.id
        WHERE info.deleted = 0
          <if test="childFlag">
              AND location.layer_code != #{parentLayerCode}
          </if>
          AND location.layer_code LIKE concat(#{parentLayerCode} , '%')
          <if test="null != equipmentIds and equipmentIds.size() > 0">
              AND info.id IN
              <foreach collection="equipmentIds" open="(" close=")" separator="," item="equipmentId" index="index">
                  #{equipmentId}
              </foreach>
          </if>
    </select>
</mapper>
