<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.equipment.mapper.RunningParameterMapper">
    <select id="getList" resultType="cn.getech.ehm.equipment.dto.runningparam.RunningParameterListDto">
        SELECT * FROM running_parameter
        <where>
            spare_parts_id = #{param.sparePartsId}
            <if test="null != param.keyword and '' != param.keyword">
                AND name LIKE CONCAT('%', #{param.keyword}, '%')
            </if>
            <if test="null != param.filterId and '' != param.filterId">
                AND id != #{param.filterId}
            </if>
        </where>
        ORDER BY sort ASC
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort), 0) FROM running_parameter WHERE spare_parts_id = #{sparePartsId}
    </select>
</mapper>
