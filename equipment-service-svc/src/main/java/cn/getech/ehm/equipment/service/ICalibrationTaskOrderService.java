package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.calibration.*;
import cn.getech.ehm.equipment.entity.CalibrationTaskOrder;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * 校准工单 服务类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ICalibrationTaskOrderService extends IBaseService<CalibrationTaskOrder> {

        /**
         * 分页查询，返回Dto
         *
         * @param calibrationTaskOrderQueryParam
         * @return
         */
        PageResult<CalibrationTaskOrderPageDto> pageDto(CalibrationTaskOrderQueryParam calibrationTaskOrderQueryParam);

        /**
         * 保存
         * @param calibrationTaskOrderAddParam
         * @return
         */
        String saveCalibration(CalibrationTaskOrderAddParam calibrationTaskOrderAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CalibrationTaskOrderDto getDtoById(String id);

        /**
         * 获取对比仪器仪表详细数据
         */
        RefEquipmentDto getRefEquipment(String equipmentId);

        /**
         * 更新
         * @param calibrationTaskOrderEditParam
         */
        boolean updateByParam(CalibrationTaskOrderEditParam calibrationTaskOrderEditParam);

        /**
         * 获取外校合格的设备ids
         * @return
         */
        List<String> getEquipmentIds();
}