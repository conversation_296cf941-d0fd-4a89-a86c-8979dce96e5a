package cn.getech.ehm.equipment.dto.special;

import cn.getech.ehm.common.util.excel.FormExcel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 特种设备列表
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentSpecialListDto", description = "特种设备列表")
public class EquipmentSpecialListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "部件id")
    private String structureId;

    @ApiModelProperty(value = "部件关联备件id")
    private String structurePartId;

    @ApiModelProperty(value = "部件位置")
    private String structureLocation;

    @ApiModelProperty(value = "部件父级备件id")
    private String structureParentPartId;

    @ApiModelProperty(value = "编码(设备/备件)")
    @FormExcel(name="编码", cellType = FormExcel.ColumnType.STRING)
    private String code;

    @ApiModelProperty(value = "名称(设备/部件-位置)")
    @FormExcel(name="名称", cellType = FormExcel.ColumnType.STRING)
    private String name;

    @ApiModelProperty(value = "设备位号")
    @FormExcel(name="位号", cellType = FormExcel.ColumnType.STRING)
    private String itemNo;

    @ApiModelProperty(value = "类型(1设备2部件)")
    private Integer type;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备/部件上级设备或部件)")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备/部件上级设备或部件))")
    @FormExcel(name="位置", cellType = FormExcel.ColumnType.STRING)
    private String parentName;

    @ApiModelProperty(value = "登记证号")
    @FormExcel(name="登记证号", cellType = FormExcel.ColumnType.STRING)
    private String certificateNo;

    @ApiModelProperty(value = "注册代码")
    @FormExcel(name="注册代码", cellType = FormExcel.ColumnType.STRING)
    private String registrationCode;

    @ApiModelProperty(value = "发证机关")
    @FormExcel(name="发证机关", cellType = FormExcel.ColumnType.STRING)
    private String issuingAuthority;

    @ApiModelProperty(value = "检验机构")
    @FormExcel(name="检验机构", cellType = FormExcel.ColumnType.STRING)
    private String inspectionOrg;

    @ApiModelProperty(value = "使用单位")
    @FormExcel(name="使用单位", cellType = FormExcel.ColumnType.STRING)
    private String useOrg;

    @ApiModelProperty(value = "检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @FormExcel(name="检验日期", dateFormat = "yyyy-MM-dd", cellType = FormExcel.ColumnType.STRING)
    private Date inspectionDate;

    @ApiModelProperty(value = "下次检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @FormExcel(name="下次检验日期", dateFormat = "yyyy-MM-dd", cellType = FormExcel.ColumnType.STRING)
    private Date nextInspectionDate;

    @ApiModelProperty(value = "检验报告编号")
    @FormExcel(name="检验报告编号", cellType = FormExcel.ColumnType.STRING)
    private String reportNo;

    @ApiModelProperty("责任人id")
    private String dutyUserId;

    private String dutyUserName;

    @ApiModelProperty("提前提醒天数")
    private Integer deadlineDays;

    private Boolean isOverTime;

}