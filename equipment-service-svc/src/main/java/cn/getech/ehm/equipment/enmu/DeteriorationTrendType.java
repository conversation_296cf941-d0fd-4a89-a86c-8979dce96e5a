package cn.getech.ehm.equipment.enmu;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 劣化趋势枚举
 */
public enum DeteriorationTrendType {
    STABLE(1,"平稳", "运行稳定，未出现明显劣化"),
    OSCILLATORY(2,"振荡波动", "运行状态和振动趋势在区间内有明显的起伏"),
    SLOW_DETERIORATION(3,"缓慢劣化", "设备劣化趋势缓慢，需要关注处理"),
    FAST_DETERIORATION(4,"快速劣化", "设备劣化趋势加速，需立即检查处理"),
    UN_KNOW(5,"未知", "由于数据中断或者异常，影响状态评估");


    DeteriorationTrendType(int value, String name, String content) {
        this.value = value;
        this.name = name;
        this.content = content;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (DeteriorationTrendType sourceType : DeteriorationTrendType.values()) {
                if (sourceType.getValue() == value) {
                    return sourceType.getName();
                }
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(DeteriorationTrendType sourceType : DeteriorationTrendType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(sourceType.value);
            enumListDto.setName(sourceType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    private String content;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() { return content; }

    public void setContent(String content) { this.content = content; }
}
