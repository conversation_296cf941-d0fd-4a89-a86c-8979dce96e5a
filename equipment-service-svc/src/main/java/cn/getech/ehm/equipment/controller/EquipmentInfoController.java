package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.bearing.BearingDbDto;
import cn.getech.ehm.equipment.dto.bearing.BearingDbQueryParam;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.category.StructureSortDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoEditParam;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.iot.StudioEditParam;
import cn.getech.ehm.equipment.dto.iot.StudioQueryParam;
import cn.getech.ehm.equipment.dto.iot.StudioResDetailDto;
import cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.enmu.RemainingLifeOperationType;
import cn.getech.ehm.equipment.enums.HealthStatusType;
import cn.getech.ehm.equipment.enums.RunningStatusType;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.equipment.dto.OeeDetailListDto;
import cn.getech.ehm.equipment.dto.OeeQueryParam;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.*;

import cn.getech.ehm.equipment.dto.info.EquipmentInfoQueryParam;

/**
 * 设备控制器
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/equipmentInfo")
@Api(tags = "设备服务接口")
@Slf4j
public class EquipmentInfoController {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IBearingDbService bearingDbService;
    @Autowired
    private IBearingFactoryService factoryService;
    @Autowired
    private IBearingModelService modelService;
    @Autowired
    private IEquipmentInfoSpecialService specialService;
    @Autowired
    private IEquipmentCategoryService categoryService;
    @Autowired
    private IEquipmentStructureService structureService;
    @Autowired
    private PartClient partClient;
    @Autowired
    private IStructureSpareService structureSpareService;
    @Autowired
    private IInfoStructureParameterService structureParameterService;

    /**
     * 分页获取设备列表
     */
    @ApiOperation("分页获取设备列表")
    @PostMapping("/list")
    //@Permission("equipment:list")
    public RestResponse<PageResult<EquipmentInfoListDto>> pageList(@RequestBody @Valid EquipmentInfoQueryParam equipmentInfoQueryParam) {
        return RestResponse.ok(equipmentInfoService.pageDto(equipmentInfoQueryParam));
    }

    @ApiOperation("批量更新设备参数")
    @PostMapping("/batch/update")
    //@Permission("equipment:list")
    public RestResponse<Boolean> batchUpdate(@RequestBody @Valid EquipmentInfoBatchUpdateParam equipmentInfoBatchUpdateParam) {
        return RestResponse.ok(equipmentInfoService.batchUpdate(equipmentInfoBatchUpdateParam));
    }

    /**
     * 弹框分页获取设备详细信息
     */
    @ApiOperation("弹框分页获取设备详细信息")
    @PostMapping("/detailList")
    public RestResponse<PageResult<EquipmentInfoDetailDto>> detailList(@RequestBody DetailQueryParam queryParam) {
        return RestResponse.ok(equipmentInfoService.detailList(queryParam));
    }

    /**
     * 获取当前节点下设备运行统计
     * 所选位置则主设备统计
     * 所选设备则下一级统计
     */
    @ApiOperation("获取当前节点下设备运行统计")
    @PostMapping("/getRunningStatus")
    //@Permission("equipment:list")
    public RestResponse<List<RunningStatusDto>> getRunningStatus(@RequestBody @Valid StatusSearchDto statusSearchDto) {
        return RestResponse.ok(equipmentInfoService.getRunningStatus(statusSearchDto));
    }

    /**
     * 设备结构树
     */
    @ApiOperation("设备结构树")
    @GetMapping("/equipmentTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父节点id(ROOT节点为0)", dataType = "string", paramType = "query", required = true)
    })
    public RestResponse<List<LocationEquipmentTreeDto>> equipmentTree(@RequestParam("parentId") String parentId) {
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        //位置根节点都是0，不同租户生成不同缓存
        return RestResponse.ok(equipmentInfoService.equipmentTree(parentId, userBaseInfo.getTenantId()));
    }

    /**
     * 懒加载设备结构树
     */
    @ApiOperation("懒加载设备结构树")
    @GetMapping("/lazyEquipmentTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父节点id(ROOT节点为0)", dataType = "string", paramType = "query", required = true),
            @ApiImplicitParam(name = "type", value = "类型(1位置11主设备12子设备)", dataType = "int", paramType = "query", required = true)
    })
    public RestResponse<List<LazyEquipmentTreeDto>> lazyEquipmentTree(@RequestParam String parentId, @RequestParam Integer type) {
        return RestResponse.ok(equipmentInfoService.lazyEquipmentTree(parentId, type, null));
    }

    /**
     * 清理设备树缓存
     */
    @ApiOperation("清理设备树缓存")
    @GetMapping("/clearTreeCache")
    public RestResponse<Boolean> clearTreeCache() {
        return RestResponse.ok(equipmentInfoService.clearCache());
    }

    /**
     * 设备结构树新增
     */
    @ApiOperation("设备结构树新增")
    @PostMapping("/equipmentTreeAdd")
    public RestResponse<String> equipmentTreeAdd(@RequestBody EquipmentTreeEditDto editDto) {
        return RestResponse.ok(equipmentInfoService.equipmentTreeAdd(editDto));
    }

    /**
     * 设备结构树修改
     */
    @ApiOperation("设备结构树修改")
    @PostMapping("/equipmentTreeEdit")
    public RestResponse<Boolean> equipmentTreeEdit(@RequestBody EquipmentTreeEditDto editDto) {
        return RestResponse.ok(equipmentInfoService.equipmentTreeEdit(editDto));
    }

    /**
     * 设备结构树查询
     */
    @ApiOperation("设备结构树查询(只查询设备)")
    @PostMapping("/searchTree")
    public RestResponse<List<EquipmentTreeDto>> searchTree(@RequestBody RelSearchDto relSearchDto) {
        return RestResponse.ok(equipmentInfoService.searchTree(relSearchDto));
    }

    /**
     * 参数监测设备结构树
     */
    @ApiOperation("参数监测设备结构树")
    @PostMapping("/paramEquipmentTree")
    public RestResponse<List<EquipmentTreeDto>> paramEquipmentTree(@RequestBody RelSearchDto relSearchDto) {
        return RestResponse.ok(equipmentInfoService.paramEquipmentTree(relSearchDto));
    }

    /**
     * 设备Bom树
     */
    @ApiOperation("设备Bom树")
    @GetMapping("/equipmentBomTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentId", value = "设备id", dataType = "string", paramType = "query", required = true),
    })
    public RestResponse<EquipmentBomTreeDto> equipmentBomTree(@RequestParam("equipmentId") String equipmentId) {
        return RestResponse.ok(equipmentInfoService.equipmentBomTree(equipmentId));
    }

    /**
     * 分页获取设备列表-计划视图
     */
    @ApiOperation("分页获取设备列表-计划视图")
    @PostMapping("/listPlanView")
    //@Permission("equipment:list")
    public RestResponse<PageResult<EquipmentInfoPlanViewDto>> pagePlanViewList(@RequestBody @Valid EquipmentInfoPlanViewQueryParam equipmentInfoPlanViewQueryParam) {
        EquipmentInfoQueryParam equipmentInfoQueryParam = new EquipmentInfoQueryParam();
        BeanUtils.copyProperties(equipmentInfoPlanViewQueryParam, equipmentInfoQueryParam);
        PageResult<EquipmentInfoListDto> equipmentInfoDtoPageResult = equipmentInfoService.pageDto(equipmentInfoQueryParam);
        List<EquipmentInfoPlanViewDto> list = new ArrayList<>();
        equipmentInfoDtoPageResult.getRecords().forEach(equipmentInfoDto -> {
            EquipmentInfoPlanViewDto dto = new EquipmentInfoPlanViewDto();
            BeanUtils.copyProperties(equipmentInfoDto, dto);
            list.add(dto);
        });

        return RestResponse.ok(Optional.ofNullable(PageResult.<EquipmentInfoPlanViewDto>builder()
                        .records(list)
                        .total(equipmentInfoDtoPageResult.getTotal())
                        .build())
                .orElse(new PageResult<>()));
    }

    /**
     * 分页获取设备列表
     */
    @ApiOperation("获取主设备列表（小窗口查询用）")
    @PostMapping("/mainInfoList")
    public RestResponse<PageResult<EquipmentTreeDetailDto>> mainInfoList(@RequestBody InfoSearchDto param) {
        return RestResponse.ok(equipmentInfoService.listPageDto(param));
    }

    /**
     * 获取设备、子设备列表
     */
    @ApiOperation("获取设备、子设备列表(小窗口查询用)")
    @GetMapping("/secondaryInfoList")
    public RestResponse<List<EquipmentTreeDetailDto>> secondaryInfoList(@RequestParam("parentId") String parentId) {
        return RestResponse.ok(equipmentInfoService.secondaryInfoList(parentId));
    }

    /**
     * 新增设备
     */
    @ApiOperation("新增设备")
    @AuditLog(title = "设备", desc = "新增设备", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:update")
    public RestResponse<String> add(@RequestBody @Valid EquipmentInfoAddParam equipmentInfoAddParam) {
        return RestResponse.ok(equipmentInfoService.saveByParam(equipmentInfoAddParam));
    }

    /**
     * 修改设备
     */
    @ApiOperation(value = "修改设备")
    @AuditLog(title = "设备", desc = "修改设备", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:update")
    public RestResponse<Boolean> update(@RequestBody @Valid EquipmentInfoEditParam equipmentInfoEditParam) {
        return RestResponse.ok(equipmentInfoService.updateByParam(equipmentInfoEditParam));
    }

    /**
     * 根据id删除设备
     */
    @ApiOperation(value = "根据id删除设备")
    @AuditLog(title = "设备", desc = "设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("equipment:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(equipmentInfoService.removeByIds(ids));
    }

    /**
     * 根据id获取设备
     */
    @ApiOperation(value = "根据id获取设备")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<EquipmentInfoDto> get(@PathVariable String id) {
        return RestResponse.ok(equipmentInfoService.getDtoById(id));
    }

    @ApiOperation(value = "根据positionTagId获取设备")
    @GetMapping(value = "/positionTagId/{id}")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<EquipmentInfoDto> getByPositionTagId(@PathVariable String id) {
        return RestResponse.ok(equipmentInfoService.getDtoByPositionTagId(id));
    }

    /**
     * 根据id获取设备详情
     */
    @ApiOperation(value = "根据id获取设备详情")
    @GetMapping(value = "/getEquipmentInfoById")
    public RestResponse<EquipmentInfoDto> getEquipmentInfo(@RequestParam String id) {
        return RestResponse.ok(equipmentInfoService.getDtoById(id));
    }

    /**
     * 根据id获取设备文档
     */
    @ApiOperation(value = "根据id获取设备文档")
    @GetMapping(value = "/getDocByInfoId")
    //@Permission("equipment:list")
    public RestResponse<EquipmentDocDto> getDocByInfoId(@RequestParam String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getDocByInfoId(equipmentId));
    }

    /**
     * 修改设备文档
     */
    @ApiOperation(value = "修改设备文档")
    @PostMapping(value = "/updateDoc")
    //@Permission("equipment:list")
    public RestResponse<Boolean> updateDoc(@RequestBody EquipmentDocEditParam editParam) {
        return RestResponse.ok(equipmentInfoService.updateDoc(editParam));
    }

    /**
     * 生成二维码图片
     */
    @ApiOperation(value = "生成二维码图片")
    @PostMapping(value = "/qr-code/{ids}")
    //@Permission("equipment:list")
    public RestResponse<List<EquipmentInfoDto>> createQrCode(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(equipmentInfoService.createQrCodeByIds(ids));
    }

    /**
     * 获取二维码对应图片、名称
     */
    @ApiOperation(value = "获取二维码对应图片、名称")
    @PostMapping(value = "/qrLabel")
    //@Permission("equipment:list")
    public RestResponse<EquipmentLabelDto> qrLabel() {
        return RestResponse.ok(equipmentInfoService.qrLabel());
    }

    /**
     * 导出设备列表
     */
    @ApiOperation(value = "导出设备列表(按照类型，扩展属性做表头)")
    @AuditLog(title = "设备", desc = "导出设备列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void excelExport(@RequestBody EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentInfoExcel> util = new FormExcelUtils<>(EquipmentInfoExcel.class);
        util.exportExcel(equipmentInfoService.getExcelList(queryParam), "设备导出", response,
                equipmentInfoService.getShowFields(true), equipmentInfoService.getComboMap(), categoryService.getPropList(queryParam.getCategoryId(), true));

    }

    /**
     * 导出设备列表模板
     * 导出的为台账配置中已展示的字段
     */
    @ApiOperation(value = "导入模板(按照类型，扩展属性做表头)")
    @AuditLog(title = "设备", desc = "导出设备列表模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportModel")
    public void exportModel(@RequestBody EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentSpecialExcel> util = new FormExcelUtils<>(EquipmentSpecialExcel.class);
        util.exportExcel(new ArrayList<>(), "设备导入模板", response,
                equipmentInfoService.getShowFields(true), equipmentInfoService.getComboMap(), categoryService.getPropList(queryParam.getCategoryId(), true));
    }

    /**
     * Excel导入设备(按照类型，扩展属性做表头)
     * 导入的设备不会生成oee/状态参数，减少iot服务压力
     */
    @ApiOperation(value = "Excel导入设备(按照类型，扩展属性做表头)")
    @AuditLog(title = "设备", desc = "Excel导入设备(按照类型，扩展属性做表头)", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("equipment:import")
    public RestResponse<String> excelImport(@RequestPart("file") MultipartFile file) {
        FormExcelUtils<EquipmentSpecialExcel> util = new FormExcelUtils<>(EquipmentSpecialExcel.class);
        List<EquipmentSpecialExcel> rows = util.importExcel(file, equipmentInfoService.getShowFields(true));
        return RestResponse.ok(equipmentInfoService.excelImport(rows, true));
    }

    /**
     * Excel导入设备(扩展属性为json格式)
     * 导入的设备不会生成oee/状态参数，减少iot服务压力
     */
    @ApiOperation(value = "Excel导入设备(扩展属性为json格式)")
    @AuditLog(title = "设备", desc = "Excel导入设备(扩展属性为json格式)", businessType = BusinessType.INSERT)
    @PostMapping("/importPropJson")
    //@Permission("equipment:import")
    public RestResponse<String> importPropJson(@RequestPart("file") MultipartFile file) {
        FormExcelUtils<EquipmentImportJsonExcel> util = new FormExcelUtils<>(EquipmentImportJsonExcel.class);
        List<EquipmentImportJsonExcel> rows = util.importExcel(file, equipmentInfoService.getShowFields(true));
        return RestResponse.ok(equipmentInfoService.importPropJson(rows));
    }

    /**
     * 导出设备列表模板(扩展属性为json格式)
     */
    @ApiOperation(value = "导出设备列表模板(扩展属性为json格式)")
    @AuditLog(title = "设备", desc = "导出设备列表模板", businessType = BusinessType.EXPORT)
    @PostMapping("/exportPropJsonModel")
    public void exportPropJsonModel(@RequestBody EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentImportJsonExcel> util = new FormExcelUtils<>(EquipmentImportJsonExcel.class);
        util.exportExcel(new ArrayList<>(), "设备导入模板(扩展属性为json格式)", response,
                equipmentInfoService.getShowFields(true), equipmentInfoService.getComboMap(), null);

    }

    /**
     * 导出设备列表(无扩展属性)
     */
    @ApiOperation(value = "导出设备列表(无扩展属性)")
    @AuditLog(title = "设备", desc = "导出设备列表(无扩展属性)", businessType = BusinessType.EXPORT)
    @PostMapping("/exportPropJson")
    public void exportPropJson(@RequestBody EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentExportJsonExcel> util = new FormExcelUtils<>(EquipmentExportJsonExcel.class);
        util.exportExcel(equipmentInfoService.getExcelInfoList(queryParam), "设备列表", response,
                equipmentInfoService.getShowFields(true), equipmentInfoService.getComboMap(), null);

    }

    /**
     * 根据设备名称模糊查询设备id
     *
     * @param equipmentName
     * @return
     */
    @GetMapping("/getInfosIdByName")
    public RestResponse<List<String>> getInfoIdsByName(@RequestParam(value = "equipmentName",required = false) String equipmentName) {
        return RestResponse.ok(equipmentInfoService.getInfoIdsByName(equipmentName));
    }

    /**
     * 校验是否被设备使用
     *
     * @param relatedId 相关id
     * @return
     */
    @AuditLog(title = "校验是否被设备使用", desc = "校验是否被设备使用", businessType = BusinessType.INSERT)
    @GetMapping("/checkSupplierUsed")
    public RestResponse<Boolean> checkSupplierUsed(String relatedId) {
        return RestResponse.ok(equipmentInfoService.checkSupplierUsed(relatedId));
    }

    /**
     * 根据名称和编码检查设备是否存在
     *
     * @param code
     * @param name
     * @return
     */
    @AuditLog(title = "校验设备是否存在", desc = "校验是否存在", businessType = BusinessType.INSERT)
    @GetMapping("/checkExists")
    public RestResponse<Boolean> checkUsed(@RequestParam("code") String code, @RequestParam(value = "name", required = false) String name) {
        return RestResponse.ok(equipmentInfoService.checkExists(code, name));
    }

    /**
     * 校验备件是否被设备相关使用
     *
     * @param partIds
     * @return
     */
    @AuditLog(title = "校验备件是否被设备相关使用", desc = "校验备件是否被设备相关使用", businessType = BusinessType.QUERY)
    @PostMapping("/checkPartUsed")
    public RestResponse<List<String>> checkPartUsed(@RequestBody String[] partIds) {
        return RestResponse.ok(equipmentInfoService.checkPartUsed(partIds));
    }

    /**
     * 根据id集合获取设备列表
     *
     * @param equipmentIds
     * @returnmainInfoList
     */
    @PostMapping("/getListByIds")
    RestResponse<Map<String, EquipmentListDto>> getListByIds(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(equipmentInfoService.getListByIds(equipmentIds));
    }

    /**
     * 根据id集合获取设备维护人员列表
     *
     * @param equipmentIds
     * @return
     */
    @PostMapping("/getMaintainerListByIds")
    RestResponse<List<EquipmentMaintainerDto>> getMaintainerListByIds(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(equipmentInfoService.getMaintainerListByIds(equipmentIds));
    }

    @ApiOperation("根据查询条件获取设备ids")
    @PostMapping("/getEquipmentIdsByParam")
    //@Permission("equipment:list")
    public RestResponse<List<String>> getEquipmentIdsByParam(@RequestBody EquipmentInfoSearchDto param) {
        return RestResponse.ok(equipmentInfoService.getEquipmentIdsByParam(param));
    }

    @ApiOperation("根据查询条件获取设备ids无权限")
    @PostMapping("/getEquipmentIdsByParamNonAuth")
    //@Permission("equipment:list")
    public RestResponse<List<String>> getEquipmentIdsByParamNonAuth(@RequestBody EquipmentInfoSearchDto param) {
        return RestResponse.ok(equipmentInfoService.getEquipmentIdsByParamNonAuth(param));
    }

    /**
     * 根据类型ids、位置ids获取设备信息集合
     */
    @ApiOperation("根据类型ids、位置ids获取设备信息集合")
    @PostMapping("/getEquipmentStatistics")
    public RestResponse<Map<String, EquipmentStatisticsDto>> getEquipmentStatistics(@RequestBody InfoSearchDto param) {
        return RestResponse.ok(equipmentInfoService.getEquipmentStatistics(param));
    }

    /**
     * 获取组态列表
     */
    @ApiOperation("获取组态列表")
    @PostMapping("/getStudioList")
    @AuditLog(title = "设备", desc = "获取组态列表", businessType = BusinessType.QUERY)
    RestResponse<PageResult<StudioResDetailDto>> getStudioList(@RequestBody StudioQueryParam studioQueryParam) {
        return RestResponse.ok(equipmentInfoService.getStudioList(studioQueryParam));
    }

    /**
     * 根据id获取设备组态信息
     */
    @ApiOperation(value = "根据id获取设备组态信息")
    @GetMapping(value = "/studio/{id}")
    //@Permission("equipment:list")
    public RestResponse<EquipmentStudioDto> getStudioByInfoId(@PathVariable String id) {
        return RestResponse.ok(equipmentInfoService.getStudioByInfoId(id));
    }

    /**
     * 根据id获取设备组态信息
     */
    @ApiOperation(value = "根据id获取设备iot组态")
    @GetMapping(value = "/pcStudioUrl/{id}")
    //@Permission("equipment:list")
    public RestResponse<String> pcStudioUrl(@PathVariable String id) {
        return RestResponse.ok(equipmentInfoService.pcStudioUrl(id));
    }

    /**
     * 编辑设备组态
     */
    @ApiOperation("编辑设备组态")
    @PostMapping("/updateInfoStudio")
    @AuditLog(title = "设备", desc = "编辑设备组态", businessType = BusinessType.UPDATE)
    RestResponse<Boolean> updateInfoStudio(@RequestBody StudioEditParam studioEditParam) {
        return RestResponse.ok(equipmentInfoService.updateInfoStudio(studioEditParam));
    }

    /**
     * 判断是否客户/工程师/审核用户
     */
    @ApiOperation("判断是否客户/工程师/审核用户")
    @GetMapping("/getClientEquipmentIds")
    public RestResponse<CheckEquipmentIdDto> getClientEquipmentIds() {
        return RestResponse.ok(equipmentInfoService.getClientEquipmentIds());
    }

    /**
     * 根据设备总数(供license校验设备数)
     *
     * @return
     */
    @GetMapping(value = "/getEquipmentTotal")
    public RestResponse<Integer> getEquipmentTotal() {
        return RestResponse.ok(equipmentInfoService.getTotalCount());
    }

    @ApiOperation(value = "获取设备概要信息列表")
    @PostMapping("/getSummaryMap")
    RestResponse<Map<String, EquipmentSummaryDto>> getSummaryMap(@RequestBody EquipmentInfoSearchDto searchDto) {
        return RestResponse.ok(equipmentInfoService.getSummaryMap(searchDto));
    }

    @ApiOperation(value = "分页获取轴承列表")
    @PostMapping(value = "/bearingPageList")
    public RestResponse<PageResult<BearingDbDto>> bearingPageList(@RequestBody BearingDbQueryParam queryParam) {
        return RestResponse.ok(bearingDbService.pageDto(queryParam));
    }

    @ApiOperation(value = "获取轴承生产厂家列表")
    @GetMapping(value = "/factoryList")
    public RestResponse<List<FactoryModelDto>> factoryList(String keyword) {
        return RestResponse.ok(factoryService.getList(keyword));
    }

    @ApiOperation(value = "获取轴承型号列表")
    @GetMapping(value = "/modelList")
    public RestResponse<List<FactoryModelDto>> modelList(String keyword) {
        return RestResponse.ok(modelService.getList(keyword));
    }

    /**
     * 分页获取特种设备列表
     */
    @ApiOperation("分页获取特种设备列表")
    @PostMapping("/specialList")
    //@Permission("equipment:list")
    public RestResponse<PageResult<EquipmentSpecialListDto>> specialPageList(@RequestBody EquipmentSpecialQueryParam queryParam) {
        return RestResponse.ok(specialService.pageDto(queryParam));
    }

    /**
     * 获取特种设备类型集合
     */
    @ApiOperation("获取特种设备类型集合")
    @GetMapping("/specialCategoryList")
    //@Permission("equipment:list")
    public RestResponse<List<SpecialInfoCategoryDto>> specialCategoryList() {
        return RestResponse.ok(specialService.specialCategoryList());
    }

    /**
     * 导出特种设备列表
     */
    @ApiOperation(value = "导出特种设备列表")
    @AuditLog(title = "设备", desc = "导出特种设备列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSpecial")
    public void exportSpecial(@RequestBody EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        if (null != queryParam.getType() && queryParam.getType() == StaticValue.TWO) {
            //备件扩展属性名称集合
            List<String> propNames = new ArrayList<>();
            RestResponse<List<String>> restResponse = partClient.getPropNameList(queryParam.getCategoryId());
            if (!restResponse.isOk()) {
                log.error("远程调用part-service出错");

            } else {
                propNames = restResponse.getData();
            }
            FormExcelUtils<SpecialStructureExcelDto> util = new FormExcelUtils<>(SpecialStructureExcelDto.class);
            util.exportExcel(specialService.getSpecialStructureList(queryParam), "特种设备导出", response,
                    null, null, propNames);
        } else {
            FormExcelUtils<EquipmentSpecialExcel> util = new FormExcelUtils<>(EquipmentSpecialExcel.class);
            util.exportExcel(equipmentInfoService.getSpecialExcelList(queryParam), "特种设备导出", response,
                    equipmentInfoService.getShowFields(true), equipmentInfoService.getComboMap(), categoryService.getPropList(queryParam.getCategoryId(), true));
        }
    }

    /**
     * 特种设备导出
     */
    @ApiOperation(value = "特种设备导出")
    @AuditLog(title = "设备", desc = "特种设备导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export/specialEq")
    public void exportSpecialEq(@RequestBody @Valid EquipmentSpecialQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentSpecialListDto> util = new FormExcelUtils<>(EquipmentSpecialListDto.class);
        util.exportExcel(specialService.exportSpecialEq(queryParam), "特种设备导出", response,
                null, null, null);
    }

    /**
     * 特种设备导出数量
     */
    @ApiOperation(value = "特种设备导出数量")
    @AuditLog(title = "设备", desc = "特种设备导出数量", businessType = BusinessType.EXPORT)
    @PostMapping("/export/specialEqNum")
    public RestResponse<BigDecimal> exportSpecialEqNum(@RequestBody @Valid EquipmentSpecialQueryParam queryParam) {
        List<EquipmentSpecialListDto> list = specialService.exportSpecialEq(queryParam);
        if (CollectionUtils.isEmpty(list)) {
            return RestResponse.ok(new BigDecimal(0));
        }
        return RestResponse.ok(BigDecimal.valueOf(list.size()));
    }

    @ApiOperation(value = "获取设备最顶级主设备id")
    @GetMapping(value = "/getRootInfoId")
    public RestResponse<String> getRootInfoId(String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getRootInfoId(equipmentId));
    }

    /**
     * 新增设备零部件
     */
    @ApiOperation("新增设备零部件")
    @AuditLog(title = "设备表", desc = "新增设备零部件", businessType = BusinessType.INSERT)
    @PostMapping("/addStructure")
    public RestResponse<String> addStructure(@RequestBody EquipmentStructureAddParam addParam) {
        return RestResponse.ok(structureService.addStructure(addParam));
    }

    /**
     * 编辑设备零部件
     */
    @ApiOperation("编辑设备零部件")
    @AuditLog(title = "设备表", desc = "编辑设备零部件", businessType = BusinessType.UPDATE)
    @PutMapping("/editStructure")
    public RestResponse<Boolean> editStructure(@RequestBody EquipmentStructureEditParam editParam) {
        return RestResponse.ok(structureService.editStructure(editParam));
    }

    /**
     * 删除设备零部件
     */
    @ApiOperation("删除设备零部件")
    @AuditLog(title = "设备表", desc = "删除设备结构表", businessType = BusinessType.UPDATE)
    @DeleteMapping("/delStructure/{id}")
    public RestResponse<Boolean> delStructure(@PathVariable String id) {
        return RestResponse.ok(structureService.deleteStructure(id));
    }

    /**
     * 获取设备零部件
     */
    @ApiOperation("获取设备零部件")
    @GetMapping("/getStructureByEquipmentId")
    public RestResponse<List<EquipmentStructureDto>> getStructureByEquipmentId(@RequestParam String equipmentId,@RequestParam(value = "keyword",required = false)String keyword) {
        return RestResponse.ok(structureService.getListByEquipmentId(equipmentId,keyword));
    }

    @ApiOperation("重新计算部件排序")
    @PostMapping("/updateStructureSort")
    public RestResponse<Boolean> updateStructureSort(@RequestBody List<StructureSortDto> dtos) {
        return RestResponse.ok(structureService.updateStructureSort(dtos));
    }

    @ApiOperation("获取零部件对应名称")
    @PostMapping("/getStructureNameMap")
    public RestResponse<Map<String, String>> getStructureNameMap(@RequestBody String[] structureIds) {
        return RestResponse.ok(structureService.getStructureNameMap(structureIds));
    }

    /**
     * 获取特征参数频率图表
     */
    @ApiOperation("获取特征参数频率图表")
    @GetMapping("/getFeatureParameterFFT")
    public RestResponse<List<List<String>>> getFeatureParameterFFT(@RequestParam String equipmentId, @RequestParam Double rpm) {
        return RestResponse.ok(structureService.getFeatureParameterFFT(equipmentId, rpm));
    }

    /**
     * 获取特征参数阶次图表
     */
    @ApiOperation("获取特征参数阶次图表")
    @GetMapping("/getFeatureParameterOS")
    public RestResponse<List<List<String>>> getFeatureParameterOS(@RequestParam String equipmentId) {
        return RestResponse.ok(structureService.getFeatureParameterOS(equipmentId));
    }

    /**
     * 获取监测中的特种参数
     */
    @ApiOperation("获取监测中的特种参数")
    @GetMapping("/getFeatureParameter")
    public RestResponse<List<FeatureParameterDto>> getFeatureParameter(@RequestParam String equipmentId) {
        return RestResponse.ok(structureService.getFeatureParameter(equipmentId));
    }

    /**
     * 获取下拉框设备部件
     */
    @ApiOperation("获取下拉框设备部件")
    @GetMapping("/getRootStructureDetail")
    public RestResponse<List<EquipmentStructureDetailDto>> getRootStructureDetail(@RequestParam String equipmentId) {
        return RestResponse.ok(structureService.getRootStructureDetail(equipmentId));
    }

    /**
     * 根据id集合获取设备部件名称集合
     */
    @ApiOperation("根据id集合获取设备部件名称集合")
    @PostMapping("/getStructureMapByIds")
    public RestResponse<Map<String, String>> getStructureMapByIds(@RequestBody String[] structureIds) {
        return RestResponse.ok(structureService.getStructureMapByIds(structureIds));
    }

    /**
     * 获取设备零部件参数
     */
    @ApiOperation("获取设备零部件参数")
    @GetMapping("/getStructureParameter")
    public RestResponse<StructureParameterResDto> getStructureParameter(@RequestParam String structureId) {
        return RestResponse.ok(structureParameterService.getStructureParameter(structureId));
    }

    /**
     * 修改设备零部件参数值
     */
    @ApiOperation("修改设备零部件参数值")
    @PostMapping("/updateStructureParameter")
    public RestResponse<Boolean> updateStructureParameter(@RequestBody StructureParameterResDto dto) {
        return RestResponse.ok(structureParameterService.updateStructureParameter(dto));
    }

    /**
     * 计算参数值
     */
    @ApiOperation("计算参数值")
    @GetMapping("/countStructureParameter")
    public RestResponse<BigDecimal> countStructureParameter(@RequestParam("structureParameterId") String structureParameterId) {
        return RestResponse.ok(structureParameterService.countStructureParameter(structureParameterId));
    }

    /**
     * 引用设备类型结构
     */
    @ApiOperation("引用设备类型结构")
    @GetMapping("/quoteCategoryStructure")
    public RestResponse<Boolean> quoteCategoryStructure(@RequestParam String equipmentId,@RequestParam String categoryId) {
        return RestResponse.ok(structureService.quoteCategoryStructure(equipmentId, categoryId));
    }

    @ApiOperation("引用选择的设备类型结构")
    @PostMapping("/quoteSelectCategoryStructure")
    public RestResponse<Map<String, String>> quoteSelectCategoryStructure(@RequestBody QuoteCategoryStructureDto dto) {
        return RestResponse.ok(structureService.quoteSelectCategoryStructure(dto));
    }

    /**
     * 编辑设备转速
     */
    @ApiOperation("编辑设备转速")
    @PostMapping("/updateRate")
    public RestResponse<Boolean> updateEquipmentRate(@RequestBody EquipmentRateDto equipmentRateDto) {
        return RestResponse.ok(equipmentInfoService.updateEquipmentRate(equipmentRateDto));
    }

    /**
     * 查询设备转速
     */
    @ApiOperation("查询设备转速")
    @GetMapping("/getRate")
    public RestResponse<EquipmentRateDto> getRate(@RequestParam String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getRate(equipmentId));
    }

    /**
     * 编辑结构特种设备数据
     */
    @ApiOperation("编辑结构特种设备数据")
    @AuditLog(title = "设备表", desc = "编辑结构特种设备数据", businessType = BusinessType.INSERT)
    @PostMapping("/editStructureSpecial")
    public RestResponse<String> editStructureSpecial(@RequestBody EquipmentSpecialDto editParam) {
        return RestResponse.ok(specialService.saveOrUpdateByParam(editParam, editParam.getEquipmentId()));
    }

    /**
     * 编辑结构零件数据
     */
    @ApiOperation("编辑结构零件数据")
    @AuditLog(title = "设备表", desc = "编辑结构零件数据", businessType = BusinessType.INSERT)
    @PostMapping("/editStructureSpare")
    public RestResponse<String> editStructureSpare(@RequestBody StructureSpareDto spareDto) {
        return RestResponse.ok(structureSpareService.editStructureSpare(spareDto));
    }

    /**
     * 获取结构零件数据
     */
    @ApiOperation("获取结构零件数据")
    @AuditLog(title = "设备表", desc = "获取结构零件数据", businessType = BusinessType.INSERT)
    @GetMapping("/getStructureSpare")
    public RestResponse<StructureSpareDto> editStructureSpare(@RequestParam String structureId) {
        return RestResponse.ok(structureSpareService.getDtoByStructureId(structureId));
    }

    /**
     * 获取组态设备树(子设备、部件)
     */
    @ApiOperation("获取组态设备树(子设备、测点、部件)")
    @PostMapping("/getStudioEquipmentBomList")
    public RestResponse<List<StudioEquipmentBomDto>> getStudioEquipmentBomList(@RequestBody RelSearchDto relSearchDto) {
        return RestResponse.ok(equipmentInfoService.getStudioEquipmentBomList(relSearchDto));
    }

    /**
     * SDSH用户Excel导入设备
     */
    @ApiOperation(value = "SDSH用户Excel导入设备")
    @AuditLog(title = "设备", desc = "Excel导入设备", businessType = BusinessType.INSERT)
    @PostMapping("/userImport")
    //@Permission("equipment:import")
    public RestResponse<Void> userExcelImport(@RequestPart("file") MultipartFile file) {
        FormExcelUtils<SDSHUserExcel> util = new FormExcelUtils<>(SDSHUserExcel.class);
        List<SDSHUserExcel> rows = util.importExcel(file, null);
        equipmentInfoService.userExcelImport(rows);
        return RestResponse.ok();
    }

    /**
     * 获取设备转速范围
     */
    @ApiOperation(value = "获取设备转速范围")
    @AuditLog(title = "设备", desc = "获取设备转速范围", businessType = BusinessType.INSERT)
    @PostMapping("/getEquipmentRateMap")
    //@Permission("equipment:import")
    public RestResponse<Map<String, EquipmentRateDto>> getEquipmentRate(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(equipmentInfoService.getRateMap(equipmentIds));
    }

    @ApiOperation("获取特种设备预警数量-下次检验日期")
    @GetMapping("/getSpecialWarningCount/next")
    public RestResponse<String> getSpecialWarningCountOfNext(String param) {
        return RestResponse.ok(specialService.getSpecialWarningCountOfNext(Integer.parseInt(param)));
    }

    @ApiOperation("获取特种设备预警数量-超过提醒日期")
    @GetMapping("/getSpecialWarningCount/over/remind")
    public RestResponse<String> getSpecialWarningCountOfOverRemind(String param) {
        return RestResponse.ok(specialService.getSpecialWarningCountOfOverRemind());
    }

    @ApiOperation("获取特种设备预警数量-超过下次检验日期")
    @GetMapping("/getSpecialWarningCount/over/next")
    public RestResponse<String> getSpecialWarningCountOfOverNext(String param) {
        return RestResponse.ok(specialService.getSpecialWarningCountOfOverNext());
    }

    /**
     * 驾驶舱-不需要权限过滤
     * app统计状态需过滤
     */
    @ApiOperation("驾驶舱-设备统计(运行状态)")
    @GetMapping("/equipmentCount")
    @ApiImplicitParams({@ApiImplicitParam(name="auth",value="是否过滤权限",dataType="boolean")})
    public RestResponse<List<EquipmentRunningStatusDto>> equipmentCount(@RequestParam(required = false) Boolean auth) {
       return RestResponse.ok(equipmentInfoService.getEquipmentRunningStatusCount(auth, null, null));
    }

    @ApiOperation("驾驶舱-设备统计(报警状态)")
    @GetMapping("/equipmentIotStatusCount")
    @ApiImplicitParams({@ApiImplicitParam(name="auth",value="是否过滤权限",dataType="boolean")})
    public RestResponse<List<EquipmentRunningStatusDto>> equipmentIotStatusCount(@RequestParam(required = false) Boolean auth) {
        return RestResponse.ok(equipmentInfoService.equipmentIotStatusCount(auth));
    }

    /**
     * 驾驶舱-不需要权限过滤
     */
    @ApiOperation("驾驶舱-设备状态")
    @GetMapping("/equipmentStatus")
    public RestResponse<List<EquipmentWarnTrendDto>> equipmentStatus() {
        return RestResponse.ok(equipmentInfoService.getEquipmentAllStatus());
    }

    /**
     * 驾驶舱-不需要权限过滤
     */
    @ApiOperation("驾驶舱-设备报警趋势")
    @GetMapping("/equipmentWarnTrend")
    public RestResponse<WarnCountDto> equipmentWarnTrend() {
         return RestResponse.ok(equipmentInfoService.getEquipmentWarnTrend());
    }

    /**
     * 驾驶舱-不需要权限过滤
     */
    @ApiOperation("驾驶舱-设备使用率")
    @GetMapping("/equipmentRateOfUtilization")
    public RestResponse<List<EquipmentRateOfUtilizationDto>> equipmentRateOfUtilization() {
        return RestResponse.ok(equipmentInfoService.getEquipmentRateOfUtilization());
    }

    /**
     * 驾驶舱-不需要权限过滤
     * @return
     */
    @ApiOperation("驾驶舱-时间统计")
    @GetMapping("/timeStatistics")
    public RestResponse<TimeStatisticsDto> timeStatistics() {
        return RestResponse.ok(equipmentInfoService.getTimeStatistics());
    }

    @ApiOperation("获取当前设置权限的用户可查看设备ids")
    @GetMapping("/currentUserInfoIds")
    public RestResponse<BuildInfoSearchDto> getCurrentUserInfoIds(){
        return RestResponse.ok(equipmentInfoService.getCurrentUserInfoIds());
    }

    @ApiOperation("获取设备运行状态枚举")
    @GetMapping("/getRunningStatusTypes")
    public RestResponse<List<EnumListDto>> getRunningStatusTypes(){
        return RestResponse.ok(RunningStatusType.getList());
    }

    @ApiOperation("获取健康状态枚举")
        @GetMapping("/getHealthStatusTypes")
    public RestResponse<List<EnumListDto>> getHealthStatusTypes(){
        return RestResponse.ok(HealthStatusType.getList());
    }

    @ApiOperation("oee分页获取设备列表")
    @PostMapping("/oeeEquipmentPageList")
    public RestResponse<PageResult<OeeDetailListDto>> oeeEquipmentPageList(@RequestBody OeeQueryParam queryParam) {
        return RestResponse.ok(equipmentInfoService.oeeEquipmentPageList(queryParam));
    }

    @ApiOperation("oee获取设备列表")
    @PostMapping("/oeeEquipmentList")
    public RestResponse<List<OeeDetailListDto>> oeeEquipmentList(@RequestBody OeeQueryParam queryParam) {
        return RestResponse.ok(equipmentInfoService.oeeEquipmentList(queryParam));
    }

    @ApiOperation("获取开启oee的设备ids")
    @PostMapping("/getOeeOpenInfoIds")
    public RestResponse<List<String>> getOeeOpenInfoIds() {
        return RestResponse.ok(equipmentInfoService.getOeeOpenInfoIds());
    }

    @ApiOperation("获取设备零部件")
    @PostMapping("/getListByStructureId")
    public RestResponse<List<EquipmentStructureDto>> getListByStructureId(@RequestBody List<String> structureIds) {
        return RestResponse.ok(structureService.getListByStructureId(structureIds));
    }

    @ApiOperation("更新设备停机标识")
    @GetMapping("/updateEquipmentStopEnable")
    RestResponse<Boolean> updateEquipmentStopEnable(@RequestParam String equipmentId, @RequestParam Boolean enabled){
        return RestResponse.ok(equipmentInfoService.updateEquipmentStopEnable(equipmentId, enabled));
    }

    @ApiOperation(value = "获取设备是否停机")
    @GetMapping("/getInfoParamStop")
    RestResponse<EquipmentSummaryDto> getInfoParamStop(@RequestParam String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getInfoParamStop(equipmentId));
    }

    @PostMapping("/update/sort")
    @ApiOperation("更新排序")
    public RestResponse updateSort(@RequestBody List<EquipmentSortEditParam> param){
        equipmentInfoService.updateSort(param);
        return RestResponse.ok();
    }

    @ApiOperation(value = "获取健康指数操作符号枚举")
    @GetMapping(value = "/getHealthIndexOperationType")
    public RestResponse<List<EnumListDto>> getHealthIndexOperationType() {
        return RestResponse.ok(RemainingLifeOperationType.getList());
    }

    @ApiOperation("获取特种设备预警数量-下次检验日期")
    @GetMapping("/getStatisticsOfRemainingLifeCount")
    public RestResponse<String> getStatisticsOfRemainingLifeCount(@RequestParam String param) {
        return RestResponse.ok(equipmentInfoService.getStatisticsOfRemainingLifeCount(param));
    }

    @ApiOperation("清空剩余寿命")
    @GetMapping("/clearRemainingLife")
    public RestResponse<Boolean> clearRemainingLife(@RequestParam String equipmentId) {
        return RestResponse.ok(equipmentInfoService.clearRemainingLife(equipmentId));
    }

    @ApiOperation("算法修改设备残余寿命/健康指数/健康状态")
    @PostMapping("/algorithmUpdateEquipment")
    public RestResponse<Boolean> algorithmUpdateEquipment(@RequestBody InfoDefaultParamDto dto) {
        return RestResponse.ok(equipmentInfoService.algorithmUpdateEquipment(dto));
    }

    @ApiOperation("获取当前节点下设备报警状态统计")
    @PostMapping("/getHealthStatus")
    //@Permission("equipment:list")
    public RestResponse<List<RunningStatusDto>> getHealthStatus(@RequestBody @Valid StatusSearchDto statusSearchDto) {
        return RestResponse.ok(equipmentInfoService.getHealthStatus(statusSearchDto));
    }

    @ApiOperation("获取当前节点下设备健康状态统计")
    @PostMapping("/getIotStatus")
    //@Permission("equipment:list")
    public RestResponse<List<RunningStatusDto>> getIotStatus(@RequestBody @Valid StatusSearchDto statusSearchDto) {
        return RestResponse.ok(equipmentInfoService.getIotStatus(statusSearchDto));
    }

    @ApiOperation(value = "根据设备编码获取设备信息")
    @GetMapping("/getEquipmentInfoByCode")
    RestResponse<EquipmentSummaryDto> getEquipmentInfoByCode(@RequestParam String equipmentCode) {
        return RestResponse.ok(equipmentInfoService.getEquipmentInfoByCode(equipmentCode));
    }

    @PostMapping("/getIdByCode")
    @ApiOperation("根据编码获取id")
    public RestResponse<Map<String, String>> getIdByCode(@RequestBody String[] codes){
        return RestResponse.ok(equipmentInfoService.getIdByCode(Arrays.asList(codes)));
    }

    @ApiOperation("根据code获取id")
    @GetMapping("/getFirstIdByCode")
    public RestResponse<String> getFirstIdByCode(@RequestParam String code) {
        String id = equipmentInfoService.getFirstIdByCode(code);
        if(StringUtils.hasText(id)){
            return RestResponse.ok(id);
        }else{
            return RestResponse.failed();
        }
    }
}
