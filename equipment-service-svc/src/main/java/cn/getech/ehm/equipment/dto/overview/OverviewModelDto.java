package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 概览图模板返回数据模型
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "OverviewModelDto", description = "概览图模板返回数据模型")
public class OverviewModelDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "版式(1,2)")
    private Integer modelType;

    @ApiModelProperty(value = "类型(1位置2类型)")
    private Integer type;

    @ApiModelProperty(value = "类型对应标识(位置type; 类型id)")
    private String relationKey;

    @ApiModelProperty(value = "版式2卡片标识集合")
    private Integer[] cardList;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}