package cn.getech.ehm.equipment.dto.calibration;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 校准工单 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CalibrationTaskOrder编辑", description = "校准工单编辑参数")
public class CalibrationTaskOrderEditParam extends ApiParam {

    @ApiModelProperty(value = "id",required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "仪器仪表id",required = true)
    @NotBlank(message = "仪器仪表id不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "比对校准单ID")
    private String refCalibrationId;

    @ApiModelProperty(value = "比对测试项目")
    List<CalibrationTaskItemDto> itemDtos;

    @ApiModelProperty(value = "校准日期",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "校准日期不能为空")
    private Date actualCalibrateDate;

    @ApiModelProperty(value = "1:正常4限用5禁用",required = true)
    @NotNull(message = "结果不能为空")
    private Integer result;

    @ApiModelProperty(value = "0:内校，1:外校",required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "证书附件ID")
    private String attachmentId;

    @ApiModelProperty(value = "证书编号")
    private String certificateNo;

    @ApiModelProperty(value = "备注")
    private String remark;

}
