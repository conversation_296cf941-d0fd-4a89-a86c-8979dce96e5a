package cn.getech.ehm.equipment.dto.bearing;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 轴承轴承厂商/型号
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "FactoryModelDto", description = "轴承厂商/型号返回数据模型")
public class FactoryModelDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

}
