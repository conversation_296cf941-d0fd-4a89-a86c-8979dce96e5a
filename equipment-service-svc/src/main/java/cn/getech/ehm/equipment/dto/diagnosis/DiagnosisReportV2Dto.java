package cn.getech.ehm.equipment.dto.diagnosis;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 诊断报告V2返回数据模型
 */
@Data
@ApiModel(value = "DiagnosisReportV2Dto", description = "诊断报告V2返回数据模型")
public class DiagnosisReportV2Dto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "报告类型")
    private Integer type;

    @ApiModelProperty(value = "报告类型名称")
    private String typeName;

    @ApiModelProperty(value = "发布状态")
    private Integer status;

    @ApiModelProperty(value = "描述")
    private String content;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    @ApiModelProperty(value = "发布人")
    private String createUserName;

    @ApiModelProperty(value = "健康概览")
    private List<ReportV2DetailHealthDto> healthList;

    @ApiModelProperty(value = "关注设备")
    private List<ReportV2DetailDetailDto> followEquipments;

    @ApiModelProperty(value = "设备汇总")
    private List<ReportV2DetailDetailDto> equipmentList;
}