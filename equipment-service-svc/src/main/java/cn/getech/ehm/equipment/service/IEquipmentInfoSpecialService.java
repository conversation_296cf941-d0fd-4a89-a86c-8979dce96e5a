package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.info.SpecialInfoCategoryDto;
import cn.getech.ehm.equipment.dto.info.SpecialStructureExcelDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentInfoSpecial;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 特种设备属性 服务类
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
public interface IEquipmentInfoSpecialService extends IBaseService<EquipmentInfoSpecial> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<EquipmentSpecialListDto> pageDto(EquipmentSpecialQueryParam queryParam);

    /**
     * 获取特种设备类型集合
     * @return
     */
    List<SpecialInfoCategoryDto> specialCategoryList();

    /**
     * 新增或修改
     * @param dto
     * @return
     */
    String saveOrUpdateByParam(EquipmentSpecialDto dto, String equipmentId);

    /**
     * 根据设备id获取特种设备详情
     * @param equipmentId
     * @return
     */
    EquipmentSpecialDto getDtoByInfoId(String equipmentId);

    /**
     * 根据设备ids删除特种设备
     * @param equipmentIds
     * @return
     */
    Boolean deleteByInfoIds(String[] equipmentIds);

    /**
     * 根据部件ids删除特种设备
     * @param structureIds
     * @return
     */
    Boolean deleteByStructureIds(String[] structureIds);

    /**
     * 根据部件id获取特种设备详情
     * @param structureIds
     * @return
     */
    Map<String, EquipmentSpecialDto> getListByStructureIds(List<String> structureIds);

    /**
     * 获取部件特种设备列表
     * @param queryParam
     * @return
     */
    List<SpecialStructureExcelDto> getSpecialStructureList(EquipmentSpecialQueryParam queryParam);

    String getSpecialWarningCountOfNext(Integer offsetDays);

    String getSpecialWarningCountOfOverRemind();

    String getSpecialWarningCountOfOverNext();

    List<EquipmentSpecialListDto> exportSpecialEq(EquipmentSpecialQueryParam queryParam);
}