package cn.getech.ehm.equipment.service;


import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BearingModel;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 轴承型号 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IBearingModelService extends IBaseService<BearingModel> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<FactoryModelDto> pageDto(FactoryModelQueryParam queryParam);

    /**
     * 保存
     * @param name
     * @return
     */
    String saveByName(String name);

    /**
     * 保存
     * @return
     */
    boolean saveByParam(FactoryModelDto dto);

    /**
     * 更新
     *
     */
    boolean updateByParam(FactoryModelDto dto);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 查询；列表
     * @param keyword
     * @return
     */
    List<FactoryModelDto> getList(String keyword);

    /**
     * 获取型号名称，id键值对
     * @return
     */
    Map<String, String> getModelMap();

    /**
     * 获取geek下默认信息
     * @return
     */
    List<BearingModel> getDefaultModel();
}