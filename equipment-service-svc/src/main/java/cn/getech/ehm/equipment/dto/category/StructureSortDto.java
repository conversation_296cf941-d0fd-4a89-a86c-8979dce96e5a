package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部件排序
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "StructureSortDto", description = "部件排序")
public class StructureSortDto {

    @ApiModelProperty("部件ID")
    private String id;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
