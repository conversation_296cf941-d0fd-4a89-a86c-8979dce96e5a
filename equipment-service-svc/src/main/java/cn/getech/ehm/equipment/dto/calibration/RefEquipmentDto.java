package cn.getech.ehm.equipment.dto.calibration;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 校准工单 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@ApiModel(value = "RefEquipmentDto", description = "对比仪器仪表")
public class RefEquipmentDto {

    @ApiModelProperty(value = "比对校准单ID")
    private String refCalibrationId;

    @ApiModelProperty(value = "比对设备ID")
    private String refEquipmentId;

    @ApiModelProperty(value = "比对仪器仪表编码")
    private String refEquipmentCode;

    @ApiModelProperty(value = "比对仪器仪表名称")
    private String refEquipmentName;

    @ApiModelProperty(value = "比对设备规格型号")
    private String refEquipmentSpecification;

    @ApiModelProperty(value = "比对设备生产厂家Id")
    private String refEquipmentManufacturerId;

    @ApiModelProperty(value = "比对设备生产厂家")
    private String refEquipmentManufacturerName;

    @ApiModelProperty(value = "比对设备出厂编码")
    private String refEquipmentFactoryCode;

    @ApiModelProperty(value = "比对设备校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refCalibrateDate;

    @ApiModelProperty(value = "比对设备有效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refEffectiveDate;

    @ApiModelProperty(value = "比对设备证书编号")
    private String refCertificateNo;

}