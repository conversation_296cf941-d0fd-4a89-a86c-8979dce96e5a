package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.EquipmentFaultStatisticsDto;
import cn.getech.ehm.equipment.service.IFaultStatisticsService;
import cn.getech.ehm.task.dto.FaultStatisticsDto;
import cn.getech.ehm.task.dto.FaultStatisticsParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 故障统计报表控制器
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@RestController
@RequestMapping("/statistics")
@Api(tags = "web接口：故障统计服务接口")
public class WebFaultStatisticsController {

    @Autowired
    private IFaultStatisticsService faultStatisticsService;


    /**
     * 故障类型统计
     */
    @ApiOperation("故障类型统计")
    @AuditLog(title = "故障类型统计",desc = "故障类型统计",businessType = BusinessType.QUERY)
    @PostMapping("/faultCategoryStatistics")
    public RestResponse<List<FaultStatisticsDto>> faultCategoryStatistics(@RequestBody FaultStatisticsParam param) {
        return RestResponse.ok(faultStatisticsService.faultCategoryStatistics(param));
    }

    /**
     * 故障设备统计
     */
    @ApiOperation("故障设备统计")
    @AuditLog(title = "故障设备统计",desc = "故障设备统计",businessType = BusinessType.QUERY)
    @PostMapping("/faultEquipmentStatistics")
    public RestResponse<List<EquipmentFaultStatisticsDto>> faultEquipmentStatistics(@RequestBody FaultStatisticsParam param) {
        return RestResponse.ok(faultStatisticsService.faultEquipmentStatistics(param));
    }


}