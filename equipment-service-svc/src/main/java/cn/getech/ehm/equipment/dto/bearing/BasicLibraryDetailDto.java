package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 基础库详情表
 */
@Data
@ApiModel(value = "BasicLibraryDetailDto", description = "基础库详情返回数据模型")
public class BasicLibraryDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "基础库id")
    private String basicLibraryId;

    @ApiModelProperty(value = "生产厂家id")
    private String factoryId;

    @ApiModelProperty(value = "生产厂家名称")
    private String factoryName;

    @ApiModelProperty(value = "型号id")
    private String modelId;

    @ApiModelProperty(value = "型号名称")
    private String modelName;

    @ApiModelProperty(value = "详情")
    private String detail;
}
