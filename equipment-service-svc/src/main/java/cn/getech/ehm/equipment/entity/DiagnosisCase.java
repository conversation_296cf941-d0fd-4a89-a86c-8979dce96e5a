package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 诊断案例
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_diagnosis_case")
public class DiagnosisCase extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 设备名称
     */
    @TableField("equipment_name")
    private String equipmentName;

    /**
     * 位置id
     */
    @TableField("location_id")
    private String locationId;

    /**
     * 位置名称
     */
    @TableField("location_name")
    private String locationName;

    /**
     * 分析图片
     */
    @TableField(value = "analyze_pics", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] analyzePics;

    /**
     * 分析结论
     */
    @TableField("analyze_conclusion")
    private String analyzeConclusion;

    /**
     * 分析原因
     */
    @TableField("analyze_reason")
    private String analyzeReason;

    /**
     * 处理建议
     */
    @TableField("handling_suggestions")
    private String handlingSuggestions;

    /**
     * 附件
     */
    @TableField(value = "doc_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class )
    private String[] docIds;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
