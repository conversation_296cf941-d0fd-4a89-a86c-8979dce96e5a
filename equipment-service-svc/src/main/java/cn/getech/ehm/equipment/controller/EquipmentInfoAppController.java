package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.EquipmentDocDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.equipment.dto.WarnFirstPageDto;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryTreeDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.location.EquipmentLocationTreeDto;
import cn.getech.ehm.equipment.dto.warn.EquipmentWarnAppDto;
import cn.getech.ehm.equipment.dto.WarnAppQueryParam;
import cn.getech.ehm.equipment.service.*;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 设备控制器
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/equipmentInfoApp")
@Api(tags = "设备服务App接口")
public class EquipmentInfoAppController {
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentCategoryService equipmentCategoryService;
    @Autowired
    private IEquipmentLocationService equipmentLocationService;
    @Autowired
    private IEquipmentWarnService equipmentWarnService;

    /**
     * 分页获取设备列表
     */
    @ApiOperation("分页获取设备列表")
    @PostMapping("/list")
    //@Permission("equipment:list")
    public RestResponse<PageResult<EquipmentInfoAppDetailDto>> pageList(@RequestBody @Valid InfoAppQueryParam infoAppQueryParam){
        return RestResponse.ok(equipmentInfoService.appPageDto(infoAppQueryParam));
    }

    /**
     * app首页搜索 - 非分页搜索
     */
    @ApiOperation("app首页搜索 非分页搜索")
    @PostMapping("/search")
    //@Permission("equipment:list")
    public RestResponse<List<EquipmentListDto>> search(@RequestBody PageParam pageParam){
        return RestResponse.ok(equipmentInfoService.search(pageParam));
    }

    /**
     * 根获取设备类型树
     */
    @ApiOperation("根获取设备类型树")
    @GetMapping("/categoryNode")
    @ApiImplicitParam(name="parentId",value="父节点id(默认null)",dataType="string", paramType = "query")
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentCategoryTreeDto>> categoryNode(@RequestParam(required = false) String parentId){
        return RestResponse.ok(equipmentCategoryService.node(parentId, null));
    }

    /**
     * 获取设备位置树
     */
    @ApiOperation("获取设备位置树")
    @GetMapping("/locationNode")
    @ApiImplicitParam(name="parentId",value="父节点id(默认0)",dataType="string", paramType = "query")
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentLocationTreeDto>> locationNode(@RequestParam(required = false) String parentId){
        return RestResponse.ok(equipmentLocationService.node(parentId));
    }

    /**
     * 根据id获取设备
     */
    @ApiOperation(value = "根据id获取设备")
    @GetMapping(value = "/{id}")
    //@Permission("equipment:list")
    public RestResponse<EquipmentInfoAppDto> get(@PathVariable  String id) {
        return RestResponse.ok(equipmentInfoService.getAppDtoById(id));
    }

    /**
     * 根据id获取设备文档
     */
    @ApiOperation(value = "根据id获取设备文档")
    @GetMapping(value = "/getDocByInfoId")
    //@Permission("equipment:list")
    public RestResponse<EquipmentDocDto> getDocByInfoId(@RequestParam  String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getDocByInfoId(equipmentId));
    }

    /**
     * 根据id获取设备备件列表
     */
    @ApiOperation(value = "根据id获取设备备件列表")
    @GetMapping(value = "/getAppPartById")
    //@Permission("equipment:list")
    public RestResponse<List<EquipmentPartAppDto>> getAppPartById(@RequestParam  String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getAppPartById(equipmentId));
    }

    /**
     * 根据id获取设备二维码
     */
    @ApiOperation(value = "根据id获取设备二维码")
    @GetMapping(value = "/getQrByInfoId")
    //@Permission("equipment:list")
    public RestResponse<String> getQrByInfoId(@RequestParam  String equipmentId) {
        return RestResponse.ok(equipmentInfoService.getQrByInfoId(equipmentId));
    }

    /**
     * 分页获取设备告警列表
     */
    @ApiOperation("分页获取设备告警列表")
    @PostMapping("/warn/pageList")
    //@Permission("equipment:warn:list")
    public RestResponse<PageResult<EquipmentWarnAppDto>> warnPageList(@RequestBody @Valid WarnAppQueryParam warnAppQueryParam){
        return RestResponse.ok(equipmentWarnService.appPageDto(warnAppQueryParam));
    }

    /**
     * 获取首页设备管理(告警), 更多
     */
    @ApiOperation("获取首页设备管理(告警)，更多")
    @PostMapping("/warn/firstPage")
    //@Permission("equipment:warn:list")
    public RestResponse<PageResult<WarnFirstPageDto>> firstPage(@RequestBody @Valid WarnAppQueryParam warnAppQueryParam){
        return RestResponse.ok(equipmentWarnService.firstPage(warnAppQueryParam));
    }

    /**
     * 根据id获取设备告警
     */
    @ApiOperation("根绝id获取设备告警")
    @GetMapping("/warn/getWarnById")
    //@Permission("equipment:warn:list")
    public RestResponse<EquipmentWarnAppDto> getWarnById(@RequestParam String id){
        return RestResponse.ok(equipmentWarnService.getWarnById(id));
    }

}
