package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryProp;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 设备类型扩展属性 服务类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
public interface IEquipmentCategoryPropService extends IBaseService<EquipmentCategoryProp> {

        /**
         * 保存
         * @param equipmentCategoryPropDtos
         * @param categoryId
         * @return
         */
        Boolean saveList(List<EquipmentCategoryPropDto> equipmentCategoryPropDtos, String categoryId, Boolean isAdd);

        /**
         * 初始化新增保存
         * @param equipmentCategoryPropDtos
         * @param categoryId
         * @return
         */
        Boolean initialization(List<EquipmentCategoryPropDto> equipmentCategoryPropDtos, String categoryId);

        /**
         * 根据设备类型id查询集合
         * @param categoryId
         * @return
         */
        List<EquipmentCategoryPropDto> getListByCategoryId(String categoryId, Boolean fixed, Boolean exported);

        /**
         * 获取详情
         * @param categoryId
         * @param fixed
         * @param exported 是否过滤导出字段
         * @return
         */
        List<EquipmentCategoryPropDetailDto> getDetailListByCategoryId(String categoryId, Boolean fixed, Boolean exported);
        /**
         * 获取详情
         * @param categoryIds
         * @return
         */
        Map<String, List<EquipmentCategoryPropDetailDto>> getDetailMapByCategoryIds(List<String> categoryIds);

        /**
         * 获取扩展信息组名-名称集合
         * @param categoryId
         * @param exported 是否过滤导出字段
         * @return
         */
        List<String> getNameListByCategoryId(String categoryId, Boolean exported);

        /**
         * 清空类型id下所有属性
         * @param categoryId
         * @return
         */
        Boolean deleteByCategoryId(String categoryId);
}