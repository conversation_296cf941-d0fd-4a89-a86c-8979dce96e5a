package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型扩展属性
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_category_prop")
public class EquipmentCategoryProp extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 属性名称
     */
    @TableField("name")
    private String name;

    /**
     * 分组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 属性类型(0文本1数值2选项)
     */
    @TableField("prop_type")
    private Integer propType;

    /**
     * 属性定义
     */
    @TableField("define")
    private String define;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 设备类型id
     */
    @TableField("equipment_category_id")
    private String equipmentCategoryId;

    /**
     * 是否导出
     */
    @TableField("exported")
    private Boolean exported;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否固定0否1是
     */
    @TableField("fixed")
    private Integer fixed;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

}
