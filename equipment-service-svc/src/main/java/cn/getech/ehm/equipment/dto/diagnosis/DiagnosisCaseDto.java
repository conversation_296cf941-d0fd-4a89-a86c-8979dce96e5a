package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <pre>
 * 诊断案例 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "DiagnosisCaseDto", description = "诊断案例返回数据模型")
public class DiagnosisCaseDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "分析图片")
    private String[] analyzePics;

    @ApiModelProperty(value = "分析图片dtos")
    List<AttachmentClientDto> analyzeDtos;

    @ApiModelProperty(value = "分析结论")
    private String analyzeConclusion;

    @ApiModelProperty(value = "分析原因")
    private String analyzeReason;

    @ApiModelProperty(value = "处理建议")
    private String handlingSuggestions;

    @ApiModelProperty(value = "附件")
    private String[] docIds;

    @ApiModelProperty(value = "附件dtos")
    List<AttachmentClientDto> docDtos;

    @ApiModelProperty(value = "备注")
    private String remark;

}