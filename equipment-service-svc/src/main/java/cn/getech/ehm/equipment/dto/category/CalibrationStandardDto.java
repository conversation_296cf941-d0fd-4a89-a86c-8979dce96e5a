package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 设备类型校准 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@ApiModel(value = "CalibrationStandardDto", description = "设备类型校准返回数据模型")
public class CalibrationStandardDto{

    /**
     * 比对测试项目
     */
    @ApiModelProperty(value = "比对测试项目", required = true)
    @NotBlank(message = "比对测试项目不能为空")
    private String title;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位", required = true)
    @NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * 对比值
     */
    @ApiModelProperty(value = "对比值", required = true)
    @NotBlank(message = "对比值不能为空")
    private String value;

    /**
     * 允许误差
     */
    @ApiModelProperty(value = "允许误差", required = true)
    @NotBlank(message = "允许误差不能为空")
    private String error;

}