package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto;
import cn.getech.ehm.equipment.entity.RunningParameterVariable;
import cn.getech.ehm.equipment.mapper.RunningParameterVariableMapper;
import cn.getech.ehm.equipment.service.IRunningParameterVariableService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 零部件运行参数变量 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class RunningParameterVariableServiceImpl extends BaseServiceImpl<RunningParameterVariableMapper, RunningParameterVariable> implements IRunningParameterVariableService {

    @Autowired
    private RunningParameterVariableMapper variableMapper;

    @Override
    public List<ParameterVariableDto> getList(List<String> runningParameterIds) {
        if(CollectionUtils.isEmpty(runningParameterIds)){
            return new ArrayList<>();
        }
        return variableMapper.getList(runningParameterIds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveOrUpdateByParam(List<ParameterVariableDto> dtos, String runningParameterId) {
        if(CollectionUtils.isNotEmpty(dtos)){
            List<RunningParameterVariable> variables = new ArrayList<>(dtos.size());
            int sort = 1;
            for(ParameterVariableDto dto : dtos){
                RunningParameterVariable variable = CopyDataUtil.copyObject(dto, RunningParameterVariable.class);
                variable.setSort(sort++);
                variable.setRunningParameterId(runningParameterId);
                variables.add(variable);
            }
            return saveOrUpdateBatch(variables);
        }
        return true;
    }

    @Override
    public Boolean deleteByParamIds(List<String> runningParameterIds) {
        LambdaQueryWrapper<RunningParameterVariable> wrapper = Wrappers.lambdaQuery();
        wrapper.in(RunningParameterVariable::getRunningParameterId, runningParameterIds);
        return variableMapper.delete(wrapper) > 0;
    }
}
