package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.enmu.DeteriorationTrendType;
import cn.getech.ehm.equipment.enmu.ReportCreateType;
import cn.getech.ehm.equipment.enmu.ReportStatusType;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReport;
import cn.getech.ehm.equipment.enums.HealthStatusType;
import cn.getech.ehm.equipment.handler.CommonGetHandler;
import cn.getech.ehm.equipment.mapper.DiagnosisReportV2Mapper;
import cn.getech.ehm.equipment.service.IDiagnosisReportV2DetailService;
import cn.getech.ehm.equipment.service.IDiagnosisReportV2Service;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.util.BytePictureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断报告v2 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class DiagnosisReportV2ServiceImpl extends BaseServiceImpl<DiagnosisReportV2Mapper, EquipmentDiagnosisReport> implements IDiagnosisReportV2Service {

    @Autowired
    private DiagnosisReportV2Mapper diagnosisReportMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IDiagnosisReportV2DetailService detailService;
    @Autowired
    private IEquipmentCategoryService categoryService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private CommonGetHandler commonGetHandler;

    /**
     * 审批角色
     */
    @Value("${report.audit.role_code:web-admin_reportAudit}")
    private String auditRoleCode;

    /**
     * springboot当前运行的jar包目录
     */
    private String currentDirectory = new ApplicationHome(getClass()).getSource().getParentFile().toString() + File.separator;


    @Override
    public PageResult<DiagnosisReportV2ListDto> pageDto(DiagnosisReportV2QueryParam queryParam) {
        Page<DiagnosisReportV2QueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        List<String> auditUids = commonGetHandler.getUidsByRoleCode(auditRoleCode);
        if(null != userBaseInfo && CollectionUtils.isNotEmpty(auditUids) && auditUids.contains(userBaseInfo.getUid())){
            queryParam.setAuditPerson(true);
        }else{
            queryParam.setAuditPerson(false);
        }
        queryParam.setCurrentUid(userBaseInfo.getUid());
        IPage<DiagnosisReportV2ListDto> result = diagnosisReportMapper.pageList(page, queryParam);
        return Optional.ofNullable(PageResult.<DiagnosisReportV2ListDto>builder()
                        .records(result.getRecords())
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String saveByParam(DiagnosisReportV2AddParam addParam) {
        EquipmentDiagnosisReport diagnosisReport = CopyDataUtil.copyObject(addParam, EquipmentDiagnosisReport.class);
        String id = UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
        diagnosisReport.setId(id);
        String preCode = "RG" + DateUtil.format(new Date(), "yyyy-MM-dd");
        String maxCode = diagnosisReportMapper.getMaxCode(preCode);
        Integer num = buildCodeNum(maxCode);
        diagnosisReport.setCode(preCode + String.format("%03d", num));
        diagnosisReport.setStatus(ReportStatusType.TO_BE_PUBLISHED.getValue());
        diagnosisReport.setType(ReportCreateType.ARTIFICIAL.getValue());
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        diagnosisReport.setCreateUserName(null != userBaseInfo ? userBaseInfo.getName() : StringPool.EMPTY);
        this.save(diagnosisReport);

        detailService.saveByParam(id, addParam.getEquipmentIds());
        return id;
    }

    private Integer buildCodeNum(String maxCode){
        Integer num = 1;
        if (StringUtils.isNotBlank(maxCode)) {
            String suffix = maxCode.substring(maxCode.length() - 3);
            try {
                num = Integer.valueOf(suffix) + 1;
            } catch (Exception e) {
                log.error("生成编码失败");
            }
        }
        return num;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(DiagnosisReportV2EditParam editParam) {
        EquipmentDiagnosisReport diagnosisReport = CopyDataUtil.copyObject(editParam, EquipmentDiagnosisReport.class);
        if(editParam.getStatus() == ReportStatusType.PUBLISHED.getValue()){
            diagnosisReport.setPublishTime(new Date());
        }
        updateById(diagnosisReport);

        detailService.updateByParam(editParam.getId(), editParam.getFollowEquipments(), editParam.getEquipmentList());

        return true;
    }

    private List<ReportV2DetailHealthDto> buildHealthList(List<ReportV2DetailDetailDto> detailDtos){
        Map<Integer, Long> healthStatusMap = detailDtos.stream().collect(Collectors.groupingBy(ReportV2DetailDetailDto::getHealthStatus, Collectors.counting()));
        List<ReportV2DetailHealthDto> healthList = new ArrayList<>();
        Integer num = 1;
        for(HealthStatusType type : HealthStatusType.values()){
            ReportV2DetailHealthDto healthDto = new ReportV2DetailHealthDto();
            healthDto.setNum(num++);
            healthDto.setValue(type.getValue());
            healthDto.setName(type.getName());
            healthDto.setEquipmentNum(null != healthStatusMap.get(type.getValue()) ? healthStatusMap.get(type.getValue()) : 0L);
            healthDto.setContent(type.getContent());
            healthDto.setRate(new BigDecimal(healthDto.getEquipmentNum() * 100).divide(new BigDecimal(detailDtos.size()), 1, BigDecimal.ROUND_HALF_UP).doubleValue());
            healthList.add(healthDto);
        }
        return healthList;
    }

    @Override
    public File exportWord(String id, String url) {
        DiagnosisReportV2Dto dto = CopyDataUtil.copyObject(diagnosisReportMapper.selectById(id), DiagnosisReportV2Dto.class);
        if(null != dto){
            dto.setTypeName(ReportCreateType.getNameByValue(dto.getType()));
            List<ReportV2DetailDetailDto> detailDtos = detailService.getListByReportId(id);
            Map<Integer, String> healthStatusMap = Arrays.stream(HealthStatusType.values()).collect(Collectors.toMap(HealthStatusType::getValue, HealthStatusType::getName));
            Map<Integer, String> deteriorationTrendMap = Arrays.stream(DeteriorationTrendType.values()).collect(Collectors.toMap(DeteriorationTrendType::getValue, DeteriorationTrendType::getName));
            if(CollectionUtils.isNotEmpty(detailDtos)){
                dto.setHealthList(buildHealthList(detailDtos));
                List<ReportV2DetailDetailDto> followEquipments = new ArrayList<>();
                Integer followEquiomentNum = 1;
                Integer equipmentNum = 1;
                for(ReportV2DetailDetailDto detailDto : detailDtos){
                    detailDto.setHealthStatusName(null != detailDto.getHealthStatus() ? healthStatusMap.get(detailDto.getHealthStatus()) : StringPool.EMPTY);
                    detailDto.setDeteriorationTrendName(null != detailDto.getDeteriorationTrend() ? deteriorationTrendMap.get(detailDto.getDeteriorationTrend()) : StringPool.EMPTY);
                    if(detailDto.getFollow()){
                        ReportV2DetailDetailDto followDetail = CopyDataUtil.copyObject(detailDto, ReportV2DetailDetailDto.class);
                        followDetail.setNum(followEquiomentNum++);
                        followEquipments.add(followDetail);
                    }
                    detailDto.setNum(equipmentNum++);
                    if(StringUtils.isNotBlank(detailDto.getAnalyzePicInfo())){
                        List<AnalyzePicInfoDto> analyzePicInfoDtos = JSONObject.parseArray(detailDto.getAnalyzePicInfo(), AnalyzePicInfoDto.class);
                        for(AnalyzePicInfoDto analyzePicInfoDto : analyzePicInfoDtos){
                            analyzePicInfoDto.setExportUrl(new PictureRenderData(600, 250, ".png", BytePictureUtils.getUrlBufferedImage(url + analyzePicInfoDto.getUrl())));
                        }
                        detailDto.setAnalyzePicInfoDtos(analyzePicInfoDtos);
                    }
                    String[] analyzePics = detailDto.getAnalyzePics();
                    List<Map<String, Object>> analyzeDtos = new ArrayList<>();
                    if(null != analyzePics && analyzePics.length > 0) {
                        Map<String, AttachmentClientDto> attachmentMap = commonGetHandler.getAttachmentMap(Arrays.asList(analyzePics));
                        for(String picId : analyzePics){
                            AttachmentClientDto attachmentClientDto = attachmentMap.get(picId);
                            if(null != attachmentClientDto && StringUtils.isNotBlank(attachmentClientDto.getUrl()) && StringUtils.isNotBlank(attachmentClientDto.getFileType())) {
                                Map<String, Object> map = new HashMap<>();
                                map.put("name", attachmentClientDto.getName());
                                String[] fileTypes = attachmentClientDto.getFileType().split(StringPool.SLASH);
                                map.put("url", new PictureRenderData(600, 250, fileTypes.length >= 2 ? "." + fileTypes[1] : ".png", BytePictureUtils.getUrlBufferedImage(url + attachmentClientDto.getUrl())));
                                analyzeDtos.add(map);
                            }
                        }
                    }
                    detailDto.setAnalyzeDtoExports(analyzeDtos);
                }
                dto.setFollowEquipments(followEquipments);
                dto.setEquipmentList(detailDtos);
            }
        }
        return WordDownloadUtil.fillDataIntoReportV2Word(dto, currentDirectory);
    }


    @Override
    public DiagnosisReportV2Dto getDtoById(String id) {
        DiagnosisReportV2Dto dto = CopyDataUtil.copyObject(diagnosisReportMapper.selectById(id), DiagnosisReportV2Dto.class);
        if(null != dto){
            List<ReportV2DetailDetailDto> detailDtos = detailService.getListByReportId(id);
            if(CollectionUtils.isNotEmpty(detailDtos)){
                List<String> categoryIds = detailDtos.stream().map(ReportV2DetailDetailDto::getCategoryId).collect(Collectors.toList());
                List<String> equipmentIds = detailDtos.stream().map(ReportV2DetailDetailDto::getEquipmentId).collect(Collectors.toList());
                Map<String, String> categoryNameMap = categoryService.getDepthName(categoryIds);
                //设备父级节点名称集合，不包含自身
                Map<String, String> equipmentNameMap = equipmentInfoService.getDepthName(equipmentIds,true);

                dto.setHealthList(buildHealthList(detailDtos));
                List<ReportV2DetailDetailDto> followEquipments = new ArrayList<>();
                for(ReportV2DetailDetailDto detailDto : detailDtos){
                    detailDto.setCategoryAllName(categoryNameMap.get(detailDto.getCategoryId()));
                    detailDto.setParentAllName(equipmentNameMap.get(detailDto.getEquipmentId()));
                    if(detailDto.getFollow()){
                        followEquipments.add(detailDto);
                    }
                    if(StringUtils.isNotBlank(detailDto.getAnalyzePicInfo())){
                        List<AnalyzePicInfoDto> analyzePicInfoDtos = JSONObject.parseArray(detailDto.getAnalyzePicInfo(), AnalyzePicInfoDto.class);
                        detailDto.setAnalyzePicInfoDtos(analyzePicInfoDtos);
                    }
                    String[] analyzePics = detailDto.getAnalyzePics();
                    if(null != analyzePics && analyzePics.length > 0) {
                        Map<String, AttachmentClientDto> map = commonGetHandler.getAttachmentMap(Arrays.asList(analyzePics));
                        detailDto.setAnalyzeDtos(buildPic(analyzePics, map));
                    }
                }
                dto.setFollowEquipments(followEquipments);
                dto.setEquipmentList(detailDtos);
            }
        }
        return dto;
    }

    private List<AttachmentClientDto> buildPic(String[] picStrs, Map<String, AttachmentClientDto> map){
        List<AttachmentClientDto> picUrls = new ArrayList<>(picStrs.length);
        for(String picId : picStrs){
            AttachmentClientDto attachmentClientDto = map.get(picId);
            if(null != attachmentClientDto) {
                picUrls.add(attachmentClientDto);
            }
        }
        return picUrls;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteByIds(List<String> ids){
        LambdaQueryWrapper<EquipmentDiagnosisReport> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentDiagnosisReport::getId, ids);
        this.remove(wrapper);
        detailService.deleteByReportIds(ids);
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveAuto(List<DiagnosisReportAutoDto> dtos){
        if(CollectionUtils.isNotEmpty(dtos)){
            List<EquipmentDiagnosisReport> diagnosisReports = new ArrayList<>();
            String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
            String preCode = "ZN" + dateStr;
            String maxCode = diagnosisReportMapper.getMaxCode(preCode);
            Integer num = buildCodeNum(maxCode);
            for(DiagnosisReportAutoDto dto : dtos) {
                if(null != dto.getEquipmentIds() && dto.getEquipmentIds().length > 0) {
                    EquipmentDiagnosisReport diagnosisReport = new EquipmentDiagnosisReport();
                    String id = UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
                    diagnosisReport.setId(id);
                    diagnosisReport.setName(dto.getName() + dateStr);
                    diagnosisReport.setCode(preCode + String.format("%03d", num++));
                    diagnosisReport.setStatus(ReportStatusType.TO_BE_PUBLISHED.getValue());
                    diagnosisReport.setType(ReportCreateType.AUTOMATIC.getValue());
                    diagnosisReport.setCreateUserName("admin");
                    diagnosisReports.add(diagnosisReport);

                    detailService.saveByParam(id, Arrays.asList(dto.getEquipmentIds()));
                }
            }
            if(CollectionUtils.isNotEmpty(diagnosisReports)){
                this.saveBatch(diagnosisReports);
            }
        }

        return true;
    }
}
