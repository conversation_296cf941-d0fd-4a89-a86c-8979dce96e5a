package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.DiagnosisCaseAddParam;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.DiagnosisCase;
import cn.getech.ehm.equipment.mapper.DiagnosisCaseMapper;
import cn.getech.ehm.equipment.service.IDiagnosisCaseService;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.deepoove.poi.data.HyperLinkTextRenderData;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.util.BytePictureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * <pre>
 * 诊断案例 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class DiagnosisCaseServiceImpl extends BaseServiceImpl<DiagnosisCaseMapper, DiagnosisCase> implements IDiagnosisCaseService {

    @Autowired
    private DiagnosisCaseMapper diagnosisCaseMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    /**
     * springboot当前运行的jar包目录
     */
    private String currentDirectory = new ApplicationHome(getClass()).getSource().getParentFile().toString() + File.separator;


    @Override
    public PageResult<DiagnosisCaseListDto> pageDto(DiagnosisCaseQueryParam queryParam) {
        LambdaQueryWrapper<DiagnosisCase> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(queryParam.getName())){
            wrapper.like(DiagnosisCase::getName, queryParam.getName());
        }
        if(null != queryParam.getBeginTime() && null != queryParam.getEndTime()){
            wrapper.between(DiagnosisCase::getCreateTime, queryParam.getBeginTime(), queryParam.getEndTime());
        }
        wrapper.orderByDesc(DiagnosisCase::getUpdateTime);
        wrapper.select(DiagnosisCase::getId, DiagnosisCase::getName, DiagnosisCase::getEquipmentId, DiagnosisCase::getEquipmentName,
                DiagnosisCase::getCreateBy, DiagnosisCase::getCreateTime, DiagnosisCase::getLocationId, DiagnosisCase::getLocationName);
        PageResult<DiagnosisCase> pageResult = page(queryParam, wrapper);
        return Optional.ofNullable(PageResult.<DiagnosisCaseListDto>builder()
                        .records(CopyDataUtil.copyList(pageResult.getRecords(), DiagnosisCaseListDto.class))
                        .total(pageResult.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(DiagnosisCaseAddParam addParam) {
        DiagnosisCase diagnosisCase = CopyDataUtil.copyObject(addParam, DiagnosisCase.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,diagnosisCase);
        return save(diagnosisCase);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(DiagnosisCaseEditParam diagnosisCaseEditParam) {
        DiagnosisCase diagnosisCase = CopyDataUtil.copyObject(diagnosisCaseEditParam, DiagnosisCase.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,diagnosisCase);
        return updateById(diagnosisCase);
    }

    @Override
    public File exportWord(String id, String url) {
        DiagnosisCaseDto dto = this.getDtoById(id);
        List<Map<String, Object>> analyzeDtos = new ArrayList<>();
        List<Map<String, Object>> docDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dto.getAnalyzeDtos())){
            for(AttachmentClientDto attachmentClientDto : dto.getAnalyzeDtos()){
                Map<String, Object> map = new HashMap<>();
                map.put("name", attachmentClientDto.getName());
                if(StringUtils.isNotBlank(attachmentClientDto.getUrl()) && StringUtils.isNotBlank(attachmentClientDto.getFileType())){
                    String[] fileTypes = attachmentClientDto.getFileType().split(StringPool.SLASH);
                    map.put("url", new PictureRenderData(600, 250, fileTypes.length >= 2 ? "." + fileTypes[1] : ".png", BytePictureUtils.getUrlBufferedImage(url + attachmentClientDto.getUrl())));
                }
                analyzeDtos.add(map);
            }
        }
        if(CollectionUtils.isNotEmpty(dto.getDocDtos())){
            for(AttachmentClientDto attachmentClientDto : dto.getDocDtos()){
                Map<String, Object> map = new HashMap<>();
                if(StringUtils.isNotBlank(attachmentClientDto.getUrl())){
                    map.put("link", new HyperLinkTextRenderData(attachmentClientDto.getName(), url + attachmentClientDto.getUrl()));
                }
                docDtos.add(map);
            }
        }
        return WordDownloadUtil.fillDataIntoCaseWord(dto, currentDirectory, analyzeDtos, docDtos);
    }


    @Override
    public DiagnosisCaseDto getDtoById(String id) {
        DiagnosisCaseDto dto = CopyDataUtil.copyObject(this.getById(id), DiagnosisCaseDto.class);
        if(null != dto){
            String[] analyzePics = dto.getAnalyzePics();
            if(null != analyzePics && analyzePics.length > 0) {
                Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(analyzePics));
                dto.setAnalyzeDtos(buildPic(analyzePics, map));
            }
            String[] docIds = dto.getDocIds();
            if(null != docIds && docIds.length > 0) {
                Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(docIds));
                dto.setDocDtos(buildPic(docIds, map));
            }
        }
        return dto;
    }

    private Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }

    private List<AttachmentClientDto> buildPic(String[] picStrs, Map<String, AttachmentClientDto> map){
        List<AttachmentClientDto> picUrls = new ArrayList<>(picStrs.length);
        for(String picId : picStrs){
            AttachmentClientDto attachmentClientDto = map.get(picId);
            if(null != attachmentClientDto) {
                picUrls.add(attachmentClientDto);
            }
        }
        return picUrls;
    }
}
