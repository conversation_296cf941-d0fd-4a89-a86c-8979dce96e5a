package cn.getech.ehm.equipment.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 结构参数数据来源
 */
public enum StructureSourceType {
    ENTER(1,"录入"),
    COUNT(2,"计算");


    StructureSourceType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (StructureSourceType sourceType : StructureSourceType.values()) {
                if (sourceType.getValue() == value) {
                    return sourceType.getName();
                }
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(StructureSourceType sourceType : StructureSourceType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(sourceType.value);
            enumListDto.setName(sourceType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
