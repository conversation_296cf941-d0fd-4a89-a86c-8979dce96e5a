package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 诊断报告V2详情
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_diagnosis_report_detail")
public class EquipmentDiagnosisReportDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 诊断报告id
     */
    @TableField("report_id")
    private String reportId;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 是否关注
     */
    @TableField("follow")
    private Boolean follow;

    /**
     * 关注说明
     */
    @TableField("follow_content")
    private String followContent;

    /**
     * 劣化趋势
     */
    @TableField("deterioration_trend")
    private Integer deteriorationTrend;

    /**
     * 分析说明
     */
    @TableField("analyze_info")
    private String analyzeInfo;

    /**
     * 分析图片
     */
    @TableField(value = "analyze_pics", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] analyzePics;

    /**
     * 分析图片信息
     */
    @TableField(value = "analyze_pic_info")
    private String analyzePicInfo;

    /**
     * 状态描述
     */
    @TableField("analyze_reason")
    private String analyzeReason;

    /**
     * 处理建议
     */
    @TableField("handling_suggestions")
    private String handlingSuggestions;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
