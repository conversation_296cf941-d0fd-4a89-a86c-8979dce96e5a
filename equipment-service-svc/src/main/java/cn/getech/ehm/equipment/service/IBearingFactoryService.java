package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BearingFactory;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 轴承生产厂家(品牌) 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IBearingFactoryService extends IBaseService<BearingFactory> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<FactoryModelDto> pageDto(FactoryModelQueryParam queryParam);

    /**
     * 保存
     * @param name
     * @return
     */
    String saveByName(String name);

    /**
     * 保存
     * @return
     */
    boolean saveByParam(FactoryModelDto dto);

    /**
     * 更新
     *
     */
    boolean updateByParam(FactoryModelDto dto);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    List<FactoryModelDto> getList(String keyword);

    /**
     * 获取品牌名称，id键值对
     * @return
     */
    Map<String, String> getFactoryMap();

    /**
     * 获取geek默认信息
     * @return
     */
    List<BearingFactory> getDefaultFactory();
}