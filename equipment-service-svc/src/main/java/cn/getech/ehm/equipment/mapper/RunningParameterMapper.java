package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.runningparam.RunningParameterListDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterQueryParam;
import cn.getech.ehm.equipment.entity.RunningParameter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 零部件运行参数Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface RunningParameterMapper extends BaseMapper<RunningParameter> {
    /**
     * 获取运行参数列表
     * @return
     */
    List<RunningParameterListDto> getList(@Param("param") RunningParameterQueryParam queryParam);

    /**
     * 获取最大排序
     */
    Integer getMaxSort(@Param("sparePartsId") String sparePartsId);
}
