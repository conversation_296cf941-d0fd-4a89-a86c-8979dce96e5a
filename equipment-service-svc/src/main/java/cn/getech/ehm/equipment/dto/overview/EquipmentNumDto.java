package cn.getech.ehm.equipment.dto.overview;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备完好率
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "IntactRateDto", description = "设备完好率")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentNumDto {

    @ApiModelProperty(value = "总数量")
    private Integer total = StaticValue.ZERO;

    @ApiModelProperty(value = "运行统计集合")
    List<ParamStatusDto> runningStatusCount;

    @ApiModelProperty(value = "报警统计集合")
    List<ParamStatusDto> warnStatusCount;
}