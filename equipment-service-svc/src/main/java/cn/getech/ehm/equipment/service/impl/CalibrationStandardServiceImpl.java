package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.category.CalibrationStandardDto;
import cn.getech.ehm.equipment.entity.CalibrationStandard;
import cn.getech.ehm.equipment.mapper.CalibrationStandardMapper;
import cn.getech.ehm.equipment.service.ICalibrationStandardService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 设备类型校准 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Slf4j
@Service
public class CalibrationStandardServiceImpl extends BaseServiceImpl<CalibrationStandardMapper, CalibrationStandard> implements ICalibrationStandardService {

    @Autowired
    private CalibrationStandardMapper calibrationStandardMapper;

    @Override
    public Boolean updateList(List<CalibrationStandardDto> calibrationStandardDtoList, String categoryId) {
        LambdaQueryWrapper<CalibrationStandard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CalibrationStandard::getCategoryId, categoryId);
        calibrationStandardMapper.delete(wrapper);

        List<CalibrationStandard> entities = CopyDataUtil.copyList(calibrationStandardDtoList, CalibrationStandard.class);
        int i = StaticValue.ONE;
        for(CalibrationStandard entity : entities) {
            entity.setCategoryId(categoryId);
            entity.setSort(i++);
        }

        return this.saveBatch(entities);
    }

    @Override
    public List<CalibrationStandardDto> getByCategoryId(String categoryId) {
        if(StringUtils.isBlank(categoryId)){
            return null;
        }
        LambdaQueryWrapper<CalibrationStandard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CalibrationStandard::getCategoryId, categoryId);
        wrapper.select(CalibrationStandard::getTitle, CalibrationStandard::getUnit, CalibrationStandard::getValue, CalibrationStandard::getError);
        wrapper.orderByAsc(CalibrationStandard::getSort);
        List<CalibrationStandard> entities = calibrationStandardMapper.selectList(wrapper);
        return CopyDataUtil.copyList(entities, CalibrationStandardDto.class);
    }
}
