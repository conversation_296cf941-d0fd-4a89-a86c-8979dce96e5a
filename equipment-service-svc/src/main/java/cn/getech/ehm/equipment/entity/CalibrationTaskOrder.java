package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 校准工单
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calibration_task_order")
public class CalibrationTaskOrder extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 工单编号
     */
    @TableField("code")
    private String code;

    /**
     * 当前设备ID
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 关联校准单id
     */
    @TableField("ref_calibration_id")
    private String refCalibrationId;

    /**
     * 显示日期
     */
    @TableField("display_date")
    private Date displayDate;

    /**
     * 计划校准日期
     */
    @TableField("plan_calibrate_date")
    private Date planCalibrateDate;

    /**
     * 实际校准日期
     */
    @TableField("actual_calibrate_date")
    private Date actualCalibrateDate;

    /**
     * 校准状态,0:待校准，1:已校准
     */
    @TableField("status")
    private Integer status;

    /**
     * 校准结果，1:正常4限用5禁用
     */
    @TableField("result")
    private Integer result;

    /**
     * 校准类型,0:内校，1:外校
     */
    @TableField("type")
    private Integer type;

    /**
     * 证书附件ID
     */
    @TableField("attachment_id")
    private String attachmentId;

    /**
     * 证书编号
     */
    @TableField("certificate_no")
    private String certificateNo;

    /**
     * 删除标识
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
