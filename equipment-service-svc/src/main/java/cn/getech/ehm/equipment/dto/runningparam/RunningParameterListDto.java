package cn.getech.ehm.equipment.dto.runningparam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 零部件运行参数列表
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "RunningParameterListDto", description = "零部件运行参数列表")
public class RunningParameterListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "零部件id")
    private String sparePartsId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码(特征参数/基础库)")
    private String code;

    @ApiModelProperty(value = "参数分类值")
    private Integer paramType;

    @ApiModelProperty(value = "参数分类")
    private String paramTypeName;

    @ApiModelProperty(value = "数据来源值")
    private Integer sourceType;

    @ApiModelProperty(value = "数据来源(1录入2计算)")
    private String sourceTypeName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

}
