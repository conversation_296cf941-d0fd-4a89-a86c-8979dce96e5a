package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 概览图模板
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("overview_model")
public class OverviewModel extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 版式(1,2)
     */
    @TableField("model_type")
    private Integer modelType;

    /**
     * 类型(1位置2类型)
     */
    @TableField("type")
    private Integer type;

    /**
     * 类型对应标识(位置id；设备id)
     */
    @TableField("relation_key")
    private String relationKey;

    /**
     * 版式2卡片标识集合
     */
    @TableField(value ="card_list", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] cardList;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
