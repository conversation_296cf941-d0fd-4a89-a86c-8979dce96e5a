package cn.getech.ehm.equipment.dto.calibration;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 校准工单 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@ApiModel(value = "CalibrationTaskOrderDto", description = "校准工单返回数据模型")
public class CalibrationTaskOrderDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "当前设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "仪器仪表名称")
    private String equipmentName;

    @ApiModelProperty(value = "当前设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "当前设备类型id")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "当前设备使用部门")
    private String equipmentLocation;

    @ApiModelProperty(value = "当前设备规格型号")
    private String equipmentSpecification;

    @ApiModelProperty(value = "当前设备生产厂家Id")
    private String equipmentManufacturerId;

    @ApiModelProperty(value = "当前设备生产厂家")
    private String equipmentManufacturerName;

    @ApiModelProperty(value = "当前设备出厂编码")
    private String equipmentFactoryCode;

    @ApiModelProperty(value = "比对校准单ID")
    private String refCalibrationId;

    @ApiModelProperty(value = "比对设备ID")
    private String refEquipmentId;

    @ApiModelProperty(value = "比对仪器仪表名称")
    private String refEquipmentName;

    @ApiModelProperty(value = "比对设备规格型号")
    private String refEquipmentSpecification;

    @ApiModelProperty(value = "比对设备生产厂家")
    private String refEquipmentManufacturer;

    @ApiModelProperty(value = "比对设备出厂编码")
    private String refEquipmentFactoryCode;

    @ApiModelProperty(value = "比对设备校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refCalibrateDate;

    @ApiModelProperty(value = "比对设备有效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refEffectiveDate;

    @ApiModelProperty(value = "比对设备证书编号")
    private String refCertificateNo;

    @ApiModelProperty(value = "比对测试项目")
    List<CalibrationTaskItemDto> itemDtos;

    @ApiModelProperty(value = "计划校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planCalibrateDate;

    @ApiModelProperty(value = "实际校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCalibrateDate;

    @ApiModelProperty(value = "1:正常4限用5禁用")
    private Integer result;

    @ApiModelProperty(value = "校准状态,0:待校准，1:已校准")
    private Integer status;

    @ApiModelProperty(value = "0:内校，1:外校")
    private Integer type;

    @ApiModelProperty(value = "证书附件ID")
    private String attachmentId;

    @ApiModelProperty(value = "证书编号")
    private String certificateNo;

    @ApiModelProperty(value = "备注")
    private String remark;
}