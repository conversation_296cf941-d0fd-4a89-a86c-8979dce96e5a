package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.bearing.BasicLibraryFieldDto;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryField;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 基础库字段服务类
 */
public interface IBasicLibraryFieldService extends IBaseService<BasicLibraryField> {
    /**
     * 获取基础库字段列表
     * @param basicLibraryId 基础库id
     * @return 基础库字段列表
     */
    List<BasicLibraryFieldDto> getList(String basicLibraryId, Boolean includeModelFactory,String filterSparePartsId);

    /**
     * 编辑基础库字段
     * @param dto 基础库字段
     * @return 是否成功
     */
    Boolean addField(BasicLibraryFieldDto dto);

    /**
     * 编辑基础库字段
     */
    Boolean editField(List<BasicLibraryFieldDto> dtos);

    /**
     * 获取geek默认信息
     * @return
     */
    List<BasicLibraryField> getDefaultFields();

    /**
     * 获取编码单位map
     * @param basicLibraryId
     * @return
     */
    Map<String, String> getCodeUnitMap(String basicLibraryId);
}