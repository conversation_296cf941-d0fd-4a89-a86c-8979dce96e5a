package cn.getech.ehm.equipment.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.dto.EquipmentStructureDto;
import cn.getech.ehm.equipment.dto.category.CategoryStructureDto;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.PartDetailDto;
import cn.getech.ehm.part.dto.PartSearchDto;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.getech.poros.permission.dto.SecStaffUidParam;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CommonGetHandler {

    @Autowired
    private PartClient partClient;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private PorosSecGrantClient porosSecGrantClient;

    public Map<String, PartDetailDto> getPartInfo(List<String> partIdList) {
        Map<String, PartDetailDto> data = Maps.newHashMap();
        if (CollectionUtils.isEmpty(partIdList)) {
            return data;
        }
        PartSearchDto partSearchDto = new PartSearchDto();
        partSearchDto.setPartIds(partIdList);
        RestResponse<Map<String, PartDetailDto>> partMapByIds = partClient.getPartMapByIds(partSearchDto);
        if (!partMapByIds.isOk()) {
            log.error("获取备件信息失败");
        }else {
            data = partMapByIds.getData();
        }
        return data;
    }

    public void completeCategoryPartInfo(List<CategoryStructureDto> structureDtos, List<String> partIdList) {
        Map<String, PartDetailDto> partInfo = this.getPartInfo(partIdList);
        structureDtos.stream().forEach(item -> {
            PartDetailDto orDefault = partInfo.getOrDefault(item.getPartId(), null);
            if (orDefault != null) {
                item.setPartName(orDefault.getPartName());
                item.setPartCode(orDefault.getPartCode());
                item.setPartCurrentStock(orDefault.getPartCurrentStock());
            }
        });
        structureDtos.stream().filter(item -> CollectionUtils.isNotEmpty(item.getChildren())).forEach(item -> {
            item.getChildren().stream().forEach(child -> {
                PartDetailDto orDefault = partInfo.getOrDefault(child.getPartId(), null);
                if (orDefault != null) {
                    child.setPartName(orDefault.getPartName());
                    child.setPartCode(orDefault.getPartCode());
                    child.setPartCurrentStock(orDefault.getPartCurrentStock());
                }
            });
        });
    }

    public void completePartInfo(List<EquipmentStructureDto> equipmentStructureResDtos, List<String> partIdList) {
        Map<String, PartDetailDto> partInfo = this.getPartInfo(partIdList);
        equipmentStructureResDtos.stream().forEach(item -> {
            PartDetailDto orDefault = partInfo.getOrDefault(item.getPartId(), null);
            if (orDefault != null) {
                item.setPartName(orDefault.getPartName());
                item.setPartCode(orDefault.getPartCode());
                item.setPartCurrentStock(orDefault.getPartCurrentStock());
            }
        });
        equipmentStructureResDtos.stream().filter(item -> CollectionUtils.isNotEmpty(item.getChildren())).forEach(item -> {
            item.getChildren().stream().forEach(child -> {
                PartDetailDto orDefault = partInfo.getOrDefault(child.getPartId(), null);
                if (orDefault != null) {
                    child.setPartName(orDefault.getPartName());
                    child.setPartCode(orDefault.getPartCode());
                    child.setPartCurrentStock(orDefault.getPartCurrentStock());
                }
            });
        });
    }

    //获取字典信息并输出错误log
    public Map<String, DictionaryItemDto> getDictMap(String dictType) {
        RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(dictType);
        if (!baseServiceClientItemMapByCode.isOk()) {
            log.info("连接base-service获取{}字典表失败", dictType);
            return Maps.newHashMap();
        } else {
            return baseServiceClientItemMapByCode.getData();
        }
    }

    /**
     * 根据uid集合获取uid,名称键值对
     *
     * @param uids
     * @return
     */
    public Map<String, String> getMapByUids(List<String> uids) {
        Map<String, String> uidMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(uids)) {
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(uids);
            RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if (staffRes.isOk()) {
                uidMap = staffRes.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));
            } else {
                log.error("获取用户失败");
            }
        }
        return uidMap;
    }

    public List<String> getUidsByRoleCode(String roleCode){
        List<String> uids = null;
        RestResponse<List<String>> restResponse = porosSecGrantClient.getUidsByRoleCode(roleCode);
        if (restResponse.isOk()) {
            return restResponse.getData();
        } else {
            log.error("获取中台工单评价角色人员失败->" + JSONObject.toJSON(restResponse));
        }
        return CollectionUtils.isNotEmpty(uids) ? uids : Lists.newArrayList();
    }

    public Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }
}
