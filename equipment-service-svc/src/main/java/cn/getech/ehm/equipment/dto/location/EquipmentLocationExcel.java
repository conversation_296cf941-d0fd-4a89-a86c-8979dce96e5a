package cn.getech.ehm.equipment.dto.location;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备位置导入
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentLocationExcel", description = "设备位置导入")
public class EquipmentLocationExcel {

    @ApiModelProperty(value = "设备位置名称")
    @Excel(name="名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "位置类型(1集团2公司3车间4单元)")
    @Excel(name="位置类型",cellType = Excel.ColumnType.STRING,readConverterExp = "0=未定义,1=集团,2=公司,3=车间,4=单元")
    private String type;

    @ApiModelProperty(value = "上级节点名称")
    @Excel(name="上级节点名称",cellType = Excel.ColumnType.STRING)
    private String parentName;

    @ApiModelProperty(value = "地理位置")
    @Excel(name="地理位置",cellType = Excel.ColumnType.STRING)
    private String address;

}