package cn.getech.ehm.equipment.dto.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 设备表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentInfoListDto", description = "设备表返回列表")
public class EquipmentInfoListDto {
    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型全路径名称")
    private String categoryAllName;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备)")
    private String parentName;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String locationId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备)")
    private String locationName;

    @ApiModelProperty(value = "上级节点名称")
    private String parentAllName;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "投产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date completionTime;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "健康指数")
    private Double healthIndex;

    @ApiModelProperty(value = "健康指数更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date healthIndexUpdateTime;

    @ApiModelProperty(value = "剩余寿命")
    private Integer remainingLife;

    @ApiModelProperty(value = "剩余寿命字符串")
    private String remainingLifeStr;

    @ApiModelProperty(value = "剩余寿命更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date remainingLifeUpdateTime;

    @ApiModelProperty(value = "报警状态值")
    private Integer iotStatus;

    @ApiModelProperty(value = "健康状态")
    private Integer healthStatus;
}