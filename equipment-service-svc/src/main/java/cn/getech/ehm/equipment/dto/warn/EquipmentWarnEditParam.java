package cn.getech.ehm.equipment.dto.warn;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备告警 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentWarn编辑", description = "设备告警编辑参数")
public class EquipmentWarnEditParam extends ApiParam {

    @ApiModelProperty(value = "iot告警id", required = true)
    @NotBlank(message = "iot告警id不能为空")
    private String id;

    @ApiModelProperty(value = "修复时间", required = true)
    @NotNull(message = "修复时间不能为空")
    private Long endTs;

}
