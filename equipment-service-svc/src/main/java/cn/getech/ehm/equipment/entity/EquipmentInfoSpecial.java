package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 特种设备
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_info_special")
public class EquipmentInfoSpecial extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 部件id
     */
    @TableField("structure_id")
    private String structureId;

    /**
     * 登记证号
     */
    @TableField("certificate_no")
    private String certificateNo;

    /**
     * 注册代码
     */
    @TableField("registration_code")
    private String registrationCode;

    /**
     * 检验日期
     */
    @TableField("inspection_date")
    private Date inspectionDate;

    /**
     * 下次检验日期
     */
    @TableField("next_inspection_date")
    private Date nextInspectionDate;

    /**
     * 检验报告编号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 检验机构
     */
    @TableField("inspection_org")
    private String inspectionOrg;

    /**
     * 发证机关
     */
    @TableField("issuing_authority")
    private String issuingAuthority;

    /**
     * 使用单位
     */
    @TableField("use_org")
    private String useOrg;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private String dutyUserId;

    private String dutyUserName;

    private Integer deadlineDays;

}
