package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 设备类型扩展属性 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentCategoryPropDto", description = "设备类型扩展属性返回数据模型")
public class EquipmentCategoryPropDto {


    @ApiModelProperty(value = "组名称")
    private String groupName;

    @ApiModelProperty(value = "属性名称")
    private List<EquipmentCategoryPropDetailDto> children;

}