package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.auth.EquipmentOrgAuthDto;
import cn.getech.ehm.equipment.service.IEquipmentOrgAuthService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 设备部门权限控制器
 * <AUTHOR>
 * @since 2021-04-07
 */
@RestController
@RequestMapping("/info/auth")
@Api(tags = "设备部门权限接口")
public class EquipmentOrgAuthController {
    @Autowired
    private IEquipmentOrgAuthService orgAuthService;

    @ApiOperation("根据父节点code获取部门集合")
    @GetMapping("/list")
    @ApiImplicitParams(@ApiImplicitParam(name="orgCode",value="父节点编码，根节点null",dataType="string"))
    public RestResponse<List<EquipmentOrgAuthDto>> getList(@RequestParam(required = false) String orgCode){
        return RestResponse.ok(orgAuthService.getList(orgCode));
    }
    @ApiOperation(value="修改部门权限")
    @PostMapping("/edit")
    public RestResponse<Boolean> update(@RequestBody EquipmentOrgAuthDto dto) {
        return RestResponse.ok(orgAuthService.updateByParam(dto));
    }

}
