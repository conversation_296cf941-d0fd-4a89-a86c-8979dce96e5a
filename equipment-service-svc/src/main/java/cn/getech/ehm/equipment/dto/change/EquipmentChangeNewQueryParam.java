package cn.getech.ehm.equipment.dto.change;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 设备异动表查询参数
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "设备异动表查询参数", description = "设备异动表查询参数")
public class EquipmentChangeNewQueryParam extends PageParam {
    @ApiModelProperty("申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty("申请人uid集合")
    private List<String> uidList;

    @ApiModelProperty("申请人集合")
    private List<String> optUserNames;

    @ApiModelProperty("设备id")
    private String equipmentId;

    @ApiModelProperty("变动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startChangeDate;

    @ApiModelProperty("变动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endChangeDate;

    @ApiModelProperty("类型列表")
    private Integer type;

    @ApiModelProperty("是否为我审批")
    private Boolean isMyTodo;

    @ApiModelProperty("异动id")
    private String id;

    @ApiModelProperty("当前用户uid")
    private String currentUid;

    @ApiModelProperty("排序")
    private String sortValue;

    @ApiModelProperty("设备ids")
    private List<String> equipmentIds;

    @ApiModelProperty("审批状态")
    private Integer status;

}
