package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.equipment.dto.special.EquipmentSpecialDto;
import cn.getech.ehm.part.dto.PartSupplierDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备部件 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "EquipmentStructureResDto", description = "设备部件返回数据模型")
public class EquipmentStructureResDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型id")
    private String partCategoryId;

    @ApiModelProperty(value = "备件类型名称")
    private String partCategoryName;

    @ApiModelProperty(value = "备件类型全路径")
    private String partAllCategoryName;

    @ApiModelProperty(value = "备件编码")
    private String partCode;

    @ApiModelProperty(value = "当前库存")
    private BigDecimal partCurrentStock;

    @ApiModelProperty(value = "二级备件父节点id")
    private String parentId;

    @ApiModelProperty(value = "数量")
    private Integer num;

    @ApiModelProperty(value = "位置")
    private String location;

    @ApiModelProperty(value = "子部件")
    private List<EquipmentStructureResDto> children;

    @ApiModelProperty(value = "SN号")
    private String sn;

    @ApiModelProperty(value = "上机时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date computerTime;

    @ApiModelProperty(value = "下机时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date downComputerTime;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "是否生成特种设备")
    private Boolean convertSpecialInfo = false;

    @ApiModelProperty(value = "特种设备详情")
    private EquipmentSpecialDto specialDto;

    @ApiModelProperty(value = "厂商id")
    private String supplierId;

    @ApiModelProperty(value = "厂商名称")
    private String supplierName;

    @ApiModelProperty(value = "可选厂商集合")
    private List<PartSupplierDto> supplierDtos  = new ArrayList<>();

    @ApiModelProperty(value = "是否拥有测点")
    private Boolean haveMeasuring = false;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "类型(0无1轴承2齿轮)")
    private String spareType;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    @ApiModelProperty(value = "轴承厂家")
    private String factoryName;

    @ApiModelProperty(value = "是否零件级")
    private Boolean sparePart;

    @ApiModelProperty(value = "是否配置零件数据")
    private Boolean haveSpareParam = false;
}