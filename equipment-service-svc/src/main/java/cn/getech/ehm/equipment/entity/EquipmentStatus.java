package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备状态标记时间
 * <AUTHOR>
 * @since 2020-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "equipment_status")
public class EquipmentStatus extends BaseEntity {

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 运行状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 标记时间
     */
    @TableField("mark_time")
    private Date markTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
