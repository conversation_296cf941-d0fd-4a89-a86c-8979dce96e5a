package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 零部件类型
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("spare_parts_category")
public class SparePartsCategory extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型(1部件2参数)
     */
    @TableField("type")
    private Integer type;

    /**
     * 基础库类型id
     */
    @TableField("basic_library_id")
    private String basicLibraryId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;
}
