package cn.getech.ehm.equipment.dto.special;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 特种设备查询参数
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentSpecialQueryParam", description = "特种设备查询参数")
public class EquipmentSpecialQueryParam extends PageParam {

    @ApiModelProperty(value = "类型Id")
    private String categoryId;

    @ApiModelProperty(value = "位置Id")
    private String locationId;

    @ApiModelProperty(value = "类型(1设备2部件)")
    private Integer type;

    @ApiModelProperty(value = "备件Ids", hidden = true)
    private List<String> partIds;

    @ApiModelProperty(value = "登记证号")
    private String certificateNo;

    @ApiModelProperty(value = "注册代码")
    private String registrationCode;

    @ApiModelProperty(value = "开始检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginInspectionDate;

    @ApiModelProperty(value = "结束检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endInspectionDate;

    @ApiModelProperty(value = "下次开始检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginNextDate;

    @ApiModelProperty(value = "下次结束检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endNextDate;

    @ApiModelProperty(value = "检验报告编号")
    private String reportNo;

    @ApiModelProperty(value = "检验机构")
    private String inspectionOrg;

    @ApiModelProperty(value = "发证机关")
    private String issuingAuthority;

    @ApiModelProperty(value = "使用单位")
    private String useOrg;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty("责任人id")
    private String dutyUserId;

    private String dutyUserName;

    @ApiModelProperty("提前提醒天数")
    private Integer deadlineDays;

    private Boolean isOverTime;

    @ApiModelProperty("父级位置id")
    private String parentId;

    @ApiModelProperty("父级位置ids")
    private List<String> parentIds;

    @ApiModelProperty("设备ids")
    private List<String> equipmentIds;
}
