package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.PorosClient;
import cn.getech.ehm.equipment.dto.PorosOrgDetailDto;
import cn.getech.ehm.equipment.dto.auth.EquipmentOrgAuthDto;
import cn.getech.ehm.equipment.entity.EquipmentOrgAuth;
import cn.getech.ehm.equipment.mapper.EquipmentOrgAuthMapper;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.service.IEquipmentOrgAuthService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备部门权限 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class EquipmentOrgAuthImpl extends BaseServiceImpl<EquipmentOrgAuthMapper, EquipmentOrgAuth> implements IEquipmentOrgAuthService {
    @Autowired
    private EquipmentOrgAuthMapper orgAuthMapper;
    @Autowired
    private IEquipmentLocationService locationService;
    @Autowired
    private PorosClient porosClient;

    @Override
    public List<EquipmentOrgAuthDto> getList(String orgCode) {
        List<EquipmentOrgAuthDto> dtos = new ArrayList<>();
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        RestResponse<List<PorosOrgDetailDto>> restResponse = porosClient.getOrgList(orgCode,"0", userBaseInfo.getTenantId(), "web-admin");
        List<PorosOrgDetailDto> orgDetailDtos = new ArrayList<>();
        if(restResponse.isOk()){
            orgDetailDtos = restResponse.getData();
        }else{
            log.error("获取组织信息失败");
        }
        if(CollectionUtils.isNotEmpty(orgDetailDtos)) {
            List<String> orgCodes = orgDetailDtos.stream().map(PorosOrgDetailDto::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<EquipmentOrgAuth> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentOrgAuth::getOrgCode, orgCodes);
            Map<String, EquipmentOrgAuth> map = orgAuthMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentOrgAuth::getOrgCode, v -> v, (v1, v2) -> v1));
            Map<String, Integer> orgUserNumMap = new HashMap<>();
            try {
                RestResponse<Map<String, Integer>> orgUserNumRes = porosClient.getUserCountMapByOrgCodes(orgCodes.toArray(new String[orgCodes.size()]));
                if (orgUserNumRes.isOk()) {
                    orgUserNumMap = orgUserNumRes.getData();
                } else {
                    log.error("获取组织里人员数量失败");
                }
            }catch (Exception e){
                log.error("poros3不存在此接口");
            }
            for(PorosOrgDetailDto detailDto : orgDetailDtos) {
                EquipmentOrgAuthDto authDto = new EquipmentOrgAuthDto();
                authDto.setOrgCode(detailDto.getCode());
                authDto.setOrgName(detailDto.getName());
                authDto.setSystemCode(detailDto.getSystemCode());
                authDto.setTenantId(detailDto.getTenantId());
                authDto.setIsLeaf(detailDto.getLeaf());
                EquipmentOrgAuth orgAuth = map.get(detailDto.getCode());
                if (null != orgAuth) {
                    authDto.setId(orgAuth.getId());
                    authDto.setLocationIds(orgAuth.getLocationIds());
                    if (null != orgAuth.getLocationIds() && orgAuth.getLocationIds().length > 0) {
                        authDto.setLocationNames(locationService.getNamesByIds(orgAuth.getLocationIds()));
                    }
                }else{
                    authDto.setId(UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY));
                }
                Integer userNum = orgUserNumMap.get(detailDto.getCode());
                authDto.setUserNum(null != userNum ? userNum : 0);
                dtos.add(authDto);
            }
        }
        return dtos;
    }

    @Override
    public Boolean updateByParam(EquipmentOrgAuthDto dto) {
        LambdaQueryWrapper<EquipmentOrgAuth> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentOrgAuth::getOrgCode, dto.getOrgCode());
        EquipmentOrgAuth old = orgAuthMapper.selectOne(wrapper);
        EquipmentOrgAuth equipmentOrgAuth = CopyDataUtil.copyObject(dto, EquipmentOrgAuth.class);
        if(null != old){
            return updateById(equipmentOrgAuth);
        }else {
            return save(equipmentOrgAuth);
        }
    }

    @Override
    public List<String> getLocationIdsByOrgCodes(List<String> orgCodes){
        List<String> locationIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orgCodes)) {
            LambdaQueryWrapper<EquipmentOrgAuth> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentOrgAuth::getOrgCode, orgCodes);
            wrapper.select(EquipmentOrgAuth::getId, EquipmentOrgAuth::getLocationIds);
            List<EquipmentOrgAuth> equipmentOrgAuths = orgAuthMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(equipmentOrgAuths)) {
                for (EquipmentOrgAuth entity : equipmentOrgAuths) {
                    if (null != entity.getLocationIds() && entity.getLocationIds().length > 0) {
                        locationIds.addAll(Arrays.asList(entity.getLocationIds()));
                    }
                }
            }
        }
        return locationIds;
    }
}
