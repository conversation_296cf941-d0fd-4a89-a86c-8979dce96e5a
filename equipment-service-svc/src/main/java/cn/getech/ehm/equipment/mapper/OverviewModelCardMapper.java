package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.OverviewModelCard;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 概览模板卡片 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Repository
public interface OverviewModelCardMapper extends BaseMapper<OverviewModelCard> {

    /**
     * 获取geek下所有卡片
     * @return
     */
    @SqlParser(filter = true)
    List<OverviewModelCard> getAllList();
}
