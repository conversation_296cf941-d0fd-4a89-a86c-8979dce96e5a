package cn.getech.ehm.equipment.dto.warn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * iot回调dto
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "IotCallbackDto", description = "iot回调dto")
public class IotCallbackDto {

    /**
     * 功能码(设备信息-device_basic_data、告警信息device_alarm_data)
     */
    @ApiModelProperty(value = "功能码")
    private String functionCode;

    @ApiModelProperty(value = "操作类型(add-update-delete)")
    private String functionType;

    @ApiModelProperty(value = "租户标识")
    private String tenantId;

    /**
     * 数据json字符串--value值为List<Object>转成的json字符串
     */
    @ApiModelProperty(value = "返回数据")
    private String dataContent;

}