package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 概览图详情返回数据模型
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "OverviewDetailNameDto", description = "概览图详情名称")
public class OverviewDetailNameDto {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "名称",required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "类型(1位置2设备)", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "类型对应标识(位置id；设备id)", required = true)
    @NotBlank(message = "标识不能为空")
    private String relationKey;

}