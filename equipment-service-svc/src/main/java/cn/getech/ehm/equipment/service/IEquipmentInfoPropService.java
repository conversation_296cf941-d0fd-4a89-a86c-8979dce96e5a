package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.info.EquipmentPropDetailDto;
import cn.getech.ehm.equipment.dto.info.EquipmentPropMainDto;
import cn.getech.ehm.equipment.entity.EquipmentInfoProp;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 设备扩展属性 服务类
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
public interface IEquipmentInfoPropService extends IBaseService<EquipmentInfoProp> {

    Boolean saveOrUpdateBath(EquipmentPropMainDto mainDto, String equipmentId, Boolean isAdd);

    Boolean deleteByInfoId(String equipmentId);

    /**
     * 获取设备的校准日期
     * @param equipmentId
     * @return
     */
    String getCalibrationDateByEquipmentId(String equipmentId);

    /**
     * 获取多台设备的校准日期
     * @param equipmentIds
     * @return
     */
    List<EquipmentInfoProp> getListByEquipmentIds(String[] equipmentIds);


    /**
     * 获取设备扩展属性
     * @param equipmentId
     * @return
     */
    EquipmentPropMainDto getListByInfoId(String equipmentId, String categoryId);

    /**
     * 获取设备对应属性键值对
     * @param equipmentIds
     * @param categoryId
     * @return
     */
    Map<String, List<EquipmentPropDetailDto>> getPropMap(List<String> equipmentIds, String categoryId, Boolean exported);


    /**
     * 判断设备校准日期是否修改
     * @param equipmentId
     * @param defaultValue
     * @return
     */
    Boolean checkCalibrationDate(String equipmentId, String defaultValue);

    /**
     * 根据属性值查询设备id
     * @param property
     * @return
     */
    List<String> searchEquipmentIds(String property);
}