package cn.getech.ehm.equipment.service;

import cn.getech.ehm.base.dto.EagerTreeNodeDto;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.base.dto.OeeOpenDto;
import cn.getech.ehm.equipment.dto.CategoryStatisticsDto;
import cn.getech.ehm.equipment.dto.category.*;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.poros.framework.common.service.IBaseService;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.Map;

/**
 * 设备类型 服务类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
public interface IEquipmentCategoryService extends IBaseService<EquipmentCategory> {

        /**
         * 根据父节点获取子节点
         * @param  parentId
         * @return
         */
        List<EquipmentCategoryTreeDto> node(String parentId, Integer isClient);

        /**
         * 类型树
         * @return
         */
        List<EquipmentCategoryTreeDto> tree();

        /**
         * 根据编码集合获取设备类型名称集合
         * @param codes
         * @return
         */
        Map<String, String> getCategoryNamesByCodes(String[] codes);

        /**
         * 搜索
         * @param keyword
         * @return
         */
        List<EquipmentCategoryDto> queryList(String keyword);

        /**
         * 保存
         * @param equipmentCategoryAddParam
         * @return
         */
        EquipmentCategoryTreeDto saveByParam(EquipmentCategoryAddParam equipmentCategoryAddParam);

        /**
         * 获取父节点属性
         * @param categoryId
         * @return
         */
        List<EquipmentCategoryPropDto> getParentProps(String categoryId);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        EquipmentCategoryDto getDtoById(String id);

        /**
         * 层级编码
         * @param idList
         * @return
         */
        Map<String, String> fetchLayerCode(List<String> idList);

        /**
         * 更新
         * @param equipmentCategoryEditParam
         * @return
         */
        Boolean updateByParam(EquipmentCategoryEditParam equipmentCategoryEditParam);

        /**
         * 根据id查询校准项
         * @param id
         * @return
         */
        CategoryCalibrationDto getCalibrationById(String id);

        /**
         * 更新
         * @param categoryCalibrationDto
         * @return
         */
        Boolean updateCalibration(CategoryCalibrationDto categoryCalibrationDto);

        /**
         * 逻辑删除
         * @param id
         * @return
         */
        Boolean deleteById(String id);

        /**
         * 根据编码批量逻辑删除
         */
        Boolean deleteByCodes(List<String> codes, String tenantId);

        /**
         * 初始化公用设备类型数据
         * @return
         */
        Boolean initialization(String tenantId);

        /**
         * 根据名称获取名称，id键值对
         * @param categoryNames
         * @return
         */
        Map<String, EquipmentCategory> getMapByNames(List<String> categoryNames);

        /**
         * 获取位置全路径名称，id键值对，保证唯一性
         * @return
         */
        Map<String, EquipmentCategory> getAllNameIdMap();

        /**
         * 获取设备类型map
         * @return
         */
        Map<String, CategoryStatisticsDto> getCategoryStatistics();

        /**
         * 获取设备中心字段在数据库中个条数
         * @param tableName
         * @param columnName
         * @param value
         * @return
         */
        Integer getCount(String tableName, String columnName, String value);

        /**
         * 获取所有设备类型树
         * @return
         */
        @GetMapping("/getAllNode")
        List<EagerTreeNodeDto> getAllNode();

        /**
         * 获取类型名称模糊查询code
         * @return
         */
        List<String> getCodesByName(String name);

        /**
         * 根据设备类型ParentId 查询所有该类型的子类型
         * @param categoryIds
         * @return
         */
        List<String> getCategoryIdsByParentId(List<String> categoryIds);

        /**
         * 设备导入
         * @param excels
         * @return
         */
        Boolean excelImport(List<EquipmentCategoryExcel> excels);

        /**
         * 类型导入厂务
         * @param excels
         * @return
         */
        Boolean excelImportCW(List<CategoryCWExcel> excels);

        /**
         * 获取扩展属性集合
         * @param categoryId
         * @return
         */
        List<String> getPropList(String categoryId, Boolean exported);

        /**
         * 获取设备类型向上depth级的名称集合
         * @param categoryIds
         * @return
         */
        Map<String, String> getDepthName(List<String> categoryIds);

        /**
         * 根据id获取名称，id键值对
         * @param categoryIds
         * @return
         */
        Map<String, String> getMapByIds(String[] categoryIds);

        /**
         * 获取全部设备类型
         * @return
         */
        List<CategoryInfoDto> getAllCategory(List<String> categoryIds);

        /**
         * 根据ids获取dtos(包含子节点)
         * @return
         */
        List<CategoryInfoDto> getDtosIncludeChild(List<String> categoryIds);

        /**
         * 获取设备类型全路径
         * @return
         */
        Map<String, String> getCategoryAllNameMap();

        /**
         * 获取设备类型oee/状态参数开关/图片
         * @return
         */
        EquipmentCategoryDto getCategorySummary(String categoryId);

        /**
         * 开关设备默认状态参数
         */
        Boolean enabledDefaultStatusParam(String categoryId, Boolean enabled);

        /**
         * 获取基础数据
         * @param id
         * @return
         */
        CategorySummaryDto getSummaryDto(String id);

        /**
         * 更新预置参数
         * @param dto
         * @return
         */
        Boolean updatePresetParam(CategorySummaryDto dto);
}