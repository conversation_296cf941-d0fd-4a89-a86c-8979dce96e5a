package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.enmu.DeteriorationTrendType;
import cn.getech.ehm.equipment.enmu.ReportCreateType;
import cn.getech.ehm.equipment.enmu.ReportStatusType;
import cn.getech.ehm.equipment.schedule.DiagnosisReportAutoSchedule;
import cn.getech.ehm.equipment.service.IDiagnosisReportAutoService;
import cn.getech.ehm.equipment.service.IDiagnosisReportV2Service;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 诊断报告v2控制器
 */
@RestController
@RequestMapping("/diagnosisReport/v2")
@Api(tags = "诊断报告v2服务接口")
public class DiagnosisReportV2Controller {

    @Autowired
    private IDiagnosisReportV2Service diagnosisReportService;
    @Autowired
    private IDiagnosisReportAutoService autoService;
    @Autowired
    private DiagnosisReportAutoSchedule autoSchedule;

    @ApiOperation("分页获取诊断报告列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<DiagnosisReportV2ListDto>> pageList(@RequestBody @Valid DiagnosisReportV2QueryParam queryParam){
        return RestResponse.ok(diagnosisReportService.pageDto(queryParam));
    }

    @ApiOperation("新增诊断报告")
    @AuditLog(title = "诊断报告",desc = "新增诊断报告",businessType = BusinessType.INSERT)
    @PostMapping
    public RestResponse<String> add(@RequestBody @Valid DiagnosisReportV2AddParam addParam) {
        return RestResponse.ok(diagnosisReportService.saveByParam(addParam));
    }

    @ApiOperation(value="修改诊断报告")
    @AuditLog(title = "诊断报告",desc = "修改诊断报告",businessType = BusinessType.UPDATE)
    @PutMapping
    public RestResponse<Boolean> update(@RequestBody @Valid DiagnosisReportV2EditParam editParam) {
        return RestResponse.ok(diagnosisReportService.updateByParam(editParam));
    }

    @ApiOperation(value="根据id删除诊断报告")
    @AuditLog(title = "诊断报告",desc = "诊断报告",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(diagnosisReportService.deleteByIds(Arrays.asList(ids)));
    }

    @ApiOperation(value = "根据id获取诊断报告")
    @GetMapping(value = "/{id}")
    public RestResponse<DiagnosisReportV2Dto> get(@PathVariable("id")  String id) {
        return RestResponse.ok(diagnosisReportService.getDtoById(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "/exportWord")
    public void exportWord(@RequestParam("id") String id, @RequestParam("url") String url, HttpServletResponse response) {
        File file = diagnosisReportService.exportWord(id, url);
        WordDownloadUtil.download(response, file, "");
        if (file.exists()) {
            file.delete();
        }
    }

    @ApiOperation("获取报告类型枚举")
    @GetMapping("/getReportCreateTypes")
    public RestResponse<List<EnumListDto>> getReportCreateTypes(){
        return RestResponse.ok(ReportCreateType.getList());
    }

    @ApiOperation("获取发布状态枚举")
    @GetMapping("/getReportStatusTypes")
    public RestResponse<List<EnumListDto>> getReportStatusTypes(){
        return RestResponse.ok(ReportStatusType.getList());
    }

    @ApiOperation("获取劣化趋势枚举")
    @GetMapping("/getDeteriorationTrendTypes")
    public RestResponse<List<EnumListDto>> getDeteriorationTrendTypes(){
        return RestResponse.ok(DeteriorationTrendType.getList());
    }

    @ApiOperation("分页获取智能诊断报告列表")
    @PostMapping("/auto/pageList")
    public RestResponse<PageResult<DiagnosisReportAutoDto>> autoPageList(@RequestBody @Valid DiagnosisReportAutoQueryParam queryParam){
        return RestResponse.ok(autoService.pageDto(queryParam));
    }

    @ApiOperation("新增/编辑智能诊断报告")
    @PostMapping("/auto/edit")
    public RestResponse<Boolean> editByParam(@RequestBody @Valid DiagnosisReportAutoDto dto) {
        return RestResponse.ok(autoService.editByParam(dto));
    }

    @ApiOperation(value="根据id删除智能诊断报告")
    @DeleteMapping("/auto/{ids}")
    public RestResponse<Boolean> deleteAuto(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(autoService.deleteByIds(Arrays.asList(ids)));
    }

    @ApiOperation(value = "触发诊断报告(用作测试)")
    @GetMapping(value = "/releaseReport")
    public RestResponse<Boolean> releaseReport() {
        autoSchedule.releaseReport();
        return RestResponse.ok();
    }
}
