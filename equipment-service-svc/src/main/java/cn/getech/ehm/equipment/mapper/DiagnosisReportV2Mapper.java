package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 诊断报告V2 Mapper 接口
 */
@Repository
public interface DiagnosisReportV2Mapper extends BaseMapper<EquipmentDiagnosisReport> {
    /**
     * 分页查询，返回Dto
     * @param param
     * @return
     */
    IPage<DiagnosisReportV2ListDto> pageList(Page<DiagnosisReportV2QueryParam> page,
                                             @Param("param") DiagnosisReportV2QueryParam param);

    /**
     * 获取最大编号
     *
     * @param prex
     * @return
     */
    String getMaxCode(@Param("prex") String prex);
}
