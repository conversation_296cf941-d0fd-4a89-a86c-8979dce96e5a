package cn.getech.ehm.equipment.dto.diagnosis;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 诊断报告V2返回列表
 */
@Data
@ApiModel(value = "DiagnosisReportV2ListDto", description = "诊断报告V2返回列表")
public class DiagnosisReportV2ListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "报告类型")
    private Integer type;

    @ApiModelProperty(value = "发布状态")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    @ApiModelProperty(value = "发布人")
    private String createUserName;

}