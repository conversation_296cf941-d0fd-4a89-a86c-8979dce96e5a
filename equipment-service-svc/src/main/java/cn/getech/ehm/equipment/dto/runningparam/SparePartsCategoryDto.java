package cn.getech.ehm.equipment.dto.runningparam;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 结构类型数据模型
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "SparePartsCategoryDto", description = "零部件类型数据模型")
public class SparePartsCategoryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

    @ApiModelProperty(value = "基础库类型id")
    private String basicLibraryId;

    @ApiModelProperty(value = "基础库")
    private String basicLibraryName;

    @ApiModelProperty(value = "特征参数")
    private Long featureNum;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

}
