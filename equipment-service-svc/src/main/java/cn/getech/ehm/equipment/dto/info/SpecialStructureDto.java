package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 特种部件 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "SpecialStructureDto", description = "特种部件返回数据模型")
public class SpecialStructureDto {

    @ApiModelProperty(value = "部件id")
    private String structureId;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型id")
    private String partCategoryId;

    @ApiModelProperty(value = "备件类型名称")
    private String partCategoryName;

    @ApiModelProperty(value = "备件类型全路径")
    private String partAllCategoryName;

    @ApiModelProperty(value = "备件编码")
    private String partCode;

    @ApiModelProperty(value = "备件规格型号")
    private String partSpecification;

    @ApiModelProperty(value = "当前库存")
    private BigDecimal partCurrentStock;

    @ApiModelProperty(value = "二级备件父节点id")
    private String parentId;

    @ApiModelProperty(value = "二级备件父节点备件id")
    private String parentPartId;

    @ApiModelProperty(value = "二级备件父节点id")
    private String parentName;

    @ApiModelProperty(value = "位置")
    private String location;
}