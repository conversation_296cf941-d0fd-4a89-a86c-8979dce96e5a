package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.bearing.*;
import cn.getech.ehm.equipment.service.IBasicLibraryDetailService;
import cn.getech.ehm.equipment.service.IBasicLibraryFieldService;
import cn.getech.ehm.equipment.service.IBasicLibraryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 基础库服务接口
 */
@RestController
@RequestMapping("/basicLibrary")
@Api(tags = "基础库服务接口")
public class BasicLibraryController {
    @Autowired
    private IBasicLibraryService basicLibraryService;
    @Autowired
    private IBasicLibraryFieldService fieldService;
    @Autowired
    private IBasicLibraryDetailService detailList;

    @ApiOperation("获取基础库类型")
    @GetMapping("/getList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型(1部件2零件)", dataType = "string", paramType = "query")})
    public RestResponse<List<BasicLibraryDto>> getList(@RequestParam Integer type) {
        return RestResponse.ok(basicLibraryService.getList(type));
    }

    @ApiOperation("新增/编辑基础库类型")
    @PostMapping("/edit")
    public RestResponse<Boolean> edit(@RequestBody BasicLibraryDto dto) {
        return RestResponse.ok(basicLibraryService.edit(dto));
    }

    @ApiOperation(value="根据id删除基础库类型")
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> deleteById(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(basicLibraryService.deleteById(id));
    }

    @ApiOperation("获取基础库字段")
    @GetMapping("/getFields")
    public RestResponse<List<BasicLibraryFieldDto>> getFields(@RequestParam String basicLibraryId,@RequestParam(required = false) Boolean includeModelFactory, @RequestParam(required = false) String filterSparePartId) {
        return RestResponse.ok(fieldService.getList(basicLibraryId, includeModelFactory, filterSparePartId));
    }

    @ApiOperation("新增基础库字段")
    @PostMapping("/addField")
    public RestResponse<Boolean> addField(@RequestBody BasicLibraryFieldDto dto) {
        return RestResponse.ok(fieldService.addField(dto));
    }

    @ApiOperation("编辑基础库字段")
    @PostMapping("/editField")
    public RestResponse<Boolean> editField(@RequestBody List<BasicLibraryFieldDto> dtos) {
        return RestResponse.ok(fieldService.editField(dtos));
    }

    @ApiOperation("分页获取基础库详情列表")
    @PostMapping("/detailList")
    public RestResponse<PageResult<BasicLibraryDetailDto>> detailList(@RequestBody BasicLibraryQueryParam queryParam){
        return RestResponse.ok(detailList.detailList(queryParam));
    }

    @ApiOperation("新增/编辑基础库详情")
    @PostMapping("/editDetail")
    public RestResponse<Boolean> editDetail(@RequestBody Object dto) {
        return RestResponse.ok(detailList.editDetail(dto));
    }

    @ApiOperation(value="根据id删除基础库详情")
    @DeleteMapping("/deleteDetail/{id}")
    public RestResponse<Boolean> deleteDetail(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(detailList.deleteDetail(id));
    }

    @ApiOperation("只同步旧轴承数据(用于升级,不同步bearing_factory/bearing_model这两张表)")
    @GetMapping("/addBearingDb")
    public RestResponse<Boolean> addDb() {
        return RestResponse.ok(detailList.addBearingDb());
    }

    @ApiOperation("同步geek下基础库相关数据")
    @GetMapping("/initialization")
    public RestResponse<Boolean> initialization(@RequestParam String tenantId) {
        return RestResponse.ok(basicLibraryService.initialization(tenantId));
    }
}
