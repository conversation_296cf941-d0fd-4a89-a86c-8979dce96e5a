package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备部件Map
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "EquipmentStructureDetailDto", description = "设备部件查询")
public class EquipmentStructureDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;
}