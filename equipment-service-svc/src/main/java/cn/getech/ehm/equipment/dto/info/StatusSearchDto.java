package cn.getech.ehm.equipment.dto.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 设备运行状态统计
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "StatusSearchDto", description = "设备运行状态统计")
public class StatusSearchDto {

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1位置11主设备12子设备)")
    private Integer type;

    @ApiModelProperty("搜索字符串")
    private String keyword;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型Ids", hidden = true)
    private List<String> categoryIds;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "父级Ids", hidden = true)
    private List<String> parentIds;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "投产开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginCompletionTime;

    @ApiModelProperty(value = "投产结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCompletionTime;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "运行状态")
    private List<Integer> runningStatus;

    @ApiModelProperty(value = "设备ids")
    private List<String> equipmentIds;

}