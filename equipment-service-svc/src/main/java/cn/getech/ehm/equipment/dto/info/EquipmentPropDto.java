package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 设备扩展属性
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentPropDto", description = "设备扩展属性")
public class EquipmentPropDto {

    @ApiModelProperty(value = "组名称")
    private String groupName;

    @ApiModelProperty(value = "属性名称")
    private List<EquipmentPropDetailDto> children;

}