package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.authorization.EquipmentAuthorizationDto;
import cn.getech.ehm.equipment.entity.EquipmentAuthorization;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 设备授权 服务类
 *
 * <AUTHOR>
 * @since 2020-08-07
 */
public interface IEquipmentAuthorizationService extends IBaseService<EquipmentAuthorization> {

        /**
         * 获取设备授权对象
         * @param code
         * @param isStaff
         * @return
         */
        EquipmentAuthorizationDto getEquipmentAuthorization(String code, String parentCode, boolean isStaff);

        /**
         * 获取当前登录用户设备id权限集合
         * @return
         */
        List<String> getCurrentUserAuz();

        /**
         * 获取当前code下所有设备id集合(如果没有继承父级)
         * @param code
         * @return
         */
        String[] getCurrentEquipmentIds(String code);
}