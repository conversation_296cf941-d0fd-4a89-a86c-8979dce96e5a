package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 设备测点返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentPointDto", description = "设备测点返回数据模型")
public class EquipmentInfoPointDto {

    @ApiModelProperty("测点id/部件id")
    private String id;

    @ApiModelProperty("测点名称/部件名称")
    private String name;

    @ApiModelProperty(value = "二级备件父节点id", hidden = true)
    private String parentId;

    @ApiModelProperty("类型(0设备1测点2部件)")
    private Integer type;

    @ApiModelProperty(value = "子集")
    private List<EquipmentInfoPointDto> children;

    @ApiModelProperty("是否拥有参数")
    private Boolean haveParameter = false;
}