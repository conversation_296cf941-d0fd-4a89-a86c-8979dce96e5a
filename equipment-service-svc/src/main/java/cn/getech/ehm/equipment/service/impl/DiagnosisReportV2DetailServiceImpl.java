package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.diagnosis.ReportV2DetailDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReportDetail;
import cn.getech.ehm.equipment.mapper.DiagnosisReportV2DetailMapper;
import cn.getech.ehm.equipment.service.IDiagnosisReportV2DetailService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 诊断报告v2详情 服务实现类
 */
@Slf4j
@Service
public class DiagnosisReportV2DetailServiceImpl extends BaseServiceImpl<DiagnosisReportV2DetailMapper, EquipmentDiagnosisReportDetail> implements IDiagnosisReportV2DetailService {

    @Autowired
    private DiagnosisReportV2DetailMapper detailMapper;

    @Override
    public Boolean saveByParam(String reportId, List<String> equipmentIds){
        List<EquipmentDiagnosisReportDetail> detailList = new ArrayList<>();
        Integer sort = 1;
        for(String equipmentId : equipmentIds){
            EquipmentDiagnosisReportDetail detail = new EquipmentDiagnosisReportDetail();
            detail.setReportId(reportId);
            detail.setEquipmentId(equipmentId);
            detail.setFollow(false);
            detail.setSort(sort++);
            detailList.add(detail);
        }
        return saveBatch(detailList);
    }

    @Override
    public List<ReportV2DetailDetailDto> getListByReportId(String reportId){
        return detailMapper.getListByReportId(reportId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateByParam(String reportId, List<ReportV2DetailDetailDto> followEquipments, List<ReportV2DetailDetailDto> equipmentList){
        this.deleteByReportIds(Collections.singletonList(reportId));
        Map<String, ReportV2DetailDetailDto> followMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(followEquipments)){
            followMap = followEquipments.stream().collect(Collectors.toMap(ReportV2DetailDetailDto::getEquipmentId, v -> v, (k1, k2) -> k1));
        }
        if(CollectionUtils.isNotEmpty(equipmentList)){
            List<EquipmentDiagnosisReportDetail> detailList = new ArrayList<>();
            Integer sort = 1;
            for(ReportV2DetailDetailDto detailDto : equipmentList){
                EquipmentDiagnosisReportDetail detail = CopyDataUtil.copyObject(detailDto, EquipmentDiagnosisReportDetail.class);
                detail.setReportId(reportId);
                ReportV2DetailDetailDto followEquipment = followMap.get(detailDto.getEquipmentId());
                detail.setFollow(null != followEquipment ? true : false);
                detail.setFollowContent(null != followEquipment ? followEquipment.getFollowContent() : "");
                if(CollectionUtils.isNotEmpty(detailDto.getAnalyzePicInfoDtos())){
                    detail.setAnalyzePicInfo(JSONObject.toJSONString(detailDto.getAnalyzePicInfoDtos()));
                }
                detail.setSort(sort++);
                detailList.add(detail);
            }
            this.saveBatch(detailList);
        }
        return true;
    }

    @Override
    public Boolean deleteByReportIds(List<String> reportIds){
        LambdaQueryWrapper<EquipmentDiagnosisReportDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentDiagnosisReportDetail::getReportId, reportIds);
        return this.remove(wrapper);
    }

    @Override
    public List<String> getEquipmentIdByReportId(String reportId){
        LambdaQueryWrapper<EquipmentDiagnosisReportDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentDiagnosisReportDetail::getReportId, reportId);
        wrapper.select(EquipmentDiagnosisReportDetail::getId,EquipmentDiagnosisReportDetail::getEquipmentId);
        return detailMapper.selectList(wrapper).stream().map(EquipmentDiagnosisReportDetail::getEquipmentId).distinct().collect(Collectors.toList());
    }
}
