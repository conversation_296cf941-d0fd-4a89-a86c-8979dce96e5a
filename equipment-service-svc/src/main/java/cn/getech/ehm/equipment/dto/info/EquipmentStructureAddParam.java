package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备部件 新增参数对象
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentStructureAddParam", description = "设备部件新增参数")
public class EquipmentStructureAddParam extends ApiParam {

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "零部件id")
    private String sparePartsCategoryId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

    @ApiModelProperty(value = "是否监测")
    private Boolean monitored;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    private String partId;

}
