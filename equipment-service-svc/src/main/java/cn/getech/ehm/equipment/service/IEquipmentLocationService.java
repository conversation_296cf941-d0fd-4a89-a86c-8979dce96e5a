package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.LocationStatisticsDto;
import cn.getech.ehm.equipment.dto.LocationSummaryDto;
import cn.getech.ehm.equipment.dto.info.BomParentDto;
import cn.getech.ehm.equipment.dto.info.EquipmentSortEditParam;
import cn.getech.ehm.equipment.dto.overview.EquipmentNumDto;
import cn.getech.ehm.equipment.dto.overview.IntactRateDto;
import cn.getech.ehm.equipment.dto.info.LazyEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.location.*;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.dto.RelSearchDto;
import cn.getech.ehm.iot.dto.warn.CardWarnStatisticDto;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.poros.framework.common.service.IBaseService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备位置 服务类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
public interface IEquipmentLocationService extends IBaseService<EquipmentLocation> {

        /**
         * 根据父节点获取子节点
         * 最高节点父节点为0
         * @param  parentId
         * @return
         */
        List<EquipmentLocationTreeDto> node(String parentId);

        /**
         * 根据父节点获取子节点
         * 最高节点父节点为0
         * @param  parentId
         * @return
         */
        List<EquipmentLocationTreeDto> getListByParentId(String parentId, List<String> locationIds);

        /**
         * 懒加载位置树
         * @param parentId
         * @return
         */
        List<LazyEquipmentTreeDto> lazyLocationTree(String parentId, String keyword, List<String> authLocationIds);

        /**
         * 获取位置树
         * @return
         */
        List<LocationEquipmentTreeDto> locationTree(Boolean auth);

        /**
         * 获取位置设备树
         * @return
         */
        List<LocationEquipmentTreeDto> tree(String tenantId);

        /**
         * 递归获取设备树
         * @param parentId
         * @return
         */
        List<LocationEquipmentTreeDto> getTreeListByParentId(String parentId);

        /**
         * 获取所有位置
         * @return
         */
        List<LocationEquipmentTreeDto> getAllTreeList(List<String> locationIds);

        /**
         * 面包屑信息
         * @param id
         * @return
         */
        BomParentDto getBomDtoById(@Param("id") String id);

        /**
         * 查询
         * @param keyword
         * @return
         */
        List<EquipmentLocationDto> queryList(String keyword);

        /**
         * 保存
         * @param equipmentLocationAddParam
         * @return
         */
        EquipmentLocationDto saveByParam(EquipmentLocationAddParam equipmentLocationAddParam);

        /**
         * 清理缓存
         */
        void clearCache();

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        EquipmentLocationDto getDtoById(String id);

        /**
         * 获取当前节点下所有子节点id
         * @param locationId
         * @return
         */
        List<String> getChildIds(String locationId);

        /**
         * 更新
         * @param equipmentLocationEditParam
         * @return
         */
        Boolean updateByParam(EquipmentLocationEditParam equipmentLocationEditParam);

        /**
         * 逻辑删除
         * @param id
         * @return
         */
        Boolean deleteById(String id);

        /**
         * 根据编码批量逻辑删除
         * @return
         */
        Boolean deleteByCodes(List<String> codes, String tenantId);

        /**
         * 根据位置名称获取名称，id键值对
         * @param locationNames
         * @return
         */
        Map<String, String> getMapByNames(List<String> locationNames);

        /**
         * 获取位置全路径(去除根节点集团)名称，id键值对，保证唯一性
         * @return
         */
        Map<String, EquipmentLocation> getAllNameIdMap();

        /**
         * 获取设备位置map
         */
        Map<String, LocationStatisticsDto> getLocationStatistics(String[] locationIds);

        /**
         * 根据设备位置ParentId 查询所有该位置的子类型
         * @param notDeleted 是否未删除
         * @param locationIds
         * @return
         */
        List<String> getLocationIdsByParentId(List<String> locationIds, Boolean notDeleted);

        /**
         * 位置导入
         * @param excels
         * @return
         */
        Boolean excelImport(List<EquipmentLocationExcel> excels);

        /**
         * 位置导入厂务
         * @param excels
         * @return
         */
        Boolean excelImportCW(List<LocationCWExcel> excels);

        /**
         * 根据设备ids获取对应位置id及其父节点
         * @param relSearchDto
         * @return
         */
        List<String> getLocationIdByInfoIds(RelSearchDto relSearchDto);

        /**
         * 判断id是否位置id
         * @param id
         * @return
         */
        Boolean checkIsLocation(String id);

        /**
         * 修改名称
         * @param id
         * @param parentId
         * @param name
         * @return
         */
        Boolean updateName(String id, String parentId, String name);

        /**
         * 新增名称
         * @param parentId
         * @param name
         * @return
         */
        String insertName(String parentId, String name, Integer type);

        /**
         * 获取设备位置向上depth级的名称集合
         * @param locationIds
         * @return
         */
        Map<String, String> getDepthName(List<String> locationIds);

        /**
         * 根据id获取名称，id键值对
         * @param locationIds
         * @return
         */
        Map<String, String> getMapByIds(String[] locationIds);

        /**
         * 获取租户id对应根节点id
         * @return
         */
        Map<String,String> getRootIdMap(List<String> tenantIds);

        /**
         * 设备数量(位置)
         * @param locationId
         * @return
         */
        EquipmentNumDto cardEquipmentNum(String locationId);

        /**
         * 获取子位置设备完好率(取最高12个)
         * @param locationId
         * @return
         */
        List<IntactRateDto> equipmentIntactRate(String locationId);

        /**
         * 概览报警卡片
         * @param locationId
         * @return
         */
        CardWarnStatisticDto cardWarnStatistic(String locationId);

        /**
         * 报警趋势卡片(位置)
         * @param locationId
         * @return
         */
        WarnCountDto cardWarnTrend(String locationId);

        /**
         * 获取根节点下第一级位置
         * @return
         */
        List<EquipmentLocationTreeDto> getRootChild();

        /**
         * 根据id集合获取名称
         * @param locationIds
         * @return
         */
        String getNamesByIds(String[] locationIds);

        /**
         * 获取整条链路的父位置
         */
        List<String> getParentIdsByLocationIds(List<String> locationIds);

        /**
         * 判断父节点是否存在(父节点为位置)
         * @param parentIds
         * @return
         */
        List<String> getExistParentIds(List<String> parentIds);
        public void updateSort(List<EquipmentSortEditParam> param);

        /**
         * 获取位置信息
         * @param locationIds
         * @return
         */
        Map<String, LocationSummaryDto> getMapByLocationIds(String[] locationIds);

}