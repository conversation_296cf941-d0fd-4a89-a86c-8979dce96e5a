package cn.getech.ehm.equipment.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 搜索参数数据模型
 *
 * <AUTHOR>
 * @date 2020-12-02
 */
@Data
@ApiModel(value = "SearchParamDto", description = "搜索参数数据模型")
public class SearchParamDto {

    @ApiModelProperty(value = "关键词。")
    private String keyword;

    @ApiModelProperty(value = "数据类型。1-新闻，2-课程，3-文档，4-订单，5-设备名称，6-设备客户名称")
    private Integer type;

}