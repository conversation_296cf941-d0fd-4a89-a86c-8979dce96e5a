package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.DiagnosisCaseAddParam;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.DiagnosisCase;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;

import java.io.File;

/**
 * <pre>
 * 诊断案例 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IDiagnosisCaseService extends IBaseService<DiagnosisCase> {

        /**
         * 分页查询，返回Dto
         *
         * @param diagnosisCaseQueryParam
         * @return
         */
        PageResult<DiagnosisCaseListDto> pageDto(DiagnosisCaseQueryParam diagnosisCaseQueryParam);

        /**
         * 保存
         * @param diagnosisCaseAddParam
         * @return
         */
        boolean saveByParam(DiagnosisCaseAddParam diagnosisCaseAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        DiagnosisCaseDto getDtoById(String id);

        /**
         * 更新
         * @param diagnosisCaseEditParam
         */
        boolean updateByParam(DiagnosisCaseEditParam diagnosisCaseEditParam);

        /**
         * 导出word
         * @param id
         * @return
         */
        File exportWord(String id, String url);
}