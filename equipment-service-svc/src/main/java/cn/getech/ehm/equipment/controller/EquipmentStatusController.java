package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.EquipmentIotStatusDto;
import cn.getech.ehm.equipment.service.IEquipmentStatusService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备状态接口接口
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/infoStatus")
@Api(tags = "设备状态接口")
@Slf4j
public class EquipmentStatusController {
    @Autowired
    private IEquipmentStatusService statusService;

    @ApiOperation("iot更新设备运行状态记录")
    @PostMapping("/updateStatus")
    public RestResponse<Boolean> updateStatus(@RequestBody EquipmentIotStatusDto dto) {
        return RestResponse.ok(statusService.updateStatus(dto));
    }

    @ApiOperation("iot更新设备报警状态记录")
    @PostMapping("/updateIotStatus")
    public RestResponse<Boolean> updateIotStatus(@RequestBody EquipmentIotStatusDto dto) {
        return RestResponse.ok(statusService.updateIotStatus(dto));
    }
}
