package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运行状态设备数量
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "RunningEquipmentCountDto", description = "运行状态设备数量")
public class RunningEquipmentCountDto {

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备id", hidden = true)
    private String equipmentId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型全名称")
    private String categoryAllName;

    @ApiModelProperty(value = "数量")
    private Integer num;
}