package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.base.dto.TaskAuditParam;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeDto;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeEditParam;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeAddParam;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import java.util.List;
import cn.getech.ehm.equipment.service.IEquipmentChangeService;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备异动控制器
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@RestController
@RequestMapping("/equipmentChange")
@Api(tags = "设备异动服务接口")
public class EquipmentChangeController {
    @Autowired
    private IEquipmentChangeService equipmentChangeService;

    /**
     * 新增设备异动（支持批量）
     */
    @ApiOperation("新增设备异动（支持批量）")
    @AuditLog(title = "设备异动",desc = "新增设备异动",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:change:update")
    public RestResponse<Boolean> addBatch(@RequestBody @Valid EquipmentChangeAddParam equipmentChangeAddParam) {
        return RestResponse.ok(equipmentChangeService.saveBatchByParam(equipmentChangeAddParam));
    }

    /**
     * 修改设备异动（支持批量）
     */
    @ApiOperation("修改设备异动（支持批量）")
    @AuditLog(title = "设备异动",desc = "修改设备异动",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:change:update")
    public RestResponse<Boolean> updateBatch(@RequestBody @Valid EquipmentChangeEditParam equipmentChangeEditParam) {
        return RestResponse.ok(equipmentChangeService.updateBatchByParam(equipmentChangeEditParam));
    }

    @ApiOperation(value = "异动流程审核提交")
    @PostMapping(value = "/submitProcessTask")
    public RestResponse<Boolean> submitProcessTask(@RequestBody TaskAuditParam taskAuditParam) {
        return RestResponse.ok(equipmentChangeService.submitProcessTask(taskAuditParam));
    }

    /**
     * 驳回以后，重新提交
     */
    @ApiOperation(value="驳回以后，重新提交")
    @AuditLog(title = "调拨单",desc = "驳回以后，重新提交",businessType = BusinessType.UPDATE)
    @PostMapping(value = "/againSubmit")
    public RestResponse<Boolean> againSubmit(@RequestBody @Valid TaskAuditParam taskAuditParam) {
        return RestResponse.ok(equipmentChangeService.againSubmit(taskAuditParam));
    }

    /**
     * 根据设备id列表获取设备列表
     */
    @ApiOperation(value = "根据设备id列表获取设备列表")
    @GetMapping(value = "getEquipmentInfoList/{equipmentIds}")
    //@Permission("equipment:list")
    public RestResponse<List<EquipmentInfoDto>> getEquipmentInfoList(@PathVariable String[] equipmentIds) {
        return RestResponse.ok(equipmentChangeService.getEquipmentInfoList(equipmentIds));
    }

    /**
     * 根据设备id获取异动列表
     */
    @ApiOperation(value = "根据设备id列表获取异动列表")
    @GetMapping(value = "/{equipmentId}")
    //@Permission("equipment:list")
    public RestResponse<List<EquipmentChangeDto>> getEquipmentChangeList(@PathVariable String equipmentId) {
        return RestResponse.ok(equipmentChangeService.getEquipmentChangeList(equipmentId));
    }

    /**
     * 根据流程实例ID获取异动表单
     */
    @ApiOperation(value = "根据流程实例ID获取异动表单")
    @GetMapping("/getProcessInstanceId")
    RestResponse<EquipmentChangeDto> getEquipChangeByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        return RestResponse.ok(equipmentChangeService.getEquipChangeByProcessInstanceId(processInstanceId));
    }

}
