package cn.getech.ehm.equipment.dto.runningparam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 零部件类型运行参数变量
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "ParameterVariableDto", description = "零部件类型运行参数变量")
public class ParameterVariableDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "运行参数参数变量id")
    private String runningParameterId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "来源(1录入2参数)")
    private Integer sourceType;

    @ApiModelProperty(value = "值(来源参数为运行参数id,否则为输入)")
    private String value;

    @ApiModelProperty(value = "来源为运行参数,对应名称;输入的为值")
    private String valueName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "变量描述")
    private String remark;

}
