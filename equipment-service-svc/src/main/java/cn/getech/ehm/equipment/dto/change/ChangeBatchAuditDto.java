package cn.getech.ehm.equipment.dto.change;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备异动批量审批参数
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChangeBatchAuditDto", description = "设备异动批量审批参数")
public class ChangeBatchAuditDto extends ApiParam {

    @ApiModelProperty("异动ids")
    private List<String> ids;

    @ApiModelProperty(value = "审批结果 1 同意 0 驳回", required = true)
    @NotNull(message = "审批结果不能为空")
    private Integer result;

    @ApiModelProperty("处理意见")
    protected String comment;
}
