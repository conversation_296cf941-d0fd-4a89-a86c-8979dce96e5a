package cn.getech.ehm.equipment.dto.app;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 搜索参数数据模型
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Data
@ApiModel(value = "SearchPageParam", description = "搜索参数数据模型")
public class SearchPageParam extends PageParam {

    @ApiModelProperty(value = "数据类型。5-设备名称 6-客户名称")
    private Integer type;

    @ApiModelProperty(value = "是否为客户")
    private Boolean isCustomer;

    @ApiModelProperty(value = "客户Id")
    private String customerId;

}