package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.bearing.BasicLibraryDetailDto;
import cn.getech.ehm.equipment.dto.bearing.BasicLibraryQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryDetail;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 基础库详情Mapper
 */
@Repository
public interface BasicLibraryDetailMapper extends BaseMapper<BasicLibraryDetail> {

    /**
     * 分页查询，返回Dto
     * @param param
     * @return
     */
    IPage<BasicLibraryDetailDto> pageList(Page<BasicLibraryQueryParam> page,
                                          @Param("param") BasicLibraryQueryParam param);
    /**
     * 获取最大排序
     * @return 最大排序
     */
    Integer getMaxSort(@Param("basicLibraryId") String basicLibraryId);

    /**
     * 获取默认geek下基础库详情
     * @return
     */
    @SqlParser(filter = true)
    List<BasicLibraryDetail> getDefaultDetails();
}
