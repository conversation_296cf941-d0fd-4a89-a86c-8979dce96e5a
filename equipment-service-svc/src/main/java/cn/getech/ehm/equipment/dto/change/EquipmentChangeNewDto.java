package cn.getech.ehm.equipment.dto.change;


import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备异动 返回列表参数
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "设备异动返回列表参数", description = "设备异动返回列表参数")
public class EquipmentChangeNewDto extends ApiParam {

    @ApiModelProperty("异动id")
    private String id;

    @ApiModelProperty("设备ID")
    private String equipmentId;

    @ApiModelProperty("设备编码")
    private String equipmentCode;

    @ApiModelProperty("设备名称")
    private String equipmentName;

    @ApiModelProperty("异动类型(0-位置,1-状态)")
    private Integer type;

    @ApiModelProperty("初始值")
    private String initialValue;

    @ApiModelProperty("目标值")
    private String targetValue;

    @ApiModelProperty("位置异动的初始父节点id")
    private String parentId;

    @ApiModelProperty("位置异动的目标父节点id")
    private String targetParentId;

    @ApiModelProperty("位置异动的目标类型1-位置 11主设备 12子设备")
    private Integer targetParentType;

    @ApiModelProperty("流程实例ID")
    private String processInstanceId;

    @ApiModelProperty("任务id")
    private String activityId;

    @ApiModelProperty("操作用户列表")
    private String processUser;

    @ApiModelProperty("流程状态(0待审批 1审批通过 2审批驳回)")
    private Integer status;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("申请人名称")
    private String createUserName;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("申请人uid")
    private String createBy;

    @ApiModelProperty("变动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeDate;

    @ApiModelProperty("原因备注")
    private String remark;

    @ApiModelProperty("操作人人名称")
    private String processUserName;

    @ApiModelProperty("是否具备审批权限")
    private Boolean isApproval;

    @ApiModelProperty("是否具备删除权限")
    private Boolean canDeleted;
}
