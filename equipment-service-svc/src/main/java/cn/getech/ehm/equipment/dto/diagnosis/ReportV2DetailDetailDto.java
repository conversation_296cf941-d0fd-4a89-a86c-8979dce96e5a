package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 诊断报告V2详情
 */
@Data
@ApiModel(value = "ReportV2DetailDetailDto", description = "诊断报告V2详情")
public class ReportV2DetailDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "序号")
    private Integer num;

    @ApiModelProperty(value = "报告id")
    private String reportId;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编号")
    private String equipmentCode;

    @ApiModelProperty(value = "设备健康状态")
    private Integer healthStatus;

    @ApiModelProperty(value = "设备健康状态名称")
    private String healthStatusName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "上级节点名称全路由")
    private String parentAllName;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "类型名称全路由")
    private String categoryAllName;

    @ApiModelProperty(value = "是否关注")
    private Boolean follow;

    @ApiModelProperty(value = "关注说明")
    private String followContent;

    @ApiModelProperty(value = "劣化趋势")
    private Integer deteriorationTrend;

    @ApiModelProperty(value = "劣化趋势名称")
    private String deteriorationTrendName;

    @ApiModelProperty(value = "分析说明")
    private String analyzeInfo;

    @ApiModelProperty(value = "分析图片")
    @TableField(value = "analyze_pics", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] analyzePics;

    @ApiModelProperty(value = "分析图片dtos")
    private List<AttachmentClientDto> analyzeDtos;

    @ApiModelProperty(value = "分析图片导出dtos")
    private List<Map<String, Object>> analyzeDtoExports;

    @ApiModelProperty(value = "分析图片信息")
    private String analyzePicInfo;

    @ApiModelProperty(value = "分析图片信息集合")
    private List<AnalyzePicInfoDto> analyzePicInfoDtos;

    @ApiModelProperty(value = "状态描述")
    private String analyzeReason;

    @ApiModelProperty(value = "处理建议")
    private String handlingSuggestions;

}