package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备类型校准
 *
 * <AUTHOR>
 * @date 2020-07-23
 */
@Data
@ApiModel(value = "CategoryCalibrationDto", description = "设备类型校准")
public class CategoryCalibrationDto {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "类型id不能为空")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "校准周期", required = true)
    @NotNull(message = "校准周期不能为空")
    private Integer intervalPeriod;

    @ApiModelProperty(value = "校准提醒", required = true)
    @NotNull(message = "校准提醒不能为空")
    private Integer remindBefore;

    @ApiModelProperty(value = "校准任务项")
    private List<CalibrationStandardDto> calibrationStandardDtos;

}