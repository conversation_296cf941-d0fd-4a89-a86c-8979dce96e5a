package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.EquipmentIotStatusDto;
import cn.getech.ehm.equipment.entity.EquipmentStatus;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 设备状态服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IEquipmentStatusService extends IBaseService<EquipmentStatus> {

    /**
     * 更新设备运行状态
     * @param dto
     * @return
     */
    Boolean updateStatus(EquipmentIotStatusDto dto);

    /**
     * 更新设备报警状态
     * @param dto
     * @return
     */
    Boolean updateIotStatus(EquipmentIotStatusDto dto);

    /**
     * 更新运行状态
     * @return
     */
    Boolean updateEquipmentStatus(Integer runningStatus, String equipmentId, Date markTime);

    /**
     * 获取列表
     * @param beginTime
     * @param endTime
     * @return
     */
    List<EquipmentStatus> getList(String equipmentId, Date beginTime, Date endTime, Integer[] statusList);
}