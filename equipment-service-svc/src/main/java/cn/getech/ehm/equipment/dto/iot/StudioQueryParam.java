package cn.getech.ehm.equipment.dto.iot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 组态查询数据模型
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudioQueryParam", description = "组态查询数据模型")
public class StudioQueryParam{

    @ApiModelProperty(value = "分类id", hidden = true)
    private String classifyId;

    @ApiModelProperty(value = "组态id", hidden = true)
    private String configToolId;

    @ApiModelProperty(value = "组态名称")
    private String studioName;

    @ApiModelProperty(value = "当前页", required = true)
    @NotBlank(message = "当前页不能为空")
    private Integer current;

    @ApiModelProperty(value = "页大小", required = true)
    @NotBlank(message = "页大小不能为空")
    private Integer size;

    @ApiModelProperty(value = "0-所有应用1-收藏应用", hidden = true)
    private Integer type;

    @ApiModelProperty(value = "0-全部1-手机端应用2-PC端应用")
    @NotBlank(message = "类型(0-全部1-手机2-PC)不能为空")
    private String appType;

    @ApiModelProperty(value = "发布状态0-未发布1-发布2-全部", hidden = true)
    private Integer status;

    @ApiModelProperty(value = "token", hidden = true)
    private String accessToken;

}