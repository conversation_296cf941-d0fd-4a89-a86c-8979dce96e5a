package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 零部件运行参数变量值
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "ParameterVariableValueDto", description = "零部件运行参数变量值")
public class ParameterVariableValueDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "来源(1录入2参数)")
    private Integer sourceType;

    @ApiModelProperty(value = "值(来源参数为运行参数id,否则为输入)")
    private String value;

    @ApiModelProperty(value = "来源为运行参数,对应参数的值")
    private String paramValue;

}
