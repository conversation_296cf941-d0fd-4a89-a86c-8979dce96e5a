package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.WarnAppQueryParam;
import cn.getech.ehm.equipment.dto.WarnFirstPageDto;
import cn.getech.ehm.equipment.dto.info.ClientHomeDto;
import cn.getech.ehm.equipment.dto.info.EquipmentScreenDto;
import cn.getech.ehm.equipment.dto.screen.CustomerScreenDto;
import cn.getech.ehm.equipment.dto.warn.EquipmentWarnDto;
import cn.getech.ehm.equipment.dto.warn.EquipmentWarnQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.service.IEquipmentWarnService;
import cn.getech.ehm.equipment.utils.PorosDeviceUtils;
import cn.getech.poros.device.client.DeviceInfoClient;
import cn.getech.poros.device.client.DeviceLocationClient;
import cn.getech.poros.device.client.DeviceTypeClient;
import cn.getech.poros.device.dto.infoclient.InfoDataSyncDto;
import cn.getech.poros.device.dto.locationclient.LocationDataSyncDto;
import cn.getech.poros.device.dto.typeclient.TypeDataSyncDto;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.SystemClient;
import cn.getech.ehm.system.dto.CustomerDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作台首页
 * <AUTHOR>
 * @since 2020-12-04
 */
@RestController
@RequestMapping("/clientHome")
@Api(tags = "客户端工作台首页")
@Slf4j
public class HomeController {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private IEquipmentWarnService equipmentWarnService;

    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Autowired
    private DeviceLocationClient deviceLocationClient;

    @Autowired
    private DeviceTypeClient deviceTypeClient;

    @Autowired
    private DeviceInfoClient deviceInfoClient;

    @Autowired
    private IEquipmentLocationService iEquipmentLocationService;

    @Autowired
    private IEquipmentCategoryService iEquipmentCategoryService;

    @Autowired
    private IEquipmentInfoService iEquipmentInfoService;
    /**
     * 客户端获取当前登录用户系统数量
     */
    @ApiOperation("客户端当前登录用户系统数量")
    @GetMapping("/equipmentCount")
    @AuditLog(title = "工作台",desc = "当前登录用户系统数量",businessType = BusinessType.QUERY)
    //@Permission("maint:plan:list")
    public RestResponse<Integer> equipmentCount(){
        RestResponse<List<String>> restResponse = systemClient.getCurrentInfoIds();
        if (restResponse.isOk()) {
            List<String> customerInfoIds = restResponse.getData();
            if (CollectionUtils.isEmpty(customerInfoIds)) {
                return RestResponse.ok(StaticValue.ZERO);
            } else {
                ClientHomeDto clientHomeDto = new ClientHomeDto();
                clientHomeDto.setType(StaticValue.ZERO);
                clientHomeDto.setEquipmentIds(customerInfoIds);
                return RestResponse.ok(equipmentInfoService.equipmentCount(clientHomeDto));
            }
        } else {
            log.info("系统服务返回数据->" + JSONObject.toJSON(restResponse));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @ApiOperation("客户端当前登录用户接入数量")
    @GetMapping("/equipmentIotCount")
    @AuditLog(title = "工作台",desc = "当前登录用户接入数量",businessType = BusinessType.QUERY)
    //@Permission("maint:plan:list")
    public RestResponse<Integer> equipmentIotCount(){
        //获取当前分配设备
        RestResponse<List<String>> restResponse = systemClient.getCurrentInfoIds();
        if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
            List<String> customerInfoIds = restResponse.getData();
            if (CollectionUtils.isEmpty(customerInfoIds)) {
                return RestResponse.ok(StaticValue.ZERO);
            } else {
                ClientHomeDto clientHomeDto = new ClientHomeDto();
                clientHomeDto.setType(StaticValue.ONE);
                clientHomeDto.setEquipmentIds(customerInfoIds);
                return RestResponse.ok(equipmentInfoService.equipmentCount(clientHomeDto));
            }
        } else {
            log.info("系统服务返回数据->" + JSONObject.toJSON(restResponse));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
    }

    /**
     * 获取当前登录用户工程师数量
     */
    @ApiOperation("客户端获取当前登录用户工程师数量")
    @GetMapping("/engineerCount")
    @AuditLog(title = "工作台",desc = "获取当前登录用户工程师数量",businessType = BusinessType.QUERY)
    //@Permission("maint:plan:list")
    public RestResponse<Integer> engineerCount(){
        return systemClient.engineerCount();
    }

    /**
     * 获取告警列表
     */
    @ApiOperation("获取告警列表")
    @PostMapping("/warnList")
    @AuditLog(title = "工作台",desc = "获取告警列表",businessType = BusinessType.QUERY)
    //@Permission("equipment:warn:list")
    public RestResponse<PageResult<WarnFirstPageDto>> warnList(@RequestBody @Valid WarnAppQueryParam warnAppQueryParam){
        return RestResponse.ok(equipmentWarnService.warnList(warnAppQueryParam));
    }

    /**
     * 获取大屏客户信息
     */
    @ApiOperation("客户端获取大屏客户信息")
    @GetMapping("/getScreenCustomer")
    @AuditLog(title = "大屏",desc = "获取大屏客户信息",businessType = BusinessType.QUERY)
//    @ApiImplicitParam(name="customerId",value="客户id",dataType="string", paramType = "query")
    //@Permission("maint:plan:list")
    public RestResponse<CustomerScreenDto> getScreenCustomer(@RequestParam("customerId") String customerId){
        RestResponse<CustomerDto> customerRes = systemClient.getScreenDto(customerId);
        if(customerRes.isOk()){
            CustomerDto customerDto = customerRes.getData();
            CustomerScreenDto customerScreenDto = new CustomerScreenDto();
            if(null != customerDto) {
                customerScreenDto.setCustomerId(customerDto.getId());
                customerScreenDto.setCustomerCode(customerDto.getCode());
                customerScreenDto.setCustomerName(customerDto.getName());
                //获取设备数量、接入数量
                RestResponse<List<String>> restResponse = systemClient.getInfoIdsByCustomerId(customerId);
                if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
                    List<String> customerInfoIds = restResponse.getData();
                    if (CollectionUtils.isEmpty(customerInfoIds)) {
                        customerScreenDto.setInfoCount(StaticValue.ZERO);
                        customerScreenDto.setIotInfoCount(StaticValue.ZERO);
                    } else {
                        ClientHomeDto clientHomeDto = new ClientHomeDto();
                        clientHomeDto.setType(StaticValue.ZERO);
                        clientHomeDto.setEquipmentIds(customerInfoIds);
                        Integer infoCount = equipmentInfoService.equipmentCount(clientHomeDto);
                        customerScreenDto.setInfoCount(infoCount);
                        clientHomeDto.setType(StaticValue.ONE);
                        Integer iotInfoCount = equipmentInfoService.equipmentCount(clientHomeDto);
                        customerScreenDto.setIotInfoCount(iotInfoCount);

                        //获取客户全部设备信息
                        List<EquipmentScreenDto> equipmentScreenDtos = equipmentInfoService.getScreenInfos(customerInfoIds);

                        //获取客户全部告警
                        EquipmentWarnQueryParam equipmentWarnQueryParam = new EquipmentWarnQueryParam();
                        equipmentWarnQueryParam.setStatus(StaticValue.ONE);
                        equipmentWarnQueryParam.setEquipmentIds(customerInfoIds);
                        List<EquipmentWarnDto> equipmentWarnDtos = equipmentWarnService.getList(equipmentWarnQueryParam);
                        List<String> warnEquipmentIds = null;
                        if(CollectionUtils.isNotEmpty(equipmentWarnDtos)){
                            warnEquipmentIds = equipmentWarnDtos.stream().map(EquipmentWarnDto::getEquipmentId).distinct().collect(Collectors.toList());
                        }
                        for(EquipmentScreenDto equipmentScreenDto : equipmentScreenDtos){
                            if(CollectionUtils.isNotEmpty(warnEquipmentIds) && warnEquipmentIds.contains(equipmentScreenDto.getId())){
                                equipmentScreenDto.setStatus(StaticValue.ONE);
                            }else{
                                equipmentScreenDto.setStatus(StaticValue.THREE);
                            }
                        }
                        customerScreenDto.setEquipmentDtos(equipmentScreenDtos);
                    }
                } else {
                    log.error("系统服务返回数据->" + JSONObject.toJSON(restResponse));
                    return RestResponse.failed();
                }
            }
            return RestResponse.ok(customerScreenDto);
        }else{
            log.error("获取客户信息失败");
            return RestResponse.failed();
        }
    }

    /**
     * 获取系统总共数量
     */
    @ApiOperation("客户端获取系统总共数量和所有的系统接入数")
    @GetMapping("/equipmentAllCount")
    @AuditLog(title = "大屏",desc = "获取系统总共数量和所有可用的系统接入数",businessType = BusinessType.QUERY)
    //@Permission("maint:plan:list")
    public RestResponse<List<Integer>> equipmentAllCount(){
        List<Integer> list = new ArrayList<>();
        list.add(equipmentInfoService.equipmentJoinCount());
        list.add(equipmentInfoService.equipmentAllCount());
        return RestResponse.ok(list);
    }

    @GetMapping("/porosDevice/sync")
    public RestResponse<?> syncPorosDevice(){
        RestResponse<List<LocationDataSyncDto>> locationRestResponse =  deviceLocationClient.getByVersionIncrementNoTenant(0L,null, null);
        if (locationRestResponse.isOk()) {
            List<LocationDataSyncDto> locationDataSyncDtos = locationRestResponse.getData();
            if (locationDataSyncDtos.isEmpty()) {
                if (log.isDebugEnabled()) {
                    log.debug("设备位置数据为空");
                }
            }else {
                List<String> tenantIds = locationDataSyncDtos.stream().map(LocationDataSyncDto::getTenantId).distinct().collect(Collectors.toList());
                Map<String, String> rootMap = iEquipmentLocationService.getRootIdMap(tenantIds);
                List<EquipmentLocation> equipmentLocations = new ArrayList<>();
                for(LocationDataSyncDto locationDataSyncDto : locationDataSyncDtos){
                    String rootId = rootMap.get(locationDataSyncDto.getTenantId());
                    //租户未查询到根节点，需要取租户下新增
                    EquipmentLocation equipmentLocation = PorosDeviceUtils.locationDataConvertor(locationDataSyncDto, StringUtils.isNotBlank(rootId) ? rootId : "porosSynchroRootId");
                    equipmentLocations.add(equipmentLocation);
                }
                iEquipmentLocationService.saveOrUpdateBatch(equipmentLocations);
                if (log.isDebugEnabled()) {
                    log.debug("成功同步设备[{}]位置数据", equipmentLocations.size());
                }
            }
        }else {
            log.error("获取设备位置数据失败{}", locationRestResponse.getMsg());
        }
        RestResponse<List<TypeDataSyncDto>> typeRestResponse =  deviceTypeClient.getByVersionIncrementNoTenant(0L,null, null);
        if (typeRestResponse.isOk()) {
            List<TypeDataSyncDto> typeDataSyncDtos = typeRestResponse.getData();
            if (typeDataSyncDtos.isEmpty()) {
                if (log.isDebugEnabled()) {
                    log.debug("设备类型数据为空");
                }
            }
            List<EquipmentCategory> equipmentCategories = typeDataSyncDtos.stream().map(PorosDeviceUtils::categoryDataConvertor).collect(Collectors.toList());
            iEquipmentCategoryService.saveOrUpdateBatch(equipmentCategories);
            if (log.isDebugEnabled()) {
                log.debug("成功同步设备[{}]类型数据", equipmentCategories.size());
            }
        }else {
            log.error("获取设备类型数据失败{}", typeRestResponse.getMsg());
        }
        RestResponse<List<InfoDataSyncDto>> infoRestResponse =  deviceInfoClient.getSyncByVersionIncrementNoTenant(0L, PorosDeviceUtils.SYSTEM_LABEL, null, null);
        if (infoRestResponse.isOk()) {
            List<InfoDataSyncDto> infoDataSyncDtos = infoRestResponse.getData();
            if (infoDataSyncDtos.isEmpty()) {
                if (log.isDebugEnabled()) {
                    log.debug("设备信息数据为空");
                }
            }
            List<EquipmentInfo> equipmentInfos = infoDataSyncDtos.stream().map(PorosDeviceUtils::infoDataConvertor).collect(Collectors.toList());
            iEquipmentInfoService.saveOrUpdateBatch(equipmentInfos);
            if (log.isDebugEnabled()) {
                log.debug("成功同步设备[{}]信息数据", equipmentInfos.size());
            }
        }else {
            log.error("获取设备信息数据失败{}", infoRestResponse.getMsg());
        }
        return RestResponse.ok("同步逻辑处理完成");
    }
}
