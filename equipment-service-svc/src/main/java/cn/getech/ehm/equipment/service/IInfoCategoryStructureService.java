package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.category.CategoryStructureDto;
import cn.getech.ehm.equipment.dto.category.StructureSortDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryStructure;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 设备类型部件 服务类
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
public interface IInfoCategoryStructureService extends IBaseService<EquipmentCategoryStructure> {

        /**
         * 根据categoryId查询集合
         * @param categoryId
         * @return
         */
        List<CategoryStructureDto> getListByCategoryId(String categoryId);

        /**
         * 保存
         * @param editParam
         * @return
         */
        String editStructure(CategoryStructureDto editParam);

        public List<CategoryStructureDto> getListByCategoryId(String categoryId,Boolean filter);

        /**
         * 重新排序
         * @return
         */
        Boolean updateStructureSort(List<StructureSortDto> dtos);

        /**
         * 获取选中类型部件集合
         */
        List<CategoryStructureDto> getSelectList(String categoryId, List<String> ids);
}