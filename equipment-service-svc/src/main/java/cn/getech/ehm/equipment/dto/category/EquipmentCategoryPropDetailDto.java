package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备类型扩展属性详情返回数据模型
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentCategoryPropDetailDto", description = "设备类型扩展属性详情返回数据模型")
public class EquipmentCategoryPropDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型属性id")
    private String categoryPropId;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "组名称")
    private String groupName;

    @ApiModelProperty(value = "属性名称")
    private String name;

    @ApiModelProperty(value = "组名称-属性名称")
    private String allName;

    @ApiModelProperty(value = "属性类型(0文本1数值2选项3日期)")
    private Integer propType;

    @ApiModelProperty(value = "属性定义")
    private String define;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "是否固定0否1是")
    private Integer fixed;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否导出")
    private Boolean exported;

}