package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能诊断报告查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportAutoQueryParam", description = "智能诊断报告查询参数")
public class DiagnosisReportAutoQueryParam extends PageParam {

    @ApiModelProperty(value = "名称")
    private String name;

}
