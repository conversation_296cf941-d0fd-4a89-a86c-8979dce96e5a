package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.info.StructureSpareDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureSpare;
import cn.getech.ehm.equipment.mapper.StructureSpareMapper;
import cn.getech.ehm.equipment.service.IStructureSpareService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部件零件
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@Slf4j
@Service
public class StructureSpareServiceImpl extends BaseServiceImpl<StructureSpareMapper, EquipmentStructureSpare> implements IStructureSpareService {

    @Autowired
    private StructureSpareMapper structureSpareMapper;

    @Override
    public String editStructureSpare(StructureSpareDto spareDto) {
        EquipmentStructureSpare structureSpare = CopyDataUtil.copyObject(spareDto, EquipmentStructureSpare.class);
        saveOrUpdate(structureSpare);
        return structureSpare.getId();
    }

    @Override
    public StructureSpareDto getDtoByStructureId(String structureId) {
        LambdaQueryWrapper<EquipmentStructureSpare> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructureSpare::getStructureId, structureId);
        return CopyDataUtil.copyObject(structureSpareMapper.selectOne(wrapper), StructureSpareDto.class);
    }

    @Override
    public List<String> alreadyExistsIds(List<String> structureIds){
        List<String> ids = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(structureIds)){
            LambdaQueryWrapper<EquipmentStructureSpare> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentStructureSpare::getStructureId, structureIds);
            wrapper.select(EquipmentStructureSpare::getStructureId);
            ids = structureSpareMapper.selectList(wrapper).stream().map(EquipmentStructureSpare::getStructureId).distinct().collect(Collectors.toList());
        }
        return ids;
    }
}
