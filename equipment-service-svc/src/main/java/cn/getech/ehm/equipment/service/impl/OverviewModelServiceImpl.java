package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.overview.OverviewModelDto;
import cn.getech.ehm.equipment.entity.OverviewModel;
import cn.getech.ehm.equipment.mapper.OverviewModelMapper;
import cn.getech.ehm.equipment.service.IOverviewModelService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 概览图 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class OverviewModelServiceImpl extends BaseServiceImpl<OverviewModelMapper, OverviewModel> implements IOverviewModelService {

    @Autowired
    private OverviewModelMapper modelMapper;

    @Override
    public Boolean editOverviewModel(OverviewModelDto modelDto) {
        OverviewModel overviewModel = CopyDataUtil.copyObject(modelDto, OverviewModel.class);
        return this.saveOrUpdate(overviewModel);
    }

    @Override
    public OverviewModelDto getModelDto(String relationKey, Integer type) {
        OverviewModelDto overviewModelDto = new OverviewModelDto();
        LambdaQueryWrapper<OverviewModel> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(OverviewModel::getRelationKey, relationKey);
        wrapper.eq(OverviewModel::getType, type);
        OverviewModel overviewModel = modelMapper.selectOne(wrapper);
        if(null == overviewModel){
            String id = UUID.randomUUID().toString().replace("-", "");
            overviewModelDto.setId(id);
            overviewModelDto.setName("模板名称");
            overviewModelDto.setEnable(false);
            overviewModelDto.setType(type);
            overviewModelDto.setRelationKey(relationKey);
            overviewModelDto.setModelType(1);
            //位置概览图对应标识2，设备概览图对应标识4
            overviewModelDto.setCardList(type == 1 ? new Integer[]{2} : new Integer[]{4});
        }else{
            overviewModelDto = CopyDataUtil.copyObject(overviewModel, OverviewModelDto.class);
        }
        return overviewModelDto;
    }

    @Override
    public Map<String, Boolean> getModelStatus(List<String> relationKeys, Integer type){
        Map<String, Boolean> enableMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(relationKeys)) {
            LambdaQueryWrapper<OverviewModel> wrapper = Wrappers.lambdaQuery();
            wrapper.in(OverviewModel::getRelationKey, relationKeys);
            wrapper.eq(OverviewModel::getType, type);
            wrapper.select(OverviewModel::getId, OverviewModel::getEnable, OverviewModel::getRelationKey);
            Map<String, OverviewModel> overviewModelMap = modelMapper.selectList(wrapper).stream().collect(Collectors.toMap(OverviewModel::getRelationKey, v -> v, (v1, v2) -> v1));
            for (String relationKey : relationKeys) {
                Boolean enable = false;
                OverviewModel overviewModel = overviewModelMap.get(relationKey);
                if (null != overviewModel && null != overviewModel.getEnable()) {
                    enable = overviewModel.getEnable();
                }
                enableMap.put(relationKey, enable);
            }
        }
        return enableMap;
    }
}
