package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备使用率
 * <AUTHOR>
 * @date 2022-11-11
 */
@Data
@ApiModel(value = "EquipmentRateOfUtilizationDto", description = "设备使用率")
public class EquipmentRateOfUtilizationDto {

    @ApiModelProperty(value = "车间名字")
    private String name;

    @ApiModelProperty(value = "设备使用率")
    private float rateOfUtilization;

}