package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.info.BomParentDto;
import cn.getech.ehm.equipment.dto.location.EquipmentLocationDto;
import cn.getech.ehm.equipment.dto.location.EquipmentLocationTreeDto;
import cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.overview.CardAtlasCountDto;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.dto.RelSearchDto;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备位置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Repository
public interface EquipmentLocationMapper extends BaseMapper<EquipmentLocation> {

    /**
     * 获取层级编码
     * @param id
     * @return
     */
    String getLayerCode(String id);

    /**
     * 获取同一深度所有树节点
     * @param layerCode
     * @return
     */
    List<EquipmentLocation> getListByLayerCode(String layerCode);

    /**
     * 逻辑删除
     * @param id
     * @return
     */
    Integer deleteById(@Param("id") String id);

    /**
     * 查看详情
     * @param id
     * @return
     */
    EquipmentLocationDto getById(String id);

    /**
     * 获取节点下子节点列表
     * @param parentId
     * @return
     */
    List<EquipmentLocationTreeDto> getListByParentId(@Param("parentId") String parentId,
                                                     @Param("locationIds") List<String> locationIds,@Param("keyword") String keyword);

    /**
     * 获取节点下子节点列表
     * @param parentId
     * @return
     */
    List<LocationEquipmentTreeDto> getTreeListByParentId(@Param("parentId") String parentId);

    /**
     * 获取所有位置
     * @return
     */
    List<LocationEquipmentTreeDto> getAllTreeList(@Param("locationIds") List<String> locationIds);

    /**
     * 面包屑信息
     * @param id
     * @return
     */
    BomParentDto getBomDtoById(@Param("id") String id);

    /**
     * 根据设备ids获取对应位置id及其父节点
     * @param relSearchDto
     * @return
     */
    List<String> getLocationIdByInfoIds(@Param("param") RelSearchDto relSearchDto);

    /**
     * 导入CW
     * @param entities
     * @return
     */
    @SqlParser(filter = true)
    Boolean importCW(@Param("entities") List<EquipmentLocation> entities);

    /**
     * 获取租户下根节点集合
     * @param tenantIds
     * @return
     */
    @SqlParser(filter = true)
    List<EquipmentLocation> getTenantRoot(@Param("tenantIds") List<String> tenantIds);

    /**
     * poros删除位置
     * @param codes
     * @return
     */
    @SqlParser(filter = true)
    Integer updateByCodes(@Param("codes") List<String> codes, @Param("tenantId") String tenantId);

    /**
     * 获取位置下所有设备id
     * @param length 层级编码长度
     * @param childFlag 是否只选子节点
     * @return
     */
    List<CardAtlasCountDto> getInfoIdListByLocationId(@Param("parentLayerCode") String parentLayerCode, @Param("length") Integer length,
                                                      @Param("childFlag") Boolean childFlag, @Param("equipmentIds") List<String> equipmentIds);
}
