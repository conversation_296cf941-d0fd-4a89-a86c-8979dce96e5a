package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReportAuto;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 智能诊断报告 服务类
 */
public interface IDiagnosisReportAutoService extends IBaseService<EquipmentDiagnosisReportAuto> {

        /**
         * 分页查询，返回Dto
         * @return
         */
        PageResult<DiagnosisReportAutoDto> pageDto(DiagnosisReportAutoQueryParam queryParam);

        /**
         * 保存
         * @return
         */
        Boolean editByParam(DiagnosisReportAutoDto dto);

        /**
         * 获取列表
         */
        List<DiagnosisReportAutoDto> getList(Boolean enable);

        /**
         * 删除
         * @return
         */
        Boolean deleteByIds(List<String> ids);
}