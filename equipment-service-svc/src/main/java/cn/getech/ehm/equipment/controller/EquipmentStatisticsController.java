package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.info.EquipmentRunningStatusDto;
import cn.getech.ehm.equipment.dto.info.InfoStatisticsSearchDto;
import cn.getech.ehm.equipment.dto.info.RunningEquipmentDto;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.iot.dto.warn.FaultEquipmentNumDto;
import cn.getech.ehm.iot.dto.warn.StatisticsLineDto;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 设备分析控制器
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/statistics")
@Api(tags = "设备分析接口")
public class EquipmentStatisticsController {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;

    @ApiOperation("设备分析-设备状态")
    @PostMapping("/equipmentRunningStatus")
    public RestResponse<List<EquipmentRunningStatusDto>> equipmentRunningStatus(@RequestBody InfoStatisticsSearchDto dto) {
        return RestResponse.ok(equipmentInfoService.getEquipmentRunningStatusCount(true, dto.getLocationIds(), dto.getCategoryIds()));
    }


    @PostMapping("/getFaultInfoIds")
    @ApiOperation("设备分析-近期报警的设备(按数量排序)")
    public RestResponse<List<FaultEquipmentNumDto>> getFaultInfo(@RequestBody InfoStatisticsSearchDto dto){
        return RestResponse.ok(equipmentInfoService.getFaultInfo(dto));
    }

    //zhy
    @PostMapping("/failureRate")
    @ApiOperation("设备分析-故障率")
    public RestResponse<StatisticsLineDto> failureRate(@RequestBody InfoStatisticsSearchDto dto){
        return RestResponse.ok(equipmentInfoService.failureRate(dto));
    }

    @ApiOperation("设备分析-设备统计")
    @PostMapping("/getRunningStatusCount")
    public RestResponse<WarnCountDto> getRunningStatusCount(@RequestBody InfoStatisticsSearchDto dto) {
        return RestResponse.ok(equipmentInfoService.getRunningStatusCount(dto));
    }

    @ApiOperation("设备分析-在线数量")
    @PostMapping("/getRunningInfoNum")
    public RestResponse<RunningEquipmentDto> getRunningInfoNum(@RequestBody InfoStatisticsSearchDto dto) {
        return RestResponse.ok(equipmentInfoService.getRunningInfoNum(dto));
    }
}
