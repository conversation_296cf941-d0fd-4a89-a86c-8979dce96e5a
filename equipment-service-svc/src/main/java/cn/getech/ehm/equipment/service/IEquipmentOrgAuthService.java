package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.auth.EquipmentOrgAuthDto;
import cn.getech.ehm.equipment.entity.EquipmentOrgAuth;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 设备部门权限服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IEquipmentOrgAuthService extends IBaseService<EquipmentOrgAuth> {
    /**
     * 获取列表
     * @return
     */
    List<EquipmentOrgAuthDto> getList(String orgCode);

    /**
     * 更新
     */
    Boolean updateByParam(EquipmentOrgAuthDto dto);

    /**
     * 获取部门对应的位置信息
     * @param orgCodes
     * @return
     */
    List<String> getLocationIdsByOrgCodes(List<String> orgCodes);

}