package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设备零部件运行参数数据模型
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "EquipmentStructureParameterDto", description = "设备零部件运行参数数据模型")
public class EquipmentStructureParameterDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "零部件id")
    private String structureId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "值")
    private BigDecimal value;

    @ApiModelProperty(value = "参数类型")
    private Integer paramType;

    @ApiModelProperty(value = "数据来源(1录入2计算)")
    private Integer sourceType;

    @ApiModelProperty(value = "变量")
    private List<StructureParameterVariableDto> variables;

    @ApiModelProperty(value = "计算脚本")
    private String expression;

}
