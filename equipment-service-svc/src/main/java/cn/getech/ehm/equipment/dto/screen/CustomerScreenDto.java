package cn.getech.ehm.equipment.dto.screen;

import cn.getech.ehm.equipment.dto.info.EquipmentScreenDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 客户列表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "CustomerDto", description = "客户列表返回数据模型")
public class CustomerScreenDto {

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "绑定设备数量")
    private Integer infoCount;

    @ApiModelProperty(value = "接入iot设备数量")
    private Integer iotInfoCount;

    @ApiModelProperty(value = "设备列表")
    List<EquipmentScreenDto> equipmentDtos;

}