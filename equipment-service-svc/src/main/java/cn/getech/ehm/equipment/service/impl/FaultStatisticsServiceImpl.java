package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.EquipmentFaultStatisticsDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.service.IFaultStatisticsService;
import cn.getech.ehm.task.client.TaskClient;
import cn.getech.ehm.task.dto.FaultStatisticsDto;
import cn.getech.ehm.task.dto.FaultStatisticsParam;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 统计报表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@Slf4j
@Service
public class FaultStatisticsServiceImpl implements IFaultStatisticsService {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentCategoryService equipmentCategoryService;
    @Autowired
    private TaskClient taskClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Autowired
    private IEquipmentLocationService equipmentLocationService;


    @Override
    public List<EquipmentFaultStatisticsDto> faultEquipmentStatistics(FaultStatisticsParam param) {
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        List<String> locationIdList = Lists.newArrayList();
        locationIdList.add(param.getLocationId());
        equipmentInfoSearchDto.setLocationIds(locationIdList);
        equipmentInfoSearchDto.setCategoryId(param.getCategoryId());
        List<String> equipmentIdList = equipmentInfoService.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (CollectionUtils.isEmpty(equipmentIdList)) {
            return Lists.newArrayList();
        }
        param.setEquipmentIdList(equipmentIdList);
        param.setTopCount(StaticValue.FIVE * 10);   // 查询前50，防止部分设备被删除，凑不足5个，见return注释
        RestResponse<List<FaultStatisticsDto>> response = taskClient.equipmentFaultStatistics(param);
        if (!response.isOk()){
            log.error("查询设备故障信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        if (CollectionUtils.isEmpty(response.getData())){
            return Collections.emptyList();
        }

        List<FaultStatisticsDto> resultList =  response.getData();
        Map<String, FaultStatisticsDto> dtoMap = new HashMap();
        List<String> idList = new ArrayList();
        for (FaultStatisticsDto faultCategoryDto : resultList){
            idList.add(faultCategoryDto.getId());
            dtoMap.put(faultCategoryDto.getId(), faultCategoryDto);
        }
        List<EquipmentInfo> equipmentInfoList = equipmentInfoService.getBaseMapper().selectBatchIds(idList);
        for (EquipmentInfo info : equipmentInfoList){
            FaultStatisticsDto faultStatisticsDto = dtoMap.get(info.getId());
            faultStatisticsDto.setName(info.getName());
        }
        // 过滤掉部分设备被删除导致名称为null的数据
        resultList = resultList.stream().filter(statisticsDto -> StringUtils.isNotBlank(statisticsDto.getName())).limit(StaticValue.FIVE).collect(Collectors.toList());
        List<EquipmentFaultStatisticsDto> equipmentFaultStatisticsDtos = this.dealTransfer(resultList);
        return equipmentFaultStatisticsDtos;
    }

    @Override
    public List<FaultStatisticsDto> faultCategoryStatistics(FaultStatisticsParam param) {
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        List<String> locationIdList = Lists.newArrayList();
        locationIdList.add(param.getLocationId());
        equipmentInfoSearchDto.setLocationIds(locationIdList);
        equipmentInfoSearchDto.setCategoryId(param.getCategoryId());
        List<String> equipmentIdList = equipmentInfoService.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (CollectionUtils.isEmpty(equipmentIdList)) {
            return Lists.newArrayList();
        }
        param.setEquipmentIdList(equipmentIdList);
        RestResponse<List<FaultStatisticsDto>> response = taskClient.allEquipmentFaultStatistics(param);
        if (!response.isOk()){
            log.error("查询设备类型失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        if (CollectionUtils.isEmpty(response.getData())){
            return Collections.emptyList();
        }

        Map<String, FaultStatisticsDto> dtoMap = new HashMap();
        List<String> idList = new ArrayList();
        for (FaultStatisticsDto faultStatisticsDto : response.getData()){
            dtoMap.put(faultStatisticsDto.getId(), faultStatisticsDto);
            idList.add(faultStatisticsDto.getId());
        }
        List<EquipmentInfo> equipmentInfoList = equipmentInfoService.getBaseMapper().selectBatchIds(idList);
        List<String> itemIdList = new ArrayList();
        Map<String, Integer> countMap = new HashMap();
        for (EquipmentInfo equipmentInfo : equipmentInfoList){
            countMap.merge(equipmentInfo.getCategoryId(), dtoMap.get(equipmentInfo.getId()).getCount(), Integer::sum);
            itemIdList.add(equipmentInfo.getCategoryId());
        }
        List<EquipmentCategory> categoryList = equipmentCategoryService.getBaseMapper().selectBatchIds(itemIdList);
        List<FaultStatisticsDto> resultList = new ArrayList();
        for (EquipmentCategory category : categoryList){
            FaultStatisticsDto statisticsDto = new FaultStatisticsDto();
            statisticsDto.setId(category.getId());
            statisticsDto.setName(category.getName());
            statisticsDto.setCount(countMap.get(category.getId()));
            resultList.add(statisticsDto);
        }
        return resultList.stream().sorted(Comparator.comparing(FaultStatisticsDto::getCount).reversed()).limit(StaticValue.TEN).collect(Collectors.toList());
    }

    public List<EquipmentFaultStatisticsDto> dealTransfer(List<FaultStatisticsDto> resultList) {
        List<FaultStatisticsDto> collect = resultList.stream().filter(statisticsDto -> StringUtils.isNotBlank(statisticsDto.getName())).limit(StaticValue.FIVE).collect(Collectors.toList());
        Set<String> equipmentIdSet = collect.stream().map(item -> item.getId()).collect(Collectors.toSet());
        List<EquipmentInfo> equipmentInfos = equipmentInfoService.list(new QueryWrapper<EquipmentInfo>().lambda().in(EquipmentInfo::getId, equipmentIdSet));
        Map<String, String> equipmentIdToLocationId = equipmentInfos.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getParentId(), (v1, v2) -> v1));
        Set<String> locationIdSet = equipmentInfos.stream().map(item -> item.getParentId()).collect(Collectors.toSet());
        List<EquipmentLocation> equipmentLocations = equipmentLocationService.list(new QueryWrapper<EquipmentLocation>().lambda().in(EquipmentLocation::getId, locationIdSet));
        Map<String, List<String>> parentToLayerMap = Maps.newHashMap();
        for (EquipmentLocation temp : equipmentLocations) {
            if (StringUtils.isNotBlank(temp.getLayerCode())) {
                String[] split = temp.getLayerCode().split("/");
                if (ArrayUtils.isNotEmpty(split)) {
                    locationIdSet.addAll(Arrays.asList(split));
                    parentToLayerMap.put(temp.getId(), Arrays.asList(split));
                }
            }
        }
        equipmentLocations = equipmentLocationService.list(new QueryWrapper<EquipmentLocation>().lambda().in(EquipmentLocation::getId, locationIdSet));
        Map<String, String> locationNameMap = equipmentLocations.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getName(), (v1, v2) -> v1));
        List<EquipmentFaultStatisticsDto> equipmentFaultStatisticsDtos = CopyDataUtil.copyList(collect, EquipmentFaultStatisticsDto.class);
        for (EquipmentFaultStatisticsDto temp : equipmentFaultStatisticsDtos) {
            String tempLocationId = equipmentIdToLocationId.getOrDefault(temp.getId(), "");
            if (StringUtils.isNotBlank(tempLocationId)) {
                List<String> layerCodeList = parentToLayerMap.getOrDefault(tempLocationId, Lists.newArrayList());
                StringBuilder locationName = new StringBuilder();
                int i = 1;
                for (String locationId : layerCodeList) {
                    locationName.append(locationNameMap.getOrDefault(locationId, ""));
                    if (i != layerCodeList.size()) {
                        locationName.append("/");
                    }
                    i++;
                }
                temp.setLocation(locationName.toString());
            }
        }
        return equipmentFaultStatisticsDtos;
    }

}
