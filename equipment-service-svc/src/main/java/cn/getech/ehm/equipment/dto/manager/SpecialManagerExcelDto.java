package cn.getech.ehm.equipment.dto.manager;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 安全管理人员导入导出
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "SpecialManagerExcelDto", description = "安全管理人员导入导出")
public class SpecialManagerExcelDto {

    @ApiModelProperty(value = "姓名")
    @FormExcel(name="姓名",cellType = FormExcel.ColumnType.STRING, required = true)
    private String name;

    @ApiModelProperty(value = "身份证号")
    @FormExcel(name="身份证号",cellType = FormExcel.ColumnType.STRING)
    private String idNumber;

    @ApiModelProperty(value = "证书名称")
    @FormExcel(name="证书名称",cellType = FormExcel.ColumnType.STRING)
    private String certificateName;

    @ApiModelProperty(value = "证书编号")
    @FormExcel(name="证书编号",cellType = FormExcel.ColumnType.STRING, required = true)
    private String certificateNo;

    @ApiModelProperty(value = "证书类型")
    @FormExcel(name="证书类型",cellType = FormExcel.ColumnType.STRING)
    private String certificateType;

    @ApiModelProperty(value = "提前预警天数")
    @FormExcel(name="提前预警天数",cellType = FormExcel.ColumnType.STRING, required = true)
    private Integer deadlineDays;

    @ApiModelProperty(value = "发证日期")
    @FormExcel(name="发证日期",dateFormat = "yyyy-MM-dd", required = true)
    private Date issueDate;

    @ApiModelProperty(value = "复审日期")
    @FormExcel(name="复审日期",dateFormat = "yyyy-MM-dd" )
    private Date reviewDate;

    @ApiModelProperty(value = "证件有效期")
    @FormExcel(name="证件有效期",dateFormat = "yyyy-MM-dd", required = true)
    private Date certificateValidity;

    @ApiModelProperty(value = "发证机关")
    @FormExcel(name="发证机关",cellType = FormExcel.ColumnType.STRING )
    private String issuingAuthority;

    @ApiModelProperty(value = "聘用单位")
    private String employer;

    @ApiModelProperty(value = "聘用单位名称")
    @FormExcel(name="聘用单位",cellType = FormExcel.ColumnType.STRING )
    private String employerName;

    @ApiModelProperty(value = "所属车间code")
    private String workshop;

    @ApiModelProperty(value = "所属车间名称")
    @FormExcel(name="所属组织",cellType = FormExcel.ColumnType.STRING )
    private String workshopName;

    @ApiModelProperty(value = "作业项目")
    private String jobItem;

    @ApiModelProperty(value = "作业项目名称")
    @FormExcel(name="作业项目",cellType = FormExcel.ColumnType.STRING )
    private String jobItemName;


}