package cn.getech.ehm.equipment.enmu;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 诊断报告状态枚举
 */
public enum ReportStatusType {
    TO_BE_PUBLISHED(1,"待发布"),
    PUBLISHED(2,"已发布");


    ReportStatusType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (ReportStatusType sourceType : ReportStatusType.values()) {
                if (sourceType.getValue() == value) {
                    return sourceType.getName();
                }
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(ReportStatusType sourceType : ReportStatusType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(sourceType.value);
            enumListDto.setName(sourceType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
