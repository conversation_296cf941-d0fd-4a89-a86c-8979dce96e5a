package cn.getech.ehm.equipment.dto.location;

import cn.getech.ehm.equipment.dto.info.BomParentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 位置设备全加载缓存树
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "LocationEquipmentTreeDto", description = "位置设备全加载缓存树")
public class LocationEquipmentTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "位置类型")
    private Integer locationType;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1位置11主设备12子设备)")
    private Integer type;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    @ApiModelProperty(value = "节点装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("switcherIcon", "switcherIcon");put("title", "title");}};

    @ApiModelProperty(value = "子节点")
    private List<LocationEquipmentTreeDto> children = new ArrayList<>();

    @ApiModelProperty(value = "是否可编辑")
    private Boolean edit = false;

    @ApiModelProperty(value = "面包屑列表")
    private List<BomParentDto> bomParent  = new ArrayList<>();

}