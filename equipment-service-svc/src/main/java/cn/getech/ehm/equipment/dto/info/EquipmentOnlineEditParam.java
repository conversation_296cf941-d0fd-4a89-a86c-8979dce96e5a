package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备在线离线编辑参数
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentOnlineEditParam", description = "设备在线离线编辑参数")
public class EquipmentOnlineEditParam extends ApiParam {

    @ApiModelProperty(value = "iot关联设备id", required = true)
    @NotBlank(message = "iot关联设备id不能为空")
    private String deviceId;

    @ApiModelProperty(value = "在线状态(0离线1在线)", required = true)
    @NotNull(message = "状态不能为空")
    private Integer onlineStatus;

}
