package cn.getech.ehm.equipment.dto.special;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 特种设备属性
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentSpecialDto", description = "特种设备属性")
public class EquipmentSpecialDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "部件id")
    private String structureId;

    @ApiModelProperty(value = "登记证号")
    private String certificateNo;

    @ApiModelProperty(value = "注册代码")
    private String registrationCode;

    @ApiModelProperty(value = "检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inspectionDate;

    @ApiModelProperty(value = "下次检验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nextInspectionDate;

    @ApiModelProperty(value = "检验报告编号")
    private String reportNo;

    @ApiModelProperty(value = "检验机构")
    private String inspectionOrg;

    @ApiModelProperty(value = "发证机关")
    private String issuingAuthority;

    @ApiModelProperty(value = "使用单位")
    private String useOrg;

    @ApiModelProperty("责任人id")
    private String dutyUserId;

    private String dutyUserName;

    @ApiModelProperty("提前提醒天数")
    private Integer deadlineDays;

}