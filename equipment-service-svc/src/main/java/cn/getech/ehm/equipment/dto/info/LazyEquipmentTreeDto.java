package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 位置设备懒加载树
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "LazyEquipmentTreeDto", description = "位置设备懒加载树")
public class LazyEquipmentTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "位置类型")
    private Integer locationType;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1位置11主设备12子设备)")
    private Integer type;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    @ApiModelProperty(value = "节点装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("switcherIcon", "switcherIcon");put("title", "title");}};

    @ApiModelProperty(value = "是否开启概览图")
    private Boolean enableOverview = false;

    @ApiModelProperty("排序比重")
    private Integer sort;
}