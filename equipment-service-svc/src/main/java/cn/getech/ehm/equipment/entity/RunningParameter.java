package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 零部件运行参数
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("running_parameter")
public class RunningParameter extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 零部件id
     */
    @TableField("spare_parts_id")
    private String sparePartsId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 编码(特征参数/基础库)
     */
    @TableField("code")
    private String code;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 参数分类
     */
    @TableField("param_type")
    private Integer paramType;

    /**
     * 数据来源(1录入2计算)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 是否生成参数表(0否1是)
     */
    @TableField("create_param")
    private Integer createParam;

    /**
     * 计算脚本
     */
    @TableField("expression")
    private String expression;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
