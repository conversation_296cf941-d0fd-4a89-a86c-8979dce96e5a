package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备完好率
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "IntactRateDto", description = "设备完好率")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntactRateDto {

    @ApiModelProperty(value = "x轴值")
    private String xaxis;

    @ApiModelProperty(value = "y轴值(今年)")
    private Double yaxisOfNow;

    @ApiModelProperty(value = "y轴值(去年)")
    private Double yaxisOfLast;
}