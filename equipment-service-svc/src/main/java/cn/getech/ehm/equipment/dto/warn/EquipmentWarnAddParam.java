package cn.getech.ehm.equipment.dto.warn;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备告警 新增参数
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentWarn新增", description = "设备告警新增参数")
public class EquipmentWarnAddParam extends ApiParam {
    @ApiModelProperty(value = "iot告警id", required = true)
    @NotBlank(message = "iot告警id不能为空")
    private String id;

    @ApiModelProperty(value = "iot关联设备id", required = true)
    @NotBlank(message = "iot关联设备id不能为空")
    private String deviceId;

    @ApiModelProperty(value = "告警级别(1预警2报警)", required = true)
    @NotNull(message = "告警级别不能为空")
    private Integer alarmType;

    @ApiModelProperty(value = "告警信息")
    private String alarmInfo;

    @ApiModelProperty(value = "告警状态(1告警中3结束)", required = true)
    @NotNull(message = "告警状态不能为空")
    private Integer alarmStatus;

    @ApiModelProperty(value = "告警时间", required = true)
    @NotNull(message = "告警时间不能为空")
    private Long startTs;

    @ApiModelProperty(value = "租户id")
    private String tenantId;
}