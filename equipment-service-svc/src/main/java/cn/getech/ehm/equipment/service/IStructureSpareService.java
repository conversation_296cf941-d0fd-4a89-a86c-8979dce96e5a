package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.info.StructureSpareDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureSpare;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 部件零件
 *
 * <AUTHOR>
 * @since 2020-12-04
 */
public interface IStructureSpareService extends IBaseService<EquipmentStructureSpare> {

        /**
         * 编辑
         * @param spareDto
         * @return
         */
        String editStructureSpare(StructureSpareDto spareDto);

        /**
         * 根据id查询，转dto
         * @param structureId
         * @return
         */
        StructureSpareDto getDtoByStructureId(String structureId);

        /**
         * 已配置零件属性的部件id集合
         * @param structureIds
         * @return
         */
        List<String> alreadyExistsIds(List<String> structureIds);
}