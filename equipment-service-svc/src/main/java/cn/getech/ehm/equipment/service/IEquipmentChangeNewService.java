package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.activiti.EquipmentTaskAuditParam;
import cn.getech.ehm.equipment.dto.change.*;
import cn.getech.ehm.equipment.entity.EquipmentChangeNew;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 设备异动表 服务类
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
public interface IEquipmentChangeNewService extends IBaseService<EquipmentChangeNew> {

    Boolean saveByParam(EquipmentChangeNewAddParam addParam);

    PageResult<EquipmentChangeNewDto> pageList(EquipmentChangeNewQueryParam queryParam);

    EquipmentChangeNewDto getDtoById(String id);

    Boolean deleteById(String[] ids);

    /**
     * 批量更新
     * @param batchAuditDto
     * @return
     */
    Boolean batchAudit(ChangeBatchAuditDto batchAuditDto);

    /**
     * 查询该设备是否存在位置异动
     * @param equipmentIds
     * @return
     */
    Boolean checkEquipmentHaveChange(List<String> equipmentIds);

    /**
     * 获取导出列表
     * @param queryParam
     * @return
     */
    List<EquipmentChangeExcelDto> getExportList(EquipmentChangeNewQueryParam queryParam);

    /**
     * 获取导出条数
     * @param queryParam
     * @return
     */
    Integer exportNum(EquipmentChangeNewQueryParam queryParam);
}