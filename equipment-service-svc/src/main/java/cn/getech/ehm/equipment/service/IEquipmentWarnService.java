package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.WarnAppQueryParam;
import cn.getech.ehm.equipment.dto.WarnFirstPageDto;
import cn.getech.ehm.equipment.dto.warn.*;
import cn.getech.ehm.equipment.entity.EquipmentWarn;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * 设备告警 服务类
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
public interface IEquipmentWarnService extends IBaseService<EquipmentWarn> {

        /**
         * 分页获取IOT设备告警列表
         *
         * @param equipmentWarnQueryParam
         * @return
         */
        PageResult<EquipmentWarnDto> pageDto(EquipmentWarnQueryParam equipmentWarnQueryParam);

        /**
         * 获取设备告警列表
         *
         * @param equipmentWarnQueryParam
         * @return
         */
        List<EquipmentWarnDto> getList(EquipmentWarnQueryParam equipmentWarnQueryParam);

        /**
         * 保存
         * @param equipmentWarnAddParam
         * @return
         */
        boolean saveByParam(EquipmentWarnAddParam equipmentWarnAddParam);

        /**
         * 更新
         * @param equipmentWarnEditParam
         */
        boolean updateByParam(EquipmentWarnEditParam equipmentWarnEditParam);

        /**
         * 批量保存
         * @param iotDetailDtos
         * @return
         */
        boolean saveIotBatch(List<IotDetailDto> iotDetailDtos, String tenantId);

        /**
         * 批量更新
         * @param iotDetailDtos
         */
        boolean updateIotBatch(List<IotDetailDto> iotDetailDtos);

        /**
         * app分页查询，返回Dto
         *
         * @param warnAppQueryParam
         * @return
         */
        PageResult<EquipmentWarnAppDto> appPageDto(WarnAppQueryParam warnAppQueryParam);

        /**
         * 获取首页设备管理(告警)
         *
         * @param warnAppQueryParam
         * @return
         */
        PageResult<WarnFirstPageDto> firstPage(WarnAppQueryParam warnAppQueryParam);

        /**
         * 工作台告警列表
         *
         * @param warnAppQueryParam
         * @return
         */
        PageResult<WarnFirstPageDto> warnList(WarnAppQueryParam warnAppQueryParam);

        /**
         * 根据id获取设备告警
         * @param id
         * @return
         */
        EquipmentWarnAppDto getWarnById(@RequestParam String id);
}