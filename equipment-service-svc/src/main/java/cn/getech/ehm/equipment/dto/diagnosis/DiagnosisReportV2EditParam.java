package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 诊断报告V2编辑参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportV2EditParam", description = "诊断报告V2编辑参数")
public class DiagnosisReportV2EditParam extends ApiParam {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String content;

    @ApiModelProperty(value = "发布状态")
    private Integer status;

    @ApiModelProperty(value = "关注设备")
    private List<ReportV2DetailDetailDto> followEquipments;

    @ApiModelProperty(value = "设备汇总")
    private List<ReportV2DetailDetailDto> equipmentList;

}
