package cn.getech.ehm.equipment.dto.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备权限
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "EquipmentOrgAuthDto", description = "设备部门权限")
public class EquipmentOrgAuthDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "部门编码")
    private String orgCode;

    @ApiModelProperty(value = "部门名称")
    private String orgName;

    @ApiModelProperty(value = "位置ids")
    private String[] locationIds;

    @ApiModelProperty(value = "位置名称集合")
    private String locationNames;

    @ApiModelProperty(value = "用户数量")
    private Integer userNum;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "系统")
    private String systemCode;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

}
