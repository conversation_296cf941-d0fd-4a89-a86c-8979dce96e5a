package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.category.EquipmentCategoryDto;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryTreeDto;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备类型 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Repository
public interface EquipmentCategoryMapper extends BaseMapper<EquipmentCategory> {

    /**
     * 获取层级编码
     * @param id
     * @return
     */
    String getLayerCode(String id);

    /**
     * 获取同一深度所有树节点
     * @param layerCode
     * @return
     */
    List<EquipmentCategory> getListByLayerCode(String layerCode);

    /**
     * 逻辑删除
     * @param id
     * @return
     */
    Integer deleteById(@Param("id") String id);

    /**
     * 查看详情
     * @param id
     * @return
     */
    EquipmentCategoryDto getById(String id);

    /**
     * 获取节点下子节点列表
     * @param parentId
     * @return
     */
    List<EquipmentCategoryTreeDto> getListByParentId(String parentId);

    /**
     * 获取节点下子节点列表
     * @param parentCode
     * @return
     */
    List<EquipmentCategoryTreeDto> getListByParentCode(String parentCode);

    /**
     * 获取设备中心字段在数据库中个条数
     * @param tableName
     * @param columnName
     * @param value
     * @return
     */
    Integer getCount(String tableName, String columnName, String value);

    /**
     * 初始化新增
     * @return
     */
    @SqlParser(filter = true)
    Boolean saveDto(@Param("entity") EquipmentCategory entity);

    /**
     * 判断是否初始化
     * @return
     */
    @SqlParser(filter = true)
    Integer checkInit(@Param("tenantId") String tenantId);

    /**
     * 导入CW
     * @param entities
     * @return
     */
    @SqlParser(filter = true)
    Boolean importCW(@Param("entities") List<EquipmentCategory> entities);

    /**
     * poros删除位置
     * @param codes
     * @return
     */
    @SqlParser(filter = true)
    Integer updateByCodes(@Param("codes") List<String> codes, @Param("tenantId") String tenantId);
}
