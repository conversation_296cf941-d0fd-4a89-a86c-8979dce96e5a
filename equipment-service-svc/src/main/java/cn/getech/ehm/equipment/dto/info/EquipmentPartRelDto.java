package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.constant.StaticValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;


/**
 * 设备备件关联表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
@ApiModel(value = "EquipmentPartRelDto", description = "设备备件关联表返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentPartRelDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "备件id")
    @NotBlank(message = "备件不能为空")
    private String partId;

    @ApiModelProperty(value = "位置")
    private String location;

    @ApiModelProperty(value = "备件编码")
    private String partCode;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型名称")
    private String partCategoryName;

    @ApiModelProperty(value = "规格型号")
    private String partSpecification;

    @ApiModelProperty(value = "当前库存")
    private Integer currentStock = StaticValue.ZERO;

    @ApiModelProperty(value = "最小库存")
    private BigDecimal minStock;

    @ApiModelProperty(value = "最大库存")
    private BigDecimal maxStock;

    @ApiModelProperty(value = "图片ids")
    private String picIds;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}