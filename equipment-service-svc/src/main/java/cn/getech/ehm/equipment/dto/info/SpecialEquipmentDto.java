package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 特种设备 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-03
 */
@Data
@ApiModel(value = "SpecialEquipmentDto", description = "特种设备返回数据模型")
public class SpecialEquipmentDto {
    @ApiModelProperty(value = "设备类别(1主设备2子设备)")
    private Integer equipmentType;

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "编码")
    private String equipmentCode;

    @ApiModelProperty(value = "位号")
    private String equipmentItemNo;

    @ApiModelProperty(value = "名称")
    private String equipmentName;

    @ApiModelProperty(value = "结构位置id")
    private String parentId;

    @ApiModelProperty(value = "结构位置名称")
    private String parentName;

    @ApiModelProperty(value = "类型Id")
    private String categoryId;

    @ApiModelProperty(value = "类型")
    private String categoryName;

    @ApiModelProperty(value = "规格型号")
    private String specification;
}