package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 特种设备类型
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "SpecialInfoCategoryDto", description = "特种设备类型")
public class SpecialInfoCategoryDto {

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "1设备类型2备件类型")
    private Integer type;

}