package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryProp;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备类型扩展属性 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Repository
public interface EquipmentCategoryPropMapper extends BaseMapper<EquipmentCategoryProp> {

    /**
     * 初始化新增
     * @return
     */
    @SqlParser(filter = true)
    Boolean saveDto(@Param("info") EquipmentCategoryProp info);

    /**
     * 根据类型id获取属性
     * @param categoryIds
     * @return
     */
    List<EquipmentCategoryPropDetailDto> getDetailListByCategoryIds(@Param("categoryIds") List<String> categoryIds, @Param("fixed") Boolean fixed, @Param("exported") Boolean exported);

    /**
     * 获取扩展信息组名-名称集合
     * @param categoryId
     * @return
     */
    List<String> getNameListByCategoryId(@Param("categoryId") String categoryId, @Param("exported") Boolean exported);
}
