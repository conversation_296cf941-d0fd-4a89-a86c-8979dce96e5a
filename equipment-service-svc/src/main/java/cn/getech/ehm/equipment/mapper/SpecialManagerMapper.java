package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.manager.SpecialManagerDto;
import cn.getech.ehm.equipment.dto.manager.SpecialManagerQueryParam;
import cn.getech.ehm.equipment.entity.SpecialManager;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 管理人员
 * <AUTHOR>
 * @since 2020-08-27
 */
@Repository
public interface SpecialManagerMapper extends BaseMapper<SpecialManager> {
    /**
     * 分页查询
     * @param page
     * @param param
     * @return
     */
    IPage<SpecialManagerDto> getPageList(Page<SpecialManagerQueryParam> page,
                                         @Param("param") SpecialManagerQueryParam param);
}
