package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.dto.EagerTreeNodeDto;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.BuildInfoSearchDto;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.equipment.dto.CategoryStatisticsDto;
import cn.getech.ehm.equipment.dto.category.*;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentCategoryProp;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.mapper.EquipmentCategoryMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.parameter.InfoDefaultStatusParamDto;
import cn.getech.ehm.iot.dto.parameter.OeeEditDto;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备类型 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Slf4j
@Service
public class EquipmentCategoryServiceImpl extends BaseServiceImpl<EquipmentCategoryMapper, EquipmentCategory> implements IEquipmentCategoryService {

   private static final String ALL = "全部" ;
   private static final String ZERO = "0";
   private static final String MINUS_ONE = "-1";
    @Autowired
    private EquipmentCategoryMapper equipmentCategoryMapper;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentCategoryPropService equipmentCategoryPropService;
    @Autowired
    private ICalibrationStandardService calibrationStandardService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private ParameterClient parameterClient;

    @Override
    public List<EquipmentCategoryTreeDto> node(String parentId, Integer isClient){
        List<EquipmentCategoryTreeDto> resDtos = new ArrayList<>();
        if(StringUtils.isBlank(parentId)){
            //首次进入需要新增默认为全部的空根节点
            EquipmentCategoryTreeDto treeDto = new EquipmentCategoryTreeDto();
            treeDto.setId(ZERO);
            treeDto.setName(ALL);
            treeDto.setFixed(StaticValue.ONE);
            List<EquipmentCategoryTreeDto> childDto = equipmentCategoryMapper.getListByParentId(ZERO);
            if(childDto.size() > StaticValue.ZERO) {
                treeDto.setIsLeaf(false);
                treeDto.setChild(childDto.size());
            }
            resDtos.add(treeDto);
        }else{
            //客户平台/设置部门权限之后需要过滤
            BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
            List<String> customerInfoIds = buildInfoSearchDto.getEquipmentIds();
            /*if(null != isClient && isClient.intValue() == StaticValue.ONE) {
                RestResponse<List<String>> restResponse = systemClient.getCurrentInfoIds();
                if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
                    customerInfoIds = restResponse.getData();
                }
            }*/
            resDtos = equipmentCategoryMapper.getListByParentId(parentId);
            for(EquipmentCategoryTreeDto treeDto : resDtos) {
                List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.getListByLayerCode(treeDto.getLayerCode());
                if(equipmentCategories.size() > StaticValue.ONE) {
                    treeDto.setIsLeaf(false);
                    List<EquipmentCategory> childNum = equipmentCategories.stream()
                            .filter(dto -> treeDto.getId().equals(dto.getParentId()))
                            .collect(Collectors.toList());
                    treeDto.setChild(childNum.size());
                }
                /*if(null != isClient && isClient.intValue() == StaticValue.ONE && CollectionUtils.isEmpty(customerInfoIds)){
                    treeDto.setTotal(StaticValue.ZERO);
                }*/
                if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(customerInfoIds)){
                    treeDto.setTotal(StaticValue.ZERO);
                }else {
                    List<String> ids = equipmentCategories.stream().map(EquipmentCategory::getId).collect(Collectors.toList());
                    Integer num = equipmentInfoService.getCountByTypeId(ids, customerInfoIds);
                    treeDto.setTotal(num);
                }
            }

        }

        return resDtos;
    }

    @Override
    public List<EquipmentCategoryTreeDto> tree(){
        List<EquipmentCategoryTreeDto> resDtos = new ArrayList<>();
        EquipmentCategoryTreeDto treeDto = new EquipmentCategoryTreeDto();
        treeDto.setId(ZERO);
        treeDto.setName(ALL);
        treeDto.setFixed(StaticValue.ONE);
        List<EquipmentCategoryTreeDto> children = buildTree("0");
        if(CollectionUtils.isNotEmpty(children)) {
            treeDto.setChildren(children);
            treeDto.setIsLeaf(false);
        }
        resDtos.add(treeDto);
        return resDtos;
    }

    private List<EquipmentCategoryTreeDto> buildTree(String parentId){
        List<EquipmentCategoryTreeDto> resDtos = equipmentCategoryMapper.getListByParentId(parentId);
        if(CollectionUtils.isEmpty(resDtos)){
            return null;
        }
        for(EquipmentCategoryTreeDto treeDto : resDtos) {
            List<EquipmentCategoryTreeDto> children = buildTree(treeDto.getId());
            if(CollectionUtils.isNotEmpty(children)) {
                treeDto.setIsLeaf(false);
                treeDto.setChildren(children);
            }
        }
        return resDtos;
    }

    @Override
    public Map<String, String> getCategoryNamesByCodes(String[] codes){
        if(null == codes || codes.length < 0){
            return new HashMap<>(StaticValue.ONE);
        }
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentCategory::getCode, codes);
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getCode,EquipmentCategory::getName);
        List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.selectList(wrapper);
        Map<String, String> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(equipmentCategories)){
            for(EquipmentCategory category : equipmentCategories){
                if(map.containsKey(category.getCode())){
                    continue;
                }
                map.put(category.getCode(), category.getName());
            }
        }
        return map;
    }

    @Override
    public List<EquipmentCategoryDto> queryList(String keyword) {
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(keyword)){
            wrapper.and(w -> w.like(EquipmentCategory::getName, keyword).or().like(EquipmentCategory::getCode, keyword));
        }
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        return CopyDataUtil.copyList(equipmentCategoryMapper.selectList(wrapper), EquipmentCategoryDto.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public EquipmentCategoryTreeDto saveByParam(EquipmentCategoryAddParam addParam) {
        if(checkCode(null, addParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentCategory equipmentCategory = CopyDataUtil.copyObject(addParam, EquipmentCategory.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentCategory);
        String id = UUID.randomUUID().toString().replace("-","");
        EquipmentCategoryDto parent = equipmentCategoryMapper.getById(addParam.getParentId());
        equipmentCategory.setId(id);
        String layerCode = this.getLayerCode(parent, id);
        equipmentCategory.setLayerCode(layerCode);
        /*String prefix = this.getPrefix(parent, equipmentCategory.getCode());
        equipmentCategory.setPrefix(prefix);*/
        equipmentCategory.setDeleted(DeletedType.NO.getValue());
        save(equipmentCategory);

        if(CollectionUtils.isNotEmpty(addParam.getPropDtos())){
            equipmentCategoryPropService.saveList(addParam.getPropDtos(), equipmentCategory.getId(), true);
        }

        EquipmentCategoryTreeDto equipmentCategoryTreeDto = CopyDataUtil.copyObject(equipmentCategory, EquipmentCategoryTreeDto.class);

        return equipmentCategoryTreeDto;
    }

    private Boolean checkCode(String id ,String code){
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(EquipmentCategory::getId, id);
        }
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.eq(EquipmentCategory::getCode, code);
        return equipmentCategoryMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<EquipmentCategoryPropDto> getParentProps(String categoryId){
        return equipmentCategoryPropService.getListByCategoryId(categoryId, false, false);
    }

    /**
     * 获取设备编码前缀
     * @param parent
     * @return
     */

    private String getLayerCode(EquipmentCategoryDto parent, String id){
        log.info("生成层级编码");
        StringBuffer layerCode = new StringBuffer();
        String parentLayerCode = null != parent ? parent.getLayerCode() : "";
        if(StringUtils.isNotBlank(parentLayerCode)){
            layerCode.append(parentLayerCode).append("/").append(id);
        }else{
            layerCode.append(id);
        }
        return layerCode.toString();
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateByParam(EquipmentCategoryEditParam editParam) {
        if(checkCode(editParam.getId(), editParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentCategory equipmentCategory = CopyDataUtil.copyObject(editParam, EquipmentCategory.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentCategory);

        //上级节点修改，修改树结构下所有层级编码
        //父级节点不能为自己
        EquipmentCategoryDto parent = equipmentCategoryMapper.getById(editParam.getParentId());
        EquipmentCategoryDto oldEquipmentCategory = equipmentCategoryMapper.getById(editParam.getId());
        if(!editParam.getParentId().equals(editParam.getId()) &&
                !oldEquipmentCategory.getParentId().equals(editParam.getParentId())){
            String layerCode = this.getLayerCode(parent, equipmentCategory.getId());
            //String prefix = this.getPrefix(parent, equipmentCategory.getCode());
            List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.getListByLayerCode(oldEquipmentCategory.getLayerCode());
            equipmentCategories.stream().forEach(info -> {
                String oldLayerCode = info.getLayerCode();
                String newLayerCode = oldLayerCode.replaceFirst(oldEquipmentCategory.getLayerCode(), layerCode);
                info.setLayerCode(newLayerCode);

            });
            this.updateBatchById(equipmentCategories);
        }

        Boolean flag =  updateById(equipmentCategory);

        equipmentCategoryPropService.deleteByCategoryId(equipmentCategory.getId());
        if(CollectionUtils.isNotEmpty(editParam.getPropDtos())){
            equipmentCategoryPropService.saveList(editParam.getPropDtos(), equipmentCategory.getId(), false);
        }

        return flag;
    }

    private void editOeeParam(Boolean oeeOpen, List<String> equipmentIds){
        try {
            if(CollectionUtils.isNotEmpty(equipmentIds)) {
                OeeEditDto oeeEditDto = new OeeEditDto();
                oeeEditDto.setEquipmentIds(equipmentIds);
                if (oeeOpen) {
                    //生成oee参数
                    parameterClient.createOeeParam(oeeEditDto);
                } else {
                    parameterClient.deleteOeeParam(oeeEditDto);
                }
            }
        }catch (Exception e){
            log.error("连接iot编辑oee失败");
        }
    }

    @Override
    public CategoryCalibrationDto getCalibrationById(String id){
        EquipmentCategory equipmentCategory = equipmentCategoryMapper.selectById(id);
        if(equipmentCategory.getType().intValue() != StaticValue.ONE){
            log.error("该类型不是仪表仪器或其子节点");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("type_error", null, LocaleContextHolder.getLocale())));
        }
        CategoryCalibrationDto categoryCalibrationDto = CopyDataUtil.copyObject(equipmentCategory, CategoryCalibrationDto.class);
        if(null != categoryCalibrationDto) {
            List<CalibrationStandardDto> categoryCalibrationDtos = calibrationStandardService.getByCategoryId(id);
            categoryCalibrationDto.setCalibrationStandardDtos(categoryCalibrationDtos);
        }
        return categoryCalibrationDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateCalibration(CategoryCalibrationDto categoryCalibrationDto){
        EquipmentCategory oldEquipmentCategory = equipmentCategoryMapper.selectById(categoryCalibrationDto.getId());
        //校准周期、校准提醒为空或修改， 修改校准单
        Boolean intervalPeriod = (null == oldEquipmentCategory.getIntervalPeriod() ||
                categoryCalibrationDto.getIntervalPeriod().intValue() != oldEquipmentCategory.getIntervalPeriod().intValue());
        Boolean remindBefore = (null == oldEquipmentCategory.getRemindBefore() ||
                categoryCalibrationDto.getRemindBefore().intValue() != oldEquipmentCategory.getRemindBefore().intValue());
        EquipmentCategory equipmentCategory = CopyDataUtil.copyObject(categoryCalibrationDto, EquipmentCategory.class);
        equipmentCategoryMapper.updateById(equipmentCategory);
        if(CollectionUtils.isNotEmpty(categoryCalibrationDto.getCalibrationStandardDtos())){
            calibrationStandardService.updateList(categoryCalibrationDto.getCalibrationStandardDtos(), categoryCalibrationDto.getId());
        }

        if(intervalPeriod || remindBefore){
            // 获取该类别已经存在的设备
            List<EquipmentInfo> equipmentInfoList = equipmentInfoService.getEquipmentListByCategoryId(categoryCalibrationDto.getId());
            if(CollUtil.isNotEmpty(equipmentInfoList)) {
                // 获取每台设备的校准日期
                equipmentInfoService.createCalibrationTaskOrderBatch(equipmentCategory, equipmentInfoList);
            }
        }
        return true;
    }

    @Override
    public EquipmentCategoryDto getDtoById(String id) {
        EquipmentCategoryDto dto = new EquipmentCategoryDto();
        //最高节点0单独返回
        if(id.equals(ZERO)){
            dto.setParentId(MINUS_ONE);
            dto.setId(ZERO);
            dto.setName("全部");
            dto.setCode("ROOT");
            UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
            dto.setCreateBy(userBaseInfo.getName());
            dto.setCreateTime(new Date());
            dto.setUpdateBy(userBaseInfo.getName());
            dto.setUpdateTime(new Date());
            return dto;
        }
        dto = equipmentCategoryMapper.getById(id);
        if(null != dto) {
            if(dto.getParentId().equals(ZERO)){
                dto.setParentName("全部");
            }
            /**获取当前节点下所有子节点列表*/
            List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.getListByLayerCode(dto.getLayerCode());
            List<String> ids = equipmentCategories.stream().map(EquipmentCategory::getId).collect(Collectors.toList());
            Integer num = equipmentInfoService.getCountByTypeId(ids, null);
            dto.setNum(num);

            List<EquipmentCategoryPropDto> propDtos = equipmentCategoryPropService.getListByCategoryId(id, false, false);
            dto.setPropDtos(propDtos);
        }
        return dto;
    }

    @Override
    public Map<String, String> fetchLayerCode(List<String> idList){
        Map<String, String> layerMap = new HashMap();
        if (CollectionUtils.isEmpty(idList)){
            return layerMap;
        }
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getLayerCode);
        wrapper.in(EquipmentCategory::getId, idList);
        List<EquipmentCategory> categoryList = this.baseMapper.selectList(wrapper);
        categoryList.stream().forEach(category -> layerMap.put(category.getId(), category.getLayerCode()));
        return layerMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteById(String id){
        Boolean flag = equipmentInfoService.checkCategoryUsed(id);
        if(flag){
            log.error("类型被设备绑定，请解除绑定");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("referenced", null, LocaleContextHolder.getLocale())));
        }
        flag =  equipmentCategoryMapper.deleteById(id) > 0;
        equipmentCategoryPropService.deleteByCategoryId(id);

        return flag;
    }

    @Override
    public Boolean deleteByCodes(List<String> codes, String tenantId){
        return equipmentCategoryMapper.updateByCodes(codes, tenantId) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean initialization(String tenantId){
        Integer count = equipmentCategoryMapper.checkInit(tenantId);
        if(count > 0){
            log.info("存在初始化数据");
            return true;
        }


        //默认最高节点0全部
        EquipmentCategory scsb = new EquipmentCategory();
        String scsbId = UUID.randomUUID().toString().replace("-","");
        scsb.setId(scsbId);
        scsb.setLayerCode(scsbId);
        scsb.setCode("SCSB");
        scsb.setName("生产设备");
        scsb.setParentId(ZERO);
        scsb.setFixed(StaticValue.ONE);
        scsb.setType(StaticValue.ZERO);
        scsb.setPrefix(scsb.getCode());
        scsb.setDeleted(DeletedType.NO.getValue());
        scsb.setTenantId(tenantId);
        scsb.setCreateBy("system");
        scsb.setCreateTime(new Date());
        scsb.setUpdateBy("system");
        scsb.setUpdateTime(new Date());
        equipmentCategoryMapper.saveDto(scsb);

        EquipmentCategory rq = CopyDataUtil.copyObject(scsb, EquipmentCategory.class);
        String rqId = UUID.randomUUID().toString().replace("-","");
        rq.setId(rqId);
        rq.setLayerCode(rqId);
        rq.setCode("RQ");
        rq.setName("容器");
        rq.setPrefix(rq.getCode());
        equipmentCategoryMapper.saveDto(rq);

        EquipmentCategory ybyq = CopyDataUtil.copyObject(scsb, EquipmentCategory.class);
        String ybyqId = UUID.randomUUID().toString().replace("-","");
        ybyq.setId(ybyqId);
        ybyq.setLayerCode(ybyqId);
        ybyq.setCode("YBYQ");
        ybyq.setName("仪表仪器");
        ybyq.setType(StaticValue.ONE);
        ybyq.setPrefix(ybyq.getCode());
        equipmentCategoryMapper.saveDto(ybyq);
        List<EquipmentCategoryPropDto> propDtos = new ArrayList<>();
        EquipmentCategoryPropDto jzrq = new EquipmentCategoryPropDto();
        /*jzrq.setName("校准日期");
        jzrq.setPropType(StaticValue.THREE);
        jzrq.setFixed(StaticValue.ONE);
        jzrq.setTenantId(tenantId);*/
        EquipmentCategoryPropDto zsbh = CopyDataUtil.copyObject(jzrq, EquipmentCategoryPropDto.class);
       /* jzrq.setName("证书编号");
        jzrq.setPropType(StaticValue.ZERO);*/
        propDtos.add(jzrq);
        propDtos.add(zsbh);
        equipmentCategoryPropService.initialization(propDtos, ybyq.getId());

        EquipmentCategory gjgz = CopyDataUtil.copyObject(scsb, EquipmentCategory.class);
        String gjgzId = UUID.randomUUID().toString().replace("-","");
        gjgz.setId(gjgzId);
        gjgz.setLayerCode(gjgzId);
        gjgz.setCode("GJGZ");
        gjgz.setName("工具工装");
        gjgz.setPrefix(gjgz.getCode());
        equipmentCategoryMapper.saveDto(gjgz);

        return true;
    }

    @Override
    public Map<String, EquipmentCategory> getMapByNames(List<String> categoryNames) {
        if(CollectionUtils.isEmpty(categoryNames)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentCategory::getName, categoryNames);
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getName, EquipmentCategory::getCode, EquipmentCategory::getOeeOpen, EquipmentCategory::getPicIds);
        return equipmentCategoryMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentCategory::getName, v -> v, (v1, v2) -> v1));
    }

    @Override
    public Map<String, EquipmentCategory> getAllNameIdMap(){
        Map<String, EquipmentCategory> map = new HashMap<>();
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getLayerCode, EquipmentCategory::getName, EquipmentCategory::getOeeOpen, EquipmentCategory::getPicIds);
        List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentCategories)){
            Map<String, String> nameMap = equipmentCategories.stream().collect(Collectors.toMap(EquipmentCategory::getId, EquipmentCategory::getName, (v1,v2) -> v1));
            for(EquipmentCategory equipmentCategory : equipmentCategories){
                String[] allIds = equipmentCategory.getLayerCode().split(StringPool.SLASH);
                String allName = "";
                for(int i = 0; i < allIds.length; i++){
                    //第一位为根节点，剔除
                    String name = nameMap.get(allIds[i]);
                    if(StringUtils.isBlank(name)){
                        //未查询到全路径名称，跳过
                        break;
                    }
                    allName += name;
                    if(i != allIds.length - 1){
                        allName += StringPool.SLASH;
                    }
                }
                map.put(allName.replace("（", "(").replace("）",")"), equipmentCategory);
            }
        }
        return map;
    }

    @Override
    public Map<String, CategoryStatisticsDto> getCategoryStatistics(){
        Map<String, CategoryStatisticsDto> map = new HashMap<>();
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.isNotNull(EquipmentCategory::getLayerCode);
        wrapper.select(EquipmentCategory::getCode, EquipmentCategory::getId, EquipmentCategory::getName,
                EquipmentCategory::getLayerCode, EquipmentCategory::getParentId, EquipmentCategory::getCreateTime);
        List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentCategories)){
            equipmentCategories.stream().forEach(dto -> map.put(dto.getId(), CopyDataUtil.copyObject(dto, CategoryStatisticsDto.class)));
        }
        return map;
    }

    @Override
    public Integer getCount(String tableName, String columnName, String value) {
        return equipmentCategoryMapper.getCount(tableName, columnName, value);
    }

    @Override
    public List<EagerTreeNodeDto> getAllNode(){
        List<EagerTreeNodeDto> dtos = new ArrayList<>();
        //默认新增id为0的全部节点
        EagerTreeNodeDto root = EagerTreeNodeDto.builder()
                .id(ZERO)
                .code("ROOT")
                .title("全部")
                .pid(MINUS_ONE)
                .disabled(false)
                .checkable(true)
                .selectable(true)
                .disableCheckbox(false)
                .build();
        dtos.add(root);
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getId,EquipmentCategory::getCode, EquipmentCategory::getName, EquipmentCategory::getParentId);
        List<EquipmentCategory> categories = equipmentCategoryMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(categories)){
            for(EquipmentCategory category : categories){
                EagerTreeNodeDto eagerTreeNodeDto = EagerTreeNodeDto.builder()
                        .id(category.getId())
                        .code(category.getCode())
                        .title(category.getName())
                        .pid(category.getParentId())
                        .disabled(false)
                        .checkable(true)
                        .selectable(true)
                        .disableCheckbox(false)
                        .build();
                dtos.add(eagerTreeNodeDto);
            }
        }
        return dtos;
    }

    @Override
    public List<String> getCodesByName(String name){
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.like(EquipmentCategory::getName, name);
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getCode);
        List<EquipmentCategory> categories = equipmentCategoryMapper.selectList(wrapper);
        return categories.stream().map(EquipmentCategory::getCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getCategoryIdsByParentId(List<String> categoryIds) {
        LambdaQueryWrapper<EquipmentCategory> queryWrapper = Wrappers.lambdaQuery();
        if(CollectionUtils.isNotEmpty(categoryIds)) {
            for (String categoryId : categoryIds) {
                queryWrapper.like(EquipmentCategory::getLayerCode, categoryId).or();
            }
        }
        queryWrapper.select(EquipmentCategory::getId);
        return equipmentCategoryMapper.selectList(queryWrapper).stream().map(EquipmentCategory::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean excelImport(List<EquipmentCategoryExcel> excels) {
        if (CollectionUtils.isNotEmpty(excels)) {
            List<EquipmentCategory> equipmentCategories = new ArrayList<>(excels.size());
            for (EquipmentCategoryExcel excel : excels) {
                EquipmentCategory equipmentCategory = new EquipmentCategory();
                String id = UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
                equipmentCategory.setId(id);
                equipmentCategory.setCode(excel.getCode());
                equipmentCategory.setName(excel.getName());
                equipmentCategory.setLayerCode(id);
                equipmentCategory.setPrefix(excel.getCode());
                equipmentCategory.setParentId("0");
                equipmentCategory.setFixed(StaticValue.ZERO);
                equipmentCategory.setType(StaticValue.ZERO);
                equipmentCategory.setDeleted(DeletedType.NO.getValue());
                equipmentCategories.add(equipmentCategory);
            }
            return saveBatch(equipmentCategories);
        }
        return false;
    }

    @Override
    public Boolean excelImportCW(List<CategoryCWExcel> excels){
        if(CollectionUtils.isNotEmpty(excels)){
            List<EquipmentCategory> equipmentCategories = new ArrayList<>();
            List<EquipmentCategoryProp> categoryProps = new ArrayList<>();
            Map<String, EquipmentCategory> nameMap = new HashMap<>();
            Map<String, EquipmentCategory> categoryMap = this.getAllNameIdMap();
            UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            for(CategoryCWExcel excel : excels){
                String[] names = excel.getName().split(StringPool.SLASH);
                String parentId = "0";
                String layerCode = "";
                String allName = "";
                Integer i = 0;
                for(String name : names){
                    i++;
                    allName = StringUtils.isNotBlank(allName) ? allName + StringPool.SLASH + name : name;
                    //查询本次是否已经使用过
                    EquipmentCategory equipmentCategory = nameMap.get(allName);
                    if (null != equipmentCategory) {
                        parentId = equipmentCategory.getId();
                        layerCode = equipmentCategory.getLayerCode();
                        continue;
                    }
                    //再次查询数据库中是否有
                    equipmentCategory = categoryMap.get(allName);
                    if (null != equipmentCategory) {
                        parentId = equipmentCategory.getId();
                        layerCode = equipmentCategory.getLayerCode();
                        nameMap.put(allName, equipmentCategory);
                        continue;
                    }

                    //否则，为新增
                    equipmentCategory = new EquipmentCategory();
                    String id = UUID.randomUUID().toString().replace("-","");
                    equipmentCategory.setId(id);
                    equipmentCategory.setName(name);
                    equipmentCategory.setCode(excel.getCode());
                    equipmentCategory.setParentId(parentId);
                    equipmentCategory.setLayerCode(StringUtils.isNotBlank(layerCode) ? layerCode + StringPool.SLASH + id : id);
                    equipmentCategory.setDeleted(DeletedType.NO.getValue());
                    //手动修改时间，保证可以按照新增时间做排序
                    equipmentCategory.setCreateBy(userBaseInfo.getUid());
                    equipmentCategory.setCreateTime(calendar.getTime());
                    equipmentCategory.setUpdateBy(userBaseInfo.getUid());
                    equipmentCategory.setUpdateTime(calendar.getTime());
                    equipmentCategory.setTenantId(userBaseInfo.getTenantId());
                    calendar.add(Calendar.MINUTE, 1);
                    //标记当前新增的位置
                    parentId = equipmentCategory.getId();
                    layerCode = equipmentCategory.getLayerCode();
                    nameMap.put(allName, equipmentCategory);
                    equipmentCategories.add(equipmentCategory);

                    if(i == names.length && StringUtils.isNotBlank(excel.getProp())){
                        //最后一个为根节点,补上扩展属性
                        //格式，分组名称-属性名称;
                        String[] props = excel.getProp().replace("；", ";").split(";");

                        int sort = 1;
                        for(String prop : props){
                            String[] propNames = prop.split(StringPool.DASH);
                            EquipmentCategoryProp categoryProp = new EquipmentCategoryProp();
                            categoryProp.setGroupName(propNames[0]);
                            categoryProp.setName(propNames[1]);
                            categoryProp.setPropType(0);
                            categoryProp.setEquipmentCategoryId(id);
                            categoryProp.setSort(sort++);
                            categoryProps.add(categoryProp);
                        }
                    }
                }
            }

            equipmentCategoryMapper.importCW(equipmentCategories);
            equipmentCategoryPropService.saveBatch(categoryProps);
        }
        return true;
    }


    @Override
    public List<String> getPropList(String categoryId, Boolean exported){
        return equipmentCategoryPropService.getNameListByCategoryId(categoryId, exported);
    }

    @Override
    public Map<String, String> getDepthName(List<String> categoryIds){
        Map<String, String> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(categoryIds)) {
            LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
            wrapper.select(EquipmentCategory::getId, EquipmentCategory::getName, EquipmentCategory::getLayerCode);
            Map<String, EquipmentCategory> categoryMap = equipmentCategoryMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentCategory::getId, v -> v, (v1, v2) -> v1));
            if (null != categoryMap && categoryMap.size() > 0) {
                for (String categoryId : categoryIds) {
                    EquipmentCategory equipmentCategory = categoryMap.get(categoryId);
                    if (null != equipmentCategory) {
                        List<String> categoryNames = new ArrayList<>();
                        String[] parentIds = equipmentCategory.getLayerCode().split(StringPool.SLASH);
                        for (int i = 0; i < parentIds.length; i++) {
                            EquipmentCategory parentCategory = categoryMap.get(parentIds[i]);
                            if (null != parentCategory) {
                                categoryNames.add(parentCategory.getName());
                            } else {
                                break;
                            }
                        }
                        String categoryName = StringUtils.join(categoryNames.toArray(), StringPool.SLASH);
                        map.put(categoryId, categoryName);
                    }
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, String> getMapByIds(String[] categoryIds){
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentCategory::getId, categoryIds);
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getName);
        return equipmentCategoryMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentCategory::getId, EquipmentCategory::getName));
    }

    @Override
    public List<CategoryInfoDto> getAllCategory(List<String> categoryIds){
        List<CategoryInfoDto> dtos = new ArrayList<>();
        //增加根节点
        CategoryInfoDto root = new CategoryInfoDto();
        root.setId("0");
        root.setName("全部");
        root.setCode("root");
        root.setLayerCode("0");
        dtos.add(root);
        List<CategoryInfoDto> categoryInfoDtos = getDtosByIds(categoryIds);
        if(CollectionUtils.isNotEmpty(categoryInfoDtos)){
            dtos.addAll(categoryInfoDtos);
        }
        return dtos;
    }

    private List<CategoryInfoDto> getDtosByIds(List<String> categoryIds){
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        if(CollectionUtils.isNotEmpty(categoryIds)){
            wrapper.in(EquipmentCategory::getId, categoryIds);
        }
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getName, EquipmentCategory::getLayerCode,
                EquipmentCategory::getCode, EquipmentCategory::getParentId);
        wrapper.orderByAsc(EquipmentCategory::getName);
        return CopyDataUtil.copyList(equipmentCategoryMapper.selectList(wrapper), CategoryInfoDto.class);
    }

    @Override
    public List<CategoryInfoDto> getDtosIncludeChild(List<String> categoryIds){
        List<CategoryInfoDto> dtos = new ArrayList<>();
        if(CollectionUtils.isEmpty(categoryIds) || categoryIds.contains("0")){
            dtos = this.getAllCategory(null);
        }else {
            List<String> allCategoryIds = this.getCategoryIdsByParentId(categoryIds);
            if (CollectionUtils.isNotEmpty(allCategoryIds)) {
                dtos = getDtosByIds(allCategoryIds);
            }
        }
        return dtos;
    }

    @Override
    public Map<String, String> getCategoryAllNameMap(){
        Map<String, String> nameMap = new HashMap<>();
        LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentCategory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentCategory::getId, EquipmentCategory::getName, EquipmentCategory::getParentId, EquipmentCategory::getLayerCode);
        List<EquipmentCategory> equipmentCategories = equipmentCategoryMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentCategories)){
            Map<String, List<EquipmentCategory>> childMap = equipmentCategories.stream().collect(Collectors.groupingBy(EquipmentCategory::getParentId));
            buildAllName("0", nameMap, childMap, null);
        }
        return nameMap;
    }

    private void buildAllName(String parentId, Map<String, String> nameMap, Map<String, List<EquipmentCategory>> childMap, String parentAllName){
        List<EquipmentCategory> childEntities = childMap.get(parentId);
        if(CollectionUtils.isEmpty(childEntities)){
            return;
        }
        for(EquipmentCategory equipmentCategory : childEntities){
            String allName = StringUtils.isNotBlank(parentAllName) ? parentAllName + StringPool.SLASH + equipmentCategory.getName() : equipmentCategory.getName();
            nameMap.put(equipmentCategory.getId(), allName);
            buildAllName(equipmentCategory.getId(), nameMap, childMap, allName);
        }
    }

    @Override
    public EquipmentCategoryDto getCategorySummary(String categoryId){
        EquipmentCategoryDto dto = null;
        if(StringUtils.isNotBlank(categoryId)){
            LambdaQueryWrapper<EquipmentCategory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(EquipmentCategory::getId, categoryId);
            wrapper.select(EquipmentCategory::getId, EquipmentCategory::getOeeOpen, EquipmentCategory::getStatusParamOpen, EquipmentCategory::getPicIds);
            EquipmentCategory equipmentCategory = equipmentCategoryMapper.selectOne(wrapper);
            if(null != equipmentCategory){
                dto = new EquipmentCategoryDto();
                dto.setId(equipmentCategory.getId());
                dto.setOeeOpen(equipmentCategory.getOeeOpen());
                dto.setStatusParamOpen(equipmentCategory.getStatusParamOpen());
                dto.setPicIds(equipmentCategory.getPicIds());
            }
        }
        return dto;
    }

    @Override
    public Boolean enabledDefaultStatusParam(String categoryId, Boolean enabled){
        LambdaUpdateWrapper<EquipmentCategory> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentCategory::getId, categoryId);
        wrapper.set(EquipmentCategory::getStatusParamOpen, enabled);
        this.update(wrapper);

        try{
            parameterClient.updateDefaultStatusParam(categoryId, enabled);
        }catch (Exception e){
            log.error("-----连接iot服务失败");
        }

        return true;
    }

    @Override
    public CategorySummaryDto getSummaryDto(String id){
        return CopyDataUtil.copyObject(this.getById(id), CategorySummaryDto.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updatePresetParam(CategorySummaryDto dto){
        EquipmentCategoryDto oldEquipmentCategory = equipmentCategoryMapper.getById(dto.getId());
        if (!oldEquipmentCategory.getOeeOpen().equals(dto.getOeeOpen())) {
            List<String> equipmentIds = equipmentInfoService.getInfosByCategoryId(dto.getId()).stream().map(EquipmentInfo::getId).distinct().collect(Collectors.toList());
            this.editOeeParam(dto.getOeeOpen(), equipmentIds);
        }
        try{
            parameterClient.updateDefaultStatusParam(dto.getId(), dto.getStatusParamOpen());
        }catch (Exception e){
            log.error("-----连接iot服务失败");
        }
        LambdaUpdateWrapper<EquipmentCategory> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentCategory::getId, dto.getId());
        wrapper.set(EquipmentCategory::getOeeOpen, dto.getOeeOpen());
        wrapper.set(EquipmentCategory::getStatusParamOpen, dto.getStatusParamOpen());
        return update(wrapper);
    }
}
