package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 概览图模板卡片
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("overview_model_card")
public class OverviewModelCard extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 卡片标识(固定，与前端对应)
     */
    @TableField("num")
    private Integer num;

    /**
     * 类型(1位置2设备)
     */
    @TableField("type")
    private Integer type;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
