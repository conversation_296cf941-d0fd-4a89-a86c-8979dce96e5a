package cn.getech.ehm.equipment.dto.diagnosis;

import com.deepoove.poi.data.PictureRenderData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 诊断报告图片信息
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "AnalyzePicInfoDto", description = "诊断报告图片信息")
public class AnalyzePicInfoDto {

    @ApiModelProperty(value = "图片路径")
    private String url;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "时间标记")
    private String timeStamp;

    @ApiModelProperty(value = "dataKey")
    private String dataKey;

    @ApiModelProperty(value = "导出url")
    private PictureRenderData exportUrl;

}