package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 概览图详情返回数据模型
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "OverviewDetailDto", description = "概览图详情返回数据模型")
public class OverviewDetailDto {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否开启概览图")
    private Boolean enable = false;

    @ApiModelProperty(value = "版式(1,2)")
    private Integer modelType;

    @ApiModelProperty(value = "类型(1位置2设备)")
    private Integer type;

    @ApiModelProperty(value = "类型对应标识(位置id；设备id)")
    private String relationKey;

    @ApiModelProperty(value = "版式2卡片标识集合")
    private Integer[] cardList;

}