package cn.getech.ehm.equipment.dto.runningparam;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;

/**
 * 零部件类型查询参数
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsCategoryQueryParam", description = "零部件类型查询参数")
public class SparePartsCategoryQueryParam extends PageParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1部件2零件)", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

}
