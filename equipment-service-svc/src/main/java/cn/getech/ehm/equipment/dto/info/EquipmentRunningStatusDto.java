package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备状态
 *
 * <AUTHOR>
 * @date 2020-11-07
 */
@Data
@ApiModel(value = "EquipmentRunningStatusDto", description = "设备状态")
public class EquipmentRunningStatusDto {

    @ApiModelProperty(value = "报警等级/运行状态名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "颜色")
    private String color;

    @ApiModelProperty(value = "状态对应值")
    private Integer value;

}