package cn.getech.ehm.equipment.dto.overview;

import cn.getech.ehm.equipment.enums.HealthStatusType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 健康评估
 */
@Data
@ApiModel(value = "HealthEstimateDto", description = "健康评估")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealthEstimateDto {

    @ApiModelProperty(value = "健康指数")
    private Double healthIndex = 100d;

    @ApiModelProperty(value = "健康状态值")
    private Integer healthStatus = HealthStatusType.NORMAL.getValue();

    @ApiModelProperty(value = "健康状态名称")
    private String healthStatusName;

    @ApiModelProperty(value = "剩余寿命")
    private Integer remainingLife;

    @ApiModelProperty(value = "剩余寿命字符串")
    private String remainingLifeStr;
}