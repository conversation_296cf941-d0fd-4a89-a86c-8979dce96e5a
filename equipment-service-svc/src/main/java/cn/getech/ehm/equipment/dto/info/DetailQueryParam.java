package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 计划设备弹框查询参数
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DetailQueryParam", description = "计划设备弹框查询参数")
public class DetailQueryParam extends PageParam {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "位置Id")
    private String locationId;

    @ApiModelProperty(value = "父级Id")
    private String parentId;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "是否包含类型子集")
    private Boolean containCategoryChild = true;

    @ApiModelProperty(value = "是否包含位置子集")
    private Boolean containLocationChild = true;

    @ApiModelProperty(value = "设备Ids")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "是否权限过滤")
    private Boolean auth = true;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "诊断报告id")
    private String diagnosisReportId;
}
