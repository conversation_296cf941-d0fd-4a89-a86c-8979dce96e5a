package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.warn.*;
import cn.getech.ehm.equipment.dto.WarnAppQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentWarn;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 设备告警 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Repository
public interface EquipmentWarnMapper extends BaseMapper<EquipmentWarn> {

    /**
     * 新增
     * @param equipmentWarns
     * @return
     */
    @SqlParser(filter = true)
    Boolean saveIotBatch(@Param("params") List<EquipmentWarn> equipmentWarns);

    /**
     * 修改
     * @param equipmentWarns
     * @return
     */
    @SqlParser(filter = true)
    Boolean updateIotBatch(@Param("params") List<EquipmentWarn> equipmentWarns);

    /**
     * iot告警单
     * @param page
     * @param equipmentWarnQueryParam
     * @return
     */
    Page<EquipmentWarnDto> pageList(@Param("page") Page<EquipmentWarnDto> page,
                                       @Param("param") EquipmentWarnQueryParam equipmentWarnQueryParam);

    /**
     * 获取告警列表
     * @param equipmentWarnQueryParam
     * @return
     */
    List<EquipmentWarnDto> getList(@Param("param") EquipmentWarnQueryParam equipmentWarnQueryParam);

    /**
     * app告警单
     * @param page
     * @param warnAppQueryParam
     * @return
     */
    Page<EquipmentWarnAppDto> appPageList(@Param("page") Page<EquipmentWarnAppDto> page,
                                          @Param("param") WarnAppQueryParam warnAppQueryParam);

    /**
     * 根据id获取告警
     * @param id
     * @return
     */
    EquipmentWarnAppDto getWarnById(@Param("id") String id);

    /**
     * 查询告警列表
     * @param equipmentId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<EquipmentWarnDto> getWarnListByEquipmentId(String equipmentId, Date beginTime, Date endTime);
}
