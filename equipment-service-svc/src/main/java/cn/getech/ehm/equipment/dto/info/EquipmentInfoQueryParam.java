package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备表 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInfoQueryParam", description = "设备表查询参数")
public class EquipmentInfoQueryParam extends PageParam {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型Ids", hidden = true)
    private List<String> categoryIds;

    @ApiModelProperty(value = "父级类型(1位置11主设备12子设备)")
    private Integer parentType;

    @ApiModelProperty(value = "父级Id")
    private String parentId;

    @ApiModelProperty(value = "父级Ids", hidden = true)
    private List<String> parentIds;

    @ApiModelProperty(value = "位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "投产开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginCompletionTime;

    @ApiModelProperty(value = "投产结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCompletionTime;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "运行状态")
    private List<Integer> runningStatus;

    @ApiModelProperty(value = "0其他1校准单")
    private Integer calibrationType;

    @ApiModelProperty(value = "设备ids")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "是否客户平台(0否1是)")
    private Integer isClient = StaticValue.ZERO;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "是否模板")
    private Boolean template;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "oee统计查询设备")
    private Boolean oeeFlag = false;

    @ApiModelProperty(value = "剩余寿命搜索操作符号值")
    private Integer remainingLifeOperation;

    @ApiModelProperty(value = "剩余寿命搜索操作符号", hidden = true)
    private String remainingLifeOperationName;

    @ApiModelProperty(value = "剩余寿命搜索值(M)")
    private Integer remainingLifeValue;

    @ApiModelProperty(value = "健康状态")
    private List<Integer> healthStatus;

    @ApiModelProperty(value = "报警状态")
    private List<Integer> iotStatus;
}
