package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_category")
public class EquipmentCategory extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 类型编码
     */
    @TableField("code")
    private String code;

    /**
     * 类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 上级节点
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 是否特种设备
     */
    @TableField("special_info")
    private Boolean specialInfo;

    /**
     * 是否需要校验
     */
    @TableField("checked")
    private Boolean checked;

    /**
     * 外观图片附件id列表
     */
    @TableField("pic_ids")
    private String picIds;

    /**
     * 文档附件id列表
     */
    @TableField("doc_ids")
    private String docIds;

    /**
     * 层级编码(每层3位数字)
     */
    @TableField("layer_code")
    private String layerCode;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 是否固定0否1是
     */
    @TableField("fixed")
    private Integer fixed;

    /**
     * 类型0其他1仪器仪表
     */
    @TableField("type")
    private Integer type;

    /**
     * 父编码-子编码
     */
    @TableField("prefix")
    private String prefix;

    /**
     * 校准周期
     */
    @TableField("interval_period")
    private Integer intervalPeriod;

    /**
     * 校准提醒
     */
    @TableField("remind_before")
    private Integer remindBefore;

    /**
     * OEE计算
     */
    @TableField("oee_open")
    private Boolean oeeOpen;

    /**
     * 设备状态开关
     */
    @TableField("status_param_open")
    private Boolean statusParamOpen;
}
