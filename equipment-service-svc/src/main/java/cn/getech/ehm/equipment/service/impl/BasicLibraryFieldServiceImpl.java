package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.BasicLibraryFieldDto;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryField;
import cn.getech.ehm.equipment.mapper.BasicLibraryFieldMapper;
import cn.getech.ehm.equipment.service.IBasicLibraryFieldService;
import cn.getech.ehm.equipment.service.IRunningParameterService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础库字段服务实现类
 */
@Slf4j
@Service
public class BasicLibraryFieldServiceImpl extends BaseServiceImpl<BasicLibraryFieldMapper, BasicLibraryField> implements IBasicLibraryFieldService {

    @Autowired
    private BasicLibraryFieldMapper fieldMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private IRunningParameterService runningParameterService;

    @Override
    public List<BasicLibraryFieldDto> getList(String basicLibraryId, Boolean includeModelFactory, String filterSparePartsId) {
        LambdaQueryWrapper<BasicLibraryField> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BasicLibraryField::getBasicLibraryId, basicLibraryId);
        if(null == includeModelFactory || !includeModelFactory){
            //默认剔除品牌型号
            wrapper.notIn(BasicLibraryField::getFieldName, new String[]{"factoryId", "modelId"});
        }
        if(StringUtils.isNotEmpty(filterSparePartsId)){
            //过滤结构已存在基础参数
            List<String> codes = runningParameterService.getBasicLibraryCodes(filterSparePartsId);
            wrapper.notIn(CollectionUtils.isNotEmpty(codes), BasicLibraryField::getFieldName, codes);
        }
        wrapper.orderByAsc(BasicLibraryField::getSort);
        List<BasicLibraryFieldDto> dtos = CopyDataUtil.copyList(fieldMapper.selectList(wrapper), BasicLibraryFieldDto.class);
        return CollectionUtils.isNotEmpty(dtos) ? dtos : Lists.newArrayList();
    }

    @Override
    public Boolean addField(BasicLibraryFieldDto dto){
        if(checkName(dto.getId(), dto.getBasicLibraryId(), dto.getName())){
            log.error("名称已存在");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        BasicLibraryField basicLibraryField = CopyDataUtil.copyObject(dto, BasicLibraryField.class);
        if(StringUtils.isBlank(dto.getId())){
            basicLibraryField.setSort(fieldMapper.getMaxSort(dto.getBasicLibraryId()) + 1);
        }
        return save(basicLibraryField);
    }

    @Override
    public Boolean editField(List<BasicLibraryFieldDto> dtos) {
        List<BasicLibraryField> basicLibraryFields = CopyDataUtil.copyList(dtos, BasicLibraryField.class);
        return this.updateBatchById(basicLibraryFields);
    }

    private Boolean checkName(String id, String basicLibraryId, String name){
        LambdaQueryWrapper<BasicLibraryField> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(StringUtils.isNotBlank(id), BasicLibraryField::getId, id);
        wrapper.eq(StringUtils.isNotBlank(basicLibraryId), BasicLibraryField::getBasicLibraryId, basicLibraryId);
        wrapper.eq(BasicLibraryField::getName, name);
        return fieldMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<BasicLibraryField> getDefaultFields(){
        return fieldMapper.getDefaultFields();
    }

    @Override
    public Map<String, String> getCodeUnitMap(String basicLibraryId){
        return this.getList(basicLibraryId, false, null).stream().filter(dto -> StringUtils.isNotEmpty(dto.getUnit())).collect(
                Collectors.toMap(BasicLibraryFieldDto::getFieldName, BasicLibraryFieldDto::getUnit)
        );
    }
}
