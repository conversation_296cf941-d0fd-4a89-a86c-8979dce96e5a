package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.equipment.dto.app.SearchItemDto;
import cn.getech.ehm.equipment.dto.app.SearchPageParam;
import cn.getech.ehm.equipment.dto.app.SearchParamDto;
import cn.getech.ehm.equipment.service.IAppIndexService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.param.PageParam;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.SystemClient;
import cn.getech.ehm.system.dto.CustomerDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * app首页信息
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@Slf4j
@Service
public class AppIndexServiceImpl implements IAppIndexService {

    @Autowired
    private SystemClient systemClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;

    @Override
    public List<SearchItemDto> searchInnerEquipment(SearchParamDto searchParamDto){
        String currentUid = PorosContextHolder.getCurrentUser().getUid();

        systemClient.saveSearchHistory(searchParamDto.getKeyword());

        // 查找是否有所属客户
        RestResponse<CustomerDto> response = systemClient.getDtoByUid(currentUid);
        if (!response.isOk()){
            log.error("查询客户信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }


        List<EquipmentListDto> equipmentList = new ArrayList<>();

        if (response.getData() != null){
            if (null == searchParamDto.getType() || searchParamDto.getType() == 5){
                SearchPageParam searchPageParam = new SearchPageParam();
                searchPageParam.setKeyword(searchParamDto.getKeyword());
                searchPageParam.setIsCustomer(true);
                searchPageParam.setCustomerId(response.getData().getId());
                searchPageParam.setType(5);
                equipmentList = equipmentInfoService.searchEquipment(searchPageParam);
            }
        }else{

            // 查询当前用户是否为审核用户
            RestResponse<Boolean> isVisitor = systemClient.checkIsVisitor();
            if (!isVisitor.isOk()){
                log.error("查询用户角色失败");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
}

            // 审核用户没有权限查询
            if (isVisitor.getData()){
                return new ArrayList<>();
            }

            if (null == searchParamDto.getType() || searchParamDto.getType() == 5){
                SearchPageParam searchPageParam = new SearchPageParam();
                searchPageParam.setKeyword(searchParamDto.getKeyword());
                searchPageParam.setIsCustomer(false);
                searchPageParam.setType(5);
                equipmentList = equipmentInfoService.searchEquipment(searchPageParam);
            }else if (null == searchParamDto.getType() || searchParamDto.getType() == 6){
                SearchPageParam searchPageParam = new SearchPageParam();
                searchPageParam.setKeyword(searchParamDto.getKeyword());
                searchPageParam.setIsCustomer(false);
                searchPageParam.setType(6);
                equipmentList = equipmentInfoService.searchEquipment(searchPageParam);
            }
        }
        List<SearchItemDto> itemDtoList = new ArrayList();
        for (int i = 0; i < equipmentList.size(); i++){
            addSearchEquipmentItem(itemDtoList, equipmentList, i);

            if (itemDtoList.size() >= 20){
                break;
            }
        }
        return itemDtoList;
    }

    private void addSearchEquipmentItem(List<SearchItemDto> itemDtoList, List<EquipmentListDto> dtoList, int index){
        if (CollectionUtils.isEmpty(dtoList) || dtoList.size() <= index){
            return;
        }
        EquipmentListDto listDto = dtoList.get(index);
        itemDtoList.add(initSearchItemDto(listDto.getEquipmentId(), listDto.getEquipmentName(), 5));
    }

    private SearchItemDto initSearchItemDto(String id, String name, int type){
        SearchItemDto itemDto = new SearchItemDto();
        itemDto.setId(id);
        itemDto.setName(name);
        itemDto.setType(type);
        return itemDto;
    }


}
