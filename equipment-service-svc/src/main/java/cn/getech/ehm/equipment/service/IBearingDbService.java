package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.bearing.BearingDbDto;
import cn.getech.ehm.equipment.dto.bearing.BearingDbExcelDto;
import cn.getech.ehm.equipment.dto.bearing.BearingDbQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BearingDb;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 轴承服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IBearingDbService extends IBaseService<BearingDb> {
    /**
     * 分页查询，返回Dto
     *
     * @param queryParam
     * @return
     */
    PageResult<BearingDbDto> pageDto(BearingDbQueryParam queryParam);

    /**
     * 根据id获取轴承
     * @param id
     * @return
     */
    BearingDbDto getDtoById(String id);

    /**
     * 保存
     *
     * @param bearingDbDto
     * @return
     */
    boolean saveByParam(BearingDbDto bearingDbDto);

    /**
     * 更新
     *
     * @param bearingDbDto
     */
    boolean updateByParam(BearingDbDto bearingDbDto);

    /**
     * 删除轴承
     * @param id
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 同步mdb数据
     * @return
     */
    boolean addDb();

    /**
     * 导入轴承
     * @param rows
     * @return
     */
    String importExcel(List<BearingDbExcelDto> rows);

    /**
     * 获取轴承导出列表
     * @param queryParam
     * @return
     */
    List<BearingDbExcelDto> getExcelList(BearingDbQueryParam queryParam);

    /**
     * 同步geek下轴承相关数据
     * @return
     */
    Boolean initialization(String tenantId);
}