package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.spareparts.SparePartsAddParam;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsDto;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsEditParam;
import cn.getech.ehm.equipment.service.ISparePartsService;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 设备零件
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/spareParts")
@Api(tags = "设备零件接口")
@Slf4j
public class SparePartsController {
    @Autowired
    private ISparePartsService sparePartsService;

    /**
     * 新增设备零件
     */
    @ApiOperation("新增设备零件")
    @AuditLog(title = "设备零件",desc = "新增设备零件",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:update")
    public RestResponse<String> add(@RequestBody @Valid SparePartsAddParam addParam) {
        return RestResponse.ok(sparePartsService.saveByParam(addParam));
    }

    /**
     * 批量新增设备零件
     */
    @ApiOperation("批量新增设备零件")
    @AuditLog(title = "设备零件",desc = "批量新增设备零件",businessType = BusinessType.INSERT)
    @PostMapping("addBatch")
    //@Permission("equipment:update")
    public RestResponse<Boolean> addBatch(@RequestBody @Valid List<SparePartsAddParam> addParams) {
        return RestResponse.ok(sparePartsService.saveBatchParams(addParams));
    }

    /**
     * 修改设备零件
     */
    @ApiOperation(value="修改设备零件")
    @AuditLog(title = "设备零件",desc = "修改设备零件",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:update")
    public RestResponse<Boolean> update(@RequestBody @Valid SparePartsEditParam editParam) {
        return RestResponse.ok(sparePartsService.updateByParam(editParam));
    }

    /**
     * 根据id删除设备零件
     */
    @ApiOperation(value="根据id删除设备零件")
    @AuditLog(title = "设备零件",desc = "设备零件",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(sparePartsService.deleteById(id));
    }

    /**
     * 根据id获取设备零件
     */
    @ApiOperation(value = "根据id获取设备零件")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    //@Permission("equipment:list")
    public RestResponse<SparePartsDto> get(@PathVariable  String id) {
        return RestResponse.ok(sparePartsService.getDtoById(id));
    }

    /**
     * 根据设备id获取设备零件集合
     */
    @ApiOperation(value = "根据设备id获取设备零件集合")
    @GetMapping(value = "/getListByInfoId")
    @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", paramType = "query")
    public RestResponse<List<SparePartsDto>> getListByInfoId(@RequestParam("equipmentId") String equipmentId) {
        return RestResponse.ok(sparePartsService.getListByInfoId(equipmentId));
    }
}
