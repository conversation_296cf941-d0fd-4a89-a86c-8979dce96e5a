package cn.getech.ehm.equipment.dto.location;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备位置 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentLocationDto", description = "位置返回数据模型")
public class EquipmentLocationDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上级节点Id")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称")
    private String parentName;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "省编码")
    private String province;

    @ApiModelProperty(value = "市编码")
    private String city;

    @ApiModelProperty(value = "区编码")
    private String area;


    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "设备数量")
    private Integer num;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片")
    private String picId;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;
}