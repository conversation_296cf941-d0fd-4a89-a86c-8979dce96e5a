package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.EquipmentSummaryDto;
import cn.getech.ehm.equipment.dto.LocationStatisticsDto;
import cn.getech.ehm.equipment.dto.LocationSummaryDto;
import cn.getech.ehm.equipment.dto.info.EquipmentSortEditParam;
import cn.getech.ehm.equipment.dto.location.*;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.dto.RelSearchDto;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 设备位置控制器
 * <AUTHOR>
 * @since 2020-07-10
 */
@RestController
@RequestMapping("/equipmentLocation")
@Api(tags = "设备位置服务接口")
public class EquipmentLocationController {

    @Autowired
    private IEquipmentLocationService equipmentLocationService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;

    /**
     * 根据父节点获取子节点
     */
    @ApiOperation("根据父节点获取子节点")
    @GetMapping("/node")
    @ApiImplicitParams({
            @ApiImplicitParam(name="parentId",value="父节点id",dataType="string", paramType = "query")
    })
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentLocationTreeDto>> node(@RequestParam(required = false) String parentId){
        return RestResponse.ok(equipmentLocationService.node(parentId));
    }

    /**
     * 获取位置树
     */
    @ApiOperation("位置树")
    @GetMapping("/locationTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name="auth",value="是否校验权限",dataType="boolean", paramType = "query")
    })
    public RestResponse<List<LocationEquipmentTreeDto>> locationTree(@RequestParam(required = false,defaultValue = "true") Boolean auth){
        return RestResponse.ok(equipmentLocationService.locationTree(auth));
    }

    /**
     * 根获取位置-设备树
     */
    @ApiOperation("位置-设备树")
    @GetMapping("/tree")
    public RestResponse<List<LocationEquipmentTreeDto>> tree(){
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        //位置根节点都是0，不同租户生成不同缓存
        return RestResponse.ok(equipmentLocationService.tree(userBaseInfo.getTenantId()));
    }

    /**
     * 根据关键字搜索类别
     */
    @ApiOperation("根据关键字搜索类别")
    @GetMapping("/query")
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentLocationDto>> query(@RequestParam String keyword){
        return RestResponse.ok(equipmentLocationService.queryList(keyword));
    }

    /**
     * 新增设备位置
     */
    @ApiOperation("新增设备位置")
    @AuditLog(title = "设备位置",desc = "新增设备位置",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:location:update")
    public RestResponse<EquipmentLocationDto> add(@RequestBody @Valid EquipmentLocationAddParam addParam) {
        return RestResponse.ok(equipmentLocationService.saveByParam(addParam));
    }

    /**
     * 修改设备位置
     */
    @ApiOperation(value="修改设备位置")
    @AuditLog(title = "设备位置",desc = "修改设备位置",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:location:update")
    public RestResponse<Boolean> update(@RequestBody @Valid EquipmentLocationEditParam editParam) {
        return RestResponse.ok(equipmentLocationService.updateByParam(editParam));
    }

    /**
     * 根据id删除设备位置
     */
    @ApiOperation(value="根据id删除设备位置")
    @AuditLog(title = "设备位置",desc = "设备位置",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:location:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(equipmentLocationService.deleteById(id));
    }

    /**
     * 根据id获取设备位置
     */
    @ApiOperation(value = "根据id获取设备位置")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    //@Permission("equipment:location:list")
    public RestResponse<EquipmentLocationDto> get(@PathVariable @NotBlank  String id) {
        return RestResponse.ok(equipmentLocationService.getDtoById(id));
    }

    /**
     * 获取设备位置map
     */
    @ApiOperation("获取设备位置map")
    @PostMapping("/getLocationStatistics")
    public RestResponse<Map<String, LocationStatisticsDto>> getLocationStatistics(@RequestBody String[] locationIds){
        return RestResponse.ok(equipmentLocationService.getLocationStatistics(locationIds));
    }

    /**
     * 根据位置ID获取设备列表
     */
    @ApiOperation("根据位置ID获取设备列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "locationId", value = "位置ID", dataType = "String")})
    @GetMapping("/getEquipmentsByLocationId")
    public RestResponse<List<EquipmentSummaryDto>> getEquipmentsByLocationId(@RequestParam(value = "locationId", required = false) String locationId,
                                                                             @RequestParam(value = "equipmentName", required = false) String keyword){
        return RestResponse.ok(equipmentInfoService.findByLocationId(locationId, keyword));
    }

    /**
     * Excel导入位置(单层级形式)
     */
    @ApiOperation(value = "Excel导入位置")
    @AuditLog(title = "位置",desc = "Excel导入位置",businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("fault:category:import")
    public RestResponse<Boolean> excelImport(@RequestPart("file") MultipartFile file){

        ExcelUtils<EquipmentLocationExcel> util = new ExcelUtils<>(EquipmentLocationExcel.class);
        List<EquipmentLocationExcel> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)){
            return RestResponse.failed();
        }
        return RestResponse.ok(equipmentLocationService.excelImport(rows));
    }

    /**
     * Excel导入位置(父节点/子节点格式)
     */
    @ApiOperation(value = "Excel导入位置")
    @AuditLog(title = "位置",desc = "Excel导入位置",businessType = BusinessType.INSERT)
    @PostMapping("/importCW")
    //@Permission("fault:category:import")
    public RestResponse<Boolean> importCW(@RequestPart("file") MultipartFile file){

        ExcelUtils<LocationCWExcel> util = new ExcelUtils<>(LocationCWExcel.class);
        List<LocationCWExcel> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)){
            return RestResponse.failed();
        }
        return RestResponse.ok(equipmentLocationService.excelImportCW(rows));
    }

    /**
     * 根据设备ids获取对应位置id及其父节点
     * @param relSearchDto
     * @return
     */
    @PostMapping("/getLocationIdByInfoIds")
    @ApiOperation(value = "根据设备ids获取对应位置id及其父节点")
    RestResponse<List<String>> getLocationIdByInfoIds(@RequestBody RelSearchDto relSearchDto){
        return RestResponse.ok(equipmentLocationService.getLocationIdByInfoIds(relSearchDto));
    }

    /**
     * 根据id集合获取设备位置名称集合
     */
    @ApiOperation("根据id集合获取设备位置名称集合")
    @PostMapping("/getMapByIds")
    public RestResponse<Map<String, String>> getMapByIds(@RequestBody String[] locationIds){
        return RestResponse.ok(equipmentLocationService.getMapByIds(locationIds));
    }

    @PostMapping("/update/sort")
    @ApiOperation("更新排序")
    public RestResponse updateSort(@RequestBody List<EquipmentSortEditParam> param){
        equipmentLocationService.updateSort(param);
        return RestResponse.ok();
    }

    @PostMapping("/getMapByLocationIds")
    @ApiOperation("获取位置信息")
    public RestResponse<Map<String, LocationSummaryDto>> getMapByLocationIds(@RequestBody String[] locationIds){
        return RestResponse.ok(equipmentLocationService.getMapByLocationIds(locationIds));
    }
}
