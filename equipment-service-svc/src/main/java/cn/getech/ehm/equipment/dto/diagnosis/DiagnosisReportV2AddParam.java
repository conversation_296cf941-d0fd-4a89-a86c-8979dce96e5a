package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 诊断报告V2新增参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportV2AddParam", description = "诊断报告V2新增参数")
public class DiagnosisReportV2AddParam extends ApiParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备ids")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "描述")
    private String content;
}