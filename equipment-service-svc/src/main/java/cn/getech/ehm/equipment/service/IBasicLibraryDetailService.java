package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.bearing.BasicLibraryDetailDto;
import cn.getech.ehm.equipment.dto.bearing.BasicLibraryQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryDetail;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 基础库详情服务类
 */
public interface IBasicLibraryDetailService extends IBaseService<BasicLibraryDetail> {

    /**
     * 基础库详情列表
     * @param queryParam 查询参数
     * @return 基础库详情列表
     */
    PageResult<BasicLibraryDetailDto> detailList(BasicLibraryQueryParam queryParam);

    /**
     * 编辑基础库详情
     * @param dto 基础库详情
     * @return 是否成功
     */
    Boolean editDetail(Object dto);

    /**
     * 删除基础库详情
     * @param id 基础库详情id
     * @return 是否成功
     */
    Boolean deleteDetail(String id);

    /**
     * 同步轴承db
     * @return
     */
    Boolean addBearingDb();

    /**
     * 获取geek下默认基础库详情
     * @return
     */
    List<BasicLibraryDetail> getDefaultDetails();

    /**
     * 获取详情数据code/value键值对
     * @return
     */
    Map<String, Object> getDetailMap(String id);
}