package cn.getech.ehm.equipment.handler;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.equipment.dto.activiti.EquipmentTaskAuditParam;
import cn.getech.ehm.equipment.dto.activiti.StartActivitiServiceResult;
import cn.getech.poros.bpm.client.ProcessServiceClient;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.dto.task.ProcessTaskDTO;
import cn.getech.poros.bpm.param.process.ProcessStartParam;
import cn.getech.poros.bpm.param.task.ProcessTaskParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskOptionParam;
import cn.getech.poros.bpm.param.task.TaskRejectParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class ActivitiHandler {
    @Autowired
    private TaskServiceClient taskServiceClient;
    @Autowired
    private ProcessServiceClient processServiceClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;


    @Transactional
    public StartActivitiServiceResult startProcess(Map<String, Object> variables, String changeCode, String changeName) {
        StartActivitiServiceResult startActivitiServiceResult = new StartActivitiServiceResult();
        ProcessStartParam processStartParam = new ProcessStartParam();
        processStartParam.setProcessDefinitionKey(changeCode);
        processStartParam.setProcessInstanceName(changeName);
        processStartParam.setLevel(StaticValue.ONE);
        processStartParam.setSetAssignees(variables);
        RestResponse<String> restResponse = processServiceClient.startProcess(processStartParam);
        log.info("流程引擎请求参数:{}, 返回参数:{}", JSONUtil.toJsonStr(processStartParam), JSONUtil.toJsonStr(restResponse));
        if (restResponse.isOk()) {
            String processInstanceId = restResponse.getData();
            startActivitiServiceResult.setProcessInstanceId(processInstanceId);
            AuditActivitiServiceResult auditActivitiServiceResult = this.getProcessTaskUserId(processInstanceId);
            startActivitiServiceResult.setProcessUser(auditActivitiServiceResult.getProcessUser());
            startActivitiServiceResult.setTaskId(auditActivitiServiceResult.getTaskId());
            return startActivitiServiceResult;
        }  else {
            log.error("流程引擎调用失败, " + restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Transactional
    public AuditActivitiServiceResult submitActivitiPass(EquipmentTaskAuditParam auditParam) {
        // 同意
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(auditParam.getActivityId());
        Map<String, Object> variables = Maps.newHashMap();
        variables.putAll(auditParam.getVariables() != null ? auditParam.getVariables() : Maps.newHashMap());
        variables.put("result", true);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(auditParam.getComment());
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk()) {
            log.error("流程引擎调用失败, " + completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        return this.getProcessTaskUserId(auditParam.getProcessInstanceId());
    }

    @Transactional
    public AuditActivitiServiceResult rejectActiviti(EquipmentTaskAuditParam auditParam) {
        // 驳回
        // 发起任务驳回流程
        TaskRejectParam taskRejectParam = new TaskRejectParam();
        taskRejectParam.setTaskId(auditParam.getActivityId());
        taskRejectParam.setVariables(auditParam.getVariables());
        taskRejectParam.setComment(auditParam.getComment());
        RestResponse<Object> rejectTask = taskServiceClient.rejectTask(taskRejectParam);
        log.info("流程节点驳回任务提交参数{}响应{}", JSON.toJSONString(taskRejectParam), JSON.toJSONString(rejectTask));
        if (!rejectTask.isOk()) {
            log.error("流程引擎调用失败, " + rejectTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        return this.getProcessTaskUserId(auditParam.getProcessInstanceId());
    }

    public AuditActivitiServiceResult getProcessTaskUserId(String processInstanceId) {
        ProcessTaskParam processTaskParam = new ProcessTaskParam();
        processTaskParam.setProcessInstanceId(processInstanceId);
        RestResponse<List<ProcessTaskDTO>> response = taskServiceClient.getUserTaskByProcessId(processTaskParam);
        if (!response.isOk()) {
            log.error("流程引擎调用失败, " + response.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        log.info("审批人列表：{}, 当前用户：{}", response.getData(), PorosContextHolder.getCurrentUser().getUid());
        //请求是成功的
        if (response.isOk()) {
            if (CollectionUtils.isNotEmpty(response.getData())) {
                return AuditActivitiServiceResult.builder()
                        .processUser(JSONUtil.toJsonStr(response.getData().get(0).getCandidateUids()))
                        .taskId(response.getData().get(0).getTaskId())
                        .build();
            } else {
                //没有后续任务
                return AuditActivitiServiceResult.builder()
                        .processUser("")
                        .taskId("")
                        .build();
            }
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("获取下一节点审批人失败,请尝试重新审批"));
        }
    }

    public void deleteByProcessId(String processId) {
        AuditActivitiServiceResult processTaskUserId = this.getProcessTaskUserId(processId);
        String taskId = "";
        if (processTaskUserId != null && StringUtils.isNotEmpty(processTaskUserId.getTaskId())) {
            taskId = processTaskUserId.getTaskId();
        }
        if (StringUtils.isEmpty(taskId)) {
            log.error("删除工作流失败：taskId为空");
            return;
        }
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskId);
        taskOptionParam.setComment("删除任务");
        RestResponse<Object> objectRestResponse = taskServiceClient.trashTask(taskOptionParam);
        if (objectRestResponse.isOk()) {
            log.info("删除工作流成功：{}", JSON.toJSONString(objectRestResponse.getData()));
        } else {
            log.error("删除工作流失败：{}", JSON.toJSONString(objectRestResponse));
        }
    }
}
