package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.bearing.BearingDbDto;
import cn.getech.ehm.equipment.dto.bearing.BearingDbExcelDto;
import cn.getech.ehm.equipment.dto.bearing.BearingDbQueryParam;
import cn.getech.ehm.equipment.dto.bearing.MdbBearingDbDto;
import cn.getech.ehm.equipment.entity.bearing.BearingDb;
import cn.getech.ehm.equipment.entity.bearing.BearingFactory;
import cn.getech.ehm.equipment.entity.bearing.BearingModel;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 轴承Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface BearingDbMapper  extends BaseMapper<BearingDb> {
    /**
     * 分页查询，返回Dto
     * @param param
     * @return
     */
    IPage<BearingDbDto> pageList(Page<BearingDbQueryParam> page,
                                 @Param("param") BearingDbQueryParam param);

    /**
     * 根据id获取轴承
     * @param id
     * @return
     */
    BearingDbDto getDtoById(@Param("id") String id);

    /**
     * 获取mdb轴承数据
     * @return
     */
    List<MdbBearingDbDto> mdbBearingList();

    /**
     * 获取轴承导出列表
     * @param queryParam
     * @return
     */
    List<BearingDbExcelDto> getExcelList(@Param("param") BearingDbQueryParam queryParam);

    /**
     * 获取默认geek下轴承库
     * @return
     */
    @SqlParser(filter = true)
    List<BearingDb> getDefaultDb();

    /**
     * 获取默认geek下轴承型号库
     * @return
     */
    @SqlParser(filter = true)
    List<BearingModel> getDefaultModel();

    /**
     * 获取默认geek下轴承轴承生产厂家(品牌)库
     * @return
     */
    @SqlParser(filter = true)
    List<BearingFactory> getDefaultFactory();
}
