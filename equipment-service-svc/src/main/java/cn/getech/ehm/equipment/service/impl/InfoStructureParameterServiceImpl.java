package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.info.EquipmentStructureParameterDto;
import cn.getech.ehm.equipment.dto.info.ParameterVariableValueDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterResDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterVariableDto;
import cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto;
import cn.getech.ehm.equipment.enmu.StructureParamType;
import cn.getech.ehm.equipment.enmu.StructureSourceType;
import cn.getech.ehm.equipment.entity.EquipmentStructureParameter;
import cn.getech.ehm.equipment.entity.EquipmentStructureVariable;
import cn.getech.ehm.equipment.entity.RunningParameter;
import cn.getech.ehm.equipment.mapper.EquipmentStructureParameterMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.ReflectUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 零部件运行参数 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class InfoStructureParameterServiceImpl extends BaseServiceImpl<EquipmentStructureParameterMapper, EquipmentStructureParameter> implements IInfoStructureParameterService {

    @Autowired
    private EquipmentStructureParameterMapper structureParameterMapper;
    @Autowired
    private IInfoStructureVariableService variableService;
    @Autowired
    private IRunningParameterService runningParameterService;
    @Autowired
    private IRunningParameterVariableService runningParameterVariableService;
    @Autowired
    private IBasicLibraryDetailService detailService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByCategory(String structureId, String sparePartsCategoryId, String relationId) {
        Map<String, Object> basicLibraryDetailMap = detailService.getDetailMap(relationId);
        List<RunningParameter> runningParameters = runningParameterService.getEntitiesByCategoryId(sparePartsCategoryId);
        if(CollectionUtils.isNotEmpty(runningParameters)){
            List<EquipmentStructureParameter> equipmentStructureParameters = new ArrayList<>(runningParameters.size());
            List<EquipmentStructureVariable> equipmentStructureVariables = new ArrayList<>();

            //类型为计算的运行参数
            List<String> runningParameterIds = runningParameters.stream().filter(dto -> dto.getSourceType() == StructureSourceType.COUNT.getValue())
                    .map(RunningParameter::getId).distinct().collect(Collectors.toList());
            Map<String, List<ParameterVariableDto>> variableMap = runningParameterVariableService.getList(runningParameterIds).stream().collect(Collectors.groupingBy(ParameterVariableDto::getRunningParameterId));

            //运行参数id与部件参数id映射
            Map<String, String> idMap = new HashMap<>();
            for(RunningParameter runningParameter : runningParameters){
                //首先给参数设置对应的新id
                String id = UUID.randomUUID().toString().replace("-","");
                idMap.put(runningParameter.getId(), id);
            }

            for(RunningParameter runningParameter : runningParameters){
                EquipmentStructureParameter equipmentStructureParameter = CopyDataUtil.copyObject(runningParameter, EquipmentStructureParameter.class);
                String newId = idMap.get(runningParameter.getId());
                equipmentStructureParameter.setId(newId);
                equipmentStructureParameter.setStructureId(structureId);
                equipmentStructureParameter.setRunningParameterId(runningParameter.getId());
                if(StringUtils.isNotBlank(relationId) && equipmentStructureParameter.getParamType() == StructureParamType.BASIC.getValue()) {
                    //绑定基础库的，对应库里面记录数据
                    try{
                        Object obj = basicLibraryDetailMap.get(equipmentStructureParameter.getCode());
                        equipmentStructureParameter.setValue(new BigDecimal(obj.toString()));
                    }catch (Exception e){
                        log.error(equipmentStructureParameter.getCode() + "基础库绑定失败", e);
                        equipmentStructureParameter.setValue(new BigDecimal(0));
                    }
                }
                equipmentStructureParameters.add(equipmentStructureParameter);
                List<ParameterVariableDto> parameterVariableDtos = variableMap.get(runningParameter.getId());
                if(CollectionUtils.isNotEmpty(parameterVariableDtos)){
                    for(ParameterVariableDto parameterVariableDto : parameterVariableDtos){
                        EquipmentStructureVariable equipmentStructureVariable = CopyDataUtil.copyObject(parameterVariableDto, EquipmentStructureVariable.class);
                        equipmentStructureVariable.setId(null);
                        equipmentStructureVariable.setStructureParameterId(newId);
                        if(parameterVariableDto.getSourceType() == 2){
                            //引用参数类型，需要修改绑定id
                            equipmentStructureVariable.setValue(idMap.get(parameterVariableDto.getValue()));
                        }
                        equipmentStructureVariables.add(equipmentStructureVariable);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(equipmentStructureParameters)){
                saveBatch(equipmentStructureParameters);
            }
            if(CollectionUtils.isNotEmpty(equipmentStructureVariables)){
                variableService.saveBatch(equipmentStructureVariables);
            }
        }
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteByStructureIds(List<String> structureIds){
        LambdaQueryWrapper<EquipmentStructureParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructureParameter::getStructureId, structureIds);
        wrapper.select(EquipmentStructureParameter::getId);
        List<String> ids = structureParameterMapper.selectList(wrapper).stream().map(EquipmentStructureParameter::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ids)){
            structureParameterMapper.deleteBatchIds(ids);
            variableService.deleteByStructureParamIds(ids);
        }
        return true;
    }

    @Override
    public Boolean updateBaseParam(String structureId, String relationId) {
        Map<String, Object> basicLibraryDetailMap = detailService.getDetailMap(relationId);
        LambdaQueryWrapper<EquipmentStructureParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructureParameter::getStructureId, structureId);
        wrapper.eq(EquipmentStructureParameter::getParamType, StructureParamType.BASIC.getValue());
        List<EquipmentStructureParameter> baseParams = structureParameterMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(baseParams)){
            for (EquipmentStructureParameter baseParam : baseParams) {
                //绑定基础库的，对应库里面记录数据
                try{
                    Object obj = basicLibraryDetailMap.get(baseParam.getCode());
                    baseParam.setValue(new BigDecimal(obj.toString()));
                }catch (Exception e){
                    log.error(baseParam.getCode() + "基础库绑定失败", e);
                    baseParam.setValue(new BigDecimal(0));
                }
            }
            return updateBatchById(baseParams);
        }
        return true;
    }

    @Override
    public List<String> hasParamStructureIds(List<String> structureIds){
        LambdaQueryWrapper<EquipmentStructureParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructureParameter::getStructureId, structureIds);
        wrapper.select(EquipmentStructureParameter::getStructureId);
        return structureParameterMapper.selectList(wrapper).stream().map(EquipmentStructureParameter::getStructureId).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, List<EquipmentStructureParameterDto>> getStructureParameterMap(List<String> structureIds, Integer[] paramTypes){
        return  structureParameterMapper.getList(structureIds, paramTypes).stream().collect(Collectors.groupingBy(EquipmentStructureParameterDto::getStructureId));
    }

    @Override
    public StructureParameterResDto getStructureParameter(String structureId){
        StructureParameterResDto resDto = new StructureParameterResDto();
        List<String> structureIds = new ArrayList<>();
        structureIds.add(structureId);
        List<EquipmentStructureParameterDto> parameterDtos = structureParameterMapper.getList(structureIds, null);
        if(CollectionUtils.isNotEmpty(parameterDtos)){
            List<String> structureParameterIds = parameterDtos.stream().filter(dto -> dto.getSourceType() == StructureSourceType.COUNT.getValue()).map(EquipmentStructureParameterDto::getId).distinct().collect(Collectors.toList());
            Map<String, List<StructureParameterVariableDto>> variableMap = variableService.getMap(structureParameterIds);
            List<EquipmentStructureParameterDto> baseParams = new ArrayList<>();
            List<EquipmentStructureParameterDto> inputParams = new ArrayList<>();
            List<EquipmentStructureParameterDto> countParams = new ArrayList<>();
            for(EquipmentStructureParameterDto parameterDto : parameterDtos){
                if(parameterDto.getParamType() == StructureParamType.BASIC.getValue()){
                    baseParams.add(parameterDto);
                }else{
                    if(parameterDto.getSourceType() == StructureSourceType.COUNT.getValue()){
                        parameterDto.setVariables(variableMap.get(parameterDto.getId()));
                        countParams.add(parameterDto);
                    }else{
                        inputParams.add(parameterDto);
                    }
                }
            }
            resDto.setBaseParams(CollectionUtils.isNotEmpty(baseParams) ? baseParams : null);
            resDto.setInputParams(CollectionUtils.isNotEmpty(inputParams) ? inputParams : null);
            resDto.setCountParams(CollectionUtils.isNotEmpty(countParams) ? countParams : null);
        }
        return resDto;
    }

    @Override
    public Boolean updateStructureParameter(StructureParameterResDto dto){
        List<EquipmentStructureParameter> parameters = new ArrayList<>();
        List<EquipmentStructureVariable> variables = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dto.getInputParams())){
            for(EquipmentStructureParameterDto inputParam : dto.getInputParams()){
                EquipmentStructureParameter parameter = new EquipmentStructureParameter();
                parameter.setId(inputParam.getId());
                parameter.setValue(inputParam.getValue());
                parameters.add(parameter);
            }
        }
        if(CollectionUtils.isNotEmpty(dto.getCountParams())){
            for(EquipmentStructureParameterDto countParam : dto.getCountParams()){
                EquipmentStructureParameter parameter = new EquipmentStructureParameter();
                parameter.setId(countParam.getId());
                parameter.setValue(countParam.getValue());
                //parameter.setExpression(countParam.getExpression());
                parameters.add(parameter);
                for(StructureParameterVariableDto variableDto : countParam.getVariables()){
                    EquipmentStructureVariable variable = new EquipmentStructureVariable();
                    variable.setId(variableDto.getId());
                    variable.setValue(variableDto.getValue());
                    variables.add(variable);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(parameters)){
            updateBatchById(parameters);
        }
        if(CollectionUtils.isNotEmpty(variables)){
            variableService.updateBatchById(variables);
        }
        return true;
    }

    @Override
    public BigDecimal countStructureParameter(String structureParameterId){
        LambdaQueryWrapper<EquipmentStructureParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructureParameter::getId, structureParameterId);
        wrapper.select(EquipmentStructureParameter::getId, EquipmentStructureParameter::getExpression);

        EquipmentStructureParameter equipmentStructureParameter = structureParameterMapper.selectOne(wrapper);
        if(null != equipmentStructureParameter){
            List<ParameterVariableValueDto> variableValueDtos = variableService.getVariableValue(equipmentStructureParameter.getId());
            if(CollectionUtils.isEmpty(variableValueDtos)){
                return null;
            }
            try {
                List<Double> variableValues = new ArrayList<>();
                List<String> paramNames = new ArrayList<>();
                for(ParameterVariableValueDto valueDto : variableValueDtos){
                    Double value = 0d;
                    if(valueDto.getSourceType() == 1){
                        if(StringUtils.isBlank(valueDto.getValue())){
                            return null;
                        }
                        value = Double.valueOf(valueDto.getValue());
                    }else if(valueDto.getSourceType() == 2){
                        if(StringUtils.isBlank(valueDto.getParamValue())){
                            return null;
                        }
                        value = Double.valueOf(valueDto.getParamValue());
                    }
                    variableValues.add(value);
                    paramNames.add(valueDto.getName());
                }
                String functionName = "runningParamCalc";
                String functionExpression = "function " + functionName + StringPool.LEFT_BRACKET + String.join(StringPool.COMMA, paramNames) +
                        StringPool.RIGHT_BRACKET + StringPool.LEFT_BRACE + "return " + equipmentStructureParameter.getExpression() + StringPool.SEMICOLON + StringPool.RIGHT_BRACE;
                ScriptEngine scriptEngine = new ScriptEngineManager().getEngineByName("nashorn");
                scriptEngine.eval(functionExpression);
                Invocable invocable = (Invocable) scriptEngine;
                BigDecimal countValue = new BigDecimal(invocable.invokeFunction(functionName, variableValues.toArray()).toString()).setScale(2, BigDecimal.ROUND_HALF_UP);

                //计算出结果直接更新
                EquipmentStructureParameter parameter = new EquipmentStructureParameter();
                parameter.setId(structureParameterId);
                parameter.setValue(countValue);
                updateById(parameter);
                return countValue;
            }catch (Exception e){
                log.error("脚本有误");
                log.error(e.getMessage());
            }
        }
        return null;
    }
}
