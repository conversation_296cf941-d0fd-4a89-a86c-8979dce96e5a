package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 设备表
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_info")
public class EquipmentInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备类型id
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 设备位置id(子设备继承最顶级主设备)
     */
    @TableField("location_id")
    private String locationId;

    /**
     * 类型(1主设备2子设备)
     */
    @TableField("type")
    private Integer type;

    /**
     * 上级节点(设备位置/上级设备);主设备父级为位置id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 层级编码
     */
    @TableField("layer_code")
    private String layerCode;

    /**
     * 设备位号
     */
    @TableField("item_no")
    private String itemNo;

    /**
     * 规格型号
     */
    @TableField("specification")
    private String specification;

    /**
     * 投产日期
     */
    @TableField("completion_time")
    private Date completionTime;

    /**
     * 重要程度
     */
    @TableField("importance")
    private String importance;

    /**
     * 设备品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 是否特种设备
     */
    @TableField("special_info")
    private Boolean specialInfo;

    /**
     * 是否需要校验
     */
    @TableField("checked")
    private Boolean checked;

    /**
     * 校验日期
     */
    @TableField("check_date")
    private Date checkDate;

    /**
     * 校验人员
     */
    @TableField("check_user")
    private String checkUser;

    /**
     * 校验状态
     */
    @TableField("check_status")
    private String checkStatus;

    /**
     * 是否联网
     */
    @TableField("networked")
    private Boolean networked;

    /**
     * 是否监控
     */
    @TableField("monitored")
    private Boolean monitored;

    /**
     * 运行状态
     */
    @TableField("running_status")
    private Integer runningStatus;

    /**
     * 健康状态
     */
    @TableField("health_status")
    private Integer healthStatus;

    /**
     * 制造商
     */
    @TableField("manufacturer_id")
    private String manufacturerId;

    /**
     * 供应商
     */
    @TableField("supplier_id")
    private String supplierId;

    /**
     * 设计单位
     */
    @TableField("design_company_id")
    private String designCompanyId;

    /**
     * 安装单位
     */
    @TableField("install_company_id")
    private String installCompanyId;

    /**
     * 出厂编码
     */
    @TableField("factory_code")
    private String factoryCode;

    /**
     * 出厂日期
     */
    @TableField("production_time")
    private Date productionTime;

    /**
     * 设计年限
     */
    @TableField("design_period")
    private Date designPeriod;

    /**
     * 保修年限
     */
    @TableField("warranty_period")
    private Date warrantyPeriod;

    /**
     * 资产编码
     */
    @TableField("asset_code")
    private String assetCode;

    /**
     * 资产状态
     */
    @TableField("asset_status")
    private String assetStatus;

    /**
     * 成本中心
     */
    @TableField("cost_center")
    private String costCenter;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 资产原值
     */
    @TableField("original_asset")
    private Double originalAsset;

    /**
     * 资产净值
     */
    @TableField("net_asset")
    private Double netAsset;

    /**
     * 管理部门
     */
    @TableField("manage_depart")
    private String manageDepart;

    /**
     * 管理责任人
     */
    @TableField("manage_principal")
    private String managePrincipal;

    /**
     * 使用部门
     */
    @TableField("use_depart")
    private String useDepart;

    /**
     * 使用责任人
     */
    @TableField("use_principal")
    private String usePrincipal;

    /**
     * 维保责任人
     */
    @TableField(value = "maintainer_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] maintainerIds;

    /**
     * 维保班组
     */
    @TableField(value = "team_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    /**
     * 图片id
     */
    @TableField("pic_id")
    private String picId;

    /**
     * 文档附件id列表
     */
    @TableField("doc_ids")
    private String docIds;

    /**
     * 扩展字段X
     */
    @TableField("alternate_field_x")
    private String alternateFieldX;

    /**
     * 扩展字段Y
     */
    @TableField("alternate_field_y")
    private String alternateFieldY;

    /**
     * 设备备件
     * 默认0，取设备类型里备件
     * 设备自身新增备件之后更新为1
     */
    @TableField("part_init")
    private Integer partInit;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 校准单id
     */
    @TableField("calibration_id")
    private String calibrationId;

    /**
     * PC组态id
     */
    @TableField("pc_studio_id")
    private String pcStudioId;

    /**
     * APP组态id
     */
    @TableField("app_studio_id")
    private String appStudioId;

    /**
     * PC组态URL
     */
    @TableField("pc_studio_url")
    private String pcStudioUrl;

    /**
     * APP组态URL
     */
    @TableField("app_studio_url")
    private String appStudioUrl;

    /**
     * 设备更多信息
     */
    @TableField(exist = false)
    private String property;

    /**
     * 维护人员姓名列表
     */
    @TableField(exist = false)
    private String maintainerNames;

    /**
     * 维护班组名称列表
     */
    @TableField(exist = false)
    private String maintTeamNames;

    /**
     * 最大转速
     */
    @TableField("max_rate")
    private Integer maxRate;

    /**
     * 最小转速
     */
    @TableField("min_rate")
    private Integer minRate;

    /**
     * 根据iot参数状态汇总的状态
     * -1停机0正常 其余为报警等级值
     */
    @TableField("iot_status")
    private Integer iotStatus;

    /**
     * 参数是否停机
     */
    @TableField("param_stop_enable")
    private Boolean paramStopEnable;

    private Integer sort;
    //标签tagId
    private String positionTagId;

    /**
     * 健康指数
     */
    @TableField("health_index")
    private Double healthIndex;

    /**
     * 健康指数更新时间
     */
    @TableField("health_index_update_time")
    private Date healthIndexUpdateTime;

    /**
     * 剩余寿命
     */
    @TableField("remaining_life")
    private Integer remainingLife;

    /**
     * 剩余寿命更新时间
     */
    @TableField("remaining_life_update_time")
    private Date remainingLifeUpdateTime;

}
