package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryDto;
import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryQueryParam;
import cn.getech.ehm.equipment.entity.SparePartsCategory;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 零部件类型 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface ISparePartsCategoryService extends IBaseService<SparePartsCategory> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<SparePartsCategoryDto> pageDto(SparePartsCategoryQueryParam queryParam);

    /**
     * 获取列表
     * @param queryParam
     * @return
     */
    List<SparePartsCategoryDto> getList(SparePartsCategoryQueryParam queryParam);

    /**
     * 保存
     * @return
     */
    boolean saveByParam(SparePartsCategoryDto dto);

    /**
     * 更新
     *
     */
    boolean updateByParam(SparePartsCategoryDto dto);

    /**
     * 复制
     */
    boolean copy(String id, String name);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    SparePartsCategoryDto getDtoById(String id);

}