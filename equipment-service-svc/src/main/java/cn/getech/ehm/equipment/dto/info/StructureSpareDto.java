package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部件零件属性
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "StructureSpareDto", description = "部件零件属性")
public class StructureSpareDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "部件id")
    private String structureId;

    @ApiModelProperty(value = "传动比")
    private String proportion;

    @ApiModelProperty(value = "最大转速")
    private Double maxSpeed;

    @ApiModelProperty(value = "最小转速")
    private Double minSpeed;

}