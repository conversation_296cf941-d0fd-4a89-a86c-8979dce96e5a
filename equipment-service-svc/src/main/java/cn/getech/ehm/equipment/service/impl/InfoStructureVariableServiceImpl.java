package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.equipment.dto.info.ParameterVariableValueDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterVariableDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureVariable;
import cn.getech.ehm.equipment.mapper.EquipmentStructureVariableMapper;
import cn.getech.ehm.equipment.service.IInfoStructureVariableService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备零部件运行参数变量 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class InfoStructureVariableServiceImpl extends BaseServiceImpl<EquipmentStructureVariableMapper, EquipmentStructureVariable> implements IInfoStructureVariableService {

    @Autowired
    private EquipmentStructureVariableMapper variableMapper;

    @Override
    public Map<String, List<StructureParameterVariableDto>> getMap(List<String> structureParameterIds) {
        if(CollectionUtils.isEmpty(structureParameterIds)){
            return Collections.emptyMap();
        }
        return variableMapper.getList(structureParameterIds).stream().collect(Collectors.groupingBy(StructureParameterVariableDto::getStructureParameterId));
    }

    @Override
    public Boolean deleteByStructureParamIds(List<String> structureParameterIds) {
        LambdaQueryWrapper<EquipmentStructureVariable> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructureVariable::getStructureParameterId, structureParameterIds);
        return variableMapper.delete(wrapper) > 0;
    }

    @Override
    public List<ParameterVariableValueDto> getVariableValue(String structureParameterId){
        return variableMapper.getVariableValue(structureParameterId);
    }

}
