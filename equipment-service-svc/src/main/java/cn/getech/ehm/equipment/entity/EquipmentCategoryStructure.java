package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型部件
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_category_structure")
public class EquipmentCategoryStructure extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 类型id
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 零部件id
     */
    @TableField("spare_parts_category_id")
    private String sparePartsCategoryId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 父节点id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 类型(1部件2零件)
     */
    @TableField("type")
    private Integer type;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private String partId;
}
