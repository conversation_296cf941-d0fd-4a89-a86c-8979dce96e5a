package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 诊断报告查询参数
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportQueryParam", description = "诊断报告查询参数")
public class DiagnosisReportQueryParam extends PageParam {

    @ApiModelProperty(value = "名称/编号")
    private String nameOrCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "左侧设备树节点id")
    private String treeId;

}
