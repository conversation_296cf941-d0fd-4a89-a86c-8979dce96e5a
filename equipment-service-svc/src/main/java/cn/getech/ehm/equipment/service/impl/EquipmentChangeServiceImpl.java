package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.ManufacturerSupplierDto;
import cn.getech.ehm.base.dto.TaskAuditParam;
import cn.getech.ehm.common.enums.AuditType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeDto;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeEditParam;
import cn.getech.ehm.equipment.entity.EquipmentChange;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.mapper.EquipmentChangeMapper;
import cn.getech.ehm.equipment.service.IEquipmentChangeService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.poros.bpm.client.ProcessServiceClient;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.param.process.ProcessStartParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskRejectParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeAddParam;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 设备异动表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Slf4j
@Service
public class EquipmentChangeServiceImpl extends BaseServiceImpl<EquipmentChangeMapper, EquipmentChange> implements IEquipmentChangeService {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private EquipmentChangeMapper equipmentChangeMapper;
    @Autowired
    private IEquipmentLocationService equipmentLocationService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private ProcessServiceClient processServiceClient;
    @Autowired
    private TaskServiceClient taskServiceClient;

    @Value("${flow.change.code:E0003}")
    private String changeCode;
    @Value("${flow.change.name:资源异动}")
    private String changeName;

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchByParam(EquipmentChangeAddParam equipmentChangeAddParam) {
        ProcessStartParam processStartParam = new ProcessStartParam();
        processStartParam.setProcessDefinitionKey(changeCode);
        processStartParam.setProcessInstanceName(changeName);
        processStartParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        Map<String, Object> variables = new HashMap<>(1);
        List<String> assigns = new ArrayList<>();
        assigns.add(equipmentChangeAddParam.getDutyId());
        variables.put("assigns", assigns);
        processStartParam.setVariables(variables);
        processStartParam.setLevel(StaticValue.ONE);
        RestResponse<String> restResponse = processServiceClient.startProcess(processStartParam);
        //     if(log.isDebugEnabled()){
        log.info("启动设备异动流程参数{}响应{}", JSON.toJSONString(processStartParam), JSON.toJSONString(restResponse));
        //    }
        if(restResponse.isOk()){
            EquipmentChange equipmentChange = new EquipmentChange();
            equipmentChange.setEquipmentIds(equipmentChangeAddParam.getEquipmentIds());
            equipmentChange.setType(equipmentChangeAddParam.getType());
            equipmentChange.setChangeTime(equipmentChangeAddParam.getChangeTime());
            equipmentChange.setType(equipmentChange.getType());
            equipmentChange.setLocationId(equipmentChangeAddParam.getLocationId());
            equipmentChange.setDutyId(equipmentChangeAddParam.getDutyId());
            equipmentChange.setDutyName(equipmentChangeAddParam.getDutyName());
            equipmentChange.setDutyContact(equipmentChangeAddParam.getDutyContact());
            equipmentChange.setDutyDept(equipmentChangeAddParam.getDutyDept());
            equipmentChange.setRemark(equipmentChangeAddParam.getRemark());
            equipmentChange.setProcessInstanceId(restResponse.getData());
            equipmentChange.setStatus(StaticValue.ZERO);
            return this.save(equipmentChange);
        }else{
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Override
    public Boolean updateBatchByParam(EquipmentChangeEditParam param) {
        LambdaUpdateWrapper<EquipmentChange> wrapper = Wrappers.lambdaUpdate();
        if(param.getEquipmentIds() != null && param.getEquipmentIds().length > 0) {
            wrapper.set(EquipmentChange::getEquipmentIds, param.getEquipmentIds());
        }
        if(param.getChangeTime() != null) {
            wrapper.set(EquipmentChange::getChangeTime, param.getChangeTime());
        }
        if(StrUtil.isNotBlank(param.getRemark())) {
            wrapper.set(EquipmentChange::getRemark, param.getRemark());
        }
        if(StrUtil.isNotBlank(param.getDutyContact())) {
            wrapper.set(EquipmentChange::getDutyContact, param.getDutyContact());
        }
        if(StrUtil.isNotBlank(param.getDutyDept())) {
            wrapper.set(EquipmentChange::getDutyDept, param.getDutyDept());
        }
        if(StrUtil.isNotBlank(param.getDutyId())) {
            wrapper.set(EquipmentChange::getDutyId, param.getDutyId());
        }
        if(StrUtil.isNotBlank(param.getDutyName())) {
            wrapper.set(EquipmentChange::getDutyName, param.getDutyName());
        }
        if(StrUtil.isNotBlank(param.getLocationId())) {
            wrapper.set(EquipmentChange::getLocationId, param.getLocationId());
        }
        if(null != param.getType()) {
            wrapper.set(EquipmentChange::getType, param.getType());
        }
        if(StrUtil.isNotBlank(param.getRemark())) {
            wrapper.set(EquipmentChange::getRemark, param.getRemark());
        }
        wrapper.eq(EquipmentChange::getId, param.getId());

        return this.update(wrapper);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<EquipmentInfoDto> getEquipmentInfoList(String[] equipmentIds) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        List<EquipmentInfo> equipmentInfoList = equipmentInfoService.list(wrapper);
        List<EquipmentInfoDto> equipmentInfoDtoList = CopyDataUtil.copyList(equipmentInfoList, EquipmentInfoDto.class);
        String[] supplierIds = equipmentInfoDtoList.stream()
                .map(EquipmentInfoDto::getSupplierId)
                .filter(Objects::nonNull).toArray(String[] :: new);

        RestResponse<Map<String, ManufacturerSupplierDto>> res = baseServiceClient.getListByIds(supplierIds);
        if(res.isOk()) {
            Map<String, ManufacturerSupplierDto> data = res.getData();
            equipmentInfoDtoList.forEach(dto -> {
                if(data.get(dto.getSupplierId()) != null) {
                    //dto.setSupplierName(data.get(dto.getSupplierId()).getName());
                }
            });
        }

        return equipmentInfoDtoList;

    }

    @Override
    public List<EquipmentChangeDto> getEquipmentChangeList(String equipmentId) {
        return equipmentChangeMapper.getEquipmentChangeList(equipmentId);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitProcessTask(TaskAuditParam taskAuditParam) {
        if(taskAuditParam.getResult() == AuditType.PASS.getValue()) {
            // 审核同意, 更新流程状态为已审核通过
            LambdaQueryWrapper<EquipmentChange> changeWrapper = Wrappers.lambdaQuery();
            changeWrapper.eq(EquipmentChange::getProcessInstanceId, taskAuditParam.getProcessInstanceId());
            EquipmentChange equipmentChange = (EquipmentChange) this.getOne(changeWrapper);
            equipmentChange.setStatus(StaticValue.ONE);
            this.updateById(equipmentChange);
            // 更新设备
            LambdaUpdateWrapper<EquipmentInfo> equipWrapper = Wrappers.lambdaUpdate();
            if(equipmentChange.getType() == StaticValue.TWO) {
                // 异动为借用, 设备状态改为借出
                //equipWrapper.set(EquipmentInfo::getStatus, StaticValue.SIX);
            }
            //equipWrapper.set(EquipmentInfo::getParentId, equipmentChange.getLocationId());
            //equipWrapper.set(EquipmentInfo::getPrincipal, equipmentChange.getDutyName());
         /*   equipWrapper.set(EquipmentInfo::getDutyId, equipmentChange.getDutyId());
            equipWrapper.set(EquipmentInfo::getDutyDept, equipmentChange.getDutyDept());*/
            equipWrapper.in(EquipmentInfo::getId, equipmentChange.getEquipmentIds());
            equipmentInfoService.update(equipWrapper);

            // 发起任务完成流程
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(taskAuditParam.getTaskId());
            taskCompleteParam.setComment(taskAuditParam.getComment());
            RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
            log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
            if(!completeTask.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
            }
        } else {
            // 驳回, 流程状态变更为驳回
            LambdaQueryWrapper<EquipmentChange> changeWrapper = Wrappers.lambdaQuery();
            changeWrapper.eq(EquipmentChange::getProcessInstanceId, taskAuditParam.getProcessInstanceId());
            EquipmentChange equipmentChange = (EquipmentChange) this.getOne(changeWrapper);
            equipmentChange.setStatus(StaticValue.TWO);
            this.updateById(equipmentChange);
            // 发起任务驳回流程
            TaskRejectParam taskRejectParam = new TaskRejectParam();
            taskRejectParam.setTaskId(taskAuditParam.getTaskId());
            taskRejectParam.setComment(taskAuditParam.getComment());
            RestResponse<Object> rejectTask = taskServiceClient.rejectTask(taskRejectParam);
            log.info("流程节点驳回任务提交参数{}响应{}", JSON.toJSONString(taskRejectParam), JSON.toJSONString(rejectTask));
            if(!rejectTask.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean againSubmit(TaskAuditParam taskAuditParam) {
        // 审核同意, 更新流程状态为审批中
        LambdaQueryWrapper<EquipmentChange> changeWrapper = Wrappers.lambdaQuery();
        changeWrapper.eq(EquipmentChange::getProcessInstanceId, taskAuditParam.getProcessInstanceId());
        EquipmentChange equipmentChange = (EquipmentChange) this.getOne(changeWrapper);
        equipmentChange.setStatus(StaticValue.ZERO);
        this.updateById(equipmentChange);
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskAuditParam.getTaskId());
        Map<String, Object> variables = new HashMap<>(1);
        List<String> assigns = new ArrayList<>();
        assigns.add(equipmentChange.getDutyId());
        variables.put("assigns", assigns);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskAuditParam.getComment());
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if(!completeTask.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    @Override
    public EquipmentChangeDto getEquipChangeByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<EquipmentChange> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentChange::getProcessInstanceId, processInstanceId);
        EquipmentChange equipmentChange = (EquipmentChange) this.getOne(wrapper);
        List<EquipmentInfoDto> equipmentInfoList = this.getEquipmentInfoList(equipmentChange.getEquipmentIds());

        EquipmentChangeDto dto = new EquipmentChangeDto();
        BeanUtil.copyProperties(equipmentChange, dto);
        dto.setEquipmentInfoDtoList(equipmentInfoList);

        EquipmentLocation equipmentLocation = (EquipmentLocation) equipmentLocationService.getById(equipmentChange.getLocationId());
        if(equipmentLocation != null) {
            dto.setLocationName(equipmentLocation.getName());
        }

        return dto;
    }

}
