package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.equipment.dto.authorization.EquipmentAuthorizationDto;
import cn.getech.ehm.equipment.entity.EquipmentAuthorization;
import cn.getech.ehm.equipment.mapper.EquipmentAuthorizationMapper;
import cn.getech.ehm.equipment.service.IEquipmentAuthorizationService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.exception.ServiceException;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecOrgClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.OrgPosRelDto;
import cn.getech.poros.permission.dto.PorosSecOrgDto;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import java.util.List;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2020-08-11 16:51:39
 **/
@Slf4j
@Service
public class EquipmentAuthorizationServiceImpl  extends BaseServiceImpl<EquipmentAuthorizationMapper, EquipmentAuthorization> implements IEquipmentAuthorizationService {

    private static final String CODE_PATH_DOT = "_";

    private static final int AUTH_TYPE_0 = 0;

    @Autowired
    private EquipmentAuthorizationMapper equipmentAuthorizationMapper;

    @Autowired
    private PorosSecOrgClient porosSecOrgClient;

    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;

    @Override
    public EquipmentAuthorizationDto getEquipmentAuthorization(String code, String parentCode, boolean isStaff) {
        EquipmentAuthorization equipmentAuthorization = this.getEquipmentAuthorization(code);
        if(equipmentAuthorization == null){
            return EquipmentAuthorizationDto.builder()
                    .code(code)
                    .parentCode(parentCode)
                    .staff(isStaff)
                    .authType(StaticValue.ZERO)
                    .equipmentIds(new String[]{})
                    .parentEquipmentIds(StringUtils.isBlank(parentCode)?new String[]{}:this.getCurrentEquipmentIds(parentCode))
                    .build();
        }else{
            String[] parentEquipmentIds = StringUtils.isBlank(parentCode)?new String[]{}:this.getCurrentEquipmentIds(parentCode);
            return EquipmentAuthorizationDto.builder()
                    .id(equipmentAuthorization.getId())
                    .code(equipmentAuthorization.getCode())
                    .staff(isStaff)
                    .authType(equipmentAuthorization.getAuthType())
                    .parentCode(equipmentAuthorization.getParentCode())
                    .equipmentIds(equipmentAuthorization.getAuthType() == AUTH_TYPE_0?parentEquipmentIds:equipmentAuthorization.getEquipmentIds())
                    .parentEquipmentIds(parentEquipmentIds)
                    .createBy(equipmentAuthorization.getCreateBy())
                    .createTime(equipmentAuthorization.getCreateTime())
                    .updateBy(equipmentAuthorization.getUpdateBy())
                    .updateTime(equipmentAuthorization.getUpdateTime())
                    .build();
        }
    }

    private List<String> getAuthorizationEquipments(String uid) {
        List<String> equipmentIds = new ArrayList<>();
        EquipmentAuthorization equipmentAuthorization = this.getEquipmentAuthorization(uid);
        if(null != equipmentAuthorization && equipmentAuthorization.getAuthType() == 1) {
            equipmentIds.addAll(Lists.newArrayList(equipmentAuthorization.getEquipmentIds()));
        }else{
            RestResponse<PorosSecStaffDto> restResponse = porosSecStaffClient.getByUid(uid, "");
            if(restResponse.isSuccess()){
                PorosSecStaffDto porosSecStaffDto = restResponse.getData();
                if(porosSecStaffDto.getRelDtoList() == null){
                    throw new ServiceException(new GlobalResultMessage("当前用户组织关系为空"));
                }
                for(OrgPosRelDto orgPosRelDto: porosSecStaffDto.getRelDtoList()){
                    equipmentIds.addAll(Lists.newArrayList(this.getCurrentEquipmentIds(orgPosRelDto.getOrgCode())));
                }
            }else{
                throw new ServiceException(new GlobalResultMessage("远程服务调用失败:" + restResponse.getMsg()));
            }
        }
        return equipmentIds;
    }

    /**
     * 根据code获取设备授权对象
     * @param code
     * @return
     */
    private EquipmentAuthorization getEquipmentAuthorization(String code){
        QueryWrapper<EquipmentAuthorization> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        EquipmentAuthorization equipmentAuthorization = equipmentAuthorizationMapper.selectOne(queryWrapper);
        return equipmentAuthorization;
    }

    /**
     * 根据code和codePath获取父级设备授权IDS
     * @param code
     * @param codePath
     * @return
     */
    private String[] getParentEquipmentIds(String code, String codePath){
        //是否用户配置的设备权限就是空
        boolean flag = false;
        String[] equipmentIds = new String[]{};
        if(StringUtils.isNotBlank(codePath) && codePath.contains(CODE_PATH_DOT)){
            String[] parentCodes = codePath.split(CODE_PATH_DOT);
            ArrayUtils.reverse(parentCodes);
            for(String parentCode : parentCodes){
                if(parentCode.contentEquals(code)){
                    continue;
                }
                EquipmentAuthorization parentEquipmentAuthorization = this.getEquipmentAuthorization(parentCode);
                if(parentEquipmentAuthorization != null && parentEquipmentAuthorization.getAuthType() == 1){
                    equipmentIds = parentEquipmentAuthorization.getEquipmentIds();
                    flag = true;
                    break;
                }
            }
        }
        if(!flag && equipmentIds.length == StaticValue.ZERO){
            List<String> allEquipmentIds = equipmentInfoService.getAllIds();
            if(CollectionUtils.isNotEmpty(allEquipmentIds)) {
                return allEquipmentIds.toArray(new String[allEquipmentIds.size()]);
            }
        }
        return equipmentIds;
    }

    /**
     * 根据code获取当前设备授权IDS
     * @param code
     * @return
     */
    @Override
    public String[] getCurrentEquipmentIds(String code){
        String[] equipmentIds = new String[]{};
        EquipmentAuthorization equipmentAuthorization = this.getEquipmentAuthorization(code);
        if(null != equipmentAuthorization && equipmentAuthorization.getAuthType() == 1){
            equipmentIds = equipmentAuthorization.getEquipmentIds();
        }else{
            RestResponse<PorosSecOrgDto> restResponse = new RestResponse<>();
            if(restResponse.isSuccess()){
                PorosSecOrgDto porosSecOrgDto = restResponse.getData();
                equipmentIds = this.getParentEquipmentIds(porosSecOrgDto.getCode(), porosSecOrgDto.getCodePath());
            }
        }
        return equipmentIds;
    }

    @Override
    public List<String> getCurrentUserAuz(){
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        Assert.notNull(userInfo, "current user is null");
        return this.getAuthorizationEquipments(userInfo.getUid());
    }
}
