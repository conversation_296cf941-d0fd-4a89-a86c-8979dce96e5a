package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 诊断报告v2查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportV2QueryParam", description = "诊断报告v2查询参数")
public class DiagnosisReportV2QueryParam extends PageParam {

    @ApiModelProperty(value = "名称/编号")
    private String nameOrCode;

    @ApiModelProperty(value = "报告类型")
    private Integer type;

    @ApiModelProperty(value = "发布状态")
    private Integer status;

    @ApiModelProperty(value = "当前人员是否为审核人员", hidden = true)
    private Boolean auditPerson;

    @ApiModelProperty(value = "当前人员uid")
    private String currentUid;

}
