package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.bearing.BasicLibraryField;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 基础库字段Mapper
 */
@Repository
public interface BasicLibraryFieldMapper extends BaseMapper<BasicLibraryField> {
    /**
     * 获取最大排序
     * @return 最大排序
     */
    Integer getMaxSort(@Param("basicLibraryId") String basicLibraryId);

    /**
     * 获取默认geek下基础库字段
     * @return
     */
    @SqlParser(filter = true)
    List<BasicLibraryField> getDefaultFields();
}
