package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.*;
import cn.getech.ehm.equipment.entity.bearing.BearingDb;
import cn.getech.ehm.equipment.entity.bearing.BearingFactory;
import cn.getech.ehm.equipment.entity.bearing.BearingModel;
import cn.getech.ehm.equipment.mapper.BearingDbMapper;
import cn.getech.ehm.equipment.service.IBearingDbService;
import cn.getech.ehm.equipment.service.IBearingFactoryService;
import cn.getech.ehm.equipment.service.IBearingModelService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 轴承 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class BearingDbServiceImpl extends BaseServiceImpl<BearingDbMapper, BearingDb> implements IBearingDbService {

    @Autowired
    private BearingDbMapper bearingDbMapper;
    @Autowired
    private IBearingFactoryService factoryService;
    @Autowired
    private IBearingModelService modelService;

    @Override
    public PageResult<BearingDbDto> pageDto(BearingDbQueryParam queryParam) {
        Page<BearingDbQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        if(StringUtils.isNotBlank(queryParam.getFactoryId())){
            queryParam.setFactoryName(null);
        }
        if(StringUtils.isNotBlank(queryParam.getModelId())){
            queryParam.setModelName(null);
        }
        IPage<BearingDbDto> result = bearingDbMapper.pageList(page, queryParam);
        return Optional.ofNullable(PageResult.<BearingDbDto>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());

    }

    @Override
    public BearingDbDto getDtoById(String id) {
        return bearingDbMapper.getDtoById(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(BearingDbDto bearingDbDto) {
        if(StringUtils.isNotBlank(bearingDbDto.getFactoryId()) && StringUtils.isNotBlank(bearingDbDto.getModelId())
        && check(null, bearingDbDto.getFactoryId(), bearingDbDto.getModelId())){
            throw new GlobalServiceException(GlobalResultMessage.of("该品牌型号下已存在轴承"));
        }
        return this.save(buildEntity(bearingDbDto));
    }

    /**
     * 校验型号+品牌是否已经存在对应轴承
     * @param id
     * @param factoryId
     * @param modelId
     * @return
     */
    private Boolean check(String id, String factoryId, String modelId){
        LambdaQueryWrapper<BearingDb> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(BearingDb::getId, id);
        }
        wrapper.eq(BearingDb::getFactoryId, factoryId);
        wrapper.eq(BearingDb::getModelId, modelId);
        wrapper.eq(BearingDb::getDeleted, DeletedType.NO.getValue());
        return bearingDbMapper.selectCount(wrapper) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(BearingDbDto bearingDbDto) {
        if(StringUtils.isNotBlank(bearingDbDto.getFactoryId()) && StringUtils.isNotBlank(bearingDbDto.getModelId())
                && check(bearingDbDto.getId(), bearingDbDto.getFactoryId(), bearingDbDto.getModelId())){
            throw new GlobalServiceException(GlobalResultMessage.of("该品牌型号下已存在轴承"));
        }
        return this.updateById(buildEntity(bearingDbDto));
    }

    @Override
    public Boolean deleteById(String id){
        LambdaUpdateWrapper<BearingDb> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(BearingDb::getId, id);
        wrapper.set(BearingDb::getDeleted, DeletedType.YES.getValue());
        return this.update(wrapper);
    }

    private BearingDb buildEntity(BearingDbDto bearingDbDto){
        if(StringUtils.isBlank(bearingDbDto.getFactoryId())){
            String factoryId = factoryService.saveByName(bearingDbDto.getFactoryName());
            bearingDbDto.setFactoryId(factoryId);
        }
        if(StringUtils.isBlank(bearingDbDto.getModelId())){
            String modelId = modelService.saveByName(bearingDbDto.getModelName());
            bearingDbDto.setModelId(modelId);
        }
        return CopyDataUtil.copyObject(bearingDbDto, BearingDb.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean addDb() {
        List<MdbBearingDbDto> mdbBearingDbDtos = bearingDbMapper.mdbBearingList();
        Map<String, String> factoryMap = new HashMap<>();
        Map<String, String> modelMap = new HashMap<>();

        List<BearingDb> bearingDbs = new ArrayList<>(mdbBearingDbDtos.size());
        for(MdbBearingDbDto dbDto: mdbBearingDbDtos){
            BearingDb bearingDb = new BearingDb();
            bearingDb.setNb(dbDto.getNB());
            bearingDb.setBpfo(dbDto.getBPFO());
            bearingDb.setBpfi(dbDto.getBPFI());
            bearingDb.setFifi(dbDto.getFTFI());
            bearingDb.setFifo(null != dbDto.getFTFI() ?  (1 - dbDto.getFTFI()) : 0);
            bearingDb.setBsf(dbDto.getBSF());
            bearingDb.setContactAngle(dbDto.getContactAngle());
            String factoryName = dbDto.getFactoryName();
            if(factoryMap.containsKey(factoryName)){
                bearingDb.setFactoryId(factoryMap.get(factoryName));
            }else{
                String factoryId = factoryService.saveByName(dbDto.getFactoryName());
                bearingDb.setFactoryId(factoryId);
                factoryMap.put(factoryName, factoryId);
            }
            String modelName = dbDto.getBearingModel();
            if(modelMap.containsKey(modelName)){
                bearingDb.setModelId(modelMap.get(modelName));
            }else {
                String modelId = modelService.saveByName(modelName);
                bearingDb.setModelId(modelId);
                modelMap.put(modelName, modelId);
            }
            bearingDbs.add(bearingDb);
        }
        return saveBatch(bearingDbs);
    }

    @Override
    public String importExcel(List<BearingDbExcelDto> rows) {
        if(CollectionUtils.isNotEmpty(rows)){
            StringBuffer builder = new StringBuffer();
            List<BearingDb> bearingDbs = new ArrayList<>(rows.size());
            Map<String, String> factoryMap = factoryService.getFactoryMap();
            Map<String, String> modelMap = modelService.getModelMap();
            List<String> existsFactoryModel = this.getFactoryModel();
            int i = 2;

            for(BearingDbExcelDto excelDto : rows){
                BearingDb bearingDb = CopyDataUtil.copyObject(excelDto, BearingDb.class);
                StringBuffer sb = new StringBuffer();
                Boolean flag = false;
                sb.append("第 ").append(i++).append(" 行数据").append(StringPool.COLON);
                String factoryId = factoryMap.get(Strings.toLowerCase(excelDto.getFactoryName()));
                bearingDb.setFactoryId(factoryId);
                if(StringUtils.isBlank(factoryId)){
                    flag = true;
                    sb.append("品牌未找到对应数据").append(StringPool.COMMA);
                }
                String modelId = modelMap.get(Strings.toLowerCase(excelDto.getModelName()));
                bearingDb.setModelId(modelId);
                if(StringUtils.isBlank(modelId)){
                    flag = true;
                    sb.append("型号未找到对应数据").append(StringPool.COMMA);
                }
                //品牌、型号都存在
                if(!flag && existsFactoryModel.contains(factoryId + StringPool.DASH + modelId)){
                    flag = true;
                    sb.append("该品牌型号已有对应轴承").append(StringPool.COMMA);
                }
                if (flag) {
                    sb.deleteCharAt(sb.length() - 1);
                    builder.append(sb.toString()).append(StringPool.SEMICOLON);
                }
                bearingDbs.add(bearingDb);
            }
            if(StringUtils.isNotBlank(builder.toString())){
                return builder.toString();
            }else{
                this.saveBatch(bearingDbs);
                return "导入成功" + bearingDbs.size() + "条";
            }
        }
        return "表单中没有数据";
    }

    @Override
    public List<BearingDbExcelDto> getExcelList(BearingDbQueryParam queryParam) {
        return bearingDbMapper.getExcelList(queryParam);
    }

    @Override
    public Boolean initialization(String tenantId) {
        Map<String, String> modelIdMap = new HashMap<>();
        Map<String, String> factoryIdMap = new HashMap<>();

        List<BearingModel> bearingModels = modelService.getDefaultModel();
        if(CollectionUtils.isNotEmpty(bearingModels)){
            for(BearingModel bearingModel : bearingModels){
                String oldId = bearingModel.getId();
                String id = UUID.randomUUID().toString().replace("-","");
                bearingModel.setId(id);
                bearingModel.setTenantId(tenantId);
                modelIdMap.put(oldId, id);
            }
            modelService.saveBatch(bearingModels);
        }

        List<BearingFactory> bearingFactories = factoryService.getDefaultFactory();
        if(CollectionUtils.isNotEmpty(bearingFactories)){
            for(BearingFactory bearingFactory : bearingFactories){
                String oldId = bearingFactory.getId();
                String id = UUID.randomUUID().toString().replace("-","");
                bearingFactory.setId(id);
                bearingFactory.setTenantId(tenantId);
                factoryIdMap.put(oldId, id);
            }
            factoryService.saveBatch(bearingFactories);
        }
        List<BearingDb> bearingDbs = bearingDbMapper.getDefaultDb();
        if(CollectionUtils.isNotEmpty(bearingDbs)){
            for(BearingDb bearingDb : bearingDbs){
                bearingDb.setId(null);
                bearingDb.setFactoryId(factoryIdMap.get(bearingDb.getFactoryId()));
                bearingDb.setModelId(modelIdMap.get(bearingDb.getModelId()));
                bearingDb.setTenantId(tenantId);
            }
            this.saveBatch(bearingDbs);
        }
        return true;
    }

    /**
     * 获取已经录入的品牌-型号集合
     * @return
     */
    private List<String> getFactoryModel(){
        List<String> factoryModelList = new ArrayList<>();
        LambdaQueryWrapper<BearingDb> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BearingDb::getDeleted, DeletedType.NO.getValue());
        wrapper.select(BearingDb::getFactoryId, BearingDb::getModelId);
        List<BearingDb> bearingDbs = bearingDbMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(bearingDbs)){
            for(BearingDb bearingDb : bearingDbs){
                factoryModelList.add(bearingDb.getFactoryId() + StringPool.DASH + bearingDb.getModelId());
            }
        }
        return factoryModelList;
    }
}
