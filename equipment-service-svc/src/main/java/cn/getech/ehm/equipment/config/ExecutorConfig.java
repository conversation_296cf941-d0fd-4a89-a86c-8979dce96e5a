package cn.getech.ehm.equipment.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2020-08-10 10:53:47
 **/
@EnableAsync
@Configuration
public class ExecutorConfig {

    @Bean
    public ExecutorService executorService(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("equipment-runner-thread-%d").build();
        ExecutorService executorService = new ThreadPoolExecutor(20, 50, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        return executorService ;
    }


}
