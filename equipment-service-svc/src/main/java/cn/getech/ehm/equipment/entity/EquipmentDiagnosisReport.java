package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 诊断报告V2
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_diagnosis_report")
public class EquipmentDiagnosisReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备名称
     */
    @TableField("code")
    private String code;

    /**
     * 1人工报告2自动生成
     */
    @TableField("type")
    private Integer type;

    /**
     * 1待发布2已发布
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 描述
     */
    @TableField("content")
    private String content;

    /**
     * 发布人
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private Date publishTime;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
