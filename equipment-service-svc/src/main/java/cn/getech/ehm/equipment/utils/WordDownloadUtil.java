package cn.getech.ehm.equipment.utils;

import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisCaseDto;
import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportDto;
import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportV2Dto;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.deepoove.poi.data.PictureRenderData;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 诊断案例word操作类
 */
@Slf4j
public class WordDownloadUtil {

    public static void download(HttpServletResponse response, File file, String prefixName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(new FileInputStream(file));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            log.info("RunningReportWordUtil:download:" + file.getAbsolutePath());
            // 清空response
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(prefixName + file.getName(), "UTF-8"));
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(buffer);
            ouputStream.flush();
            ouputStream.close();
        } catch (Exception e) {
            log.info(e.getMessage());
            log.info("RunningReportWordUtil:download exception:" + file.getAbsolutePath());
        }
    }

    @SneakyThrows
    public static File fillDataIntoCaseWord(DiagnosisCaseDto dto, String currentDirectory, List<Map<String, Object>> analyzeDtos, List<Map<String, Object>> docDtos){
        InputStream templateFile = WordDownloadUtil.class.getResourceAsStream("/mapper/template/DiagnosisCase_template.docx");
        String outFile = currentDirectory + dto.getName().replace(" ", "").replace("/", "") + ".docx";

        XWPFTemplate template = XWPFTemplate.compile(templateFile).render(
                new HashMap<String, Object>() {{
                    put("name", dto.getName());
                    put("remark", dto.getRemark());
                    put("equipmentName", dto.getEquipmentName());
                    put("locationName", dto.getLocationName());
                    put("analyzeDtos", analyzeDtos);
                    put("docDtos", docDtos);
                    put("analyzeConclusion", dto.getAnalyzeConclusion());
                    put("analyzeReason", dto.getAnalyzeReason());
                    put("handlingSuggestions", dto.getHandlingSuggestions());
                }});

        writeFile(outFile, template);
        return new File(outFile);
    }

    @SneakyThrows
    public static File fillDataIntoReportWord(DiagnosisReportDto dto, String currentDirectory, List<Map<String, Object>> analyzePicInfoMap, PictureRenderData equipmentPic){
        InputStream templateFile = WordDownloadUtil.class.getResourceAsStream("/mapper/template/DiagnosisReport_template.docx");
        String outFile = currentDirectory + dto.getName().replace(" ", "").replace("/", "") + ".docx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        XWPFTemplate template = XWPFTemplate.compile(templateFile).render(
                new HashMap<String, Object>() {{
                    put("name", dto.getName());
                    put("code", dto.getCode());
                    put("equipmentName", dto.getEquipmentName());
                    put("equipmentCode", dto.getEquipmentCode());
                    put("categoryName", dto.getCategoryName());
                    put("locationName", dto.getLocationName());
                    put("equipmentPicUrl", equipmentPic);
                    put("analyzePicInfoDtos", analyzePicInfoMap);
                    put("analyzeInfo", dto.getAnalyzeInfo());
                    put("analyzeReason", dto.getAnalyzeReason());
                    put("handlingSuggestions", dto.getHandlingSuggestions());
                    put("createBy", dto.getCreateBy());
                    put("createTime", sdf.format(dto.getCreateTime()));
                }});

        writeFile(outFile, template);
        return new File(outFile);
    }

    @SneakyThrows
    public static File fillDataIntoReportV2Word(DiagnosisReportV2Dto dto, String currentDirectory){
        InputStream templateFile = WordDownloadUtil.class.getResourceAsStream("/mapper/template/DiagnosisReport_template_V2.docx");
        HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();
        Configure config = Configure.newBuilder()
                .bind("healthList", policy)
                .bind("equipmentList", policy)
                .bind("followEquipments", policy)
                .bind("followEquipments", policy)
                .build();
        String outFile = currentDirectory + dto.getName().replace(" ", "").replace("/", "") + ".docx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        XWPFTemplate template = XWPFTemplate.compile(templateFile, config).render(
                new HashMap<String, Object>() {{
                    put("name", dto.getName());
                    put("code", dto.getCode());
                    put("createUserName", dto.getCreateUserName());
                    put("publishTime", null!= dto.getPublishTime() ? DateUtil.format(dto.getPublishTime(), "yyyy-MM-dd HH:mm:ss") : null);
                    put("typeName", dto.getTypeName());
                    put("content", dto.getContent());
                    put("healthList", dto.getHealthList());
                    put("followEquipments", dto.getFollowEquipments());
                    put("equipmentList", dto.getEquipmentList());
                }});

        writeFile(outFile, template);
        return new File(outFile);
    }

    private static void writeFile(String outFile, XWPFTemplate template){
        try {
            FileOutputStream out = new FileOutputStream(outFile);
            template.write(out);
            out.flush();
            out.close();
            template.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
