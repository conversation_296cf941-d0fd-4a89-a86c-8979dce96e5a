package cn.getech.ehm.equipment.dto.category;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;


/**
 * 设备类型 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentCategoryDto", description = "设备类型返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentCategoryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "上级节点")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称")
    private String parentName;

    @ApiModelProperty(value = "外观图片ids(多张，逗号隔开)")
    private String picIds;

    @ApiModelProperty(value = "附件ids(多个逗号隔开)")
    private String docIds;

    @ApiModelProperty(value = "设备数量")
    private Integer num;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo = false;

    @ApiModelProperty(value = "是否需要校验")
    private Boolean checked = false;

    @ApiModelProperty(value = "是否固定0否1是")
    private Integer fixed;

    @ApiModelProperty(value = "类型0其他1仪器仪表")
    private Integer type;

    @ApiModelProperty(value = "设备编码前缀")
    private String prefix;

    @ApiModelProperty(value = "扩展属性集合")
    private List<EquipmentCategoryPropDto> propDtos;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "OEE计算")
    private Boolean oeeOpen = false;

    @ApiModelProperty(value = "设备状态开关")
    private Boolean statusParamOpen = false;
}