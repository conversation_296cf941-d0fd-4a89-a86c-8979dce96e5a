package cn.getech.ehm.equipment.dto.iot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组态列表返回数据模型
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudioResDto", description = "组态列表返回数据模型")
public class StudioResDto {

    @ApiModelProperty(value = "组态id")
    private List<StudioResDetailDto> content;

    @ApiModelProperty(value = "数量")
    private Long total;

}