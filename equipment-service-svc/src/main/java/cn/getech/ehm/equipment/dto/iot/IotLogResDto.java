package cn.getech.ehm.equipment.dto.iot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 设备日志返回数据模型
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "IotLogResDto", description = "设备日志返回数据模型")
public class IotLogResDto {

    @ApiModelProperty(value = "参数id")
    private String paramId;

    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "组态名称")
    private String deviceId;

    @ApiModelProperty(value = "参数时间戳-值Map")
    private Map<String, String> resMap;

}