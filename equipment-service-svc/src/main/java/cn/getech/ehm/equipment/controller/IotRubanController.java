package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.info.LazyEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.ruban.IotTopicDto;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.GetechIotQueryParam;
import cn.getech.ehm.iot.dto.GetechIotResDto;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * getech-iot组态对应接口
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/ruban")
@Api(tags = "getech-iot组态接口")
@Slf4j
public class IotRubanController {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private KafkaTemplate kafkaTemplate;

    @ApiOperation("测试iot-topic数据推送")
    @PostMapping("/sendTopic")
    public RestResponse<Boolean> sendTopic(@RequestBody List<IotTopicDto> dtos) {
        String timestamp = DateUtil.format(new Date(), DatePattern.UTC_MS_FORMAT);
        for(IotTopicDto dto : dtos){
            dto.setTimestamp(timestamp);
        }
        String json = JSONObject.toJSON(dtos).toString();
        kafkaTemplate.send("ruban_pubsub_data", json);
        return RestResponse.ok(true);
    }

    @ApiOperation("结构树")
    @PostMapping("/structureTree")
    public RestResponse<List<GetechIotResDto>> structureTree(@RequestBody GetechIotQueryParam queryParam) {
        List<GetechIotResDto> treeResDtos = new ArrayList<>();
        List<LazyEquipmentTreeDto> equipmentTreeDtos = equipmentInfoService.lazyEquipmentTree(queryParam.getParentId(), 1, queryParam.getKeyword());
        if(CollectionUtils.isNotEmpty(equipmentTreeDtos)){
            for(LazyEquipmentTreeDto dto : equipmentTreeDtos){
                GetechIotResDto resDto = new GetechIotResDto();
                resDto.setId(dto.getId());
                resDto.setName(dto.getName());
                resDto.setParentId(dto.getParentId());
                resDto.setTenantId(queryParam.getTenantId());
                resDto.setHasChild(!dto.getIsLeaf());
                treeResDtos.add(resDto);
            }
        }
        return RestResponse.ok(treeResDtos);
    }

    @ApiOperation("根据测点id分页获取参数列表")
    @PostMapping("/parameterPageList")
    public RestResponse<PageResult<GetechIotResDto>> parameterPageList(@RequestBody GetechIotQueryParam queryParam) {
        return parameterClient.parameterPageList(queryParam);
    }

    @ApiOperation("根据设备id分页获取测点列表")
    @PostMapping("/pointPageList")
    public RestResponse<PageResult<GetechIotResDto>> pointPageList(@RequestBody GetechIotQueryParam queryParam) {
        return parameterClient.pointPageList(queryParam);
    }
}
