package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.ObjectUtils;
import cn.getech.ehm.equipment.dto.category.CategoryStructureDto;
import cn.getech.ehm.equipment.dto.category.StructureSortDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryStructure;
import cn.getech.ehm.equipment.handler.CommonGetHandler;
import cn.getech.ehm.equipment.mapper.InfoCategoryStructureMapper;
import cn.getech.ehm.equipment.service.IInfoCategoryStructureService;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.PartDetailDto;
import cn.getech.ehm.part.dto.PartSearchDto;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备类型部件服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Slf4j
@Service
public class InfoCategoryStructureServiceImpl extends BaseServiceImpl<InfoCategoryStructureMapper, EquipmentCategoryStructure> implements IInfoCategoryStructureService {

    @Autowired
    private InfoCategoryStructureMapper categoryStructureMapper;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private CommonGetHandler commonGetHandler;

    @Override
    public List<CategoryStructureDto> getListByCategoryId(String categoryId){
        return this.getListByCategoryId(categoryId,false);
    }

    @Override
    public List<CategoryStructureDto> getListByCategoryId(String categoryId, Boolean filter) {
        List<CategoryStructureDto> categoryStructureDtos = categoryStructureMapper.getListByCategoryId(categoryId, null);
        if (CollectionUtils.isNotEmpty(categoryStructureDtos)) {
            List<String> partIdList = categoryStructureDtos.stream().map(item -> item.getPartId()).collect(Collectors.toList());
            Map<String, PartDetailDto> partInfo = commonGetHandler.getPartInfo(partIdList);
            //第一级部件/零件
            List<CategoryStructureDto> structureDtos = categoryStructureDtos.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
            if (filter) {
                structureDtos = structureDtos.stream().filter(dto -> dto.getType() != null && dto.getType() == 1).collect(Collectors.toList());
            }
            //第二级零件
            Map<String, List<CategoryStructureDto>> childMap = categoryStructureDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.groupingBy(CategoryStructureDto::getParentId));
            for (CategoryStructureDto structureDto : structureDtos) {
                structureDto.setLevel(1);
                structureDto.setChildren(childMap.get(structureDto.getId()));
            }
            commonGetHandler.completeCategoryPartInfo(structureDtos, partIdList);
            return structureDtos;
        }
        return new ArrayList<>();
    }


    @Override
    public String editStructure(CategoryStructureDto editParam) {
        if(check(editParam.getId(), editParam.getName(), editParam.getParentId(), editParam.getCategoryId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentCategoryStructure categoryStructure = BeanUtil.copyProperties(editParam,EquipmentCategoryStructure.class);
        if(StringUtils.isBlank(editParam.getId())){
            categoryStructure.setSort(categoryStructureMapper.getMaxSort(editParam.getCategoryId()) + 1);
        }
        if (StringUtils.isNotBlank(categoryStructure.getId())) {
            this.updateById(categoryStructure);
        } else {
            this.save(categoryStructure);
        }
        //saveOrUpdate(categoryStructure);
        return categoryStructure.getId();
    }

    private Boolean check(String id, String name, String parentId, String categoryId){
        LambdaQueryWrapper<EquipmentCategoryStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(StringUtils.isNotBlank(id), EquipmentCategoryStructure::getId, id);
        wrapper.eq(EquipmentCategoryStructure::getName, name);
        wrapper.eq(EquipmentCategoryStructure::getCategoryId, categoryId);
        wrapper.eq(StringUtils.isNotBlank(parentId), EquipmentCategoryStructure::getParentId, parentId);
        return categoryStructureMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Boolean updateStructureSort(List<StructureSortDto> dtos){
        List<EquipmentCategoryStructure> structures = new ArrayList<>(dtos.size());
        for(StructureSortDto dto : dtos){
            EquipmentCategoryStructure structure = new EquipmentCategoryStructure();
            structure.setId(dto.getId());
            structure.setSort(dto.getSort());
            structures.add(structure);
        }
        return updateBatchById(structures);
    }

    @Override
    public List<CategoryStructureDto> getSelectList(String categoryId, List<String> ids){
        List<CategoryStructureDto> structureDtos = Lists.newArrayList();
        List<CategoryStructureDto> categoryStructureDtos = categoryStructureMapper.getListByCategoryId(categoryId, ids);
        if(CollectionUtils.isNotEmpty(categoryStructureDtos)) {
            //第一级部件/零件
            structureDtos = categoryStructureDtos.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
            //第二级零件
            Map<String, List<CategoryStructureDto>> childMap = categoryStructureDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.groupingBy(CategoryStructureDto::getParentId));
            for (CategoryStructureDto structureDto : structureDtos) {
                structureDto.setChildren(childMap.get(structureDto.getId()));
            }
        }
        return structureDtos;
    }
}
