package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BearingFactory;
import cn.getech.ehm.equipment.mapper.BearingFactoryMapper;
import cn.getech.ehm.equipment.service.IBearingFactoryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 轴承生产厂家 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class BearingFactoryServiceImpl extends BaseServiceImpl<BearingFactoryMapper, BearingFactory> implements IBearingFactoryService {

    @Autowired
    private BearingFactoryMapper bearingFactoryMapper;
    @Autowired
    private MessageSource messageSource;

    @Override
    public PageResult<FactoryModelDto> pageDto(FactoryModelQueryParam queryParam) {
        LambdaQueryWrapper<BearingFactory> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(queryParam.getName())){
            wrapper.like(BearingFactory::getName, queryParam.getName());
        }
        wrapper.eq(BearingFactory::getDeleted, DeletedType.NO.getValue());
        wrapper.orderByDesc(BearingFactory::getCreateTime);
        PageResult<BearingFactory> result = page(queryParam, wrapper);

        return Optional.ofNullable(PageResult.<FactoryModelDto>builder()
                .records(CopyDataUtil.copyList(result.getRecords(), FactoryModelDto.class))
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public String saveByName(String name) {
        LambdaQueryWrapper<BearingFactory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BearingFactory::getName, name);
        BearingFactory bearingFactory = bearingFactoryMapper.selectOne(wrapper);
        if(null == bearingFactory){
            bearingFactory = new BearingFactory();
            bearingFactory.setName(name);
            this.save(bearingFactory);
        }
        return bearingFactory.getId();
    }

    @Override
    public boolean saveByParam(FactoryModelDto dto) {
        String name = Strings.toLowerCase(dto.getName());
        if(check(null, name)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        BearingFactory bearingFactory = new BearingFactory();
        bearingFactory.setName(name);
        return save(bearingFactory);
    }

    /**
     * 校验名称
     * @param id
     * @param name
     * @return
     */
    private boolean check(String id, String name){
        return bearingFactoryMapper.checkNameExits(id, name) > 0;
    }

    @Override
    public boolean updateByParam(FactoryModelDto dto) {
        String name = Strings.toLowerCase(dto.getName());
        if(check(dto.getId(), name)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        BearingFactory bearingFactory = new BearingFactory();
        bearingFactory.setName(name);
        bearingFactory.setId(dto.getId());
        return updateById(bearingFactory);
    }

    @Override
    public Boolean deleteById(String id) {
        LambdaUpdateWrapper<BearingFactory> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(BearingFactory::getId, id);
        wrapper.set(BearingFactory::getDeleted, DeletedType.YES.getValue());
        return this.update(wrapper);
    }

    @Override
    public List<FactoryModelDto> getList(String keyword) {
        LambdaQueryWrapper<BearingFactory> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(keyword)){
            wrapper.like(BearingFactory::getName, keyword);
        }
        wrapper.eq(BearingFactory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(BearingFactory::getName, BearingFactory::getId);
        return CopyDataUtil.copyList(bearingFactoryMapper.selectList(wrapper), FactoryModelDto.class);
    }

    @Override
    public Map<String, String> getFactoryMap(){
        Map<String, String> map = new HashMap<>();
        LambdaQueryWrapper<BearingFactory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BearingFactory::getDeleted, DeletedType.NO.getValue());
        wrapper.select(BearingFactory::getName, BearingFactory::getId);
        List<BearingFactory> bearingFactories = bearingFactoryMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(bearingFactories)){
            for(BearingFactory bearingFactory : bearingFactories){
                map.put(Strings.toLowerCase(bearingFactory.getName()), bearingFactory.getId());
            }
        }

        return map;
    }

    @Override
    public List<BearingFactory> getDefaultFactory(){
        return bearingFactoryMapper.getDefaultFactory();
    }
}
