package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 部件特种设备导出
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "SpecialStructureExcelDto", description = "部件特种设备导出")
public class SpecialStructureExcelDto {

    @ApiModelProperty(value = "编码")
    @FormExcel(name="编码",cellType = FormExcel.ColumnType.STRING)
    private String code;

    @ApiModelProperty(value = "名称")
    @FormExcel(name="名称",cellType = FormExcel.ColumnType.STRING)
    private String name;

    @ApiModelProperty(value = "类型名称")
    @FormExcel(name="类型",cellType = FormExcel.ColumnType.STRING)
    private String categoryName;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备/部件上级设备或部件))")
    @FormExcel(name="位置",cellType = FormExcel.ColumnType.STRING)
    private String parentName;


    @ApiModelProperty(value = "规格型号")
    @FormExcel(name="规格",cellType = FormExcel.ColumnType.STRING)
    private String specification;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "石大胜华单位名称")
    @FormExcel(name="单位",cellType = FormExcel.ColumnType.STRING)
    private String unitName;

    @ApiModelProperty(value = "材质")
    @FormExcel(name="材质",cellType = FormExcel.ColumnType.STRING)
    private String material;

    @ApiModelProperty(value = "厂商")
    private String[] supplierIds;

    @ApiModelProperty(value = "厂商名称")
    @FormExcel(name="供应商",cellType = FormExcel.ColumnType.STRING)
    private String supplierNames;

    @ApiModelProperty(value = "登记证号")
    @FormExcel(name="登记证号",cellType = FormExcel.ColumnType.STRING )
    private String certificateNo;

    @ApiModelProperty(value = "注册代码")
    @FormExcel(name="注册代码",cellType = FormExcel.ColumnType.STRING )
    private String registrationCode;

    @ApiModelProperty(value = "特种设备检验日期")
    @FormExcel(name="特种设备检验日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd" )
    private Date inspectionDate;

    @ApiModelProperty(value = "下次检验日期")
    @FormExcel(name="下次检验日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd" )
    private Date nextInspectionDate;

    @ApiModelProperty(value = "检验报告编号")
    @FormExcel(name="检验报告编号",cellType = FormExcel.ColumnType.STRING )
    private String reportNo;

    @ApiModelProperty(value = "校验机构")
    @FormExcel(name="校验机构",cellType = FormExcel.ColumnType.STRING )
    private String inspectionOrg;

    @ApiModelProperty(value = "发证机关")
    @FormExcel(name="发证机关",cellType = FormExcel.ColumnType.STRING )
    private String issuingAuthority;

    @ApiModelProperty(value = "使用单位")
    @FormExcel(name="使用单位",cellType = FormExcel.ColumnType.STRING )
    private String useOrg;

    /**
     * 导出固定转换List
     */
    @ApiModelProperty(value = "扩展属性")
    @FormExcel(name="扩展属性",cellType = FormExcel.ColumnType.STRING, isExtend = true)
    private String extendProp;

}