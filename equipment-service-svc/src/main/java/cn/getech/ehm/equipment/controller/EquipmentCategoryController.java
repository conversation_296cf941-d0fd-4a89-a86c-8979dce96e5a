package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.base.dto.EagerTreeNodeDto;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.equipment.dto.CategoryStatisticsDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryStructure;
import cn.getech.ehm.iot.dto.SynCategoryStructureDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryStructure;
import cn.getech.ehm.iot.dto.SynCategoryStructureDto;
import cn.getech.ehm.equipment.dto.category.*;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentStructureService;
import cn.getech.ehm.equipment.service.IInfoCategoryStructureService;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备类型控制器
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@RestController
@RequestMapping("/equipmentType")
@Api(tags = "设备类型服务接口")
public class EquipmentCategoryController {

    @Autowired
    private IEquipmentCategoryService equipmentCategoryService;
    @Autowired
    private IInfoCategoryStructureService categoryStructureService;
    @Autowired
    private IEquipmentStructureService structureService;

    /**
     * 根据父节点获取子节点(获取设备类型树)
     */
    @ApiOperation("根据父节点获取子节点(获取设备类型树)")
    @GetMapping("/node")
    @ApiImplicitParams({
            @ApiImplicitParam(name="parentId",value="父节点id",dataType="string", paramType = "query"),
            @ApiImplicitParam(name="isClient",value="是否客户端0否1是",dataType="string", paramType = "query")
    })
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentCategoryTreeDto>> node(@RequestParam(required = false) String parentId,
                                                             @RequestParam(required = false) Integer isClient){
        return RestResponse.ok(equipmentCategoryService.node(parentId, isClient));
    }

    /**
     * 获取类型树
     */
    @ApiOperation("设备类型树")
    @GetMapping("/tree")
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentCategoryTreeDto>> tree(){
        return RestResponse.ok(equipmentCategoryService.tree());
    }

    /**
     * 根据编码集合获取设备类型名称集合
     */
    @ApiOperation("根据编码集合获取设备类型编码名称集合")
    @GetMapping("/getCategoryNamesByCodes")
    public RestResponse<Map<String, String>> getCategoryNamesByCodes(@RequestParam(value = "codes", required = false) String[] codes){
        return RestResponse.ok(equipmentCategoryService.getCategoryNamesByCodes(codes));
    }

    /**
     * 根据关键字搜索类别
     */
    @ApiOperation("根据关键字搜索类别")
    @GetMapping("/query")
    //@Permission("equipment:location:list")
    public RestResponse<List<EquipmentCategoryDto>> query(@RequestParam String keyword){
        return RestResponse.ok(equipmentCategoryService.queryList(keyword));
    }

    /**
     * 新增设备类型
     */
    @ApiOperation("新增设备类型")
    @AuditLog(title = "设备类型",desc = "新增设备类型",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:type:update")
    public RestResponse<EquipmentCategoryTreeDto> add(@RequestBody @Valid EquipmentCategoryAddParam equipmentCategoryAddParam) {
        return RestResponse.ok(equipmentCategoryService.saveByParam(equipmentCategoryAddParam));
    }

    /**
     * 修改设备类型
     */
    @ApiOperation(value="修改设备类型")
    @AuditLog(title = "设备类型",desc = "修改设备类型",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:type:update")
    public RestResponse<Boolean> update(@RequestBody @Valid EquipmentCategoryEditParam equipmentCategoryEditParam) {
        return RestResponse.ok(equipmentCategoryService.updateByParam(equipmentCategoryEditParam));
    }

    /**
     * 根据id删除设备类型
     */
    @ApiOperation(value="根据id删除设备类型")
    @AuditLog(title = "设备类型",desc = "设备类型",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:type:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotNull String id) {
        return RestResponse.ok(equipmentCategoryService.deleteById(id));
    }

    /**
     * 根据id获取设备类型
     */
    @ApiOperation(value = "根据id获取设备类型")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    //@Permission("equipment:type:list")
    public RestResponse<EquipmentCategoryDto> get(@PathVariable  String id) {
        return RestResponse.ok(equipmentCategoryService.getDtoById(id));
    }

    /**
     * 根据id获取层级编码
     */
    @ApiOperation(value = "根据id获取层级编码")
    @PostMapping(value = "/layerCode")
    //@Permission("equipment:type:list")
    public RestResponse<Map<String, String>> fetchLayerCode(@RequestBody List<String> idList) {
        return RestResponse.ok(equipmentCategoryService.fetchLayerCode(idList));
    }

    /**
     * 根据id获取父级设备类型扩展属性
     */
    @ApiOperation(value = "根据id获取父级设备类型扩展属性")
    @GetMapping(value = "/getParentProps")
    //@Permission("equipment:type:list")
    public RestResponse<List<EquipmentCategoryPropDto>> getParentProps(@RequestParam("id")  String id) {
        return RestResponse.ok(equipmentCategoryService.getParentProps(id));
    }

    /**
     * 修改设备类型校准项
     */
    @ApiOperation(value="修改设备类型校准项")
    @AuditLog(title = "修改设备类型校准项",desc = "修改设备类型校准项",businessType = BusinessType.UPDATE)
    @PostMapping("/updateCalibration")
    //@Permission("equipment:type:update")
    public RestResponse<Boolean> updateCalibration(@RequestBody @Valid CategoryCalibrationDto categoryCalibrationDto) {
        return RestResponse.ok(equipmentCategoryService.updateCalibration(categoryCalibrationDto));
    }

    /**
     * 根据id获取设备类型校准项
     */
    @ApiOperation(value = "根据id获取设备类型校准项")
    @GetMapping(value = "/getCalibrationById")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="id",dataType="string",required = true,paramType = "query"),
    })
    //@Permission("equipment:type:list")
    public RestResponse<CategoryCalibrationDto> getCalibrationById(@RequestParam String id) {
        return RestResponse.ok(equipmentCategoryService.getCalibrationById(id));
    }

    /**
     * 根据id获取所有的子类ID集合
     */
    @ApiOperation(value = "根据id获取所有的子类ID集合")
    @GetMapping(value = "/getChildIds/{id}")
    //@Permission("equipment:type:list")
    public RestResponse<List<String>> getChildIds(@PathVariable String id) {
        QueryWrapper<EquipmentCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).or().like("layer_code", id);
        List<EquipmentCategory> list = equipmentCategoryService.list(queryWrapper);
        List<String> ids = list.stream().map(EquipmentCategory::getId).collect(Collectors.toList());
        return RestResponse.ok(ids);
    }

    /**
     * 根据id列表获取所有的子类ID集合
     */
    @ApiOperation(value = "根据id列表获取所有的子类ID集合")
    @PostMapping(value = "/fetchCategoryIdsByCategoryParentIds")
    //@Permission("equipment:type:list")
    public RestResponse<List<String>> fetchCategoryIdsByCategoryParentIds(@RequestBody List<String> idList) {
        List<String> categoryParentIds = equipmentCategoryService.getCategoryIdsByParentId(idList);
        return RestResponse.ok(categoryParentIds);
    }

    /**
     * 初始化公用设备类型数据
     */
    @ApiOperation(value = "初始化公用设备类型数据")
    @GetMapping(value = "/initialization")
    //@Permission("equipment:type:list")
    public RestResponse<Boolean> initialization(@RequestParam("tenantId") String tenantId) {
        return RestResponse.ok(equipmentCategoryService.initialization(tenantId));
    }

    /**
     * 获取设备类型map
     */
    @ApiOperation("获取设备类型map")
    @GetMapping("/getCategoryStatistics")
    public RestResponse<Map<String, CategoryStatisticsDto>> getCategoryStatistics(){
        return RestResponse.ok(equipmentCategoryService.getCategoryStatistics());
    }

    /**
     * 获取设备中心字段在数据库中个条数
     * @param tableName
     * @param columnName
     * @param value
     * @return
     */
    @GetMapping("/getCount")
    @ApiOperation("获取设备中心字段在数据库中个条数")
    public RestResponse<Integer> getCount(@RequestParam("tableName")String tableName,
                                      @RequestParam("columnName")String columnName,
                                      @RequestParam("value")String value) {
        return RestResponse.ok(equipmentCategoryService.getCount(tableName, columnName, value));
    }

    /**
     * 获取所有设备类型树
     * @return
     */
    @GetMapping("/getAllNode")
    @ApiOperation("获取所有设备类型树")
    public RestResponse<List<EagerTreeNodeDto>> getAllNode() {
        return RestResponse.ok(equipmentCategoryService.getAllNode());
    }

    /**
     * 获取类型名称模糊查询code
     * @return
     */
    @GetMapping("/getCodesByName")
    @ApiOperation("获取类型名称模糊查询code")
    public RestResponse<List<String>> getCodesByName(String name) {
        return RestResponse.ok(equipmentCategoryService.getCodesByName(name));
    }

    /**
     * Excel导入类型(单层级形式)
     */
    @ApiOperation(value = "Excel导入类型")
    @AuditLog(title = "类型",desc = "Excel导入类型",businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("fault:category:import")
    public RestResponse<Boolean> excelImport(@RequestPart("file") MultipartFile file){
        ExcelUtils<EquipmentCategoryExcel> util = new ExcelUtils<>(EquipmentCategoryExcel.class);
        List<EquipmentCategoryExcel> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)){
            return RestResponse.failed();
        }
        return RestResponse.ok(equipmentCategoryService.excelImport(rows));
    }

    /**
     * Excel导入类型(父节点/子节点形式)
     */
    @ApiOperation(value = "Excel导入类型(名称为全路径)")
    @AuditLog(title = "类型",desc = "Excel导入类型",businessType = BusinessType.INSERT)
    @PostMapping("/importCW")
    //@Permission("fault:category:import")
    public RestResponse<Boolean> importCW(@RequestPart("file") MultipartFile file){

        ExcelUtils<CategoryCWExcel> util = new ExcelUtils<>(CategoryCWExcel.class);
        List<CategoryCWExcel> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)){
            return RestResponse.failed();
        }
        return RestResponse.ok(equipmentCategoryService.excelImportCW(rows));
    }


    /**
     * 根据id集合获取设备类型名称集合
     */
    @ApiOperation("根据id集合获取设备类型名称集合")
    @PostMapping("/getMapByIds")
    public RestResponse<Map<String, String>> getMapByIds(@RequestBody String[] categoryIds){
        return RestResponse.ok(equipmentCategoryService.getMapByIds(categoryIds));
    }

    /**
     * 编辑备件类型结构表
     */
    @ApiOperation("新增/编辑设备类型结构")
    @AuditLog(title = "设备类型结构表",desc = "新增/编辑设备类型结构表",businessType = BusinessType.INSERT)
    @PostMapping("editStructure")
    public RestResponse<String> editStructure(@RequestBody CategoryStructureDto editParam) {
        return RestResponse.ok(categoryStructureService.editStructure(editParam));
    }

    /**
     * 获取设备类型结构
     */
    @ApiOperation("获取设备类型结构")
    @GetMapping("/getStructureByCategoryId")
    public RestResponse<List<CategoryStructureDto>> getStructureByCategoryId(@RequestParam String categoryId,@RequestParam(value = "filter",defaultValue = "false")Boolean filter){
        return RestResponse.ok(categoryStructureService.getListByCategoryId(categoryId,filter));
    }

    /**
     * 根据id删除设备类型结构
     */
    @ApiOperation(value="根据id删除设备类型结构")
    @AuditLog(title = "设备类型",desc = "设备类型结构删除",businessType = BusinessType.DELETE)
    @DeleteMapping("/delStructure/{id}")
    //@Permission("equipment:type:delete")
    public RestResponse<Boolean> delStructure(@PathVariable("id") @NotNull String id) {
        categoryStructureService.remove(new QueryWrapper<EquipmentCategoryStructure>().lambda().eq(EquipmentCategoryStructure::getParentId,id));
        return RestResponse.ok(categoryStructureService.removeById(id));
    }

    @ApiOperation("同步设备类型结构")
    @PostMapping("/synCategoryStructure")
    public RestResponse<Boolean> synCategoryStructure(@RequestBody SynCategoryStructureDto dto) {
        return RestResponse.ok(structureService.synCategoryStructure(dto));
    }

    @ApiOperation("重新计算部件排序")
    @PostMapping("/updateStructureSort")
    public RestResponse<Boolean> updateStructureSort(@RequestBody List<StructureSortDto> dtos) {
        return RestResponse.ok(categoryStructureService.updateStructureSort(dtos));
    }

    @ApiOperation("获取所有设备类型")
    @GetMapping("/getAllCategory")
    public RestResponse<List<CategoryInfoDto>> getAllCategory(){
        return RestResponse.ok(equipmentCategoryService.getAllCategory(null));
    }

    @ApiOperation("根据ids获取dtos(包含子节点)")
    @PostMapping("/getDtosIncludeChild")
    public RestResponse<List<CategoryInfoDto>> getDtosIncludeChild(@RequestBody(required = false) String[] categoryIds){
        return RestResponse.ok(equipmentCategoryService.getDtosIncludeChild(null != categoryIds ? Arrays.asList(categoryIds) : null));
    }

    @ApiOperation("根据设备类型名称全路径")
    @GetMapping("/getCategoryAllNameMap")
    public RestResponse<Map<String, String>> getCategoryAllNameMap(){
        return RestResponse.ok(equipmentCategoryService.getCategoryAllNameMap());
    }

    @ApiOperation("开关设备状态参数")
    @GetMapping("/enabledDefaultStatusParam")
    RestResponse<Boolean> enabledDefaultStatusParam(@RequestParam String categoryId, @RequestParam Boolean enabled){
        return RestResponse.ok(equipmentCategoryService.enabledDefaultStatusParam(categoryId, enabled));
    }

    @ApiOperation(value = "根据id获取设备类型基础数据")
    @GetMapping(value = "/getSummaryDto")
    public RestResponse<CategorySummaryDto> getSummaryDto(@RequestParam  String id) {
        return RestResponse.ok(equipmentCategoryService.getSummaryDto(id));
    }

    @ApiOperation(value = "修改设备类型预置参数")
    @PostMapping(value = "/updatePresetParam")
    public RestResponse<Boolean> updatePresetParam(@RequestBody CategorySummaryDto dto) {
        return RestResponse.ok(equipmentCategoryService.updatePresetParam(dto));
    }

}
