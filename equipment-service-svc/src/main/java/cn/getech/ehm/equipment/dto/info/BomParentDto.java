package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 父级信息
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "BomParentDto", description = "父级信息")
public class BomParentDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型(1位置11主设备12子设备)")
    private Integer type;

    @ApiModelProperty(value = "父节点id", hidden = true)
    private String parentId;

    @ApiModelProperty(value = "名称")
    private String name;
}