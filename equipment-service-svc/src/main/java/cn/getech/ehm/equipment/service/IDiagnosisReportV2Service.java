package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReport;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.io.File;
import java.util.List;

/**
 * 诊断报告v2 服务类
 */
public interface IDiagnosisReportV2Service extends IBaseService<EquipmentDiagnosisReport> {

        /**
         * 分页查询，返回Dto
         * @return
         */
        PageResult<DiagnosisReportV2ListDto> pageDto(DiagnosisReportV2QueryParam queryParam);

        /**
         * 保存
         * @return
         */
        String saveByParam(DiagnosisReportV2AddParam addParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        DiagnosisReportV2Dto getDtoById(String id);

        /**
         * 更新
         */
        boolean updateByParam(DiagnosisReportV2EditParam editParam);

        /**
         * 导出word
         * @param id
         * @return
         */
        File exportWord(String id, String url);

        /**
         * 删除
         * @return
         */
        Boolean deleteByIds(List<String> ids);

        /**
         * 智能诊断报告生成
         * @param dtos
         * @return
         */
        Boolean saveAuto(List<DiagnosisReportAutoDto> dtos);
}