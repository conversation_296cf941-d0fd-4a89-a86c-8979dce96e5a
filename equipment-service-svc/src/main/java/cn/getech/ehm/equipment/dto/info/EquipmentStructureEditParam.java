package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备部件 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentStructureEditParam", description = "设备部件编辑参数")
public class EquipmentStructureEditParam extends ApiParam {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "零部件id")
    private String sparePartsCategoryId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否监测")
    private Boolean monitored;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    @ApiModelProperty(value = "基础库类型")
    private String basicLibraryId;

    private String partId;

}
