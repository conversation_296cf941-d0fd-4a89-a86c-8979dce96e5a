package cn.getech.ehm.equipment.dto.change;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备异动表 新增参数
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChangeNew新增", description = "EquipmentChangeNew新增参数")
public class EquipmentChangeNewAddParam extends ApiParam {
    @ApiModelProperty("设备ids")
    private List<String> equipmentIds;

    @ApiModelProperty("设备异动类型0-位置,1-状态")
    private Integer type;

    @ApiModelProperty("目标值")
    private String targetValue;

    @ApiModelProperty("位置异动目标父节点id")
    private String targetParentId;

    @ApiModelProperty("位置异动目标父节点类型")
    private Integer targetParentType;
}