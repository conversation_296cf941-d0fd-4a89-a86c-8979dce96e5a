package cn.getech.ehm.equipment.dto.app;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 搜索条目返回对象
 *
 * <AUTHOR>
 * @date 2020-12-02
 */
@Data
@ApiModel(value = "SearchItemDto", description = "搜索条目返回数据模型")
public class SearchItemDto {

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "名称。")
    @Excel(name="名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "数据类型。1-新闻，2-课程，3-文档，4-订单，5-设备")
    @Excel(name="数据类型",cellType = Excel.ColumnType.STRING )
    private Integer type;

}