package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备报警趋势统计
 * <AUTHOR>
 * @date 2022-11-09
 */
@Data
@ApiModel(value = "EquipmentWarnTrendCountDto", description = "设备报警趋势统计")
public class EquipmentWarnTrendCountDto {

    @ApiModelProperty(value = "报警名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "总数")
    private Integer all;

    @ApiModelProperty(value = "颜色")
    private String color;
}