package cn.getech.ehm.equipment.dto.iot;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备日志查询数据模型
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "IotLogQueryParam", description = "设备日志查询数据模型")
public class IotLogQueryParam {

    @ApiModelProperty(value = "开始日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "结束日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "设备id", required = true)
    @NotBlank(message = "设备id不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "结束时间，13位时间戳，不传则表示取服务器当前时间", hidden = true)
    private Long endTs;

    @ApiModelProperty(value = "查询周期，单位秒(结束时间前移)", hidden = true)
    private Long period;

    @ApiModelProperty(value = "参数名称列表")
    private String[] paramNames;

    @ApiModelProperty(value = "iot设备id", hidden = true)
    private String deviceId;

    @ApiModelProperty(value = "token", hidden = true)
    private String accessToken;

}