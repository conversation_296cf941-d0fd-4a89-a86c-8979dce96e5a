package cn.getech.ehm.equipment.dto.category;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备类型 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentType编辑", description = "设备类型编辑参数")
public class EquipmentCategoryEditParam extends ApiParam {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "上级节点")
    @NotBlank(message = "上级节点不能为空")
    private String parentId;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "是否需要校验")
    private Boolean checked;

    @ApiModelProperty(value = "外观图片Ids(多张，逗号隔开)")
    private String picIds;

    @ApiModelProperty(value = "附件Ids(多个逗号隔开)")
    private String docIds;

    @ApiModelProperty(value = "扩展属性集合")
    private List<EquipmentCategoryPropDto> propDtos;

    @ApiModelProperty(value = "类型0其他1仪器仪表", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "OEE计算")
    private Boolean oeeOpen;

    @ApiModelProperty(value = "设备状态开关")
    private Boolean statusParamOpen;

}
