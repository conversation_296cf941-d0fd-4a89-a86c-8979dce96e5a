package cn.getech.ehm.equipment.dto.change;

import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;
import java.util.List;

/**
 * 设备异动表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-25
 */
@Data
@ApiModel(value = "EquipmentChangeDto", description = "设备异动表返回数据模型")
public class EquipmentChangeDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "异动类型(0领用,1移转 2借用)")
    private Integer type;

    @ApiModelProperty(value = "异动地点Id")
    private String locationId;

    @ApiModelProperty(value = "异动地点")
    private String locationName;

    @ApiModelProperty(value = "责任人Id")
    private String dutyId;

    @ApiModelProperty(value = "责任人姓名")
    private String dutyName;

    @ApiModelProperty(value = "责任人部门")
    private String dutyDept;

    @ApiModelProperty(value = "责任人联系方式")
    private String dutyContact;

    @ApiModelProperty(value = "异动状态")
    private Integer status;

    @ApiModelProperty(value = "异动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    @ApiModelProperty(value = "资源列表")
    List<EquipmentInfoDto> equipmentInfoDtoList;

}