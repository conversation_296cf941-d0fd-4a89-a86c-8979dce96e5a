package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.category.CategoryStructureDto;
import cn.getech.ehm.equipment.dto.category.StructureSortDto;
import cn.getech.ehm.equipment.enmu.StructureParamType;
import cn.getech.ehm.equipment.enums.FeatureParameterType;
import cn.getech.ehm.equipment.handler.CommonGetHandler;
import cn.getech.ehm.iot.dto.SynCategoryStructureDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryDto;
import cn.getech.ehm.equipment.entity.EquipmentStructure;
import cn.getech.ehm.equipment.mapper.EquipmentStructureMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.PartDetailDto;
import cn.getech.ehm.part.dto.PartSearchDto;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备部件服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Slf4j
@Service
public class EquipmentStructureServiceImpl extends BaseServiceImpl<EquipmentStructureMapper, EquipmentStructure> implements IEquipmentStructureService {

    @Autowired
    private EquipmentStructureMapper structureMapper;
    @Autowired
    private PartClient partClient;
    @Autowired
    private IInfoCategoryStructureService categoryStructureService;
    @Autowired
    private IInfoStructureParameterService structureParameterService;
    @Autowired
    private ISparePartsCategoryService sparePartsCategoryService;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private CommonGetHandler commonGetHandler;

    @Override
    public List<EquipmentStructureDto> getListByEquipmentId(String equipmentId,String keyword){
        List<EquipmentStructureDto> equipmentStructureResDtos = structureMapper.getListByEquipmentId(equipmentId,keyword);
        if(CollectionUtils.isNotEmpty(equipmentStructureResDtos)){
            List<String> structureIds = equipmentStructureResDtos.stream().map(EquipmentStructureDto::getId).distinct().collect(Collectors.toList());
            List<String> partIdList = equipmentStructureResDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPartId())).map(item -> item.getPartId()).collect(Collectors.toList());
            List<String> hasParamStructureIds = structureParameterService.hasParamStructureIds(structureIds);
            for(EquipmentStructureDto equipmentStructureDto : equipmentStructureResDtos){
                if(hasParamStructureIds.contains(equipmentStructureDto.getId())){
                    equipmentStructureDto.setHasParam(true);
                }else{
                    equipmentStructureDto.setHasParam(false);
                }
            }
            commonGetHandler.completePartInfo(equipmentStructureResDtos,partIdList);
            //第一级部件
            List<EquipmentStructureDto> structureDtos = equipmentStructureResDtos.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
            //仅搜索到部件，则直接限制部件
            if (CollectionUtils.isNotEmpty(structureDtos)){
                //第二级部件
                Map<String, List<EquipmentStructureDto>> childMap = equipmentStructureResDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.groupingBy(EquipmentStructureDto::getParentId));
                for(EquipmentStructureDto structureDto : structureDtos){
                    structureDto.setLevel(1);
                    structureDto.setChildren(childMap.get(structureDto.getId()));
                }
            }else {
                structureDtos = equipmentStructureResDtos.stream().collect(Collectors.toList());
            }

            return structureDtos;
        }

        return new ArrayList<>();
    }

    @Override
    public List<EquipmentStructureDto> getListByStructureId(List<String> structureIds){
        List<EquipmentStructureDto> equipmentStructureResDtos = structureMapper.getListByStructureId(structureIds);
        if(CollectionUtils.isNotEmpty(equipmentStructureResDtos)){
            List<String> hasParamStructureIds = structureParameterService.hasParamStructureIds(structureIds);
            for(EquipmentStructureDto equipmentStructureDto : equipmentStructureResDtos){
                if(hasParamStructureIds.contains(equipmentStructureDto.getId())){
                    equipmentStructureDto.setHasParam(true);
                }else{
                    equipmentStructureDto.setHasParam(false);
                }
            }
            //第一级部件
            List<EquipmentStructureDto> structureDtos = equipmentStructureResDtos.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
            //第二级部件
            Map<String, List<EquipmentStructureDto>> childMap = equipmentStructureResDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.groupingBy(EquipmentStructureDto::getParentId));
            for(EquipmentStructureDto structureDto : structureDtos){
                structureDto.setLevel(1);
                structureDto.setChildren(childMap.get(structureDto.getId()));
            }
            return structureDtos;
        }

        return new ArrayList<>();
    }

    @Override
    public List<FeatureParameterDto> getFeatureParameter(String equipmentId){
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructure::getEquipmentId, equipmentId);
        wrapper.eq(EquipmentStructure::getMonitored, true);
        wrapper.orderByAsc(EquipmentStructure::getSort);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getParentId, EquipmentStructure::getName, EquipmentStructure::getType);
        List<EquipmentStructure> equipmentStructures = structureMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentStructures)){
            List<FeatureParameterDto> featureParameterDtos = new ArrayList<>();
            List<String> structureIds = equipmentStructures.stream().map(EquipmentStructure::getId).distinct().collect(Collectors.toList());
            Map<String, List<EquipmentStructureParameterDto>> paramMap = structureParameterService.getStructureParameterMap(structureIds, new Integer[]{StructureParamType.FEATURE.getValue()});
            for(EquipmentStructure equipmentStructure : equipmentStructures){
                List<EquipmentStructureParameterDto> parameterDtos = paramMap.get(equipmentStructure.getId());
                if(CollectionUtils.isNotEmpty(parameterDtos)){
                    FeatureParameterDto featureParameterDto = CopyDataUtil.copyObject(equipmentStructure, FeatureParameterDto.class);
                    //该部件有配置了特种参数的参数
                    String params = "";
                    for(EquipmentStructureParameterDto parameterDto : parameterDtos){
                        if(null != parameterDto.getValue()){
                            params += parameterDto.getCode() + StringPool.COLON + parameterDto.getValue() + StringPool.SEMICOLON;
                        }
                    }
                    if(StringUtils.isNotBlank(params)){
                        featureParameterDto.setParams(params);
                        featureParameterDtos.add(featureParameterDto);
                    }
                }
            }
            return featureParameterDtos;
        }
        return null;
    }

    @Override
    public List<List<String>> getFeatureParameterFFT(String equipmentId, Double rpm){
        List<FeatureParameterDto> featureParameterDtos = getFeatureParameter(equipmentId);
        if(CollectionUtils.isNotEmpty(featureParameterDtos)){
            RestResponse<String> restResponse = parameterClient.getCharFre(JSONObject.toJSONString(featureParameterDtos), rpm, equipmentId);
            if(restResponse.isOk()){
                List<FeatureParameterDto> fftFeatureDtos = JSONObject.parseArray(restResponse.getData(), FeatureParameterDto.class);
                Map<String, FeatureParameterDto> fftFeatureMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(fftFeatureDtos)){
                    fftFeatureMap = fftFeatureDtos.stream().collect(Collectors.toMap(FeatureParameterDto::getId, v -> v, (v1,v2) -> v1));
                }
                return buildList(equipmentId, fftFeatureMap, 2);
            }

        }
        return new ArrayList<>();
    }



    @Override
    public List<List<String>> getFeatureParameterOS(String equipmentId){
        return buildList(equipmentId, null, 1);
    }

    private List<List<String>> buildList(String equipmentId, Map<String, FeatureParameterDto> fftFeatureMap, Integer type){
        List<List<String>> list = new ArrayList<>();
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructure::getEquipmentId, equipmentId);
        wrapper.eq(EquipmentStructure::getMonitored, true);
        wrapper.orderByAsc(EquipmentStructure::getSort);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getParentId, EquipmentStructure::getName, EquipmentStructure::getType);
        List<EquipmentStructure> equipmentStructures = structureMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentStructures)){
            List<String> structureIds = equipmentStructures.stream().map(EquipmentStructure::getId).distinct().collect(Collectors.toList());
            Map<String, List<EquipmentStructureParameterDto>> paramMap = structureParameterService.getStructureParameterMap(structureIds, new Integer[]{StructureParamType.FEATURE.getValue()});

            List<EquipmentStructure> parents = equipmentStructures.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
            Map<String, List<EquipmentStructure>> childrenMap = equipmentStructures.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.groupingBy(EquipmentStructure::getParentId));
            for(EquipmentStructure parent : parents){
                //构造一级部件/零件对应的参数，拥有特征参数则返回对应数据
                List<EquipmentStructureParameterDto> paramDtos = paramMap.get(parent.getId());
                FeatureParameterDto featureParameterDto = null;
                if(type == 2){
                    //频率的需要获取iot算法转换后的值
                    featureParameterDto = fftFeatureMap.get(parent.getId());
                }
                List<List<String>> parentParamList = this.buildStructureParamStr(paramDtos, featureParameterDto, type);

                //二级零件
                List<EquipmentStructure> children = childrenMap.get(parent.getId());
                //构造二级零件
                List<List<String>> childrenParamList = new ArrayList<>();
                //有特征参数的子零件个数
                int childCount = 0;
                if(CollectionUtils.isNotEmpty(children)){
                    for(EquipmentStructure child : children){
                        List<EquipmentStructureParameterDto> childParamDtos = paramMap.get(child.getId());
                        if(type == 2) {
                            //频率的需要获取iot算法转换后的值
                            featureParameterDto = fftFeatureMap.get(child.getId());
                        }
                        List<List<String>> childParamList = this.buildStructureParamStr(childParamDtos, featureParameterDto, type);
                        if(CollectionUtils.isNotEmpty(childParamList)){
                            //子零件有用特征参数
                            List<String> childName = new ArrayList<>();
                            childName.add("零件名称");
                            childName.add(child.getName());
                            childrenParamList.add(childName);
                            childrenParamList.addAll(childParamList);
                            childCount++;
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(parentParamList) || childCount > 0){
                    //自身或者子节点有特征参数
                    List<String> structureName = new ArrayList<>();
                    if(parent.getType() == 1){
                        structureName.add("部件名称");
                    }else{
                        structureName.add("零件名称");
                    }
                    structureName.add(parent.getName());
                    if(parent.getType() == 1){
                        structureName.add("零件数");
                        structureName.add(childCount + "");
                    }
                    list.add(structureName);

                    if(CollectionUtils.isNotEmpty(parentParamList)){
                        list.addAll(parentParamList);
                    }
                    if(childCount > 0){
                        list.addAll(childrenParamList);
                    }
                }
            }

        }
        return list;
    }

    private List<List<String>> buildStructureParamStr(List<EquipmentStructureParameterDto> parameterDtos, FeatureParameterDto fftFeatureDto, Integer type){
        List<List<String>> paramList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(parameterDtos)) {
            //该部件有配置了特种参数的参数
            List<String> nameList = new ArrayList<>();
            nameList.add("参数名称");
            List<String> valueList = new ArrayList<>();
            valueList.add("值");

            //频率构造编码，value键值对
            Map<String, String> valueMap = new HashMap<>();
            if(type == 2 && null != fftFeatureDto && StringUtils.isNotBlank(fftFeatureDto.getParams())){
                //param格式为  code:value;
                String params = fftFeatureDto.getParams();
                String[] paramStrs = params.split(StringPool.SEMICOLON);
                for(String paramStr : paramStrs){
                    String[] strs = paramStr.split(StringPool.COLON);
                    valueMap.put(strs[0], strs[1]);
                }
            }
            for (EquipmentStructureParameterDto parameterDto : parameterDtos) {
                nameList.add(parameterDto.getName());
                if(type == 1) {
                    if (null != parameterDto.getValue()) {
                        valueList.add(parameterDto.getValue().toString());
                    } else {
                        valueList.add("");
                    }
                }else if(type == 2){
                    //频率需要转换
                    String value = valueMap.get(parameterDto.getCode());
                    if(StringUtils.isBlank(value) || value.equals("0")){
                        valueList.add("");
                    }else{
                        valueList.add(value);
                    }
                }
            }
            paramList.add(nameList);
            paramList.add(valueList);
        }
        return paramList;
    }

    @Override
    public List<EquipmentStructureDetailDto> getRootStructureDetail(String equipmentId){
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructure::getEquipmentId, equipmentId);
        wrapper.eq(EquipmentStructure::getType, StaticValue.ONE);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getName, EquipmentStructure::getType);
        wrapper.orderByAsc(EquipmentStructure::getSort);
        return CopyDataUtil.copyList(structureMapper.selectList(wrapper), EquipmentStructureDetailDto.class);
    }

    @Override
    public Map<String, String> getStructureMapByIds(String[] structureIds){
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructure::getId, structureIds);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getName);
        return structureMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentStructure::getId, EquipmentStructure::getName, (v1,v2) -> v1));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String addStructure(EquipmentStructureAddParam addParam){
        EquipmentStructure equipmentStructure = CopyDataUtil.copyObject(addParam, EquipmentStructure.class);
        equipmentStructure.setSort(structureMapper.getMaxSort(addParam.getEquipmentId()) + 1);
        equipmentStructure.setSourceType(StaticValue.ONE);
        String id = UUID.randomUUID().toString().replace("-","");
        equipmentStructure.setId(id);
        if(StringUtils.isNotBlank(addParam.getSparePartsCategoryId())){
            SparePartsCategoryDto sparePartsCategoryDto = sparePartsCategoryService.getDtoById(addParam.getSparePartsCategoryId());
            equipmentStructure.setBasicLibraryId(sparePartsCategoryDto.getBasicLibraryId());
            structureParameterService.saveByCategory(id, sparePartsCategoryDto.getId() , equipmentStructure.getRelationId());
        }
        save(equipmentStructure);
        return id;
    }

    @Override
    public Boolean editStructure(EquipmentStructureEditParam editParam) {
        EquipmentStructure old = structureMapper.selectById(editParam.getId());
        EquipmentStructure equipmentStructure = CopyDataUtil.copyObject(editParam, EquipmentStructure.class);
        if(StringUtils.isBlank(editParam.getSparePartsCategoryId())){
            equipmentStructure.setSparePartsCategoryId("");
            equipmentStructure.setBasicLibraryId("");
            equipmentStructure.setRelationId("");
            structureParameterService.deleteByStructureIds(Collections.singletonList(equipmentStructure.getId()));
        } else if(!editParam.getSparePartsCategoryId().equals(old.getSparePartsCategoryId())){
            structureParameterService.deleteByStructureIds(Collections.singletonList(equipmentStructure.getId()));
            //零部件类型修改，重新构造参数、变量
            SparePartsCategoryDto sparePartsCategoryDto = sparePartsCategoryService.getDtoById(editParam.getSparePartsCategoryId());
            if(null != sparePartsCategoryDto) {
                equipmentStructure.setBasicLibraryId(sparePartsCategoryDto.getBasicLibraryId());
                structureParameterService.saveByCategory(equipmentStructure.getId(), sparePartsCategoryDto.getId(), equipmentStructure.getRelationId());
            }
        }else if(StringUtils.isNotBlank(editParam.getRelationId()) && !editParam.getRelationId().equals(old.getRelationId())){
            //型号重新编辑，构造对应参数的值
            structureParameterService.updateBaseParam(editParam.getId(), editParam.getRelationId());
        }

        return updateById(equipmentStructure);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteStructure(String id){
        RestResponse<List<String>> restResponse = parameterClient.usedStructureIds(new String[]{id});
        if(restResponse.isOk() && restResponse.getData().size() > 0){
            throw new GlobalServiceException(GlobalResultMessage.of("该零部件已被测点引用，不能删除"));
        }
        List<String> structureIds = new ArrayList<>();
        structureIds.add(id);
        structureParameterService.deleteByStructureIds(structureIds);
        this.remove(new QueryWrapper<EquipmentStructure>().lambda().eq(EquipmentStructure::getParentId,id));
        return removeById(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteByEquipmentIds(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructure:: getEquipmentId, equipmentIds);
        wrapper.eq(EquipmentStructure::getSourceType, StaticValue.TWO);
        wrapper.select(EquipmentStructure::getId);
        List<String> structureIds = structureMapper.selectList(wrapper).stream().map(EquipmentStructure::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(structureIds)) {
            structureMapper.deleteBatchIds(structureIds);
            structureParameterService.deleteByStructureIds(structureIds);
        }
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean quoteCategoryStructure(String equipmentId,String categoryId){
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructure::getEquipmentId, equipmentId);
        wrapper.eq(EquipmentStructure::getSourceType, StaticValue.TWO);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getName);
        List<EquipmentStructure> oldStructures = structureMapper.selectList(wrapper);
        //校验部件是否被测点引用
        if(CollectionUtils.isNotEmpty(oldStructures)){
            String[] structureIds = oldStructures.stream().map(EquipmentStructure::getId).toArray(String[]::new);
            RestResponse<List<String>> restResponse = parameterClient.usedStructureIds(structureIds);
            if(restResponse.isOk() && restResponse.getData().size() > 0){
                List<String> structureNames = oldStructures.stream().filter(dto -> restResponse.getData().contains(dto.getId())).map(EquipmentStructure::getName).distinct().collect(Collectors.toList());
                throw new GlobalServiceException(GlobalResultMessage.of(structureNames + "已被测点引用，不能删除"));
            }
        }

        this.deleteByEquipmentIds(Collections.singletonList(equipmentId));

        if(StringUtils.isNotBlank(categoryId)) {
            List<CategoryStructureDto> categoryStructureDtos = categoryStructureService.getListByCategoryId(categoryId);
            if(CollectionUtils.isNotEmpty(categoryStructureDtos)){
                List<EquipmentStructure> equipmentStructures = this.copyCategoryStructures(categoryStructureDtos, equipmentId);
                saveBatch(equipmentStructures);
            }
        }
        return true;
    }

    private List<EquipmentStructure> copyCategoryStructures(List<CategoryStructureDto> categoryStructureDtos, String equipmentId){
        List<EquipmentStructure> equipmentStructures = new ArrayList<>();
        int sort = structureMapper.getMaxSort(equipmentId) + 1;
        for(CategoryStructureDto categoryStructureDto : categoryStructureDtos){
            EquipmentStructure equipmentStructure = new EquipmentStructure();
            String id = UUID.randomUUID().toString().replace("-","");
            equipmentStructure.setId(id);
            equipmentStructure.setEquipmentId(equipmentId);
            equipmentStructure.setSourceType(StaticValue.TWO);
            equipmentStructure.setType(categoryStructureDto.getType());
            equipmentStructure.setName(categoryStructureDto.getName());
            equipmentStructure.setSparePartsCategoryId(categoryStructureDto.getSparePartsCategoryId());
            equipmentStructure.setBasicLibraryId(categoryStructureDto.getBasicLibraryId());
            equipmentStructure.setSourceId(categoryStructureDto.getId());
            equipmentStructure.setPartId(categoryStructureDto.getPartId());
            //同步零部件参数，可能需要优化
            structureParameterService.saveByCategory(id, categoryStructureDto.getSparePartsCategoryId(), null);
            equipmentStructure.setSort(sort++);
            equipmentStructures.add(equipmentStructure);
            List<CategoryStructureDto> children = categoryStructureDto.getChildren();
            if(CollectionUtils.isNotEmpty(children)){
                for(CategoryStructureDto child : children){
                    EquipmentStructure childStructure = new EquipmentStructure();
                    String childStructureId = UUID.randomUUID().toString().replace("-","");
                    childStructure.setId(childStructureId);
                    childStructure.setEquipmentId(equipmentId);
                    childStructure.setSourceType(StaticValue.TWO);
                    childStructure.setType(child.getType());
                    childStructure.setName(child.getName());
                    childStructure.setSparePartsCategoryId(child.getSparePartsCategoryId());
                    childStructure.setBasicLibraryId(child.getBasicLibraryId());
                    childStructure.setSourceId(child.getId());
                    childStructure.setPartId(child.getPartId());
                    structureParameterService.saveByCategory(childStructureId, child.getSparePartsCategoryId(), null);
                    childStructure.setParentId(id);
                    childStructure.setSort(sort++);
                    equipmentStructures.add(childStructure);
                }
            }
        }
        return equipmentStructures;
    }

    @Override
    @Transactional
    public Boolean synCategoryStructure(SynCategoryStructureDto dto){
        List<EquipmentStructure> equipmentStructures = new ArrayList<>();

        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructure::getEquipmentId, dto.getEquipmentIds());
        wrapper.eq(EquipmentStructure::getSourceType, StaticValue.TWO);
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getName);
        List<EquipmentStructure> oldStructures = structureMapper.selectList(wrapper);
        //校验部件是否被测点引用
        if(CollectionUtils.isNotEmpty(oldStructures)){
            List<String> structureIds = oldStructures.stream().map(EquipmentStructure::getId).collect(Collectors.toList());
            //清空测点参数引用的部件id
            dto.setOldStructureIds(structureIds);
            parameterClient.cleanStructureIds(dto);
        }
        this.deleteByEquipmentIds(dto.getEquipmentIds());

        List<CategoryStructureDto> categoryStructureDtos = categoryStructureService.getListByCategoryId(dto.getCategoryId());
        if(CollectionUtils.isNotEmpty(categoryStructureDtos)){
            for(String equipmentId : dto.getEquipmentIds()) {
                equipmentStructures.addAll(this.copyCategoryStructures(categoryStructureDtos, equipmentId));
            }

        }
        if(CollectionUtils.isNotEmpty(equipmentStructures)){
            saveBatch(equipmentStructures);
        }
        return true;
    }

    @Override
    public List<EquipmentTreeDto> paramStructureTree(RelSearchDto relSearchDto){
        LambdaQueryWrapper<EquipmentStructure> parentWrapper = Wrappers.lambdaQuery();
        parentWrapper.in(EquipmentStructure::getId, relSearchDto.getStructureIds());
        parentWrapper.select(EquipmentStructure::getId,EquipmentStructure::getParentId);
        List<EquipmentStructure> parentStructureDtos = structureMapper.selectList(parentWrapper);
        List<String> parentStructureIds = parentStructureDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).map(EquipmentStructure::getParentId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(parentStructureIds)){
            List<String> hasParamStructureIds = relSearchDto.getStructureIds();
            hasParamStructureIds.addAll(parentStructureIds);
            relSearchDto.setStructureIds(hasParamStructureIds);
        }

        //参数监控选中的参数对应的上级设备ids
        List<String> spreadIds = new ArrayList<>();
        if(StringUtils.isNotBlank(relSearchDto.getCheckStructureId())) {
            //选中部件，获取父级部件id，用于选中
            spreadIds.add(relSearchDto.getCheckStructureId());
            EquipmentStructure equipmentStructure = structureMapper.selectById(relSearchDto.getCheckStructureId());
            if(null != equipmentStructure && StringUtils.isNotBlank(equipmentStructure.getParentId())){
                spreadIds.add(equipmentStructure.getParentId());
            }
        }

        List<EquipmentTreeDto> equipmentTreeDtos = new ArrayList<>();
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStructure::getMonitored, true);
        wrapper.in(EquipmentStructure::getId, relSearchDto.getStructureIds());
        if(relSearchDto.getParentType() == StaticValue.ONE){
            wrapper.eq(EquipmentStructure::getEquipmentId, relSearchDto.getParentId());
        }else if(relSearchDto.getParentType() == StaticValue.TWO){
            wrapper.eq(EquipmentStructure::getParentId, relSearchDto.getParentId());
        }
        wrapper.orderByAsc(EquipmentStructure::getSort);
        List<EquipmentStructure> equipmentStructures = structureMapper.selectList(wrapper);

        if(relSearchDto.getParentType() == StaticValue.ONE){
            //设备下部件的parentId为空
            equipmentStructures = structureMapper.selectList(wrapper).stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).collect(Collectors.toList());
        }else if(relSearchDto.getParentType() == StaticValue.TWO){
            equipmentStructures = structureMapper.selectList(wrapper).stream().filter(dto -> StringUtils.isNotBlank(dto.getParentId())).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(equipmentStructures)){
            for( EquipmentStructure entity : equipmentStructures){
                EquipmentTreeDto equipmentTreeDto = new EquipmentTreeDto();
                equipmentTreeDto.setId(entity.getId());
                equipmentTreeDto.setName(entity.getName());
                equipmentTreeDto.setType(StaticValue.TWO);
                equipmentTreeDto.setSort(entity.getSort());
                if(CollectionUtils.isNotEmpty(spreadIds) && spreadIds.contains(entity.getId())){
                    equipmentTreeDto.setSpread(true);
                }
                equipmentTreeDtos.add(equipmentTreeDto);
            }
        }

        return equipmentTreeDtos;
    }

    @Override
    public List<StudioEquipmentBomDto> getStudioStructureBomList(RelSearchDto relSearchDto, List<StudioEquipmentBomDto> studioEquipmentBomDtos){
        LambdaQueryWrapper<EquipmentStructure> parentWrapper = Wrappers.lambdaQuery();
        parentWrapper.in(EquipmentStructure::getId, relSearchDto.getStructureIds());
        parentWrapper.in(EquipmentStructure::getEquipmentId, relSearchDto.getEquipmentIds());
        parentWrapper.select(EquipmentStructure::getId,EquipmentStructure::getParentId);
        List<EquipmentStructure> parentStructureDtos = structureMapper.selectList(parentWrapper);
        if(CollectionUtils.isNotEmpty(parentStructureDtos)){
            List<String> parentStructureIds = parentStructureDtos.stream().filter(dto -> StringUtils.isBlank(dto.getParentId())).map(EquipmentStructure::getParentId).collect(Collectors.toList());
            List<String> structureIds = parentStructureDtos.stream().map(EquipmentStructure::getId).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(parentStructureIds)){
                structureIds.addAll(parentStructureIds);
            }
            LambdaQueryWrapper<EquipmentStructure> bomWrapper = Wrappers.lambdaQuery();
            bomWrapper.in(EquipmentStructure::getId, structureIds.stream().distinct().collect(Collectors.toList()));
            bomWrapper.orderByAsc(EquipmentStructure::getSort);
            List<EquipmentStructure> equipmentStructures = structureMapper.selectList(bomWrapper);
            for(EquipmentStructure entity : equipmentStructures){
                StudioEquipmentBomDto studioEquipmentBomDto = new StudioEquipmentBomDto();
                studioEquipmentBomDto.setId(entity.getId());
                studioEquipmentBomDto.setName(entity.getName());
                if(StringUtils.isBlank(entity.getParentId())){
                    //第一级部件父节点设置为设备id
                    studioEquipmentBomDto.setParentId(entity.getEquipmentId());
                }else{
                    studioEquipmentBomDto.setParentId(entity.getParentId());
                }
                studioEquipmentBomDto.setSort(entity.getSort());
                studioEquipmentBomDto.setType(StaticValue.TWO);
                studioEquipmentBomDtos.add(studioEquipmentBomDto);
            }
        }
        return studioEquipmentBomDtos;
    }

    @Override
    public Map<String, SpecialStructureDto> getSpecialListByIds(String[] structureIds) {
        Map<String, SpecialStructureDto> map = new HashMap<>();
        if (null != structureIds && structureIds.length > 0) {
            List<SpecialStructureDto> specialStructureDtos = structureMapper.getSpecialListByIds(structureIds);
            if (CollectionUtils.isEmpty(specialStructureDtos)) {
                return map;
            }
            List<String> partIds = specialStructureDtos.stream().map(SpecialStructureDto::getPartId).distinct().collect(Collectors.toList());
            List<String> parentPartIds = specialStructureDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getParentPartId())).map(SpecialStructureDto::getParentPartId).distinct().collect(Collectors.toList());
            partIds.addAll(parentPartIds);
            List<String> allPartIds = partIds.stream().distinct().collect(Collectors.toList());
            PartSearchDto searchDto = new PartSearchDto();
            searchDto.setPartIds(allPartIds);
            searchDto.setObtainProp(false);
            RestResponse<Map<String, PartDetailDto>> listRestResponse = partClient.getPartMapByIds(searchDto);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用part-service出错");
            } else {
                Map<String, PartDetailDto> partMap = listRestResponse.getData();
                specialStructureDtos.stream().forEach(dto -> {
                    PartDetailDto partDetailDto = partMap.get(dto.getPartId());
                    if (null != partDetailDto) {
                        dto.setPartName(partDetailDto.getPartName());
                        dto.setPartCode(partDetailDto.getPartCode());
                        dto.setPartSpecification(partDetailDto.getPartSpecification());
                        dto.setPartCategoryId(partDetailDto.getPartCategoryId());
                        dto.setPartCategoryName(partDetailDto.getPartCategoryName());
                        dto.setPartAllCategoryName(partDetailDto.getPartAllCategoryName());
                    }
                    if(StringUtils.isNotBlank(dto.getParentPartId())){
                        PartDetailDto parentPartDto = partMap.get(dto.getParentPartId());
                        if (null != parentPartDto) {
                            dto.setParentName(parentPartDto.getPartName());
                        }
                    }
                });
            }
            map = specialStructureDtos.stream().collect(Collectors.toMap(SpecialStructureDto::getStructureId, v -> v, (v1, v2) -> v1));

        }
        return map;
    }

    @Override
    public Map<String, String> getStructureNameMap(String[] structureIds){
        Map<String, String> map = new HashMap<>();
        if(null != structureIds && structureIds.length > 0){
            LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentStructure::getId, structureIds);
            wrapper.select(EquipmentStructure::getId, EquipmentStructure::getName,EquipmentStructure::getType);
            List<EquipmentStructure> equipmentStructures = structureMapper.selectList(wrapper);
            map = equipmentStructures.stream().collect(Collectors.toMap(EquipmentStructure::getId, item->(item.getName()+(item.getType()==2?"-零件":"")), (v1, v2) -> v1));
        }
        return map;
    }

    @Override
    public Boolean updateStructureSort(List<StructureSortDto> dtos){
        List<EquipmentStructure> structures = new ArrayList<>(dtos.size());
        for(StructureSortDto dto : dtos){
            EquipmentStructure structure = new EquipmentStructure();
            structure.setId(dto.getId());
            structure.setSort(dto.getSort());
            structures.add(structure);
        }
        return updateBatchById(structures);
    }

    @Override
    public Map<String, String> quoteSelectCategoryStructure(QuoteCategoryStructureDto dto){
        Map<String, String> map = new HashMap<>();
        //剔除已经同步的部件
        LambdaQueryWrapper<EquipmentStructure> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentStructure::getSourceId, dto.getCategoryStructureIds());
        wrapper.eq(EquipmentStructure::getEquipmentId, dto.getEquipmentId());
        wrapper.select(EquipmentStructure::getId, EquipmentStructure::getSourceId);
        List<EquipmentStructure> existEquipmentStructures = structureMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(existEquipmentStructures)){
            for(EquipmentStructure equipmentStructure : existEquipmentStructures) {
                dto.getCategoryStructureIds().remove(equipmentStructure.getSourceId());
                map.put(equipmentStructure.getSourceId(), equipmentStructure.getId());
            }
        }
        if(CollectionUtils.isNotEmpty(dto.getCategoryStructureIds())) {
            List<CategoryStructureDto> categoryStructureDtos = categoryStructureService.getSelectList(dto.getCategoryId(), dto.getCategoryStructureIds());
            if (CollectionUtils.isNotEmpty(categoryStructureDtos)) {
                List<EquipmentStructure> equipmentStructures = this.copyCategoryStructures(categoryStructureDtos, dto.getEquipmentId());
                saveBatch(equipmentStructures);
                for(EquipmentStructure equipmentStructure : equipmentStructures){
                    if(equipmentStructure.getType() == StaticValue.ONE){
                        map.put(equipmentStructure.getSourceId(), equipmentStructure.getId());
                    }
                }
            }
        }
        return map;
    }
}
