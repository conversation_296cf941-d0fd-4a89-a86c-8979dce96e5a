package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.info.ParameterVariableValueDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterVariableDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureVariable;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 设备零部件参数变量 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IInfoStructureVariableService extends IBaseService<EquipmentStructureVariable> {

    /**
     * 获取列表
     * @return
     */
    Map<String, List<StructureParameterVariableDto>> getMap(List<String> structureParameterIds);

    /**
     * 根据零部件参数ids删除变量
     * @param structureParameterIds
     * @return
     */
    Boolean deleteByStructureParamIds(List<String> structureParameterIds);

    /**
     * 获取参数变量实时值
     * @param structureParameterId
     * @return
     */
    List<ParameterVariableValueDto> getVariableValue(String structureParameterId);
}