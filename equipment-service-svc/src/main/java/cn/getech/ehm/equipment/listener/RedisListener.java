package cn.getech.ehm.equipment.listener;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.utils.PorosDeviceUtils;
import cn.getech.poros.device.client.DeviceInfoClient;
import cn.getech.poros.device.client.DeviceLocationClient;
import cn.getech.poros.device.client.DeviceTypeClient;
import cn.getech.poros.device.dto.infoclient.InfoDataSyncDto;
import cn.getech.poros.device.dto.locationclient.LocationDataSyncDto;
import cn.getech.poros.device.dto.typeclient.TypeDataSyncDto;
import cn.getech.poros.device.sdk.consumner.StreamEvent;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-01 11:35:13
 **/
@Slf4j
@Component
public class RedisListener {

    @Autowired
    private DeviceLocationClient deviceLocationClient;

    @Autowired
    private DeviceTypeClient deviceTypeClient;

    @Autowired
    private DeviceInfoClient deviceInfoClient;

    @Autowired
    private IEquipmentLocationService iEquipmentLocationService;

    @Autowired
    private IEquipmentCategoryService iEquipmentCategoryService;

    @Autowired
    private IEquipmentInfoService iEquipmentInfoService;

    @EventListener
    public void onEvent(StreamEvent event){
        String range = event.getConsumeMsg().getRange();
        String tenantId = event.getConsumeMsg().getTenantId();
        String operation = event.getConsumeMsg().getOperation();
        //operation: ADD/UPDATE/DEL/IMPORT [操作类型 新增/更新/删除/导入]
        switch (range){
            case "LOCATION":
                if(operation.equals("ADD") || operation.equals("UPDATE")) {
                    long locationVersion = event.getConsumeMsg().getMsgData().getVersion();
                    RestResponse<List<LocationDataSyncDto>> locationRestResponse = deviceLocationClient.getByVersion(locationVersion, tenantId);
                    if (locationRestResponse.isOk()) {
                        List<LocationDataSyncDto> locationDataSyncDtos = locationRestResponse.getData();
                        List<String> tenantIds = locationDataSyncDtos.stream().map(LocationDataSyncDto::getTenantId).distinct().collect(Collectors.toList());
                        Map<String, String> rootMap = iEquipmentLocationService.getRootIdMap(tenantIds);
                        List<EquipmentLocation> equipmentLocations = new ArrayList<>();
                        for (LocationDataSyncDto locationDataSyncDto : locationDataSyncDtos) {
                            if(locationDataSyncDto.getDeleted()){
                                //删除的跳过
                                continue;
                            }
                            String rootId = rootMap.get(locationDataSyncDto.getTenantId());
                            //租户未查询到根节点，需要取租户下新增
                            EquipmentLocation equipmentLocation = PorosDeviceUtils.locationDataConvertor(locationDataSyncDto, StringUtils.isNotBlank(rootId) ? rootId : "porosSynchroRootId");
                            equipmentLocations.add(equipmentLocation);
                        }
                        if(CollectionUtils.isNotEmpty(equipmentLocations)) {
                            Map<String, List<EquipmentLocation>> tenantMap = equipmentLocations.stream().collect(Collectors.groupingBy(EquipmentLocation::getTenantId));
                            for(Map.Entry<String, List<EquipmentLocation>> entity : tenantMap.entrySet()) {
                                List<EquipmentLocation> list = entity.getValue();
                                UserBaseInfo userBaseInfo = new UserBaseInfo();
                                userBaseInfo.setUid(list.get(0).getCreateBy());
                                userBaseInfo.setTenantId(entity.getKey());
                                UserContextHolder.switchContext(userBaseInfo);
                                iEquipmentLocationService.saveOrUpdateBatch(list);
                            }
                        }
                    } else {
                        log.error("获取设备位置数据失败{}", locationRestResponse.getMsg());
                    }
                }else if(operation.equals("DEL")){
                    List<String> codes = event.getConsumeMsg().getMsgData().getCodeList();
                    if(CollectionUtils.isNotEmpty(codes)){
                        iEquipmentLocationService.deleteByCodes(codes, tenantId);
                    }
                }
                iEquipmentLocationService.clearCache();
                break;
            case "TYPE":
                if(operation.equals("ADD") || operation.equals("UPDATE")) {
                    long typeVersion = event.getConsumeMsg().getMsgData().getVersion();
                    RestResponse<List<TypeDataSyncDto>> typeRestResponse = deviceTypeClient.getByVersion(typeVersion, tenantId);
                    if (typeRestResponse.isOk()) {
                        List<TypeDataSyncDto> typeDataSyncDtos = typeRestResponse.getData();
                        List<EquipmentCategory> equipmentCategories = new ArrayList<>();
                        for (TypeDataSyncDto typeDataSyncDto : typeDataSyncDtos) {
                            if(typeDataSyncDto.getDeleted()){
                                continue;
                            }
                            EquipmentCategory equipmentCategory = PorosDeviceUtils.categoryDataConvertor(typeDataSyncDto);
                            equipmentCategories.add(equipmentCategory);
                        }
                        if(CollectionUtils.isNotEmpty(equipmentCategories)) {
                            Map<String, List<EquipmentCategory>> tenantMap = equipmentCategories.stream().collect(Collectors.groupingBy(EquipmentCategory::getTenantId));
                            for(Map.Entry<String, List<EquipmentCategory>> entity : tenantMap.entrySet()) {
                                List<EquipmentCategory> list = entity.getValue();
                                UserBaseInfo userBaseInfo = new UserBaseInfo();
                                userBaseInfo.setUid(list.get(0).getCreateBy());
                                userBaseInfo.setTenantId(entity.getKey());
                                UserContextHolder.switchContext(userBaseInfo);
                                iEquipmentCategoryService.saveOrUpdateBatch(list);
                            }
                        }
                    } else {
                        log.error("获取设备类型数据失败{}", typeRestResponse.getMsg());
                    }
                }else if(operation.equals("DEL")){
                    List<String> codes = event.getConsumeMsg().getMsgData().getCodeList();
                    if(CollectionUtils.isNotEmpty(codes)){
                        iEquipmentCategoryService.deleteByCodes(codes, tenantId);
                    }
                }
                break;
            case "DEVICE":
                if(operation.equals("ADD") || operation.equals("UPDATE")) {
                    long infoVersion = event.getConsumeMsg().getMsgData().getVersion();
                    RestResponse<List<InfoDataSyncDto>> infoRestResponse = deviceInfoClient.getSyncByVersion(infoVersion, tenantId, PorosDeviceUtils.SYSTEM_LABEL);
                    if (infoRestResponse.isOk()) {
                        List<InfoDataSyncDto> infoDataSyncDtos = infoRestResponse.getData();
                        List<EquipmentInfo> equipmentInfos = new ArrayList<>();
                        for(InfoDataSyncDto infoDataSyncDto : infoDataSyncDtos){
                            if(infoDataSyncDto.getDeleted()){
                                continue;
                            }
                            EquipmentInfo equipmentInfo = PorosDeviceUtils.infoDataConvertor(infoDataSyncDto);
                            equipmentInfos.add(equipmentInfo);
                        }
                        if(CollectionUtils.isNotEmpty(equipmentInfos)) {
                            Map<String, List<EquipmentInfo>> tenantMap = equipmentInfos.stream().collect(Collectors.groupingBy(EquipmentInfo::getTenantId));
                            for(Map.Entry<String, List<EquipmentInfo>> entity : tenantMap.entrySet()) {
                                List<EquipmentInfo> list = entity.getValue();
                                UserBaseInfo userBaseInfo = new UserBaseInfo();
                                userBaseInfo.setUid(list.get(0).getCreateBy());
                                userBaseInfo.setTenantId(entity.getKey());
                                UserContextHolder.switchContext(userBaseInfo);
                                iEquipmentInfoService.saveOrUpdateBatch(list);
                            }
                        }
                    } else {
                        log.error("获取设备类型数据失败{}", infoRestResponse.getMsg());
                    }
                }else if(operation.equals("DEL")){
                    List<String> codes = event.getConsumeMsg().getMsgData().getCodeList();
                    if(CollectionUtils.isNotEmpty(codes)){
                        iEquipmentInfoService.deleteByCodes(codes, tenantId);
                    }
                }
                iEquipmentInfoService.clearCache();
                break;
            default:
                log.error("未被支持的消息类型{}", range);
        }
    }
}
