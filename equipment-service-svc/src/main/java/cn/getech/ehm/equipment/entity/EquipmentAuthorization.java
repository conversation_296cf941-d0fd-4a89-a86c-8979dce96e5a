package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 设备授权
 * <AUTHOR>
 * @since 2020-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_authorization")
public class EquipmentAuthorization extends BaseEntity {

    /**
     * 组织CODE
     * 对应人员ID
     */
    @TableField("code")
    private String code;

    /**
     * 组织CODE PATH
     */
    @TableField("code_path")
    private String codePath;

    /**
     * 是否是人员
     */
    @TableField("org_type")
    private boolean staff = false;

    /**
     * 父级CODE
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 授权类型
     * 0：跟随父级部门
     * 1：自定义设备
     */
    @TableField("auth_type")
    private Integer authType = 0;

    /**
     * 授权设备id列表
     */
    @TableField(value = "equipment_ids", jdbcType = JdbcType.LONGVARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] equipmentIds;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
