package cn.getech.ehm.equipment.dto.overview;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;

/**
 * 设备卡片运行历程
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentProgressDto", description = "设备卡片运行历程")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentProgressDto {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "中央时间线")
    private List<EquipmentProgressDetailDto> statusList;

    @ApiModelProperty(value = "状态统计")
    private List<EquipmentProgressDetailDto> statusCount;
}