package cn.getech.ehm.equipment.dto.overview;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * iot参数汇总设备状态
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "ParamStatusDto", description = "iot参数汇总设备状态")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParamStatusDto {

    @ApiModelProperty(value = "状态值")
    private Integer status;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private Integer count = StaticValue.ZERO;

    @ApiModelProperty(value = "颜色")
    private String color;

    @ApiModelProperty(value = "报警等级id")
    private String warnConfigId;
}