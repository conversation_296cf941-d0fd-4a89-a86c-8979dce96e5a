package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.info.ParameterVariableValueDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterVariableDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureVariable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备零部件运行参数变量Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface EquipmentStructureVariableMapper extends BaseMapper<EquipmentStructureVariable> {
    /**
     * 获取运行参数变量列表
     * @return
     */
    List<StructureParameterVariableDto> getList(@Param("structureParameterIds") List<String> structureParameterIds);

    /**
     * 获取参数变量实时值
     * @param structureParameterId
     * @return
     */
    List<ParameterVariableValueDto> getVariableValue(@Param("structureParameterId") String structureParameterId);
}
