package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.bearing.BasicLibraryDto;
import cn.getech.ehm.equipment.entity.bearing.BasicLibrary;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 基础库服务类
 */
public interface IBasicLibraryService extends IBaseService<BasicLibrary> {

    /**
     * 获取基础库类型列表
     * @param type
     * @return
     */
    List<BasicLibraryDto> getList(Integer type);

    /**
     * 新增/编辑
     * @param dto
     * @return
     */
    Boolean edit(BasicLibraryDto dto);

    /**
     * 删除
     * @param id
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 同步geek下基础库
     */
    Boolean initialization(String tenantId);

    /**
     * 获取geek下默认基础库
     * @return
     */
    List<BasicLibrary> getDefaultLibrary();
}