package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备组态信息
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentStudioDto", description = "设备组态信息")
public class EquipmentStudioDto {

    @ApiModelProperty(value = "PC组态id")
    private String pcStudioId;

    @ApiModelProperty(value = "PC组态名称")
    private String pcStudioName;

    @ApiModelProperty(value = "PC组态url")
    private String pcStudioUrl;

    @ApiModelProperty(value = "PC组态尺寸")
    private String pcLengthWidth;

    @ApiModelProperty(value = "App组态id")
    private String appStudioId;

    @ApiModelProperty(value = "App组态名称")
    private String appStudioName;

    @ApiModelProperty(value = "app组态尺寸")
    private String appLengthWidth;

    @ApiModelProperty(value = "app组态url")
    private String appStudioUrl;
}