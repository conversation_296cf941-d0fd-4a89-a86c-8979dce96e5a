package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.dto.warn.*;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentWarnService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.List;

/**
 * iot回调
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/callback")
@Api(tags = "iot回调接口")
@Slf4j
public class IotCallbackController {

    private static final String WARN = "device_alarm_data";
    private static final String DEVICE = "device_state_data";
    private static final String ADD = "add";
    private static final String UPDATE = "update";

    @Autowired
    private IEquipmentWarnService equipmentWarnService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    /**
     * 设备告警同步
     */
    @ApiOperation("设备告警同步")
    @AuditLog(title = "设备告警",desc = "设备告警同步",businessType = BusinessType.INSERT)
    @PostMapping("/warn")
    //@Permission("equipment:warn:update")
    public RestResponse<Boolean> device(@RequestBody @Valid IotCallbackDto iotCallbackDto) {
        log.info("iot推送告警数据->" + JSONObject.toJSON(iotCallbackDto));
        if(iotCallbackDto.getFunctionCode().equals(WARN)){
            //iot数据为对象字段字符串转json，然后对象再转json,所以需要反转两次
            String content = JSONObject.parseObject(iotCallbackDto.getDataContent(), String.class);
            List<IotDetailDto> iotDetailDtos = JSONArray.parseArray(content, IotDetailDto.class);
            if(iotCallbackDto.getFunctionType().equals(ADD)){
                return RestResponse.ok(equipmentWarnService.saveIotBatch(iotDetailDtos, iotCallbackDto.getTenantId()));
            }else if(iotCallbackDto.getFunctionType().equals(UPDATE)){
                return RestResponse.ok(equipmentWarnService.updateIotBatch(iotDetailDtos));
            }
            log.error("不是告警信息新增或修改");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));

        }else{
            log.error("不是告警信息功能码");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));

        }
    }

    /**
     * 设备在线离线
     */
    @ApiOperation(value="设备在线离线")
    @AuditLog(title = "设备在线离线",desc = "设备在线离线",businessType = BusinessType.UPDATE)
    @PostMapping("/equipment")
    //@Permission("equipment:warn:update")
    public RestResponse<Boolean> equipment(@RequestBody @Valid IotCallbackDto iotCallbackDto) {
        log.info("iot推送设备状态数据->" + JSONObject.toJSON(iotCallbackDto));
        if(iotCallbackDto.getFunctionCode().equals(DEVICE)){
            //iot数据为对象字段字符串转json，然后对象再转json,所以需要反转两次
            String content = JSONObject.parseObject(iotCallbackDto.getDataContent(), String.class);
            List<IotDetailDto> iotDetailDtos = JSONArray.parseArray(content, IotDetailDto.class);
            if(iotCallbackDto.getFunctionType().equals(UPDATE)){
                return RestResponse.ok(equipmentInfoService.updateOnlineStatus(iotDetailDtos));
            }
            log.error("不是设备信息修改");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }else{
            log.error("不是设备信息功能码");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
    }
}
