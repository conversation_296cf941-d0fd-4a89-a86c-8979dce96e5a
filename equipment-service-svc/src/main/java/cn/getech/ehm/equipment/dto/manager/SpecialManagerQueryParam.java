package cn.getech.ehm.equipment.dto.manager;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.Map;

/**
 * 管理人员查询
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SpecialManagerQueryParam", description = "管理人员查询")
public class SpecialManagerQueryParam extends PageParam {

    @ApiModelProperty(value = "编码/名称/规格")
    private String keyword;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "登记证号")
    private String certificateNo;

    @ApiModelProperty(value = "作业项目")
    private String jobItem;

    @ApiModelProperty(value = "开始发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginIssueDate;

    @ApiModelProperty(value = "结束发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endIssueDate;

    @ApiModelProperty(value = "开始复审日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginReviewDate;

    @ApiModelProperty(value = "结束复审日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endReviewDate;

    @ApiModelProperty(value = "开始证件有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginCertificateValidity;

    @ApiModelProperty(value = "结束证件有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endCertificateValidity;

    @ApiModelProperty(value = "发证机关")
    private String issuingAuthority;

    @ApiModelProperty(value = "聘用单位")
    private String employer;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty("提前预警天数")
    private Integer deadlineDays;

    @ApiModelProperty("证书名称")
    private String certificateName;

    @ApiModelProperty("证书类型")
    private String certificateType;

    @ApiModelProperty("身份证号")
    private String idNumber;

    private Boolean isOverTime;

}
