package cn.getech.ehm.equipment.dto.calibration;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 校准工单分页
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@ApiModel(value = "CalibrationTaskOrderPageDto", description = "校准工单分页")
public class CalibrationTaskOrderPageDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    @Excel(name = "工单号", cellType = Excel.ColumnType.STRING)
    private String code;

    @ApiModelProperty(value = "当前设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "当前设备名称")
    @Excel(name = "仪器仪表名称", cellType = Excel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "当前设备编码")
    @Excel(name = "仪器仪表编码", cellType = Excel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "当前设备位置")
    @Excel(name = "设备位置", cellType = Excel.ColumnType.STRING)
    private String equipmentLocation;

    @ApiModelProperty(value = "计划校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "计划校准日期", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm")
    private Date planCalibrateDate;

    @ApiModelProperty(value = "倒计时")
    @Excel(name = "校准倒计时（天）", cellType = Excel.ColumnType.NUMERIC)
    private Long countdown;

    @ApiModelProperty(value = "实际校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "实际校准日期", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm")
    private Date actualCalibrateDate;

    @ApiModelProperty(value = "校准状态,0:待校准，1:已校准")
    @Excel(name="校准状态",cellType = Excel.ColumnType.STRING, readConverterExp = "0=待校准,1=已校准")
    private Integer status;

    @ApiModelProperty(value = "校准结果1:合格4限用5禁用")
    @Excel(name="校准结果",cellType = Excel.ColumnType.STRING, readConverterExp = "1=合格,4=限用,5=禁用")
    private Integer result;

}