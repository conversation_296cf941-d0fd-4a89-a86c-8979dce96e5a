package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备部件
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_structure")
public class EquipmentStructure extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 零部件类型id
     */
    @TableField("spare_parts_category_id")
    private String sparePartsCategoryId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 父节点id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 类型(1部件2零件)
     */
    @TableField("type")
    private Integer type;

    /**
     * 是否监测
     */
    @TableField("monitored")
    private Boolean monitored;

    /**
     * 添加来源(1手动2设备类型)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 型号
     */
    @TableField("model")
    private String model;

    /**
     * 关联id(轴承id、齿轮id)
     */
    @TableField("relation_id")
    private String relationId;

    /**
     * 基础库类型
     */
    @TableField("basic_library_id")
    private String basicLibraryId;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    //来源id(设备类型结构引用,则为设备类型结构id
    private String sourceId;

    private String partId;
}
