package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 扩展属性
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentPropMainDto", description = "扩展属性")
public class EquipmentPropMainDto {

    @ApiModelProperty(value = "类型公有属性")
    private List<EquipmentPropDto> categoryProps;

    @ApiModelProperty(value = "设备私有属性")
    private List<EquipmentPropDto> equipmentProps;

}