package cn.getech.ehm.equipment.dto.change;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备异动表 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChange编辑", description = "设备异动表编辑参数")
public class EquipmentChangeEditParam extends ApiParam {

    @ApiModelProperty(value = "", required = true)
    private String id;

    @ApiModelProperty(value = "设备id列表")
    private String[] equipmentIds;

    @ApiModelProperty(value = "异动类型(0领用,1移转 2借用)")
    private Integer type;

    @ApiModelProperty(value = "地点ID")
    private String locationId;

    @ApiModelProperty(value = "责任人ID")
    private String dutyId;

    @ApiModelProperty(value = "责任人姓名")
    private String dutyName;

    @ApiModelProperty(value = "责任人联系方式")
    private String dutyContact;

    @ApiModelProperty(value = "责任人部门")
    private String dutyDept;

    @ApiModelProperty(value = "异动时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    @ApiModelProperty(value = "说明")
    private String remark;

}
