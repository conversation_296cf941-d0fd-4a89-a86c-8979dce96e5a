package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.overview.OverviewModelCardDto;
import cn.getech.ehm.equipment.entity.OverviewModelCard;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 概览图卡片 服务类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IOverviewModelCardService extends IBaseService<OverviewModelCard> {

        /**
         * 更新概览卡片
         * @return
         */
        Boolean editOverviewModelCard(OverviewModelCardDto cardDto);

        /**
         * 获取卡片列表
         * @return
         */
        List<OverviewModelCardDto> getCardList(String name, Integer type);

        /**
         * 从geek租户下拉取卡片列表
         * @param tenantId
         * @return
         */
        Boolean initialization(String tenantId);
}