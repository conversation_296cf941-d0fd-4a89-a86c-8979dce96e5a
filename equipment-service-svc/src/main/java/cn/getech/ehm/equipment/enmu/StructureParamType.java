package cn.getech.ehm.equipment.enmu;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 结构参数分类
 */
public enum StructureParamType {
    FEATURE(1,"特征参数"),
    BASIC(2,"基础参数"),
    CUSTOM(3,"自定义参数");


    StructureParamType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (StructureParamType paramType : StructureParamType.values()) {
                if (paramType.getValue() == value) {
                    return paramType.getName();
                }
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(StructureParamType paramType : StructureParamType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(paramType.value);
            enumListDto.setName(paramType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
