package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备结构树修改
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentTreeEditDto", description = "设备结构树编辑")
public class EquipmentTreeEditDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "父节点id", required = true)
    @NotBlank(message = "父节点id不能为空")
    private String parentId;

    @ApiModelProperty(value = "类型(1位置11主设备12子设备)", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "位置类型)")
    private Integer locationType;

}