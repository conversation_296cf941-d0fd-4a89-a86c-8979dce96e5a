package cn.getech.ehm.equipment.dto.ruban;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 父级信息
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "IotTopicDto", description = "iot组态topic推送")
public class IotTopicDto {

    @ApiModelProperty(value = "测点参数id")
    private String paramId;

    @ApiModelProperty(value = "值")
    private Double value;

    @ApiModelProperty(value = "数据上报时间(UTC时间)")
    private String timestamp;
}