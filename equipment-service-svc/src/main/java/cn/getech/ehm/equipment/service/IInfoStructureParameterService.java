package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.info.EquipmentStructureParameterDto;
import cn.getech.ehm.equipment.dto.info.StructureParameterResDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureParameter;
import cn.getech.poros.framework.common.service.IBaseService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 零部件参数 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IInfoStructureParameterService extends IBaseService<EquipmentStructureParameter> {

    /**
     * 继承零部件类型参数
     * @return
     */
    boolean saveByCategory(String structureId, String sparePartsCategoryId, String relationId);

    /**
     * 过滤拥有参数的零部件ids
     * @param structureIds
     * @return
     */
    List<String> hasParamStructureIds(List<String> structureIds);

    /**
     * 获取零部件对应参数集合
     * @return
     */
    Map<String, List<EquipmentStructureParameterDto>> getStructureParameterMap(List<String> structureIds, Integer[] paramTypes);

    /**
     * 获取设备零部件参数
     * @param structureId
     * @return
     */
    StructureParameterResDto getStructureParameter(String structureId);

    /**
     * 删除部件参数
     * @param structureIds
     * @return
     */
    Boolean deleteByStructureIds(List<String> structureIds);

    /**
     * 更新基础库类型的参数
     * @param structureId
     * @return
     */
    Boolean updateBaseParam(String structureId, String relationId);

    /**
     * 更新设备零部件参数值
     * @param dto
     * @return
     */
    Boolean updateStructureParameter(StructureParameterResDto dto);

    /**
     * 计算参数值
     * @param structureParameterId
     * @return
     */
    BigDecimal countStructureParameter(String structureParameterId);
}