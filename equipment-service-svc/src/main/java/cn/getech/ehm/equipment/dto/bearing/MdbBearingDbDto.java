package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 轴承表
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "MdbBearingDbDto", description = "mdb轴承返回数据模型")
public class MdbBearingDbDto {

    @ApiModelProperty(value = "id")
    private String BearingDBID;

    @ApiModelProperty(value = "生产厂家名称")
    private String FactoryName;

    @ApiModelProperty(value = "型号名称")
    private String BearingModel;

    @ApiModelProperty(value = "滚动体个数")
    private Integer NB;

    @ApiModelProperty(value = "外圈故障频率")
    private Double BPFO;

    @ApiModelProperty(value = "内圈故障频率")
    private Double BPFI;

    @ApiModelProperty(value = "外圈保持架故障频率")
    private Double FTFI;

    @ApiModelProperty(value = "滚动体故障频率")
    private Double BSF;

    @ApiModelProperty(value = "接触角")
    private Integer ContactAngle;

}
