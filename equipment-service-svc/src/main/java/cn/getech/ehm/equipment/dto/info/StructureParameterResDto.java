package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 设备零部件运行参数数据模型
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "StructureParameterResDto", description = "设备零部件运行参数输出")
public class StructureParameterResDto {

    @ApiModelProperty(value = "基础库")
    private List<EquipmentStructureParameterDto> baseParams;

    @ApiModelProperty(value = "录入")
    private List<EquipmentStructureParameterDto> inputParams;

    @ApiModelProperty(value = "计算")
    private List<EquipmentStructureParameterDto> countParams;

}
