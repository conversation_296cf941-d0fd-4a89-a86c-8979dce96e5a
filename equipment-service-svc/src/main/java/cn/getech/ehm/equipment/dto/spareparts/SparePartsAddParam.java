package cn.getech.ehm.equipment.dto.spareparts;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备零件 新增参数
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsAddParam", description = "设备零件新增参数")
public class SparePartsAddParam extends ApiParam {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "编码", required = true)
    @NotBlank(message = "编码不能为空")
    private String code;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "类型(1轴承2齿轮)", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "设备id", required = true)
    @NotBlank(message = "设备id不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    @ApiModelProperty(value = "轴承厂家")
    private String factoryName;

    @ApiModelProperty(value = "型号")
    private String modelName;

    @ApiModelProperty(value = "传动比")
    private String proportion;

    @ApiModelProperty(value = "最大转速")
    private Double maxSpeed;

    @ApiModelProperty(value = "最小转速")
    private Double minSpeed;
}