package cn.getech.ehm.equipment.dto.category;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备类型 新增参数
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentType新增", description = "设备类型新增参数")
public class EquipmentCategoryAddParam extends ApiParam {

    @ApiModelProperty(value = "类型编码",required = true)
    @NotBlank(message = "编码不能为空")
    private String code;

    @ApiModelProperty(value = "类型名称",required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "上级节点",required = true)
    @NotNull(message = "上级节点不能为空")
    private String parentId;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "是否需要校验")
    private Boolean checked;

    @ApiModelProperty(value = "外观图片Ids(多张，逗号隔开)")
    private String picIds;

    @ApiModelProperty(value = "附件Ids(多个逗号隔开)")
    private String docIds;

    @ApiModelProperty(value = "扩展属性")
    private List<EquipmentCategoryPropDto> propDtos;

    @ApiModelProperty(value = "类型0其他1仪器仪表", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "OEE计算")
    private Boolean oeeOpen;
}