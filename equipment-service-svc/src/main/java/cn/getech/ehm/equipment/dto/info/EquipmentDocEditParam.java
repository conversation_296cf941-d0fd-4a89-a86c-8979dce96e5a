package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;

/**
 * 设备文档编辑参数
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentDocEditParam", description = "设备文档编辑参数")
public class EquipmentDocEditParam extends ApiParam {
    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "附件")
    private String docIds;
}
