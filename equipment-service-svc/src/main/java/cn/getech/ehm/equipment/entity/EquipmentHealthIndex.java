package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 设备健康指数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "equipment_health_index")
public class EquipmentHealthIndex extends BaseEntity {

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 值
     */
    @TableField("value")
    private Double value;

    /**
     * 标记时间
     */
    @TableField("mark_time")
    private Date markTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
