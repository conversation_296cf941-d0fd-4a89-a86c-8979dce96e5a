package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto;
import cn.getech.ehm.equipment.entity.RunningParameterVariable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 零部件运行参数Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface RunningParameterVariableMapper extends BaseMapper<RunningParameterVariable> {
    /**
     * 获取运行参数变量列表
     * @return
     */
    List<ParameterVariableDto> getList(@Param("runningParameterIds") List<String> runningParameterIds);
}
