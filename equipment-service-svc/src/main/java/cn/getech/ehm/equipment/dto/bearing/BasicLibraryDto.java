package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基础库表
 */
@Data
@ApiModel(value = "BasicLibraryDto", description = "基础库返回数据模型")
public class BasicLibraryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "字段名")
    private String fieldName;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

}
