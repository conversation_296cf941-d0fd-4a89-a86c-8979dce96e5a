package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.overview.*;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.parameter.EquipmentOeeDto;
import cn.getech.ehm.iot.dto.parameter.OeeQueryDto;
import cn.getech.ehm.iot.dto.warn.CardWarnStatisticDto;
import cn.getech.ehm.iot.dto.warn.InfoParamWarnDto;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 概览控制器
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@RestController
@RequestMapping("/overview")
@Api(tags = "概览服务接口")
public class OverviewController {

    @Autowired
    private IOverviewModelService modelService;
    @Autowired
    private IOverviewDetailService detailService;
    @Autowired
    private IOverviewModelCardService cardService;
    @Autowired
    private IEquipmentLocationService locationService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private IEquipmentHealthIndexService healthIndexService;

    @ApiOperation("更新概览模板")
    @PostMapping("/model/edit")
    public RestResponse<Boolean> editOverviewModel(@RequestBody @Valid OverviewModelDto modelDto) {
        return RestResponse.ok(modelService.editOverviewModel(modelDto));
    }

    @ApiOperation(value = "获取概览模板")
    @GetMapping(value = "/getModelDto")
    @ApiImplicitParams({
            @ApiImplicitParam(name="relationKey",value="类型对应标识(位置type值；设备id)", required = true, dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2类型)",dataType="int", required = true, paramType = "query")
    })
    public RestResponse<OverviewModelDto> getModelDto(@RequestParam String relationKey, @RequestParam Integer type) {
        return RestResponse.ok(modelService.getModelDto(relationKey, type));
    }

    @ApiOperation("更新概览详情中央图片")
    @PostMapping("/detail/editDetailCard")
    public RestResponse<Boolean> editDetailCard(@RequestBody @Valid OverviewDetailCardDto dto) {
        return RestResponse.ok(detailService.editDetailCard(dto));
    }

    @ApiOperation("清空概览详情中央图片")
    @GetMapping("/detail/clearDetailCard")
    @ApiImplicitParams({
            @ApiImplicitParam(name="relationKey",value="类型对应标识(位置id；设备id)", required = true, dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2设备)",dataType="int", required = true, paramType = "query")
    })
    public RestResponse<Boolean> clearDetailCard(@RequestParam String relationKey, @RequestParam Integer type) {
        return RestResponse.ok(detailService.clearDetailCard(relationKey, type));
    }

    @ApiOperation(value = "更新概览详情名称")
    @GetMapping(value = "/editDetailName")
    @ApiImplicitParams({
            @ApiImplicitParam(name="relationKey",value="类型对应标识(位置id；设备id)", required = true, dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2设备)",dataType="int", required = true, paramType = "query"),
            @ApiImplicitParam(name="name",value="名称",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<Boolean> editDetailName(@RequestParam String relationKey, @RequestParam Integer type, @RequestParam String name) {
        return RestResponse.ok(detailService.editDetailName(relationKey, type, name));
    }

    @ApiOperation(value = "获取概览详情中央图片")
    @GetMapping(value = "/getDetailCard")
    @ApiImplicitParams({
            @ApiImplicitParam(name="relationKey",value="类型对应标识(位置id；设备id)", required = true, dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2设备)",dataType="int", required = true, paramType = "query")
    })
    public RestResponse<OverviewDetailCardDto> getDetailCard(@RequestParam String relationKey, @RequestParam Integer type) {
        return RestResponse.ok(detailService.getDetailCard(relationKey, type));
    }

    @ApiOperation(value = "获取概览详情")
    @GetMapping(value = "/getDetailDto")
    @ApiImplicitParams({
            @ApiImplicitParam(name="relationKey",value="类型对应标识(位置id；设备id)", required = true, dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2设备)",dataType="int", required = true, paramType = "query")
    })
    public RestResponse<OverviewDetailDto> getDetailDto(@RequestParam String relationKey, @RequestParam Integer type) {
        return RestResponse.ok(detailService.getDetailDto(relationKey, type));
    }

    @ApiOperation("更新概览模板卡片")
    @PostMapping("/card/edit")
    public RestResponse<Boolean> editOverviewModelCard(@RequestBody @Valid OverviewModelCardDto cardDto) {
        return RestResponse.ok(cardService.editOverviewModelCard(cardDto));
    }

    @ApiOperation("获取模板卡片列表")
    @GetMapping("/card/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name="name",value="名称",dataType="string", paramType = "query"),
            @ApiImplicitParam(name="type",value="类型(1位置2设备类型)",dataType="int", paramType = "query")
    })
    public RestResponse<List<OverviewModelCardDto>> getCardList(@RequestParam(required = false) String name, @RequestParam(required = false) Integer type){
        return RestResponse.ok(cardService.getCardList(name, type));
    }

    @ApiOperation("初始化卡片列表(从geek拉取)")
    @GetMapping("/initialization")
    public RestResponse<Boolean> initialization(@RequestParam String tenantId){
        return RestResponse.ok(cardService.initialization(tenantId));
    }

    @ApiOperation("设备数量(位置)")
    @GetMapping("/cardEquipmentNum")
    @ApiImplicitParams({
            @ApiImplicitParam(name="locationId",value="位置id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<EquipmentNumDto> cardEquipmentNum(@RequestParam String locationId){
        return RestResponse.ok(locationService.cardEquipmentNum(locationId));
    }

    @ApiOperation("设备完好率(位置)")
    @GetMapping("/equipmentIntactRate")
    @ApiImplicitParams({
            @ApiImplicitParam(name="locationId",value="父节点位置id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<List<IntactRateDto>> equipmentIntactRate(@RequestParam String locationId){
        return RestResponse.ok(locationService.equipmentIntactRate(locationId));
    }

    @ApiOperation("报警卡片(位置)")
    @GetMapping("/cardWarnStatistic")
    @ApiImplicitParams({
            @ApiImplicitParam(name="locationId",value="父节点位置id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<CardWarnStatisticDto> cardWarnStatistic(@RequestParam String locationId){
        return RestResponse.ok(locationService.cardWarnStatistic(locationId));
    }

    @ApiOperation("报警趋势卡片(位置)")
    @GetMapping("/cardWarnTrend")
    @ApiImplicitParams({
            @ApiImplicitParam(name="locationId",value="位置id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<WarnCountDto> cardWarnTrend(@RequestParam String locationId){
        return RestResponse.ok(locationService.cardWarnTrend(locationId));
    }

    @ApiOperation("当前设备(设备)")
    @GetMapping("/currentEquipmentCard")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<CardEquipmentTimeDto> currentEquipmentCard(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.currentEquipmentCard(equipmentId));
    }

    @ApiOperation("运行历程(设备)")
    @GetMapping("/equipmentProgress")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query"),
            @ApiImplicitParam(name="beginTime",value="开始时间(yyyy-MM-dd HH:mm:ss)",dataType="date", required = true, paramType = "query"),
            @ApiImplicitParam(name="endTime",value="结束时间(yyyy-MM-dd HH:mm:ss)",dataType="date", required = true, paramType = "query")
    })
    public RestResponse<EquipmentProgressDto> equipmentProgress(@RequestParam String equipmentId,
                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime){
        return RestResponse.ok(equipmentInfoService.equipmentProgress(equipmentId, beginTime, endTime));
    }

    @ApiOperation("报警趋势卡片(设备)")
    @GetMapping("/equipmentCardWarnTrend")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<WarnCountDto> equipmentCardWarnTrend(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.cardWarnTrend(equipmentId));
    }

    @ApiOperation("报警列表卡片(设备)")
    @GetMapping("/equipmentCardWarnList")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<List<InfoParamWarnDto>> equipmentCardWarnList(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.cardWarnList(equipmentId));
    }

    @ApiOperation("运行时效率(设备)")
    @GetMapping("/equipmentEfficiency")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<List<Double>> equipmentEfficiency(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.equipmentEfficiency(equipmentId));
    }

    @ApiOperation("OEE(设备)")
    @PostMapping("/equipmentOee")
    public RestResponse<EquipmentOeeDto> equipmentOee(@RequestBody OeeQueryDto queryDto){
        return parameterClient.equipmentOee(queryDto);
    }

    @ApiOperation("健康评估(设备)")
    @GetMapping("/equipmentHealthEstimate")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<HealthEstimateDto> equipmentHealthEstimate(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.equipmentHealthEstimate(equipmentId));
    }

    @ApiOperation("设备健康(设备)")
    @GetMapping("/equipmentHealthIndex")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<Double> equipmentHealthIndex(@RequestParam String equipmentId){
        return RestResponse.ok(equipmentInfoService.equipmentHealthIndex(equipmentId));
    }

    @ApiOperation("健康趋势(设备)")
    @GetMapping("/equipmentHealthTrend")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="设备id",dataType="string", required = true, paramType = "query")
    })
    public RestResponse<List<Double>> equipmentHealthTrend(@RequestParam String equipmentId){
        return RestResponse.ok(healthIndexService.equipmentHealthTrend(equipmentId));
    }
}
