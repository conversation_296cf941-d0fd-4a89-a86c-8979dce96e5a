package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 轴承详情
 */
@Data
@ApiModel(value = "BearingDetailDto", description = "轴承详情")
public class BearingDetailDto {

    @ApiModelProperty(value = "外圈故障频率")
    private Double bpfo;

    @ApiModelProperty(value = "内圈故障频率")
    private Double bpfi;

    @ApiModelProperty(value = "外圈保持架故障频率")
    private Double fifi;

    @ApiModelProperty(value = "内圈保持架故障频率")
    private Double fifo;

    @ApiModelProperty(value = "滚动体故障频率")
    private Double bsf;

    @ApiModelProperty(value = "滚动体个数")
    private Integer nb;

    @ApiModelProperty(value = "接触角")
    private Integer contactAngle;

}
