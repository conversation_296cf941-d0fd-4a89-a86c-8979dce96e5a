package cn.getech.ehm.equipment.dto.category;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CW设备类型导入
 *  父节点/子节点/子节点
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "CategoryCWExcel", description = "CW设备类型导入")
public class CategoryCWExcel {
    @ApiModelProperty(value = "类型编码")
    @Excel(name="类型编码",cellType = Excel.ColumnType.STRING )
    private String code;
    @ApiModelProperty(value = "设备类型")
    @Excel(name="设备类型",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "扩展属性")
    @Excel(name="扩展属性",cellType = Excel.ColumnType.STRING )
    private String prop;
}