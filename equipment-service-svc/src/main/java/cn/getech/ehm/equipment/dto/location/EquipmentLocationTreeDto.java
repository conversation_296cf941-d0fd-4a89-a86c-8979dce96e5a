package cn.getech.ehm.equipment.dto.location;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备位置 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentLocationTreeDto", description = "设备位置树数据模型")
public class EquipmentLocationTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备位置名称")
    private String name;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "位置类型")
    private Integer type;

    @ApiModelProperty(value = "位置类型")
    private Integer locationType;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    @ApiModelProperty(value = "类别装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("title", "title");}};

    @ApiModelProperty(value = "是否开启概览图")
    private Boolean enableOverview = false;

    @ApiModelProperty("排序比重")
    private Integer sort;

    private String parentId;
}