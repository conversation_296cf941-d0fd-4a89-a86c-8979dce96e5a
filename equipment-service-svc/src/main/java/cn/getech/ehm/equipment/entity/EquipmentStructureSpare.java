package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部件零件属性
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_structure_spare")
public class EquipmentStructureSpare extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 部件id
     */
    @TableField("structure_id")
    private String structureId;
    /**
     * 传动比
     */
    @TableField("proportion")
    private String proportion;

    /**
     * 最大转速
     */
    @TableField("max_speed")
    private Double maxSpeed;

    /**
     * 最小转速
     */
    @TableField("min_speed")
    private Double minSpeed;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
