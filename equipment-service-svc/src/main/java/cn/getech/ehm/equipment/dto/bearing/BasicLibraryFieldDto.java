package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 基础库字段表
 */
@Data
@ApiModel(value = "BasicLibraryFieldDto", description = "基础库字段返回数据模型")
public class BasicLibraryFieldDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "基础库id", required = true)
    @NotBlank(message = "未绑定基础库")
    private String basicLibraryId;

    @ApiModelProperty(value = "id")
    private String name;

    @ApiModelProperty(value = "字段名(用于保存)")
    private String fieldName;

    @ApiModelProperty(value = "字段名(用于查询)")
    private String showFieldName;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "单位key")
    private String unit;

    @ApiModelProperty(value = "是否默认基础参数")
    private Boolean defaultParam;

    @ApiModelProperty(value = "组件类型(text/number/searchSelect下拉框)")
    private String componentsType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
