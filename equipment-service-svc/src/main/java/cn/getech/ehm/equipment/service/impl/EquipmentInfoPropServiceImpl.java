package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto;
import cn.getech.ehm.equipment.dto.info.EquipmentPropDetailDto;
import cn.getech.ehm.equipment.dto.info.EquipmentPropDto;
import cn.getech.ehm.equipment.dto.info.EquipmentPropMainDto;
import cn.getech.ehm.equipment.entity.EquipmentInfoProp;
import cn.getech.ehm.equipment.mapper.EquipmentInfoPropMapper;
import cn.getech.ehm.equipment.service.IEquipmentCategoryPropService;
import cn.getech.ehm.equipment.service.IEquipmentInfoPropService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备扩展属性 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@Slf4j
@Service
public class EquipmentInfoPropServiceImpl extends BaseServiceImpl<EquipmentInfoPropMapper, EquipmentInfoProp> implements IEquipmentInfoPropService {
    @Autowired
    private EquipmentInfoPropMapper equipmentInfoPropMapper;
    @Autowired
    private IEquipmentCategoryPropService categoryPropService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveOrUpdateBath(EquipmentPropMainDto mainDto, String equipmentId, Boolean isAdd){

        List<EquipmentInfoProp> equipmentInfoProps = new ArrayList<>();
        List<EquipmentPropDto> propDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(mainDto.getCategoryProps())) {
            propDtos.addAll(mainDto.getCategoryProps());
        }
        if(CollectionUtils.isNotEmpty(mainDto.getEquipmentProps())) {
            propDtos.addAll(mainDto.getEquipmentProps());
        }
        if(CollectionUtils.isNotEmpty(propDtos)) {
            int i = StaticValue.ONE;
            for (EquipmentPropDto prop : propDtos) {
                if(CollectionUtils.isEmpty(prop.getChildren())){
                    continue;
                }
                for (EquipmentPropDetailDto detailDto : prop.getChildren()) {
                    EquipmentInfoProp equipmentInfoProp = CopyDataUtil.copyObject(detailDto, EquipmentInfoProp.class);
                    if (isAdd) {
                        equipmentInfoProp.setId(null);
                    }
                    equipmentInfoProp.setEquipmentId(equipmentId);
                    equipmentInfoProp.setGroupName(prop.getGroupName());
                    equipmentInfoProp.setFixed(StaticValue.ZERO);
                    equipmentInfoProp.setSort(i++);
                    equipmentInfoProps.add(equipmentInfoProp);
                }
            }

        }
        if(CollectionUtils.isNotEmpty(equipmentInfoProps)){
            return saveOrUpdateBatch(equipmentInfoProps);
        }
        return true;
    }

    @Override
    public Boolean deleteByInfoId(String equipmentId){
        LambdaQueryWrapper<EquipmentInfoProp> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfoProp::getEquipmentId, equipmentId);
        return equipmentInfoPropMapper.delete(wrapper) > StaticValue.ZERO;
    }

    @Override
    public String getCalibrationDateByEquipmentId(String equipmentId) {
        return equipmentInfoPropMapper.getCalibrationDateByEquipmentId(equipmentId);
    }

    @Override
    public List<EquipmentInfoProp> getListByEquipmentIds(String[] equipmentIds) {
        return equipmentInfoPropMapper.getListByEquipmentIds(equipmentIds);
    }

    @Override
    public EquipmentPropMainDto getListByInfoId(String equipmentId, String categoryId){
        EquipmentPropMainDto equipmentPropMainDto = new EquipmentPropMainDto();
        List<EquipmentPropDto> categoryProps = new ArrayList<>();
        List<EquipmentPropDto> equipmentProps = new ArrayList<>();
        List<EquipmentCategoryPropDetailDto> categoryDeatilDtos = categoryPropService.getDetailListByCategoryId(categoryId, false, false);
        List<String> equipmentIds = new ArrayList<>();
        equipmentIds.add(equipmentId);
        List<EquipmentPropDetailDto> detailDtos = equipmentInfoPropMapper.getListByInfoIds(equipmentIds);
        if(CollectionUtils.isNotEmpty(detailDtos)){
            Map<String, EquipmentPropDetailDto> detailMap = detailDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryPropId()))
                    .collect(Collectors.toMap(EquipmentPropDetailDto::getCategoryPropId, v -> v, (v1,v2) -> v1));
            if(null != detailMap && detailMap.size() > 0) {
                categoryDeatilDtos.stream().forEach(dto -> {
                    EquipmentPropDetailDto detailDto = detailMap.get(dto.getCategoryPropId());
                    if(null != detailDto) {
                        dto.setDefaultValue(StringUtils.isNotBlank(detailDto.getDefaultValue()) ? detailDto.getDefaultValue() : dto.getDefaultValue());
                        dto.setId(detailDto.getId());
                    }
                });
            }
            List<EquipmentPropDetailDto> infoDetailDtos = detailDtos.stream().filter(dto -> StringUtils.isBlank(dto.getCategoryPropId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(infoDetailDtos)){
                List<String> groupNames = infoDetailDtos.stream().map(EquipmentPropDetailDto::getGroupName).distinct().collect(Collectors.toList());
                Map<String, List<EquipmentPropDetailDto>> map = detailDtos.stream().collect(Collectors.groupingBy(EquipmentPropDetailDto::getGroupName));
                for(String groupName : groupNames){
                    EquipmentPropDto propDto = new EquipmentPropDto();
                    propDto.setGroupName(groupName);
                    propDto.setChildren(map.get(groupName));
                    equipmentProps.add(propDto);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(categoryDeatilDtos)){
            List<String> groupNames = categoryDeatilDtos.stream().map(EquipmentCategoryPropDetailDto::getGroupName).distinct().collect(Collectors.toList());
            Map<String, List<EquipmentCategoryPropDetailDto>> map = categoryDeatilDtos.stream().collect(Collectors.groupingBy(EquipmentCategoryPropDetailDto::getGroupName));
            for(String groupName : groupNames){
                EquipmentPropDto propDto = new EquipmentPropDto();
                propDto.setGroupName(groupName);
                propDto.setChildren(CopyDataUtil.copyList(map.get(groupName), EquipmentPropDetailDto.class));
                categoryProps.add(propDto);
            }
        }
        equipmentPropMainDto.setCategoryProps(categoryProps);
        equipmentPropMainDto.setEquipmentProps(equipmentProps);

        return equipmentPropMainDto;
    }

    @Override
    public Map<String, List<EquipmentPropDetailDto>> getPropMap(List<String> equipmentIds, String categoryId, Boolean exported){
        Map<String, List<EquipmentPropDetailDto>> map = new HashMap<>();
        if(StringUtils.isNotBlank(categoryId)) {
            List<EquipmentCategoryPropDetailDto> categoryDeatilDtos = categoryPropService.getDetailListByCategoryId(categoryId, false, exported);
            List<EquipmentPropDetailDto> detailDtos = equipmentInfoPropMapper.getListByInfoIds(equipmentIds);
            for (String equipmentId : equipmentIds) {
                List<EquipmentPropDetailDto> propList = CopyDataUtil.copyList(categoryDeatilDtos, EquipmentPropDetailDto.class);
                if (CollectionUtils.isNotEmpty(detailDtos)) {
                    List<EquipmentPropDetailDto> equipmentProps = detailDtos.stream().filter(dto -> dto.getEquipmentId().equals(equipmentId)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(equipmentProps)) {
                        Map<String, EquipmentPropDetailDto> detailMap = equipmentProps.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryPropId()))
                                .collect(Collectors.toMap(EquipmentPropDetailDto::getCategoryPropId, v -> v, (v1, v2) -> v1));
                        if (null != detailMap && detailMap.size() > 0) {
                            propList.stream().forEach(dto -> {
                                EquipmentPropDetailDto detailDto = detailMap.get(dto.getCategoryPropId());
                                if (null != detailDto) {
                                    dto.setDefaultValue(StringUtils.isNotBlank(detailDto.getDefaultValue()) ? detailDto.getDefaultValue() : dto.getDefaultValue());
                                    dto.setId(detailDto.getId());
                                }
                            });
                        }
                    }
                }
                map.put(equipmentId, propList);
            }
        }
        return map;
    }

    @Override
    public List<String> searchEquipmentIds(String property){
        LambdaQueryWrapper<EquipmentInfoProp> wrapper = Wrappers.lambdaQuery();
        wrapper.like(EquipmentInfoProp::getDefaultValue, property);
        wrapper.select(EquipmentInfoProp::getEquipmentId);
        List<EquipmentInfoProp> equipmentInfoProps = equipmentInfoPropMapper.selectList(wrapper);
        return equipmentInfoProps.stream().map(EquipmentInfoProp::getEquipmentId).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean checkCalibrationDate(String equipmentId, String defaultValue){
        //当前属性没有校准日期，不更新
        if(StringUtils.isBlank(defaultValue)){
            return true;
        }
        LambdaQueryWrapper<EquipmentInfoProp> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfoProp::getEquipmentId, equipmentId);
        wrapper.eq(EquipmentInfoProp::getName, "校准日期");
        wrapper.select(EquipmentInfoProp::getId,EquipmentInfoProp::getDefaultValue);
        wrapper.orderByAsc(EquipmentInfoProp::getSort);
        List<EquipmentInfoProp> entities = equipmentInfoPropMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(entities)){
            EquipmentInfoProp equipmentInfoProp = entities.get(0);
            String oldDefaultValue = equipmentInfoProp.getDefaultValue();
            if(StringUtils.isNotBlank(oldDefaultValue) && oldDefaultValue.equals(defaultValue)){
                return true;
            }
        }
        return false;
    }
}
