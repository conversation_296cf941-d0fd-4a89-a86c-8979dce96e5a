package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 概览图详情中央图片
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "OverviewDetailCardDto", description = "概览图详情中央图片")
public class OverviewDetailCardDto {

    @ApiModelProperty(value = "类型(1位置2设备)", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "类型对应标识(位置id；设备id)",required = true)
    @NotBlank(message = "类型对应标识不能为空")
    private String relationKey;

    @ApiModelProperty(value = "概览卡片图名称", required = true)
    @NotBlank(message = "图片名称不能为空")
    private String cardName;

    @ApiModelProperty(value = "概览卡片图名称是否显示")
    private Boolean cardNameEnable = false;

    @ApiModelProperty(value = "概览卡片图类型(0设备/位置图片1组态2文件)")
    private Integer cardType;

    @ApiModelProperty(value = "组态页面类型(1Ehm2poros-iot)")
    private Integer studioType;

    @ApiModelProperty(value = "概览卡片图对应路由(iot组态链接;图片url;ehm组态id)")
    private String cardUrl;

    @ApiModelProperty(value = "概览卡片图组态名称")
    private String cardStudioName;

    @ApiModelProperty(value = "概览卡片图组态缩略图")
    private String cardStudioPic;

}