package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 设备位置
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_location")
public class EquipmentLocation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备位置名称
     */
    @TableField("name")
    private String name;

    /**
     * 编码(poros同步)
     */
    @TableField("code")
    private String code;

    /**
     * 上级节点
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 位置类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 图片
     */
    @TableField("pic_id")
    private String picId;

    /**
     * 省编码
     */
    @TableField("province")
    private String province;

    /**
     * 市编码
     */
    @TableField("city")
    private String city;

    /**
     * 区编码
     */
    @TableField("area")
    private String area;

    /**
     * 位置
     */
    @TableField("address")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 层级编码(每层3位数字)
     */
    @TableField("layer_code")
    private String layerCode;

    /**
     * 状态说明
     */
    @TableField("status_remark")
    private String statusRemark;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    private Integer sort;


}
