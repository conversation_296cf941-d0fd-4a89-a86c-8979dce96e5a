package cn.getech.ehm.equipment.dto.info;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentInfoAppDetailDto", description = "设备表app返回数据模型")
@JsonInclude
public class EquipmentInfoAppDetailDto {

    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "位号")
    private String itemNo;

    @ApiModelProperty(value = "上级节点id")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称(结构位置)")
    private String parentName;

    @ApiModelProperty(value = "上级节点名称")
    private String parentAllName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryAllName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "运行状态名称")
    private String runningStatusName;

    @ApiModelProperty(value = "资产状态")
    private String assetStatus;

    @ApiModelProperty(value = "管理部门code")
    private String manageDepart;

    @ApiModelProperty(value = "管理部门名称")
    private String manageDepartName;

    @ApiModelProperty(value = "管理责任人id")
    private String managePrincipal;

    @ApiModelProperty(value = "管理责任人名称")
    private String managePrincipalName;

    @ApiModelProperty(value = "使用部门code")
    private String useDepart;

    @ApiModelProperty(value = "使用部门名称")
    private String useDepartName;

    @ApiModelProperty(value = "使用责任人id")
    private String usePrincipal;

    @ApiModelProperty(value = "使用责任人名称")
    private String usePrincipalName;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    @ApiModelProperty(value = "参数报警对应设备状态")
    private Integer iotStatus;

    @ApiModelProperty(value = "设备状态值名称")
    private String iotStatusName;

    @ApiModelProperty(value = "设备状态值颜色")
    private String iotStatusColor;
}