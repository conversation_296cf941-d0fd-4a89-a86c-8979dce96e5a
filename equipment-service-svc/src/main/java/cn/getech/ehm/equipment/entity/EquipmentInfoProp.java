package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备信息扩展属性
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_info_prop")
public class EquipmentInfoProp extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 属性名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型扩展属性id
     */
    @TableField("category_prop_id")
    private String categoryPropId;

    /**
     * 组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 属性类型(0文本1数值2选项)
     */
    @TableField("prop_type")
    private Integer propType;

    /**
     * 属性定义
     */
    @TableField("define")
    private String define;

    /**
     * 属性值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否固定0否1是
     */
    @TableField("fixed")
    private Integer fixed;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

}
