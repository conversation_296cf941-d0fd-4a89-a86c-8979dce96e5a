package cn.getech.ehm.equipment.dto.bearing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 轴承表
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "BearingDbDto", description = "轴承返回数据模型")
public class BearingDbDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "生产厂家id")
    private String factoryId;

    @ApiModelProperty(value = "生产厂家名称")
    private String factoryName;

    @ApiModelProperty(value = "型号id")
    private String modelId;

    @ApiModelProperty(value = "型号名称")
    private String modelName;

    @ApiModelProperty(value = "滚动体个数")
    private Integer nb;

    @ApiModelProperty(value = "外圈故障频率")
    private Double bpfo;

    @ApiModelProperty(value = "内圈故障频率")
    private Double bpfi;

    @ApiModelProperty(value = "外圈保持架故障频率")
    private Double fifi;

    @ApiModelProperty(value = "内圈保持架故障频率")
    private Double fifo;

    @ApiModelProperty(value = "滚动体故障频率")
    private Double bsf;

    @ApiModelProperty(value = "接触角")
    private Integer contactAngle;

}
