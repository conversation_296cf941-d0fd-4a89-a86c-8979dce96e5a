package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备异动表
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_change")
public class EquipmentChange extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备ID
     */
    @TableField("equipment_ids")
    private String[] equipmentIds;

    /**
     * 异动类型(0领用,1移转 2借用)
     */
    @TableField("type")
    private Integer type;

    /**
     * 地点ID
     */
    @TableField("location_id")
    private String locationId;

    /**
     * 责任人ID
     */
    @TableField("duty_id")
    private String dutyId;

    /**
     * 责任人姓名
     */
    @TableField("duty_name")
    private String dutyName;

    /**
     * 责任人联系方式
     */
    @TableField("duty_contact")
    private String dutyContact;

    /**
     * 责任人部门
     */
    @TableField("duty_dept")
    private String dutyDept;

    /**
     * 异动时间
     */
    @TableField("change_time")
    private Date changeTime;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 流程状态(0 审批中 1 审批通过 2 驳回)
     */
    @TableField("status")
    private Integer status;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Boolean deleted;


}
