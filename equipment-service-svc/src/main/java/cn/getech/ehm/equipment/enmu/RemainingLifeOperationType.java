package cn.getech.ehm.equipment.enmu;


import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 剩余寿命操作类型枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum RemainingLifeOperationType {
    LT(1, "<"),
    GT(2, ">"),
    LE(3, "<="),
    GE(4, ">=");


    RemainingLifeOperationType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(RemainingLifeOperationType operationType : RemainingLifeOperationType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(operationType.value);
            enumListDto.setName(operationType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (RemainingLifeOperationType operationType : RemainingLifeOperationType.values()) {
                if (operationType.getValue() == value) {
                    return operationType.getName();
                }
            }
        }
        return null;
    }

    private int value;

    private String name;

    public int getValue() { return value; }

    public void setValue(int value) { this.value = value; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
