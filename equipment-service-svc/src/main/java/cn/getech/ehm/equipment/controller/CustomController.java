package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.custom.CustomRequest;
import cn.getech.ehm.equipment.dto.custom.CustomResponse;
import cn.getech.ehm.equipment.service.impl.CustomServiceImpl;
import cn.getech.poros.framework.common.api.RestResponse;
import com.google.j2objc.annotations.AutoreleasePool;
import io.fabric8.kubernetes.client.CustomResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "定制服务")
@RequestMapping("/cus")
@RestController
public class CustomController {
    @Autowired
    private CustomServiceImpl customService;

    @ApiOperation("设备健康指数均值")
    @PostMapping("/healthIndex")
    public RestResponse<CustomResponse> healthIndex(@RequestBody CustomRequest request) {
        return RestResponse.ok(customService.healthIndex(request));
    }
}
