package cn.getech.ehm.equipment.dto.overview;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 健康评估
 */
@Data
@ApiModel(value = "HealthEstimateDto", description = "健康评估")
public class HealthIndexDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "值")
    private Double value;

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date markTime;
}