package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.equipment.dto.activiti.EquipmentTaskAuditParam;
import cn.getech.ehm.equipment.dto.activiti.StartActivitiServiceResult;
import cn.getech.ehm.equipment.dto.change.*;
import cn.getech.ehm.equipment.entity.EquipmentChangeNew;
import cn.getech.ehm.equipment.handler.ActivitiHandler;
import cn.getech.ehm.equipment.mapper.EquipmentChangeNewMapper;
import cn.getech.ehm.equipment.service.IEquipmentChangeNewService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.JsonUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备异动表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Slf4j
@Service
public class EquipmentChangeNewServiceImpl extends BaseServiceImpl<EquipmentChangeNewMapper, EquipmentChangeNew> implements IEquipmentChangeNewService {

    @Autowired
    private ActivitiHandler activitiHandler;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentLocationService equipmentLocationService;
    @Autowired
    private EquipmentChangeNewMapper changeNewMapper;

    @Value("${equipment.change.code:E008_PT}")
    private String changeCode;
    @Value("${equipment.change.name:设备异动_标品}")
    private String changeName;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveByParam(EquipmentChangeNewAddParam addParam) {
        List<EquipmentChangeNew> equipmentChangeNews = new ArrayList<>();
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        String targetValue;
        Map<String, String> initEquipmentNameMap = equipmentInfoService.getDepthName(addParam.getEquipmentIds(), true);

        if (addParam.getTargetParentType() != 1) {
            targetValue = equipmentInfoService.getDepthName(Collections.singletonList(addParam.getTargetParentId()), false).get(addParam.getTargetParentId());
        } else {
            targetValue = equipmentLocationService.getDepthName(Collections.singletonList(addParam.getTargetParentId())).get(addParam.getTargetParentId());
        }
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("assigns", user.getUid());
        for(String equipmentId : addParam.getEquipmentIds()){
            EquipmentChangeNew equipmentChange = new EquipmentChangeNew();
            equipmentChange.setType(addParam.getType());
            equipmentChange.setDeleted(false);
            equipmentChange.setStatus(0);
            equipmentChange.setCreateUserName(user.getName());
            equipmentChange.setTargetValue(targetValue);
            equipmentChange.setInitialValue(initEquipmentNameMap.get(equipmentId));
            equipmentChange.setEquipmentId(equipmentId);
            equipmentChange.setTargetParentId(addParam.getTargetParentId());
            equipmentChange.setTargetParentType(addParam.getTargetParentType());
            StartActivitiServiceResult activitiServiceResult = activitiHandler.startProcess(variables, changeCode, changeName);
            if(null != activitiServiceResult){
                equipmentChange.setActivityId(activitiServiceResult.getTaskId());
                equipmentChange.setProcessInstanceId(activitiServiceResult.getProcessInstanceId());
                equipmentChange.setProcessUser(activitiServiceResult.getProcessUser());
                equipmentChangeNews.add(equipmentChange);
            }
        }
        if(CollectionUtils.isNotEmpty(equipmentChangeNews)){
            this.saveBatch(equipmentChangeNews);
        }
        return true;
    }

    @Override
    public PageResult<EquipmentChangeNewDto> pageList(EquipmentChangeNewQueryParam queryParam) {
        Page<EquipmentChangeNewDto> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        if(null != queryParam.getIsMyTodo() && queryParam.getIsMyTodo()){
            UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
            queryParam.setCurrentUid(userBaseInfo.getUid());
        }
        Page<EquipmentChangeNewDto> pageList = changeNewMapper.pageList(page, queryParam);
        List<EquipmentChangeNewDto> dtoList = CopyDataUtil.copyList(pageList.getRecords(), EquipmentChangeNewDto.class);
        if (CollectionUtils.isEmpty(dtoList)) {
            return new PageResult<>();
        }

        String uid = PorosContextHolder.getCurrentUser().getUid();dtoList.forEach(dto -> {
            List<String> uidList = JsonUtil.readList(dto.getProcessUser(), String.class);
            if (uidList.contains(uid)) {
                dto.setIsApproval(true);
            } else {
                dto.setIsApproval(false);
            }
            dto.setCanDeleted(dto.getCreateBy().equals(uid));
        });
        return Optional.ofNullable(PageResult.<EquipmentChangeNewDto>builder()
                        .records(dtoList)
                        .total(pageList.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @Override
    public EquipmentChangeNewDto getDtoById(String id) {
        return changeNewMapper.getDtoById(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteById(String[] ids) {
        LambdaQueryWrapper<EquipmentChangeNew> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentChangeNew::getId, ids);
        wrapper.select(EquipmentChangeNew::getId, EquipmentChangeNew::getProcessInstanceId);
        List<String> processInstanceIds = changeNewMapper.selectList(wrapper).stream().filter(dto -> StringUtils.isNotBlank(dto.getProcessInstanceId())).map(EquipmentChangeNew::getProcessInstanceId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(processInstanceIds)){
            for(String processInstanceId : processInstanceIds){
                try {
                    activitiHandler.deleteByProcessId(processInstanceId);
                }catch (Exception e){
                    log.error("异动对应工作流删除失败,实例id为:" + processInstanceId);
                }
            }
        }

        LambdaUpdateWrapper<EquipmentChangeNew> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(EquipmentChangeNew::getId, ids);
        updateWrapper.set(EquipmentChangeNew::getDeleted, DeletedType.YES.getValue());
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean batchAudit(ChangeBatchAuditDto batchAuditDto){
        List<EquipmentChangeNew> updateEntities = new ArrayList<>();
        LambdaQueryWrapper<EquipmentChangeNew> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentChangeNew::getId, batchAuditDto.getIds());
        wrapper.eq(EquipmentChangeNew::getStatus, 0);
        wrapper.select(EquipmentChangeNew::getId, EquipmentChangeNew::getEquipmentId, EquipmentChangeNew::getTargetParentId,
                EquipmentChangeNew::getTargetParentType, EquipmentChangeNew::getActivityId, EquipmentChangeNew::getProcessInstanceId);
        List<EquipmentChangeNew> equipmentChangeNews = changeNewMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentChangeNews)){
            List<String> targetParentIds = equipmentChangeNews.stream().map(EquipmentChangeNew::getTargetParentId).distinct().collect(Collectors.toList());
            List<String> targetExistParentIds = equipmentInfoService.getExistParentIds(targetParentIds);
            List<String> equipmentIds = equipmentChangeNews.stream().map(EquipmentChangeNew::getEquipmentId).distinct().collect(Collectors.toList());
            Map<String,String> layerCodeMap = equipmentInfoService.getLayerCodeMap(equipmentIds);
            UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
            for(EquipmentChangeNew equipmentChangeNew : equipmentChangeNews){
                equipmentChangeNew.setRemark(batchAuditDto.getComment());
                equipmentChangeNew.setProcessUserName(userBaseInfo.getName());
                EquipmentTaskAuditParam auditParam = new EquipmentTaskAuditParam();
                auditParam.setComment(batchAuditDto.getComment());
                auditParam.setActivityId(equipmentChangeNew.getActivityId());
                auditParam.setProcessInstanceId(equipmentChangeNew.getProcessInstanceId());
                equipmentChangeNew.setChangeDate(new Date());
                if(!targetExistParentIds.contains(equipmentChangeNew.getTargetParentId())){
                    //模板位置已被删除
                    try {
                        AuditActivitiServiceResult auditActivitiServiceResult = activitiHandler.submitActivitiPass(auditParam);
                        if (auditActivitiServiceResult != null) {
                            String remark = StringUtils.isNotBlank(equipmentChangeNew.getRemark()) ? equipmentChangeNew.getRemark() + StringPool.SEMICOLON + "目标位置不存在" : "目标位置不存在";
                            equipmentChangeNew.setRemark(remark);
                            equipmentChangeNew.setStatus(2);
                            updateEntities.add(equipmentChangeNew);
                        }
                    }catch (Exception e){
                        log.error("流程调用失败");
                    }
                }else if (batchAuditDto.getResult() == 1) {
                    try {
                        AuditActivitiServiceResult auditActivitiServiceResult = activitiHandler.submitActivitiPass(auditParam);
                        if (auditActivitiServiceResult != null) {
                            equipmentChangeNew.setStatus(1);
                            equipmentInfoService.changeEquipmentParent(equipmentChangeNew.getEquipmentId(), equipmentChangeNew.getTargetParentType(), equipmentChangeNew.getTargetParentId(), layerCodeMap.get(equipmentChangeNew.getEquipmentId()));
                            updateEntities.add(equipmentChangeNew);
                        }
                    }catch (Exception e){
                        log.error("流程调用失败");
                    }
                } else if (batchAuditDto.getResult() == 0) {
                    try {
                        AuditActivitiServiceResult auditActivitiServiceResult = activitiHandler.submitActivitiPass(auditParam);
                        if (auditActivitiServiceResult != null) {
                            equipmentChangeNew.setStatus(2);
                            updateEntities.add(equipmentChangeNew);
                        }
                    }catch (Exception e){
                        log.error("流程调用失败");
                    }
                } else {
                    throw new GlobalServiceException(GlobalResultMessage.of("不存在此提交类型, 请规范重新提交."));
                }
            }
            this.saveOrUpdateBatch(updateEntities);
        }
        return true;
    }

    @Override
    public Boolean checkEquipmentHaveChange(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentChangeNew> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentChangeNew::getEquipmentId, equipmentIds);
        wrapper.eq(EquipmentChangeNew::getStatus, 0);
        wrapper.eq(EquipmentChangeNew::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentChangeNew::getId, EquipmentChangeNew::getEquipmentId);
        List<String> existEquipmentIds = changeNewMapper.selectList(wrapper).stream().map(EquipmentChangeNew::getEquipmentId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(existEquipmentIds)){
            //当前设备存在待审批
            String equipmentNames = equipmentInfoService.getNamesByIds(existEquipmentIds);
            if(StringUtils.isNotBlank(equipmentNames)){
                throw new GlobalServiceException(GlobalResultMessage.of(equipmentNames + "已存在未审批异动"));
            }
        }
        return true;
    }

    @Override
    public List<EquipmentChangeExcelDto> getExportList(EquipmentChangeNewQueryParam queryParam){
        List<EquipmentChangeExcelDto> excelDtos = changeNewMapper.getExportList(queryParam);
        if(CollectionUtils.isNotEmpty(excelDtos)){
            int i = 1;
            for(EquipmentChangeExcelDto excelDto : excelDtos){
                excelDto.setNum(i++);
            }
        }
        return excelDtos;
    }

    @Override
    public Integer exportNum(EquipmentChangeNewQueryParam queryParam){
        return changeNewMapper.getExportList(queryParam).size();
    }
}
