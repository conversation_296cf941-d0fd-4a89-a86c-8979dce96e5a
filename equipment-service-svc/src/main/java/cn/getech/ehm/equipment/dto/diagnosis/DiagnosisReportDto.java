package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 诊断报告
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "DiagnosisReportDto", description = "诊断报告返回数据模型")
public class DiagnosisReportDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编号")
    private String equipmentCode;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备图片id")
    private String equipmentPicIds;

    @ApiModelProperty(value = "设备图片url")
    private String equipmentPicUrl;

    @ApiModelProperty(value = "设备图片Dto")
    private AttachmentClientDto equipmentPicDto;

    @ApiModelProperty(value = "分析图片信息")
    private String analyzePicInfo;

    @ApiModelProperty(value = "分析图片信息集合")
    private List<AnalyzePicInfoDto> analyzePicInfoDtos;

    @ApiModelProperty(value = "分析信息")
    private String analyzeInfo;

    @ApiModelProperty(value = "分析原因")
    private String analyzeReason;

    @ApiModelProperty(value = "处理建议")
    private String handlingSuggestions;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "报告时间")
    private Date createTime;

}