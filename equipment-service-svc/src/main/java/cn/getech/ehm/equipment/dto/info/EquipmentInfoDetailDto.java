package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 计划单弹框设备
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentInfoDetailDto", description = "计划单弹框设备")
public class EquipmentInfoDetailDto {
    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备)")
    private String parentName;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String locationId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备)")
    private String locationName;

    @ApiModelProperty(value = "上级节点名称")
    private String parentAllName;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型全路径名称")
    private String categoryAllName;

    //标签tagId
    private String positionTagId;

    @ApiModelProperty(value = "默认排序")
    private Integer sort;
}