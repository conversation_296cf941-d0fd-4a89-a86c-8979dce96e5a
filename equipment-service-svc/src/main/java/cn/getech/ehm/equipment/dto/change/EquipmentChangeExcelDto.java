package cn.getech.ehm.equipment.dto.change;


import cn.getech.ehm.common.util.excel.FormExcel;
import cn.getech.poros.framework.common.annotation.Excel;
import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备异动导出列表参数
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChangeExcelDto", description = "设备异动导出列表参数")
public class EquipmentChangeExcelDto extends ApiParam {

    @ApiModelProperty("异动id")
    private String id;

    @ApiModelProperty("设备ID")
    private String equipmentId;

    @ApiModelProperty("序号")
    @FormExcel(name="序号",cellType = FormExcel.ColumnType.NUMERIC)
    private Integer num;

    @ApiModelProperty("设备名称")
    @FormExcel(name="设备名称",cellType = FormExcel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty("设备编码")
    @FormExcel(name="设备编码",cellType = FormExcel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty("异动类型(0-位置,1-状态)")
    @FormExcel(name="异动类型",cellType = FormExcel.ColumnType.STRING, readConverterExp = "0=位置,1=状态")
    private Integer type;

    @ApiModelProperty("初始值")
    @FormExcel(name="初始值",cellType = FormExcel.ColumnType.STRING)
    private String initialValue;

    @ApiModelProperty("目标值")
    @FormExcel(name="目标值",cellType = FormExcel.ColumnType.STRING)
    private String targetValue;

    @ApiModelProperty("申请人名称")
    @FormExcel(name="申请人",cellType = FormExcel.ColumnType.STRING)
    private String createUserName;

    @ApiModelProperty("申请时间")
    @FormExcel(name="申请时间",cellType = FormExcel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("流程状态(0待审批 1审批通过 2审批驳回)")
    @FormExcel(name="状态",cellType = FormExcel.ColumnType.STRING, readConverterExp = "0=待审批,1=审批通过,2=审批驳回")
    private Integer status;

    @ApiModelProperty("操作人人名称")
    @FormExcel(name="审批人",cellType = FormExcel.ColumnType.STRING)
    private String processUserName;

    @ApiModelProperty("变动时间")
    @FormExcel(name="审批时间",cellType = FormExcel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date changeDate;

    @ApiModelProperty("原因备注")
    @FormExcel(name="原因说明",cellType = FormExcel.ColumnType.STRING)
    private String remark;
}
