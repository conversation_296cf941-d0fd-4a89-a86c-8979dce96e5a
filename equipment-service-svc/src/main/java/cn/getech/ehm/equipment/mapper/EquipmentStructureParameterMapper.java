package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.info.EquipmentStructureParameterDto;
import cn.getech.ehm.equipment.entity.EquipmentStructureParameter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备零部件运行参数Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface EquipmentStructureParameterMapper extends BaseMapper<EquipmentStructureParameter> {
    /**
     * 获取运行参数列表
     * @param featureParam 是否特征参数
     * @return
     */
    List<EquipmentStructureParameterDto> getList(@Param("structureIds") List<String> structureIds, @Param("paramTypes") Integer[] paramTypes);
}
