package cn.getech.ehm.equipment.dto.change;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备异动表 新增参数
 *
 * <AUTHOR>
 * @date 2020-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChange新增", description = "设备异动表新增参数")
public class EquipmentChangeAddParam extends ApiParam {
    @ApiModelProperty(value = "设备id类别", required = true)
    @NotEmpty(message = "设备id类别不能为空")
    private String[] equipmentIds;

    @ApiModelProperty(value = "异动类型(0领用,1移转 2借用)", required = true)
    @NotNull(message = "异动类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "地点ID", required = true)
    @NotBlank(message = "地点ID不能为空")
    private String locationId;

    @ApiModelProperty(value = "责任人ID", required = true)
    @NotBlank(message = "责任人ID不能为空")
    private String dutyId;

    @ApiModelProperty(value = "责任人姓名")
    private String dutyName;

    @ApiModelProperty(value = "责任人联系方式")
    private String dutyContact;

    @ApiModelProperty(value = "责任人部门")
    private String dutyDept;

    @ApiModelProperty(value = "异动时间(yyyy-MM-dd HH:mm:ss)", required = true)
    @NotNull(message = "异动时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    @ApiModelProperty(value = "说明")
    private String remark;
}