package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.info.EquipmentPropDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentInfoProp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备扩展属性 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@Repository
public interface EquipmentInfoPropMapper extends BaseMapper<EquipmentInfoProp> {

    /**
     * 获取设备的校准日期
     * @param equipmentId
     * @return
     */
    String getCalibrationDateByEquipmentId(String equipmentId);

    /**
     * 获取多台设备的属性
     * @param equipmentIds
     * @return
     */
    List<EquipmentInfoProp> getListByEquipmentIds(@Param("equipmentIds") String[] equipmentIds);


    /**
     * 查找设备相关的扩展属性
     * @return
     */
    List<EquipmentPropDetailDto> getListByInfoIds(@Param("equipmentIds") List<String> equipmentIds);
}
