package cn.getech.ehm.equipment.dto.runningparam;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 零部件运行参数查询参数
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RunningParameterQueryParam", description = "零部件运行参数查询参数")
public class RunningParameterQueryParam extends PageParam {

    @ApiModelProperty(value = "零部件id", required = true)
    @NotBlank(message = "零部件id不能为空")
    private String sparePartsId;

    @ApiModelProperty(value = "基础库id")
    private String basicLibraryId;

    @ApiModelProperty(value = "参数名称")
    private String keyword;

    @ApiModelProperty(value = "过滤id")
    private String filterId;

}
