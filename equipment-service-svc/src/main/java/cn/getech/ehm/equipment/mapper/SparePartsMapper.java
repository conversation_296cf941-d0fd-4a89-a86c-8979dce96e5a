package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.EquipmentSpareParts;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 设备零件
 *
 * <AUTHOR>
 * @since 2020-12-04
 */
@Repository
public interface SparePartsMapper extends BaseMapper<EquipmentSpareParts> {

    /**
     * 获取当前设备下最大排序
     * @param equipmentId
     * @return
     */
    Integer getMaxSort(@Param("equipmentId") String equipmentId);

}
