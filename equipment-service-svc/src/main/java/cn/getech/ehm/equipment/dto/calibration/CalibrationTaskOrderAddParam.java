package cn.getech.ehm.equipment.dto.calibration;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 校准工单 新增参数
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CalibrationTaskOrder新增", description = "校准工单新增参数")
public class CalibrationTaskOrderAddParam extends ApiParam {

    @ApiModelProperty(value = "当前设备ID", required = true)
    @NotBlank(message = "当前设备id为空")
    private String equipmentId;

    @ApiModelProperty(value = "校准日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "校准日期不能为空")
    private Date planCalibrateDate;

    @ApiModelProperty(value = "显示日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "显示日期不能为空")
    private Date displayDate;

    @ApiModelProperty(value = "仪表仪器校准单id")
    private String calibrationId;
}