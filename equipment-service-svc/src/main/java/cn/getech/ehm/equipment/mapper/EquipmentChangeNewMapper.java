package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.change.EquipmentChangeExcelDto;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeNewDto;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeNewQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentChangeNew;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备异动表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Repository
public interface EquipmentChangeNewMapper extends BaseMapper<EquipmentChangeNew> {
    /**
     * 根据id查询
     * @param id
     * @return
     */
    EquipmentChangeNewDto getDtoById(@Param("id") String id);

    /**
     * 分页查询
     */
    Page<EquipmentChangeNewDto> pageList(@Param("page") Page<EquipmentChangeNewDto> page,
                                    @Param("param") EquipmentChangeNewQueryParam queryParam);

    /**
     * 获取导出列表
     * @param queryParam
     * @return
     */
    List<EquipmentChangeExcelDto> getExportList(@Param("param") EquipmentChangeNewQueryParam queryParam);
}
