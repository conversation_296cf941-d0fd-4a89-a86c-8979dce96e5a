package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryDto;
import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryQueryParam;
import cn.getech.ehm.equipment.entity.SparePartsCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 零部件类型Mapper
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface SparePartsCategoryMapper extends BaseMapper<SparePartsCategory> {
    /**
     * 分页获取信息
     */
    IPage<SparePartsCategoryDto> pageList(Page<SparePartsCategoryQueryParam> page,
                                          @Param("param") SparePartsCategoryQueryParam queryParam);
}
