package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备BOM结构树
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentBomTreeDto", description = "设备BOM结构树")
public class EquipmentBomTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型(对应category)")
    private String type;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer equipmentType;

    @ApiModelProperty(value = "型号")
    private String modelName;

    @ApiModelProperty(value = "父节点")
    private String parentId;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "是否是叶子节点(包含零件)")
    private Boolean isLeaf = true;

    @ApiModelProperty("是否是设备叶子节点")
    private Boolean equipmentLeaf = true;

    @ApiModelProperty(value = "子节点")
    private List<EquipmentBomTreeDto> children = new ArrayList<>();

    @ApiModelProperty(value = "面包屑列表")
    private List<BomParentDto> bomParent  = new ArrayList<>();

    @ApiModelProperty(value = "类别装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("title", "title");}};

    @ApiModelProperty(value = "数据类型(1设备2零件)")
    private Integer bomType = 1;
}