package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.overview.OverviewModelCardDto;
import cn.getech.ehm.equipment.entity.OverviewModelCard;
import cn.getech.ehm.equipment.mapper.OverviewModelCardMapper;
import cn.getech.ehm.equipment.service.IOverviewModelCardService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.UUID;

/**
 * 概览图卡片 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class OverviewModelCardServiceImpl extends BaseServiceImpl<OverviewModelCardMapper, OverviewModelCard> implements IOverviewModelCardService {

    @Autowired
    private OverviewModelCardMapper cardMapper;

    @Override
    public Boolean editOverviewModelCard(OverviewModelCardDto cardDto) {
        OverviewModelCard overviewModelCard = CopyDataUtil.copyObject(cardDto, OverviewModelCard.class);
        if(StringUtils.isNotBlank(cardDto.getId())) {
            String id = UUID.randomUUID().toString().replace("-", "");
            overviewModelCard.setId(id);
        }
        return this.saveOrUpdate(overviewModelCard);
    }

    @Override
    public List<OverviewModelCardDto> getCardList(String name, Integer type) {
        LambdaQueryWrapper<OverviewModelCard> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(name), OverviewModelCard::getName, name);
        wrapper.eq(null != type, OverviewModelCard::getType, type);
        wrapper.orderByAsc(OverviewModelCard::getNum);
        return CopyDataUtil.copyList(cardMapper.selectList(wrapper), OverviewModelCardDto.class);
    }

    @Override
    public Boolean initialization(String tenantId){
        List<OverviewModelCard> list = cardMapper.getAllList();
        if(CollectionUtils.isNotEmpty(list)){
            for(OverviewModelCard card : list){
                card.setId(null);
                card.setTenantId(tenantId);
            }
            return saveBatch(list);
        }
        return false;
    }
}
