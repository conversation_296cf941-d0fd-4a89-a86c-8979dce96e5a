package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.change.EquipmentChangeDto;
import cn.getech.ehm.equipment.entity.EquipmentChange;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备异动表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Repository
public interface EquipmentChangeMapper extends BaseMapper<EquipmentChange> {

    /**
     * 根据设备id获取异动列表
     * @param equipmentId
     * @return
     */
    List<EquipmentChangeDto> getEquipmentChangeList(String equipmentId);
}
