package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.calibration.*;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.equipment.service.ICalibrationTaskOrderService;
import org.springframework.web.bind.annotation.RestController;

/**
 * 校准工单控制器
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@RestController
@RequestMapping("/calibrationTaskOrder")
@Api(tags = "校准工单服务接口")
public class CalibrationTaskOrderController {

    @Autowired
    private ICalibrationTaskOrderService calibrationTaskOrderService;

    /**
     * 分页获取校准工单列表
     */
    @ApiOperation("分页获取校准工单列表")
    @PostMapping("/pageList")
    //@Permission("calibration:task:order:list")
    public RestResponse<PageResult<CalibrationTaskOrderPageDto>> pageList(@RequestBody @Valid CalibrationTaskOrderQueryParam calibrationTaskOrderQueryParam){
        return RestResponse.ok(calibrationTaskOrderService.pageDto(calibrationTaskOrderQueryParam));
    }

    /**
     * 新增校准工单
     */
    @ApiOperation(value="新增校准工单")
    @AuditLog(title = "校准工单",desc = "新增校准工单",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("calibration:task:order:update")
    public RestResponse<String> save(@RequestBody @Valid CalibrationTaskOrderAddParam calibrationTaskOrderAddParam) {
        return RestResponse.ok(calibrationTaskOrderService.saveCalibration(calibrationTaskOrderAddParam));
    }

    /**
     * 获取对比仪器仪表详细数据
     */
    @ApiOperation("获取对比仪器仪表详细数据")
    @GetMapping("/getRefEquipment")
    @ApiImplicitParams({
            @ApiImplicitParam(name="equipmentId",value="对比设备id",dataType="string", paramType = "query")
    })
    //@Permission("equipment:list")
    public RestResponse<RefEquipmentDto> getRefEquipment(@RequestParam("equipmentId") String equipmentId){
        return RestResponse.ok(calibrationTaskOrderService.getRefEquipment(equipmentId));
    }

    /**
     * 修改校准工单
     */
    @ApiOperation(value="修改校准工单")
    @AuditLog(title = "校准工单",desc = "修改校准工单",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("calibration:task:order:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CalibrationTaskOrderEditParam calibrationTaskOrderEditParam) {
        return RestResponse.ok(calibrationTaskOrderService.updateByParam(calibrationTaskOrderEditParam));
    }

    /**
     * 根据id获取校准单
     */
    @ApiOperation(value = "根据id获取校准单")
    @GetMapping(value = "/{id}")
    //@Permission("equipment:type:list")
    public RestResponse<CalibrationTaskOrderDto> get(@PathVariable  String id) {
        return RestResponse.ok(calibrationTaskOrderService.getDtoById(id));
    }

    /**
     * 导出远程诊断列表
     */
    @ApiOperation(value = "导出远程诊断列表")
    @PostMapping(value = "/exportList")
    public void exportList(HttpServletResponse response, @RequestBody @Valid CalibrationTaskOrderQueryParam calibrationTaskOrderQueryParam) {
        calibrationTaskOrderQueryParam.setLimit(10000);
        PageResult<CalibrationTaskOrderPageDto> pageResult = calibrationTaskOrderService.pageDto(calibrationTaskOrderQueryParam);
        ExcelUtils<CalibrationTaskOrderPageDto> util = new ExcelUtils<>(CalibrationTaskOrderPageDto.class);
        util.exportExcel(pageResult.getRecords(), "任务校准单列表", response);
    }

}
