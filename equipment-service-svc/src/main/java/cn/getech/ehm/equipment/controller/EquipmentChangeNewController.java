package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.equipment.dto.change.*;
import cn.getech.ehm.equipment.service.IEquipmentChangeNewService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 设备异动控制器
 * <AUTHOR>
 * @since 2020-08-25
 */
@RestController
@RequestMapping("/equipmentChangeNew")
@Api(tags = "设备异动新服务接口")
public class EquipmentChangeNewController {
    @Autowired
    private IEquipmentChangeNewService equipmentChangeNewService;

    /**
     * 设备异动列表查询
     */
    @ApiOperation("设备异动列表查询")
    @AuditLog(title = "设备异动列表查询",desc = "设备异动列表查询",businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public RestResponse<PageResult<EquipmentChangeNewDto>> pageList(@RequestBody @Valid EquipmentChangeNewQueryParam queryParam) {
        return RestResponse.ok(equipmentChangeNewService.pageList(queryParam));
    }

    /**
     * 新增设备异动
     */
    @ApiOperation("新增设备异动")
    @AuditLog(title = "设备异动",desc = "新增设备异动",businessType = BusinessType.INSERT)
    @PostMapping
    public RestResponse<Boolean> add(@RequestBody @Valid EquipmentChangeNewAddParam addParam) {
        return RestResponse.ok(equipmentChangeNewService.saveByParam(addParam));
    }

    /**
     * 查询设备异动详情
     */
    @ApiOperation("查询设备异动详情")
    @AuditLog(title = "查询设备异动详情",desc = "查询设备异动详情",businessType = BusinessType.QUERY)
    @GetMapping("/getById")
    public RestResponse<EquipmentChangeNewDto> getById(@RequestParam("id") String id) {
        return RestResponse.ok(equipmentChangeNewService.getDtoById(id));
    }

    @ApiOperation("根据id批量删除设备异动")
    @DeleteMapping("/{ids}")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(equipmentChangeNewService.deleteById(ids));
    }

    @ApiOperation("设备异动批量审核")
    @PostMapping("/batchAudit")
    public RestResponse<Boolean> batchAudit(@RequestBody ChangeBatchAuditDto batchAuditDto) {
        return RestResponse.ok(equipmentChangeNewService.batchAudit(batchAuditDto));
    }

    @ApiOperation("查询该设备是否存在位置异动")
    @PostMapping("/checkEquipmentHaveChange")
    public RestResponse<Boolean> checkEquipmentHaveChange(@RequestBody EquipmentChangeNewQueryParam queryParam) {
        return RestResponse.ok(equipmentChangeNewService.checkEquipmentHaveChange(queryParam.getEquipmentIds()));
    }

    @ApiOperation("设备异动导出")
    @PostMapping("/export")
    public void export(@RequestBody @Valid EquipmentChangeNewQueryParam queryParam, HttpServletResponse response) {
        FormExcelUtils<EquipmentChangeExcelDto> util = new FormExcelUtils(EquipmentChangeExcelDto.class);
        util.exportExcel(equipmentChangeNewService.getExportList(queryParam), "设备异动",response, null);
    }

    @ApiOperation("设备异动导出查询条数")
    @PostMapping("/exportNum")
    public RestResponse<Integer> exportNum(@RequestBody @Valid EquipmentChangeNewQueryParam queryParam) {
        return RestResponse.ok(equipmentChangeNewService.exportNum(queryParam));
    }
}
