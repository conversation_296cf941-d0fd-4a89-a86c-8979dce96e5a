package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.DiagnosisReport;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.io.File;

/**
 * 诊断报告 服务类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IDiagnosisReportService extends IBaseService<DiagnosisReport> {

        /**
         * 分页查询，返回Dto
         * @return
         */
        PageResult<DiagnosisReportListDto> pageDto(DiagnosisReportQueryParam queryParam);

        /**
         * 保存
         * @return
         */
        boolean saveByParam(DiagnosisReportAddParam addParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        DiagnosisReportDto getDtoById(String id);

        /**
         * 更新
         */
        boolean updateByParam(DiagnosisReportEditParam editParam);

        /**
         * 导出word
         * @param id
         * @return
         */
        File exportWord(String id, String url);
}