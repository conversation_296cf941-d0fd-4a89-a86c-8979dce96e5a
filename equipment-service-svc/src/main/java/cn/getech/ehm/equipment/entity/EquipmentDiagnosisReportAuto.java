package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 诊断报告V2
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_diagnosis_report_auto")
public class EquipmentDiagnosisReportAuto extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 是否启动
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 设备ids
     */
    @TableField(value = "equipment_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class )
    private String[] equipmentIds;

    /**
     * 描述
     */
    @TableField("content")
    private String content;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
