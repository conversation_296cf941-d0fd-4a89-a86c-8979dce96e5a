package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.*;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.client.ScadaClient;
import cn.getech.ehm.equipment.client.PorosClient;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.app.SearchPageParam;
import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderAddParam;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryDto;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto;
import cn.getech.ehm.equipment.dto.info.BomParentDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoEditParam;
import cn.getech.ehm.equipment.dto.info.EquipmentPropDetailDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.iot.StudioEditParam;
import cn.getech.ehm.equipment.dto.iot.StudioQueryParam;
import cn.getech.ehm.equipment.dto.iot.StudioResDetailDto;
import cn.getech.ehm.equipment.dto.iot.StudioResDto;
import cn.getech.ehm.equipment.dto.location.EquipmentLocationTreeDto;
import cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.overview.CardEquipmentTimeDto;
import cn.getech.ehm.equipment.dto.overview.EquipmentProgressDetailDto;
import cn.getech.ehm.equipment.dto.overview.EquipmentProgressDto;
import cn.getech.ehm.equipment.dto.overview.HealthEstimateDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.dto.warn.IotDetailDto;
import cn.getech.ehm.equipment.enmu.RemainingLifeOperationType;
import cn.getech.ehm.equipment.entity.*;
import cn.getech.ehm.equipment.enums.HealthStatusType;
import cn.getech.ehm.equipment.enums.IotParamStatusType;
import cn.getech.ehm.equipment.enums.RunningStatusType;
import cn.getech.ehm.equipment.mapper.EquipmentInfoMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.equipment.utils.IotUtils;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.equipment.dto.OeeDetailListDto;
import cn.getech.ehm.iot.dto.parameter.InfoDefaultStatusParamDto;
import cn.getech.ehm.iot.dto.parameter.OeeEditDto;
import cn.getech.ehm.equipment.dto.OeeQueryParam;
import cn.getech.ehm.iot.dto.warn.*;
import cn.getech.ehm.system.client.SystemClient;
import cn.getech.ehm.system.dto.EquipmentCustomerDto;
import cn.getech.ehm.task.client.TaskClient;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.exception.ServiceException;
import cn.getech.poros.framework.common.param.PageParam;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecOrgClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 设备表 服务实现类
 * <AUTHOR>
 * @since 2020-07-09
 */
@Slf4j
@Service
public class EquipmentInfoServiceImpl extends BaseServiceImpl<EquipmentInfoMapper, EquipmentInfo>
        implements IEquipmentInfoService {
    /**
     * 默认是黑色
     */
    private static final int QRCOLOR = 0xFF000000;
    /**
     * 背景颜色
     */
    private static final int BGWHITE = 0xFFFFFFFF;
    /**
     * 生成二维码的默认边长，因为是正方形的，所以高度和宽度一致
     */
    private static final int OR_LENGTH = 400;

    /**
     * 简单判断YYYY-MM-DD时间格式的正则表达式
     */
    private static final String EL = "^\\d{4}-\\d{2}-\\d{2}$";
    @Value("${iot.kjs.preUrl:http://iot.cloud.consen.net}")
    private String iotUrl;
    @Value("${ehm-config.equipment.qr-pic:}")
    private String qrPic;
    @Value("${ehm-config.equipment.qr-label:设备健康管理系统}")
    private String qrLabel;
    @Value("${ehm-config.equipment.qr-bottom:设备健康管理系统}")
    private String qrBottom;
    /**
     * 是否开启设备部门权限
     */
    @Value("${info-auth.enabled:false}")
    private Boolean authEnabled;
    @Autowired
    private EquipmentInfoMapper equipmentInfoMapper;
    @Autowired
    private IEquipmentLocationService equipmentLocationService;
    @Autowired
    private IEquipmentCategoryService equipmentCategoryService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private ICalibrationTaskOrderService calibrationTaskOrderService;
    @Autowired
    private IEquipmentInfoPropService equipmentInfoPropService;
    @Autowired
    private IEquipmentCategoryPropService categoryPropService;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private IEquipmentInfoSpecialService specialService;
    @Autowired
    private ISparePartsService sparePartsService;
    @Autowired
    private TaskClient taskClient;
    @Autowired
    private PorosSecOrgClient porosSecOrgClient;
    @Autowired
    private PorosClient porosClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private ScadaClient scadaClient;
    @Autowired
    private IEquipmentStructureService structureService;
    @Autowired
    private IOverviewModelService modelService;
    @Autowired
    private IEquipmentStatusService statusService;
    @Autowired
    private IEquipmentOrgAuthService orgAuthService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private IEquipmentHealthIndexService healthIndexService;
    @Autowired
    private IDiagnosisReportV2DetailService reportV2DetailService;

    @Override
    public PageResult<EquipmentInfoListDto> pageDto(EquipmentInfoQueryParam queryParam) {
        Page<EquipmentInfoQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        BuildInfoSearchDto buildInfoSearchDto = buildSearchDto(queryParam);
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), EquipmentInfo.class, "info"));
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            //过滤过，且设备id集合为空，直接返回空字符集
            return new PageResult<>();
        }else {
            queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }
        if(null != queryParam.getRemainingLifeOperation() && null != queryParam.getRemainingLifeValue()){
            queryParam.setRemainingLifeOperationName(RemainingLifeOperationType.getNameByValue(queryParam.getRemainingLifeOperation()));
            queryParam.setRemainingLifeValue(queryParam.getRemainingLifeValue() * 30);
        }
        IPage<EquipmentInfoListDto> result = equipmentInfoMapper.getPageList(page, queryParam);
        List<EquipmentInfoListDto> records = result.getRecords();
        if(CollectionUtils.isNotEmpty(records)) {
            List<String> categoryIds = new ArrayList<>();
            List<String> equipmentIds = new ArrayList<>();
            List<String> parentEquipmentIds = new ArrayList<>();
            for(EquipmentInfoListDto equipmentInfoListDto : records){
                categoryIds.add(equipmentInfoListDto.getCategoryId());
                equipmentIds.add(equipmentInfoListDto.getId());
                if(equipmentInfoListDto.getType() != StaticValue.ONE){
                    parentEquipmentIds.add(equipmentInfoListDto.getParentId());
                }
            }
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            //设备父级节点名称集合，不包含自身
            Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds,true);
            Map<String, String> equipmentParentNameMap = this.getNameMapByIds(parentEquipmentIds);

            for(EquipmentInfoListDto dto : records){
                dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
                dto.setParentAllName(equipmentNameMap.get(dto.getId()));
                if(dto.getType() != StaticValue.ONE){
                    //主设备查询位置
                    dto.setParentName(equipmentParentNameMap.get(dto.getParentId()));
                }
                dto.setRemainingLifeStr(this.buildRemainingLife(dto.getRemainingLife()));
            }
        }

        return Optional.ofNullable(PageResult.<EquipmentInfoListDto>builder()
                .records(records)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    private String buildRemainingLife(Integer remainingLife){
        if(null != remainingLife){
            if(remainingLife > 90){
                return "3M+";
            }
            String remainingLifeStr = "";
            Integer year = remainingLife / 365;
            if(year > 0){
                remainingLifeStr += year + "Y";
                remainingLife -= 365 * year;
            }
            Integer month = remainingLife / 30;
            if(StringUtils.isNotBlank(remainingLifeStr) || month > 0){
                remainingLifeStr += month + "M";
                remainingLife -= month * 30;
            }
            remainingLifeStr += remainingLife + "D";
            return remainingLifeStr;
        }
        return null;
    }

    public Boolean batchUpdate(EquipmentInfoBatchUpdateParam param){
        List<String> idList = Lists.newArrayList();
        if (param!=null && CollectionUtils.isNotEmpty(param.getIdList())){
            idList = param.getIdList();
        }else {
            EquipmentInfoQueryParam queryParam = CopyDataUtil.copyObject(param, EquipmentInfoQueryParam.class);
            BuildInfoSearchDto buildInfoSearchDto = buildSearchDto(queryParam);
            queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), EquipmentInfo.class, "info"));
            if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
                //过滤过，且设备id集合为空，直接返回空字符集
                return true;
            }else {
                queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            }
            List<EquipmentInfoListDto> result = equipmentInfoMapper.getAllListByParam(queryParam);
            idList = result.stream().map(item -> item.getId()).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(idList)) {
            log.info("批量更新设备参数：{}",idList.size());
            EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(param.getToUpdateEntity(), EquipmentInfo.class);
            UpdateWrapper<EquipmentInfo> wrapper = new UpdateWrapper<>();
            if (CollectionUtils.isNotEmpty(param.getToSetNullList())){
                for (String entity:param.getToSetNullList()){
                    wrapper.set(entity,null);
                }
            }
            wrapper.lambda().in(EquipmentInfo::getId, idList);
            this.update(equipmentInfo, wrapper);
        }
        return true;
    }

    @Override
    public PageResult<EquipmentInfoDetailDto> detailList(DetailQueryParam queryParam){

        Page<DetailQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        if(StringUtils.isNotBlank(queryParam.getDiagnosisReportId())){
            //诊断报告v2不参与设备授权体系，只查询自己选择的设备
            List<String> reportEquipmentIds = reportV2DetailService.getEquipmentIdByReportId(queryParam.getDiagnosisReportId());
            if(CollectionUtils.isNotEmpty(reportEquipmentIds)){
                queryParam.setEquipmentIds(reportEquipmentIds);
            }else{
                return new PageResult<>();
            }
        }else if(queryParam.getAuth()){
            //设备权限
            BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
            if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())) {
                return new PageResult<>();
            }else {
                queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            }
        }
        IPage<EquipmentInfoDetailDto> result = equipmentInfoMapper.detailList(page, queryParam);

        List<EquipmentInfoDetailDto> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> categoryIds = new ArrayList<>();
            List<String> equipmentIds = new ArrayList<>();
            List<String> parentEquipmentIds = new ArrayList<>();
            for(EquipmentInfoDetailDto detailDto : records){
                categoryIds.add(detailDto.getCategoryId());
                equipmentIds.add(detailDto.getId());
                if(detailDto.getType() != StaticValue.ONE){
                    parentEquipmentIds.add(detailDto.getParentId());
                }
            }
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            //设备父级节点名称集合，不包含自身
            Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds,true);
            Map<String, String> equipmentParentNameMap = this.getNameMapByIds(parentEquipmentIds);

            Integer sort = 1;
            for (EquipmentInfoDetailDto dto : records) {
                dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
                dto.setParentAllName(equipmentNameMap.get(dto.getId()));
                if (dto.getType() != StaticValue.ONE) {
                    //主设备查询位置
                    dto.setParentName(equipmentParentNameMap.get(dto.getParentId()));
                }
                dto.setSort(sort++);
            }
        }
        return Optional.ofNullable(PageResult.<EquipmentInfoDetailDto>builder()
                        .records(result.getRecords())
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    private BuildInfoSearchDto buildSearchDto(EquipmentInfoQueryParam queryParam){
        List<String> equipmentIds = queryParam.getEquipmentIds();

        //当前登录人员部门过滤设备
        BuildInfoSearchDto searchDto = this.getCurrentUserInfoIds();
        if(searchDto.getFlag()) {
            List<String> authInfoIds = searchDto.getEquipmentIds();
            if (CollectionUtils.isEmpty(authInfoIds)) {
                return BuildInfoSearchDto.builder().flag(true).build();
            }
            //取交集
            if (CollectionUtils.isNotEmpty(equipmentIds)) {
                List<String> intersection = equipmentIds.stream().filter(item -> authInfoIds.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(intersection)) {
                    equipmentIds = intersection;
                } else {
                    return BuildInfoSearchDto.builder().flag(true).build();
                }
            } else {
                equipmentIds = authInfoIds;
            }
        }
        if (StrUtil.isNotBlank(queryParam.getCategoryId())) {
            List<String> categoryIds = CollectionUtils.isNotEmpty(queryParam.getCategoryIds()) ? queryParam.getCategoryIds() : new ArrayList<>();
            categoryIds.addAll(Arrays.asList(queryParam.getCategoryId().split(StringPool.COMMA)));
            queryParam.setCategoryIds(categoryIds);
        }
        if(StringUtils.isNotBlank(queryParam.getParentId())){
            List<String> parentIds = new ArrayList<>();
            if(queryParam.getParentType() == 1){
                parentIds = equipmentLocationService.getChildIds(queryParam.getParentId());
            }else{
                parentIds.add(queryParam.getParentId());
            }
            queryParam.setParentIds(parentIds);
        }
        if (CollectionUtils.isNotEmpty(queryParam.getLocationIds())){
            List<String> locationIds = new ArrayList<>();
            for (String locationId:queryParam.getLocationIds()){
                if(queryParam.getParentType() == 1){
                    locationIds.addAll(equipmentLocationService.getChildIds(locationId));
                }else{
                    locationIds.add(locationId);
                }
            }
            queryParam.setLocationIds(locationIds);
        }

        return BuildInfoSearchDto.builder().equipmentIds(equipmentIds).flag(false).build();
    }

    @Override
    public PageResult<EquipmentInfoAppDetailDto> appPageDto(InfoAppQueryParam param) {
        List<String> equipmentIds = param.getEquipmentIds();
        //当前登录人员部门过滤设备
        BuildInfoSearchDto searchDto = this.getCurrentUserInfoIds();
        if(searchDto.getFlag()) {
            List<String> authInfoIds = searchDto.getEquipmentIds();
            if (CollectionUtils.isEmpty(authInfoIds)) {
                return new PageResult(Collections.emptyList(), 0);
            }
            //取交集
            if (CollectionUtils.isNotEmpty(equipmentIds)) {
                List<String> intersection = equipmentIds.stream().filter(item -> authInfoIds.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(intersection)) {
                    equipmentIds = intersection;
                } else {
                    return new PageResult(Collections.emptyList(), 0);
                }
            } else {
                equipmentIds = authInfoIds;
            }
        }
        param.setEquipmentIds(equipmentIds);
        if(param.getAdvancedSearch()){
            if (StrUtil.isNotBlank(param.getCategoryId())) {
                List<String> categoryIds = CollectionUtils.isNotEmpty(param.getCategoryIds()) ? param.getCategoryIds() : new ArrayList<>();
                categoryIds.add(param.getCategoryId());
                param.setCategoryIds(categoryIds);
            }
            if(StringUtils.isNotBlank(param.getLocationId()) && !param.getLocationId().equals("0")){
                List<String> locationIds = CollectionUtils.isNotEmpty(param.getLocationIds()) ? param.getLocationIds() : new ArrayList<>();
                locationIds.add(param.getLocationId());
                param.setLocationIds(locationIds);
            }
        }
        Page<InfoAppQueryParam> page = new Page<>(param.getPageNo(), param.getLimit());
        IPage<EquipmentInfoAppDetailDto> result = equipmentInfoMapper.getAppPageList(page, param);
        List<EquipmentInfoAppDetailDto> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> staffUids = new ArrayList<>();
            List<String> managePrincipalUids = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getManagePrincipal()))
                    .map(EquipmentInfoAppDetailDto::getManagePrincipal).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(managePrincipalUids)){
                staffUids.addAll(managePrincipalUids);
            }
            List<String> usePrincipalUids = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getUsePrincipal()))
                    .map(EquipmentInfoAppDetailDto::getUsePrincipal).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(usePrincipalUids)){
                staffUids.addAll(usePrincipalUids);
            }
            Map<String, String> staffMap = this.getMapByUids(staffUids);

            List<String> orgCodes = new ArrayList<>();
            List<String> manageDepartOrgCodes = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getManageDepart()))
                    .map(EquipmentInfoAppDetailDto::getManageDepart).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(manageDepartOrgCodes)){
                orgCodes.addAll(manageDepartOrgCodes);
            }
            List<String> useDepartOrgCodes = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getUseDepart()))
                    .map(EquipmentInfoAppDetailDto::getUseDepart).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(useDepartOrgCodes)){
                orgCodes.addAll(useDepartOrgCodes);
            }
            Map<String, String> orgMap = this.getOrgMapByCodes(orgCodes);
            Map<String, AttachmentClientDto> picMap = new HashMap<>();
            List<String> infoIds = records.stream().map(EquipmentInfoAppDetailDto::getId).distinct().collect(Collectors.toList());
            List<String> haveChildIds = haveChildIds(infoIds);
            List<String> parentIds = records.stream().filter(dto -> dto.getType() != StaticValue.ONE)
                    .map(EquipmentInfoAppDetailDto::getParentId).distinct().collect(Collectors.toList());
            Map<String, String> nameMap = this.getNameMapByIds(parentIds);
            List<String> categoryIds = records.stream().map(EquipmentInfoAppDetailDto::getCategoryId).distinct().collect(Collectors.toList());
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            //设备特种设备集合，不包含自身
            Map<String, String> equipmentAllNameMap = this.getDepthName(infoIds, true);

            List<String> picIdList = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicId()))
                    .map(EquipmentInfoAppDetailDto::getPicId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(picIdList)) {
                picMap = getAttachmentMap(picIdList);
            }
            Map<Integer, WarnConfigDetailDto> warnLevelMap = this.getWarnMap();
            for (EquipmentInfoAppDetailDto dto : records) {
                dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
                dto.setParentAllName(equipmentAllNameMap.get(dto.getId()));

                if(dto.getType() != StaticValue.ONE) {
                    dto.setParentName(nameMap.get(dto.getParentId()));
                }
                if(CollectionUtils.isNotEmpty(haveChildIds) && haveChildIds.contains(dto.getId())){
                    dto.setIsLeaf(false);
                }
                if(null != dto.getRunningStatus()){
                    dto.setRunningStatusName(RunningStatusType.getNameByValue(dto.getRunningStatus()));
                }

                if(StringUtils.isNotBlank(dto.getManageDepart())){
                    dto.setManageDepartName(orgMap.get(dto.getManageDepart()));
                }
                if(StringUtils.isNotBlank(dto.getManagePrincipal())){
                    dto.setManagePrincipalName(staffMap.get(dto.getManagePrincipal()));
                }
                if(StringUtils.isNotBlank(dto.getUseDepart())){
                    dto.setUseDepartName(orgMap.get(dto.getUseDepart()));
                }
                if(StringUtils.isNotBlank(dto.getUsePrincipal())){
                    dto.setUsePrincipalName(staffMap.get(dto.getUsePrincipal()));
                }

                String picIds = dto.getPicId();
                if (StringUtils.isNotBlank(picIds)) {
                    String[] picStrs = picIds.split(StringPool.COMMA);
                    AttachmentClientDto attachmentClientDto = picMap.get(picStrs[0]);
                    if(null != attachmentClientDto){
                        dto.setPicUrl(attachmentClientDto.getUrl());
                    }
                }
                IotParamStatusType iotStatus = IotParamStatusType.getEnumByValue(dto.getIotStatus());
                if(null != iotStatus){
                    dto.setIotStatusName(iotStatus.getName());
                    dto.setIotStatusColor(iotStatus.getColor());
                }else{
                    WarnConfigDetailDto warnConfigDetailDto = warnLevelMap.get(dto.getIotStatus());
                    if(null != warnConfigDetailDto){
                        dto.setIotStatusName(warnConfigDetailDto.getName());
                        dto.setIotStatusColor(warnConfigDetailDto.getColor());
                    }
                }
            }
        }else{
            records = Collections.emptyList();
        }

        return Optional.ofNullable(PageResult.<EquipmentInfoAppDetailDto>builder()
                .records(null != records ? records : Collections.emptyList())
                .total(result.getTotal())
                .build())
                .orElse(new PageResult(Collections.emptyList(), 0));
    }

    /**
     * 拥有子节点ids
     * @param equipmentIds
     * @return
     */
    private List<String> haveChildIds(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getParentId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getParentId);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        return equipmentInfos.stream().map(EquipmentInfo::getParentId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<LocationEquipmentTreeDto> getAllTreeList(){
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            return equipmentInfoMapper.getAllTreeList(buildInfoSearchDto.getEquipmentIds());
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    @Cacheable(value = "equipmentTreeCache", unless="#result == null", key = "#tenantId")
    public List<LocationEquipmentTreeDto> equipmentTree(String parentId, String tenantId){
        Map<String, List<LocationEquipmentTreeDto>> locationMap = equipmentLocationService.getAllTreeList(null).stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));

        List<LocationEquipmentTreeDto> equipmentTreeDtos = this.getAllTreeList();
        Map<String, List<LocationEquipmentTreeDto>> equipmentMap = equipmentTreeDtos.stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));

        return buildLocationInfoTree(parentId, new ArrayList<>(), locationMap, equipmentMap);
    }

    private List<LocationEquipmentTreeDto> buildLocationInfoTree(String parentId, List<BomParentDto> bomParent,
                                                                 Map<String, List<LocationEquipmentTreeDto>> locationMap, Map<String, List<LocationEquipmentTreeDto>> equipmentMap){
        List<LocationEquipmentTreeDto> resDtos = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(locationMap.get(parentId))) {
            resDtos.addAll(locationMap.get(parentId));
        }
        if(CollectionUtils.isNotEmpty(equipmentMap.get(parentId))) {
            resDtos.addAll(equipmentMap.get(parentId));
        }

        if(CollectionUtils.isEmpty(resDtos)){
            return null;
        }
        resDtos = resDtos.stream().sorted(Comparator.comparing(LocationEquipmentTreeDto::getName, (a, b) -> {
            if (a.compareToIgnoreCase(b) > 0) {
                return 1;
            } else {
                return -1;
            }
        })).collect(Collectors.toList());
        for(LocationEquipmentTreeDto treeDto : resDtos) {
            List<BomParentDto> bomParents = CopyDataUtil.copyList(bomParent, BomParentDto.class);
            BomParentDto bomParentDto = new BomParentDto();
            bomParentDto.setId(treeDto.getId());
            bomParentDto.setName(treeDto.getName());
            bomParentDto.setType(treeDto.getType());
            bomParents.add(bomParentDto);
            treeDto.setBomParent(bomParents);
            List<LocationEquipmentTreeDto> children = buildLocationInfoTree(treeDto.getId(), bomParents, locationMap, equipmentMap);
            if(CollectionUtils.isNotEmpty(children)) {
                treeDto.setIsLeaf(false);
                treeDto.setChildren(children);
            }
        }
        return resDtos;
    }

    @Override
    public List<LazyEquipmentTreeDto> lazyEquipmentTree(String parentId, Integer type, String keyword){
        List<LazyEquipmentTreeDto> treeDtos = new ArrayList<>();
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(type == StaticValue.ONE){
            //位置下 位置
            if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getLocationIds())) {
                List<LazyEquipmentTreeDto> locationTreeDtos = equipmentLocationService.lazyLocationTree(parentId, keyword, buildInfoSearchDto.getLocationIds());
                if (CollectionUtils.isNotEmpty(locationTreeDtos)) {
                    treeDtos.addAll(locationTreeDtos);
                }
            }
        }

        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            //位置/设备下设备、子设备
            List<LazyEquipmentTreeDto> equipmentTreeDtos = equipmentInfoMapper.getTreeListByParentId(parentId, keyword, buildInfoSearchDto.getEquipmentIds());
            if (CollectionUtils.isNotEmpty(equipmentTreeDtos)) {
                List<String> childIds = equipmentTreeDtos.stream().map(LazyEquipmentTreeDto::getId).distinct().collect(Collectors.toList());
                List<String> hasChildEquipIds = this.hasChildIds(childIds);

                //概览图启动状态
                List<String> categoryIds = equipmentTreeDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryId())).map(LazyEquipmentTreeDto::getCategoryId).collect(Collectors.toList());
                Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(categoryIds, 2);
                for (LazyEquipmentTreeDto dto : equipmentTreeDtos) {
                    if (CollectionUtils.isNotEmpty(hasChildEquipIds) && hasChildEquipIds.contains(dto.getId())) {
                        dto.setIsLeaf(false);
                    }
                    if (StringUtils.isNotBlank(dto.getCategoryId())) {
                        dto.setEnableOverview(overviewEnableMap.get(dto.getCategoryId()));
                    }
                }
                treeDtos.addAll(equipmentTreeDtos);
            }
        }

        return treeDtos;
    }

    @Override
    public List<String> hasChildIds(List<String> parentIds){
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            return null;
        }
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getParentId, parentIds);
        wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), EquipmentInfo::getId, buildInfoSearchDto.getEquipmentIds());
        wrapper.select(EquipmentInfo::getParentId, EquipmentInfo::getId);
        return equipmentInfoMapper.selectList(wrapper).stream().map(EquipmentInfo::getParentId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<RunningStatusDto> getRunningStatus(StatusSearchDto statusSearchDto){
        List<RunningStatusDto> runningStatusDtos = new ArrayList<>();
        if(null != statusSearchDto.getType() && StringUtils.isNotBlank(statusSearchDto.getParentId())) {
            List<String> parentIds = new ArrayList<>();
            parentIds.add(statusSearchDto.getParentId());
            if (statusSearchDto.getType() == 1) {
                parentIds = equipmentLocationService.getLocationIdsByParentId(parentIds, true);
            }
            statusSearchDto.setParentIds(parentIds);
        }
        if (StrUtil.isNotBlank(statusSearchDto.getCategoryId())) {
            List<String> categoryIds = CollectionUtils.isNotEmpty(statusSearchDto.getCategoryIds()) ? statusSearchDto.getCategoryIds() : new ArrayList<>();
            categoryIds.addAll(Arrays.asList(statusSearchDto.getCategoryId().split(StringPool.COMMA)));
            statusSearchDto.setCategoryIds(categoryIds);
        }
        Map<Integer, List<EquipmentInfo>> statusCountMap = new HashMap<>();
        //判定是否需要校验设备部门权限
        BuildInfoSearchDto authSearchDto = this.getCurrentUserInfoIds();
        if(!authSearchDto.getFlag() || CollectionUtils.isNotEmpty(authSearchDto.getEquipmentIds())){
            statusSearchDto.setEquipmentIds(authSearchDto.getEquipmentIds());
            statusCountMap = equipmentInfoMapper.getRunningStatus(statusSearchDto).stream().collect(Collectors.groupingBy(EquipmentInfo::getRunningStatus));
        }
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            RunningStatusDto runningStatusDto = new RunningStatusDto();
            runningStatusDto.setValue(runningStatusType.getValue()+"");
            runningStatusDto.setName(runningStatusType.getName());
            runningStatusDto.setColor(runningStatusType.getColor());
            runningStatusDto.setCount(CollectionUtils.isNotEmpty(statusCountMap.get(runningStatusType.getValue())) ?
                    statusCountMap.get(runningStatusType.getValue()).size() : 0);
            runningStatusDtos.add(runningStatusDto);
        }
        return runningStatusDtos;
    }

    @Override
    public String equipmentTreeAdd(EquipmentTreeEditDto editDto){
        clearCache();
        if(editDto.getType() == 1){
            return equipmentLocationService.insertName(editDto.getParentId(), editDto.getName(), editDto.getLocationType());
        }else if(editDto.getType() == 11 || editDto.getType() == 12){
            EquipmentInfo equipmentInfo = new EquipmentInfo();
            equipmentInfo.setParentId(editDto.getParentId());
            equipmentInfo.setName(editDto.getName());
            String id = UUID.randomUUID().toString().replace("-","");
            equipmentInfo.setId(id);
            String layerCode = this.getLayerCode(editDto.getParentId(),id);
            equipmentInfo.setLayerCode(layerCode);

            if(editDto.getType() == 11){
                equipmentInfo.setType(StaticValue.ONE);
                equipmentInfo.setLocationId(editDto.getParentId());
            }else if(editDto.getType() == 12){
                EquipmentInfo parentEquipment = (EquipmentInfo) this.getById(editDto.getParentId());
                equipmentInfo.setLocationId(parentEquipment.getLocationId());
                equipmentInfo.setType(StaticValue.TWO);
            }

            save(equipmentInfo);
            return id;
        }else{
            return null;
        }
    }

    @Override
    public Boolean equipmentTreeEdit(EquipmentTreeEditDto editDto){
        clearCache();
        if(editDto.getType() == 1){
            return equipmentLocationService.updateName(editDto.getId(), editDto.getParentId(), editDto.getName());
        }else if(editDto.getType() == 11 || editDto.getType() == 12){
            LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EquipmentInfo::getName, editDto.getName());
            wrapper.eq(EquipmentInfo::getId, editDto.getId());
            return this.update(wrapper);
        }else{
            return false;
        }
    }

    /**
     *
     * @param parentId
     * @param haveParamLocationIds 拥有参数的位置ids
     * @param haveParamInfoIds 拥有参数的设备ids
     * @return
     */
    private List<EquipmentTreeDto> buildLocationTree(String parentId, List<String> haveParamLocationIds, List<String> haveParamInfoIds, String checkEquipmentId){
        List<EquipmentTreeDto> treeDtos;
        //位置
        List<EquipmentLocationTreeDto> locationTreeDtos = equipmentLocationService.getListByParentId(parentId, haveParamLocationIds);
        treeDtos = CopyDataUtil.copyList(locationTreeDtos, EquipmentTreeDto.class);

        //参数监控选中的参数对应的上级设备ids
        List<String> spreadIds = null;
        if(StringUtils.isNotBlank(checkEquipmentId)) {
            String layerCode = equipmentInfoMapper.getLocationLayerCode(checkEquipmentId);
            if(StringUtils.isNotBlank(layerCode)) {
                spreadIds = Arrays.asList(layerCode.split(StringPool.SLASH));
            }
        }
        if(CollectionUtils.isNotEmpty(treeDtos)) {
            for (EquipmentTreeDto dto : treeDtos) {
                dto.setType(0);
                if(CollectionUtils.isNotEmpty(spreadIds) && spreadIds.contains(dto.getId())){
                    dto.setSpread(true);
                }
            }
        }

        //设备
        List<EquipmentTreeDto> equipmentTreeDtos = this.getListByParentId(parentId, haveParamInfoIds, checkEquipmentId);
        treeDtos.addAll(equipmentTreeDtos);

        return treeDtos;
    }

    /**
     * 根据父节点获取设备树
     * @param parentId
     * @return
     */
    private List<EquipmentTreeDto> getListByParentId(String parentId, List<String> haveParamInfoIds, String checkRelInfoId){
        List<EquipmentTreeDto> equipmentTreeDtos = new ArrayList<>();
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getParentId, parentId);
        if(CollectionUtils.isNotEmpty(haveParamInfoIds)){
            wrapper.in(EquipmentInfo::getId, haveParamInfoIds);
        }
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.orderByDesc(EquipmentInfo::getSort);
        wrapper.orderByDesc(EquipmentInfo::getUpdateTime);
        //wrapper.select(EquipmentInfo::getId, EquipmentInfo::getName,EquipmentInfo::getCode, EquipmentInfo::getParentId, EquipmentInfo::getType, EquipmentInfo::getCategoryId);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentInfos)){
            //概览图启动状态
            List<String> categoryIds = equipmentInfos.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryId())).map(EquipmentInfo::getCategoryId).collect(Collectors.toList());
            Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(categoryIds, 2);
            //参数监控选中的参数对应的上级设备ids
            List<String> spreadIds = null;
            if(StringUtils.isNotBlank(checkRelInfoId)) {
                String layerCode = equipmentInfoMapper.getLayerCode(checkRelInfoId);
                spreadIds = Arrays.asList(layerCode.split(StringPool.SLASH));
            }
            for (EquipmentInfo entity : equipmentInfos) {
                EquipmentTreeDto treeDto = CopyDataUtil.copyObject(entity, EquipmentTreeDto.class);
                treeDto.setType(StaticValue.ONE);
                if(CollectionUtils.isNotEmpty(spreadIds) && spreadIds.contains(entity.getId())){
                    treeDto.setSpread(true);
                }
                if(StringUtils.isNotBlank(entity.getCategoryId())){
                    treeDto.setEnableOverview(overviewEnableMap.get(entity.getCategoryId()));
                }
                equipmentTreeDtos.add(treeDto);
            }
        }
        return equipmentTreeDtos;
    }

    @Override
    public List<EquipmentTreeDto> searchTree(RelSearchDto relSearchDto){
        List<EquipmentTreeDto> equipmentTreeDtos = new ArrayList<>();
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(relSearchDto.getKeyword())) {
            wrapper.and(w -> w.like(EquipmentInfo::getName, relSearchDto.getKeyword())
                    .or()
                    .like(EquipmentInfo::getCode, relSearchDto.getKeyword()));
        }
        wrapper.eq(EquipmentInfo::getType, StaticValue.ONE);
        if(CollectionUtils.isNotEmpty(relSearchDto.getEquipmentIds())){
            List<String> haveParamInfoIds = this.treeParentIds(relSearchDto.getEquipmentIds());
            wrapper.in(EquipmentInfo::getId, haveParamInfoIds);
        }
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.orderByDesc(EquipmentInfo::getUpdateTime);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getName, EquipmentInfo::getParentId,
                EquipmentInfo::getType, EquipmentInfo::getLayerCode, EquipmentInfo::getCategoryId);
        List<EquipmentInfo> entities = equipmentInfoMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(entities)){
            //概览图启动状态
            List<String> categoryIds = entities.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryId())).map(EquipmentInfo::getCategoryId).collect(Collectors.toList());
            Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(categoryIds, 2);
            //参数监控选中的参数对应的上级设备ids
            List<String> spreadIds = null;
            if(StringUtils.isNotBlank(relSearchDto.getCheckEquipmentId())) {
                String layerCode = equipmentInfoMapper.getLayerCode(relSearchDto.getCheckEquipmentId());
                spreadIds = Arrays.asList(layerCode.split(StringPool.SLASH));
            }
            for (EquipmentInfo entity : entities) {
                EquipmentTreeDto treeDto = CopyDataUtil.copyObject(entity, EquipmentTreeDto.class);
                treeDto.setType(StaticValue.ONE);
                if(CollectionUtils.isNotEmpty(spreadIds) && spreadIds.contains(entity.getId())){
                    treeDto.setSpread(true);
                }
                if(StringUtils.isNotBlank(entity.getCategoryId())){
                    treeDto.setEnableOverview(overviewEnableMap.get(entity.getCategoryId()));
                }
                equipmentTreeDtos.add(treeDto);
            }
        }
        return equipmentTreeDtos;
    }

    @Override
    public List<EquipmentTreeDto> paramEquipmentTree(RelSearchDto relSearchDto){
        List<EquipmentTreeDto> equipmentTreeDtos = new ArrayList<>();
        if(relSearchDto.getParentType() == StaticValue.ZERO || relSearchDto.getParentType() == StaticValue.ONE) {
            List<String> haveParamInfoIds = this.treeParentIds(relSearchDto.getEquipmentIds());
            if (CollectionUtils.isNotEmpty(haveParamInfoIds)) {
                relSearchDto.setEquipmentIds(haveParamInfoIds);
                if (relSearchDto.getParentType() == StaticValue.ZERO) {
                    List<String> haveParamLocationIds = equipmentLocationService.getLocationIdByInfoIds(relSearchDto);
                    if (CollectionUtils.isNotEmpty(haveParamLocationIds)) {
                        equipmentTreeDtos = buildLocationTree(relSearchDto.getParentId(), haveParamLocationIds, relSearchDto.getEquipmentIds(), relSearchDto.getCheckEquipmentId());
                    }
                } else if (relSearchDto.getParentType() == StaticValue.ONE){
                    equipmentTreeDtos = this.getListByParentId(relSearchDto.getParentId(), relSearchDto.getEquipmentIds(), relSearchDto.getCheckEquipmentId());
                    //部件
                    if(CollectionUtils.isNotEmpty(relSearchDto.getStructureIds())) {
                        List<EquipmentTreeDto> structureTreeDtos = structureService.paramStructureTree(relSearchDto);
                        equipmentTreeDtos.addAll(structureTreeDtos);
                    }
                }
            }
        }else if(relSearchDto.getParentType() == StaticValue.TWO){
            //部件
            equipmentTreeDtos = structureService.paramStructureTree(relSearchDto);
        }
        return equipmentTreeDtos;
    }

    @Override
    public List<StudioEquipmentBomDto> getStudioEquipmentBomList(RelSearchDto relSearchDto){
        List<StudioEquipmentBomDto> studioEquipmentBomDtos = new ArrayList<>();
        List<String> haveParamBomInfoIds = new ArrayList<>();
        //获取设备下，所有拥有参数的子设备id集合
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, relSearchDto.getEquipmentIds());
        wrapper.like(EquipmentInfo::getLayerCode, relSearchDto.getCheckEquipmentId());
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getLayerCode);
        List<String> layerCodes = equipmentInfoMapper.selectList(wrapper).stream().map(EquipmentInfo::getLayerCode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(layerCodes)){
            for(String layerCode : layerCodes){
                List<String> ids = Arrays.asList(layerCode.split(StringPool.SLASH));
                haveParamBomInfoIds.addAll(ids);
            }
            haveParamBomInfoIds = haveParamBomInfoIds.stream().distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(haveParamBomInfoIds)){
            relSearchDto.setEquipmentIds(haveParamBomInfoIds);
            LambdaQueryWrapper<EquipmentInfo> bomWrapper = Wrappers.lambdaQuery();
            bomWrapper.in(EquipmentInfo::getId, haveParamBomInfoIds);
            bomWrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
            bomWrapper.select(EquipmentInfo::getId, EquipmentInfo::getName, EquipmentInfo::getParentId);
            bomWrapper.orderByAsc(EquipmentInfo::getCreateTime);
            List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(bomWrapper);
            int i = StaticValue.ZERO;
            for(EquipmentInfo equipmentInfo : equipmentInfos){
                StudioEquipmentBomDto studioEquipmentBomDto = new StudioEquipmentBomDto();
                studioEquipmentBomDto.setId(equipmentInfo.getId());
                studioEquipmentBomDto.setName(equipmentInfo.getName());
                if(equipmentInfo.getId().equals(relSearchDto.getCheckEquipmentId())){
                    studioEquipmentBomDto.setParentId(null);
                }else {
                    studioEquipmentBomDto.setParentId(equipmentInfo.getParentId());
                }
                studioEquipmentBomDto.setType(StaticValue.ZERO);
                studioEquipmentBomDto.setSort(i++);
                studioEquipmentBomDtos.add(studioEquipmentBomDto);
            }
            //部件集合
            if(CollectionUtils.isNotEmpty(relSearchDto.getStructureIds())) {
                structureService.getStudioStructureBomList(relSearchDto, studioEquipmentBomDtos);
            }
        }

        return studioEquipmentBomDtos;
    }

    /**
     * 获取每台设备纵深向上树设备id
     * @param equipmentIds
     * @return
     */
    private List<String> treeParentIds(List<String> equipmentIds){
        List<String> haveParamInfoIds = new ArrayList<>();
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getLayerCode);
        List<EquipmentInfo> allEquipment = equipmentInfoMapper.selectList(wrapper);
        List<String> layerCodes = allEquipment.stream().map(EquipmentInfo::getLayerCode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(layerCodes)){
            for(String layerCode : layerCodes){
                List<String> ids = Arrays.asList(layerCode.split(StringPool.SLASH));
                haveParamInfoIds.addAll(ids);
            }
        }
        return haveParamInfoIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public EquipmentBomTreeDto equipmentBomTree(String equipmentId){
        EquipmentBomTreeDto rootBom = new EquipmentBomTreeDto();
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectById(equipmentId);
        if(null != equipmentInfo){
            List<EquipmentBomTreeDto> dtos = equipmentInfoMapper.getListByLayerCode(equipmentInfo.getLayerCode());

            List<String> leafInfoIds = new ArrayList<>();
            for (EquipmentBomTreeDto dto : dtos) {
                if (dto.getId().equals(equipmentId)) {
                    rootBom = dto;
                    List<BomParentDto> bomParent = new ArrayList<>(StaticValue.ONE);
                    BomParentDto bomParentDto = new BomParentDto();
                    bomParentDto.setId(dto.getId());
                    bomParentDto.setName(dto.getName());
                    bomParent.add(bomParentDto);
                    rootBom.setBomParent(bomParent);
                }
                if (dto.getEquipmentType() == StaticValue.THREE) {
                    leafInfoIds.add(dto.getId());
                }
            }
            Map<String, List<EquipmentBomTreeDto>> map = dtos.stream().collect(Collectors.groupingBy(EquipmentBomTreeDto::getParentId));
            List<String> havePartsInfoIds = sparePartsService.havePartsInfoIds(leafInfoIds);
            if (equipmentInfo.getType() == StaticValue.THREE && havePartsInfoIds.contains(equipmentInfo.getId())) {
                rootBom.setIsLeaf(false);
            } else {
                buildBomDto(rootBom, map, havePartsInfoIds);
            }
        }
        return rootBom;
    }

    private void buildBomDto(EquipmentBomTreeDto dto, Map<String, List<EquipmentBomTreeDto>> map, List<String> havePartsInfoIds){
        List<EquipmentBomTreeDto> children = map.get(dto.getId());
        if(CollectionUtils.isNotEmpty(children)){
            for(EquipmentBomTreeDto child : children){
                List<BomParentDto> bomParent = new ArrayList<>();
                BomParentDto bomParentDto = new BomParentDto();
                bomParentDto.setId(child.getId());
                bomParentDto.setName(child.getName());
                bomParent.add(bomParentDto);
                bomParent.addAll(dto.getBomParent());
                child.setBomParent(bomParent);
                if(child.getEquipmentType() == StaticValue.THREE && havePartsInfoIds.contains(child.getId())){
                    //子设备判断是否有零件
                    child.setIsLeaf(false);
                }else{
                    buildBomDto(child, map, havePartsInfoIds);
                }
                //递归完成反转顺序
                Collections.reverse(bomParent);
                child.setBomParent(bomParent);
            }
            dto.setIsLeaf(false);
            dto.setEquipmentLeaf(false);
            dto.setChildren(children);
        }
    }

    @Override
    public CheckEquipmentIdDto getClientEquipmentIds() {
        CheckEquipmentIdDto checkEquipmentIdDto = new CheckEquipmentIdDto();
        //过滤客户端设备权限
        //先判断是否客户或者工程师
        RestResponse<String> customerIdRes = systemClient.getCurrentCustomerId();
        if (customerIdRes.isOk()) {
            String customerId = customerIdRes.getData();
            if (StringUtils.isNotBlank(customerId)) {
                RestResponse<List<String>> infosRes = systemClient.getInfoIdsByCustomerId(customerId);
                if (infosRes.isOk()) {
                    checkEquipmentIdDto.setType(StaticValue.TWO);
                    checkEquipmentIdDto.setEquipmentIds(infosRes.getData());
                } else {
                    log.info("连接system-service失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
                }
            } else {
                //非客户或工程师判断是否已审核用户
                RestResponse<Boolean> auditRes = systemClient.checkIsAudit();
                if (auditRes.isOk()) {
                    Boolean flag = auditRes.getData();
                    if (flag) {
                        //已审核用户不允许看数据
                        checkEquipmentIdDto.setType(StaticValue.ONE);
                    }
                } else {
                    log.error("获取审核信息失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
                }
            }
        } else {
            log.error("查询客户信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }
        return checkEquipmentIdDto;
    }

    @Override
    public List<EquipmentListDto> search(PageParam pageParam) {
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), EquipmentInfo::getId, buildInfoSearchDto.getEquipmentIds());
            wrapper.like(StringUtils.isNotBlank(pageParam.getKeyword()), EquipmentInfo::getCode, pageParam.getKeyword())
                .or().like(StringUtils.isNotBlank(pageParam.getKeyword()), EquipmentInfo::getName, pageParam.getKeyword());
        wrapper.orderByDesc(EquipmentInfo::getUpdateTime, EquipmentInfo::getCreateTime);
        List<EquipmentInfo> resultList = list(wrapper);
        List<EquipmentListDto> result = new ArrayList();
        for (EquipmentInfo equipmentInfo : resultList) {
            EquipmentListDto listDto = new EquipmentListDto();
            listDto.setEquipmentId(equipmentInfo.getId());
            listDto.setEquipmentCode(equipmentInfo.getCode());
            listDto.setEquipmentName(equipmentInfo.getName());
            result.add(listDto);
        }
        return result;
    }

    @Override
    public List<EquipmentListDto> searchEquipment(SearchPageParam searchPageParam) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        if (searchPageParam.getIsCustomer()) {
            // 注释掉过滤客户设备查询接口代码
//            RestResponse<List<String>> response = systemClient.getInfoIdsByCustomerId(searchPageParam.getCustomerId());
//            if (!response.isOk()) {
//                log.error("Feign : systemClient.getInfoIdsByCustomerId param  CustomerId: " + searchPageParam.getCustomerId());
//                throw new GlobalServiceException(GlobalResultMessage.of("查询客户设备信息失败！"));
//            }
//            if (!CollectionUtils.isEmpty(response.getData())) {
//                wrapper.in(EquipmentInfo::getId, response.getData());
//            } else {
//                return new ArrayList<>();
//            }

            wrapper.like(StringUtils.isNotBlank(searchPageParam.getKeyword()), EquipmentInfo::getCode, searchPageParam.getKeyword())
                    .or().like(StringUtils.isNotBlank(searchPageParam.getKeyword()), EquipmentInfo::getName, searchPageParam.getKeyword());
            wrapper.orderByDesc(EquipmentInfo::getUpdateTime, EquipmentInfo::getCreateTime);
        } else {

            if (searchPageParam.getType() == 5) {
                wrapper.like(StringUtils.isNotBlank(searchPageParam.getKeyword()), EquipmentInfo::getCode, searchPageParam.getKeyword())
                        .or().like(StringUtils.isNotBlank(searchPageParam.getKeyword()), EquipmentInfo::getName, searchPageParam.getKeyword());
                wrapper.orderByDesc(EquipmentInfo::getUpdateTime, EquipmentInfo::getCreateTime);
            } else if (searchPageParam.getType() == 6) {


                /*RestResponse<List<String>> response = systemClient.getInfoIdsByCustomerName(searchPageParam.getKeyword());
                if (!response.isOk()) {
                    log.error("查询客户设备信息失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
                }
                if (!CollectionUtils.isEmpty(response.getData())) {
                    wrapper.in(EquipmentInfo::getId, response.getData());
                } else {
                    return new ArrayList<>();
                }
                wrapper.orderByDesc(EquipmentInfo::getUpdateTime, EquipmentInfo::getCreateTime);*/
            }
        }

        PageResult<EquipmentInfo> pageResult = page(searchPageParam, wrapper);

        List<EquipmentListDto> result = new ArrayList();


        for (EquipmentInfo equipmentInfo : pageResult.getRecords()) {
            EquipmentListDto listDto = new EquipmentListDto();
            listDto.setEquipmentId(equipmentInfo.getId());
            listDto.setEquipmentCode(equipmentInfo.getCode());
            listDto.setEquipmentName(equipmentInfo.getName());
            result.add(listDto);
        }
        return result;
    }

    @Override
    public Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }

    @Override
    public PageResult<EquipmentTreeDetailDto> listPageDto(InfoSearchDto param) {
        List<String> equipmentIds = new ArrayList<>();
        Page<EquipmentInfoQueryParam> page = new Page<>(param.getPageNo(), param.getLimit());
        if (StrUtil.isNotBlank(param.getCategoryId())) {
            List<String> categoryIds = CollectionUtils.isNotEmpty(param.getCategoryIds()) ? param.getCategoryIds() : new ArrayList<>();
            categoryIds.add(param.getCategoryId());
            param.setCategoryIds(categoryIds);
        }
        if(StringUtils.isNotBlank(param.getLocationId())){
            List<String> locationIds = CollectionUtils.isNotEmpty(param.getLocationIds()) ? param.getLocationIds() : new ArrayList<>();
            locationIds.add(param.getLocationId());
            param.setLocationIds(locationIds);
            //过滤位置下设备id
            List<String> rootEquipmentIds = equipmentInfoMapper.getMainInfoIdByLocation(locationIds);
            if(CollectionUtils.isEmpty(rootEquipmentIds)){
                return new PageResult<>();
            }
            equipmentIds = rootEquipmentIds;
        }

        if(param.getSearchType() == StaticValue.ONE){
            RestResponse<List<String>> taskRes = taskClient.getTaskUsedInfoIds();
            if(taskRes.isOk()){
                List<String>  taskUsedInfoIds = rootInfoIds(taskRes.getData());
                if(CollectionUtils.isEmpty(taskUsedInfoIds)){
                    return new PageResult<>();
                }
                //取交集
                if(CollectionUtils.isNotEmpty(equipmentIds)) {
                    List<String> intersection = equipmentIds.stream().filter(item -> taskUsedInfoIds.contains(item)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(intersection)) {
                        equipmentIds = intersection;
                    } else {
                        return new PageResult<>();
                    }
                }else{
                    equipmentIds = taskUsedInfoIds;
                }
            }else{
                log.error("获取工单使用过的设备id失败  ->" + JSONObject.toJSON(taskRes));
            }
        }else if(param.getSearchType() == StaticValue.TWO){
            RestResponse<List<String>> repairRes = taskClient.getRepairUsedInfoIds();
            if(repairRes.isOk()){
                List<String> repairUsedInfoIds = rootInfoIds(repairRes.getData());
                if(CollectionUtils.isEmpty(repairUsedInfoIds)){
                    return new PageResult<>();
                }
                //取交集
                if(CollectionUtils.isNotEmpty(equipmentIds)) {
                    List<String> intersection = equipmentIds.stream().filter(item -> repairUsedInfoIds.contains(item)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(intersection)) {
                        equipmentIds = intersection;
                    } else {
                        return new PageResult<>();
                    }
                }else{
                    equipmentIds = repairUsedInfoIds;
                }
            }else{
                log.error("获取故障单使用过的设备id失败  ->" + JSONObject.toJSON(repairRes));
            }
        }
        //判定是否有部门权限
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag()){
            List<String> authInfoIds = buildInfoSearchDto.getEquipmentIds();
            if(CollectionUtils.isEmpty(authInfoIds)){
                return new PageResult<>();
            }
            //取交集
            if(CollectionUtils.isNotEmpty(equipmentIds)) {
                List<String> intersection = equipmentIds.stream().filter(item -> authInfoIds.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(intersection)) {
                    equipmentIds = intersection;
                } else {
                    return new PageResult<>();
                }
            }else{
                equipmentIds = authInfoIds;
            }
        }
        param.setEquipmentIds(equipmentIds);

        IPage<EquipmentTreeDetailDto> result = equipmentInfoMapper.listTreeDetailDto(page, param);

        List<EquipmentTreeDetailDto> treeDetailDtos = result.getRecords();

        List<String> orgCodes = new ArrayList<>();
        List<String> manageDepartOrgCodes = treeDetailDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getManageDepart()))
                .map(EquipmentTreeDetailDto::getManageDepart).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(manageDepartOrgCodes)){
            orgCodes.addAll(manageDepartOrgCodes);
        }
        List<String> useDepartOrgCodes = treeDetailDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getUseDepart()))
                .map(EquipmentTreeDetailDto::getUseDepart).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(useDepartOrgCodes)){
            orgCodes.addAll(useDepartOrgCodes);
        }
        List<String> picIdList = treeDetailDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicId()))
                .map(EquipmentTreeDetailDto::getPicId).collect(Collectors.toList());
        Map<String, AttachmentClientDto> picMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            picMap = getAttachmentMap(picIdList);
        }
        Map<String, String> orgMap = this.getOrgMapByCodes(orgCodes);
        for (EquipmentTreeDetailDto temp :treeDetailDtos){
            if(StringUtils.isNotBlank(temp.getManageDepart())){
                temp.setManageDepartName(orgMap.get(temp.getManageDepart()));
            }
            if(StringUtils.isNotBlank(temp.getUseDepart())){
                temp.setUseDepartName(orgMap.get(temp.getUseDepart()));
            }
            if (StringUtils.isNotBlank(temp.getPicId())) {
                String[] picStrs = temp.getPicId().split(StringPool.COMMA);
                AttachmentClientDto attachmentClientDto = picMap.get(picStrs[0]);
                if(null != attachmentClientDto){
                    temp.setPicUrl(attachmentClientDto.getUrl());
                }
            }
        }
        rebuildTreeDto(result.getRecords());
        return Optional.ofNullable(PageResult.<EquipmentTreeDetailDto>builder()
                .records(treeDetailDtos)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    /**
     * 获取设备对应顶级主设备id
     * @param equipmentIds
     * @return
     */
    private List<String> rootInfoIds(List<String> equipmentIds){
        List<String> rootEquipmentIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(equipmentIds)) {
            LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentInfo::getId, equipmentIds);
            wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
            wrapper.select(EquipmentInfo::getLayerCode);
            List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(equipmentInfos)) {
                equipmentInfos.stream().forEach(info -> {
                    if (StringUtils.isNotBlank(info.getLayerCode())) {
                        String[] layerCodes = info.getLayerCode().split(StringPool.SLASH);
                        rootEquipmentIds.add(layerCodes[0]);
                    }
                });
            }
        }
        return rootEquipmentIds;
    }

    /**
     * 判断是否有子节点
     * @param treeDetailDtos
     */
    private void rebuildTreeDto(List<EquipmentTreeDetailDto> treeDetailDtos){
        if(CollectionUtils.isNotEmpty(treeDetailDtos)){
            List<String> equipmentIds = treeDetailDtos.stream().map(EquipmentTreeDetailDto::getId).distinct().collect(Collectors.toList());
            Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds, true);
            LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentInfo::getParentId, equipmentIds);
            wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
            wrapper.orderByDesc(EquipmentInfo::getUpdateTime);
            wrapper.select(EquipmentInfo::getId, EquipmentInfo::getName, EquipmentInfo::getParentId, EquipmentInfo::getType);
            List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
            Map<String, List<EquipmentInfo>> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(equipmentInfos)) {
                map = equipmentInfos.stream().collect(Collectors.groupingBy(EquipmentInfo::getParentId));
            }
            for (EquipmentTreeDetailDto treeDetailDto : treeDetailDtos) {
                List<EquipmentInfo> child = map.get(treeDetailDto.getId());
                if (CollectionUtils.isNotEmpty(child)) {
                    treeDetailDto.setIsLeaf(false);
                }
                treeDetailDto.setParentAllName(equipmentNameMap.get(treeDetailDto.getId()));
            }
        }
    }

    @Override
    public List<EquipmentTreeDetailDto> secondaryInfoList(String parentId){
        List<EquipmentTreeDetailDto> treeDetailDtos = equipmentInfoMapper.secondaryInfoList(parentId);
        if(CollectionUtils.isNotEmpty(treeDetailDtos)) {
            List<String> picIdList = treeDetailDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicId()))
                    .map(EquipmentTreeDetailDto::getPicId).collect(Collectors.toList());
            Map<String, AttachmentClientDto> picMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(picIdList)) {
                picMap = getAttachmentMap(picIdList);
            }
            for (EquipmentTreeDetailDto temp :treeDetailDtos){
                if (StringUtils.isNotBlank(temp.getPicId())) {
                    String[] picStrs = temp.getPicId().split(StringPool.COMMA);
                    AttachmentClientDto attachmentClientDto = picMap.get(picStrs[0]);
                    if(null != attachmentClientDto){
                        temp.setPicUrl(attachmentClientDto.getUrl());
                    }
                }
            }
            rebuildTreeDto(treeDetailDtos);
        }
        return treeDetailDtos;
    }

    @Override
    public List<EquipmentScreenDto> getScreenInfos(List<String> equipmentIds) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getCode, EquipmentInfo::getName);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        return CopyDataUtil.copyList(equipmentInfos, EquipmentScreenDto.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String saveByParam(EquipmentInfoAddParam addParam) {
        if(StringUtils.isNotBlank(addParam.getItemNo()) && check(null, addParam.getItemNo())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("item_no_exists", null, LocaleContextHolder.getLocale())));
        }
        if(StringUtils.isNotBlank(addParam.getCode()) && checkCode(null, addParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of("编码重复"));
        }
        clearCache();
        EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(addParam, EquipmentInfo.class);
        equipmentInfo.setCode(StringUtils.isNotBlank(addParam.getCode()) ? addParam.getCode() : addParam.getItemNo());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentInfo);
        String id = UUID.randomUUID().toString().replace("-","");
        equipmentInfo.setId(id);
        String layerCode = this.getLayerCode(addParam.getParentId(),id);
        equipmentInfo.setLayerCode(layerCode);

        //校验父节点是否位置id
        if(equipmentLocationService.checkIsLocation(addParam.getParentId())){
            equipmentInfo.setType(StaticValue.ONE);
            equipmentInfo.setLocationId(addParam.getParentId());
        }else{
            //子设备继承父级位置id
            EquipmentInfo parentEquipment = (EquipmentInfo) this.getById(addParam.getParentId());
            equipmentInfo.setLocationId(parentEquipment.getLocationId());
            equipmentInfo.setType(StaticValue.TWO);
        }
        EquipmentCategoryDto equipmentCategoryDto = null;
        if(StringUtils.isNotBlank(addParam.getCategoryId())) {
            equipmentCategoryDto = equipmentCategoryService.getCategorySummary(addParam.getCategoryId());
            equipmentInfo.setPicId(StringUtils.isNotBlank(addParam.getPicId()) ? addParam.getPicId() : StringUtils.isNotBlank(equipmentCategoryDto.getPicIds()) ? equipmentCategoryDto.getPicIds() : null);
        }

        save(equipmentInfo);
        statusService.updateEquipmentStatus(addParam.getRunningStatus(), id, new Date());

        // 保存设备扩展属性
        if(null != addParam.getPropDtos()) {
            equipmentInfoPropService.saveOrUpdateBath(addParam.getPropDtos(), equipmentInfo.getId(), true);
        }
        //保存特种设备
        if(null != addParam.getSpecialInfo() && addParam.getSpecialInfo()){
            specialService.saveOrUpdateByParam(addParam.getSpecialDto(), equipmentInfo.getId());
        }
        //保存oee/状态参数
        if(null != equipmentCategoryDto){
            Boolean oeeOpen = equipmentCategoryDto.getOeeOpen();
            UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
            //异步，一方面提高速度，一方面同步测点需要回调设备服务信息，不同线程处理不会互相影响
            executorService.execute(() -> {
                UserContextHolder.getContext().setUserBaseInfo(userBaseInfo);
                this.updateOee(oeeOpen, equipmentInfo.getId());
                //同步测点、部件
                parameterClient.saveCategoryPoint(equipmentInfo.getId(), addParam.getCategoryId(), true);
            });
        }

        return equipmentInfo.getId();
    }

    /**
     * 新增/删除oee参数
     */
    private void updateOee(Boolean oeeOpen, String equipmentId){
        try {
            List<String> equipmentIds = Arrays.asList(new String[]{equipmentId});
            OeeEditDto oeeEditDto = new OeeEditDto();
            oeeEditDto.setEquipmentIds(equipmentIds);
            if (oeeOpen) {
                //生成oee参数
                parameterClient.createOeeParam(oeeEditDto);
            } else {
                parameterClient.deleteOeeParam(oeeEditDto);
            }
        }catch (Exception e){
            log.error("连接iot编辑oee失败");
        }
    }

    @Override
    public Boolean clearCache(){
        Cache cache = cacheManager.getCache("locationTreeCache");
        cache.clear();
        Cache infoTreeCache = cacheManager.getCache("equipmentTreeCache");
        infoTreeCache.clear();
        return true;
    }

    private Boolean check(String id, String itemNo){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(EquipmentInfo::getId, id);
        }
        wrapper.eq(EquipmentInfo::getItemNo, itemNo);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        return equipmentInfoMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    private Boolean checkCode(String id, String code){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(EquipmentInfo::getId, id);
        }
        wrapper.eq(EquipmentInfo::getCode, code);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        return equipmentInfoMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    /**
     * 获取层级编码
     * @param parentId
     * @return
     */
    public String getLayerCode(String parentId, String id){
        log.info("生成层级编码");
        StringBuffer layerCode = new StringBuffer();
        String parentLayerCode = equipmentInfoMapper.getLayerCode(parentId);
        if(StringUtils.isNotBlank(parentLayerCode)){
            layerCode.append(parentLayerCode).append(StringPool.SLASH).append(id);
        }else{
            layerCode.append(id);
        }
        return layerCode.toString();
    }

    @Override
    public Date getEffectiveDate(String equipmentId) {
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectById(equipmentId);
        if (null == equipmentInfo) {
            return null;
        }
        EquipmentCategory equipmentCategory = (EquipmentCategory) equipmentCategoryService
                .getById(equipmentInfo.getCategoryId());

        // 获取设备的校准日期
        String calibrationDate = equipmentInfoPropService.getCalibrationDateByEquipmentId(equipmentId);
        if (StrUtil.isNotBlank(calibrationDate)) {
            if (null != equipmentCategory && equipmentCategory.getIntervalPeriod() != null) {
                return calculateEffectiveDate(DateUtil.parseDate(calibrationDate), equipmentCategory.getIntervalPeriod());
            }
        }
        return null;
    }

    /**
     * 新增校验工单
     *
     * @param equipmentInfo
     * @param equipmentCategory
     */
    private void createCalibrationTaskOrder(EquipmentInfo equipmentInfo, EquipmentCategory equipmentCategory, Date calibrationDate) {
        if (equipmentCategory.getIntervalPeriod() != null && equipmentCategory.getRemindBefore() != null) {
            Date effectiveDate = calculateEffectiveDate(calibrationDate, equipmentCategory.getIntervalPeriod());
            if (null != effectiveDate) {
                CalibrationTaskOrderAddParam calibrationTaskOrderAddParam = new CalibrationTaskOrderAddParam();
                Date displayDate = calculateDisplayDate(effectiveDate, equipmentCategory.getRemindBefore());

                calibrationTaskOrderAddParam.setPlanCalibrateDate(effectiveDate);
                calibrationTaskOrderAddParam.setDisplayDate(displayDate);
                calibrationTaskOrderAddParam.setEquipmentId(equipmentInfo.getId());
                EquipmentInfo moreInfo = (EquipmentInfo) this.getById(equipmentInfo.getId());
                calibrationTaskOrderAddParam.setCalibrationId(moreInfo.getCalibrationId());

                String calibrationId = calibrationTaskOrderService.saveCalibration(calibrationTaskOrderAddParam);
                equipmentInfo.setCalibrationId(calibrationId);
                //更新设备关联校准单
                updateById(equipmentInfo);
            }
        }
    }

    /**
     * 计算有效日期
     *
     * @param calibrationDate 校准日期
     * @param intervalPeriod  校准周期（单位：月）
     * @return
     */
    private Date calculateEffectiveDate(Date calibrationDate, Integer intervalPeriod) {
        if (calibrationDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(calibrationDate);
            calendar.add(Calendar.MONTH, intervalPeriod);
            return calendar.getTime();
        }

        return null;
    }

    /**
     * 计算工单显示日期
     *
     * @param effectiveDate 有效日期
     * @param remindBefore  校准提醒（单位：天）
     * @return
     */
    private Date calculateDisplayDate(Date effectiveDate, Integer remindBefore) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(effectiveDate);
        calendar.add(Calendar.DATE, -remindBefore);
        return calendar.getTime();
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByParam(EquipmentInfoEditParam editParam) {
        if(StringUtils.isNotBlank(editParam.getItemNo()) && check(editParam.getId(), editParam.getItemNo()) ){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("item_no_exists", null, LocaleContextHolder.getLocale())));
        }
        if(StringUtils.isNotBlank(editParam.getCode()) &&  checkCode(editParam.getId(), editParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of("编码重复"));
        }
        rebuildOrgUser(editParam);
        EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(editParam, EquipmentInfo.class);
        equipmentInfo.setCode(StringUtils.isNotBlank(editParam.getCode()) ? editParam.getCode() : editParam.getItemNo());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentInfo);
        /**上级节点修改，修改树结构下所有层级编码*/
        EquipmentInfo oldInfo = (EquipmentInfo)this.getById(editParam.getId());
        if((null != editParam.getParentId() && !oldInfo.getParentId().equals(editParam.getParentId()))
                || !oldInfo.getName().equals(editParam.getName())
                || StringUtils.isBlank(oldInfo.getCategoryId())){
            //父节点修改、名称修改、从左侧新增的设备补充设备位置的时候，清理设备树缓存
            clearCache();
        }
        if(null != editParam.getParentId() && !oldInfo.getParentId().equals(editParam.getParentId())){
            Integer toParentType;
            if(equipmentLocationService.checkIsLocation(editParam.getParentId())){
                toParentType = StaticValue.ONE;
            }else{
                toParentType = StaticValue.TWO;
            }
            this.changeEquipmentParent(equipmentInfo.getId(), toParentType, editParam.getParentId(), oldInfo.getLayerCode());
        }
        //更新设备状态参数/运行历程
        this.pushInfoStatusParam(oldInfo, equipmentInfo);

        // 更新设备
        updateById(equipmentInfo);
        // 保存设备扩展属性
        equipmentInfoPropService.deleteByInfoId(equipmentInfo.getId());
        if(null != editParam.getPropDtos()) {
            equipmentInfoPropService.saveOrUpdateBath(editParam.getPropDtos(), equipmentInfo.getId(), false);
        }
        //修改特种设备
        if(null != editParam.getSpecialInfo() && editParam.getSpecialInfo()){
            specialService.saveOrUpdateByParam(editParam.getSpecialDto(), equipmentInfo.getId());
        }
        return true;
    }

    /**
     * 推送iot，修改设备状态默认参数
     * @param oldInfo
     * @param info
     */
    private void pushInfoStatusParam(EquipmentInfo oldInfo, EquipmentInfo info){
        InfoDefaultStatusParamDto dto = InfoDefaultStatusParamDto.builder().equipmentId(info.getId()).runningStatus(info.getRunningStatus())
                .healthStatus(info.getHealthStatus()).build();
        if(!oldInfo.getRunningStatus().equals(info.getRunningStatus())){
            Date date = new Date();
            //运行状态修改
            statusService.updateEquipmentStatus(info.getRunningStatus(), info.getId(), date);
            if (info.getRunningStatus() != RunningStatusType.NORMAL.getValue()) {
                //非正常状态，需要关闭报警日志,mars参数状态修改为正常
                dto.setCloseWarn(true);
                dto.setDate(date);
            }
        }
        try {
            parameterClient.pushInfoStatusParam(dto);
        }catch (Exception e){
            log.error("连接iot更新mars状态参数失败");
        }
    }

    private void rebuildOrgUser(EquipmentInfoEditParam editParam){
        //查询会返回成本中心、管理部门、使用部门：code/name，管理责任人、使用责任人：uid/name
        //重新编辑的情况下需要用/切割取第一个
        if(StringUtils.isNotBlank(editParam.getCostCenter())){
            editParam.setCostCenter(editParam.getCostCenter().split(StringPool.SLASH)[0]);
        }
        if(StringUtils.isNotBlank(editParam.getManageDepart())){
            editParam.setManageDepart(editParam.getManageDepart().split(StringPool.SLASH)[0]);
        }
        if(StringUtils.isNotBlank(editParam.getManagePrincipal())){
            editParam.setManagePrincipal(editParam.getManagePrincipal().split(StringPool.SLASH)[0]);
        }
        if(StringUtils.isNotBlank(editParam.getUseDepart())){
            editParam.setUseDepart(editParam.getUseDepart().split(StringPool.SLASH)[0]);
        }
        if(StringUtils.isNotBlank(editParam.getUsePrincipal())){
            editParam.setUsePrincipal(editParam.getUsePrincipal().split(StringPool.SLASH)[0]);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public EquipmentInfoDto getDtoById(String id) {
        EquipmentInfoDto equipmentInfoDto = equipmentInfoMapper.getDtoById(id);
        if(null != equipmentInfoDto) {
            buildDto(equipmentInfoDto);
            BomParentDto bomParentDto = equipmentInfoMapper.getBomDtoById(id);
            List<BomParentDto> dtos = new ArrayList<>();
            dtos.add(bomParentDto);
            getParentDtos(bomParentDto, dtos);
            Collections.reverse(dtos);
            equipmentInfoDto.setBomParent(dtos);
        }
        return equipmentInfoDto;
    }

    @Override
    public EquipmentInfoDto getDtoByPositionTagId(String id) {
        return equipmentInfoMapper.getDtoByPositionTagId(id);
    }

    private void getParentDtos(BomParentDto bomParentDto, List<BomParentDto> dtos){

        if(bomParentDto.getType() == 1 || bomParentDto.getType() == 11){
            BomParentDto parentDto = equipmentLocationService.getBomDtoById(bomParentDto.getParentId());
            dtos.add(parentDto);
            if(parentDto.getParentId().equals("0")){
                return;
            }else{
                getParentDtos(parentDto, dtos);
            }

        }else if(bomParentDto.getType() == 12){
            BomParentDto parentDto = equipmentInfoMapper.getBomDtoById(bomParentDto.getParentId());
            dtos.add(parentDto);
            getParentDtos(parentDto, dtos);
        }
    }

    private String getIotStatusName(Map<Integer, WarnConfigDetailDto> warnLevelMap, Integer iotStatus){
        if(null == iotStatus){
            return IotParamStatusType.NORMAL.getName();
        }
        IotParamStatusType statusType = IotParamStatusType.getEnumByValue(iotStatus);
        if(null != statusType){
            return statusType.getName();
        }else{
            WarnConfigDetailDto warnConfigDetailDto = warnLevelMap.get(iotStatus);
            return null != warnConfigDetailDto ? warnConfigDetailDto.getName() : warnConfigDetailDto.getName();
        }
    }

    private Map<Integer, WarnConfigDetailDto> getWarnMap(){
        Map<Integer, WarnConfigDetailDto> warnLevelMap = new HashMap<>();
        RestResponse<List<WarnConfigDetailDto>> restResponse = parameterClient.getWarnLevelList();
        if(restResponse.isOk()){
            warnLevelMap = restResponse.getData().stream().collect(Collectors.toMap(WarnConfigDetailDto::getLevel, v -> v, (v1,v2) -> v1));
        }else{
            log.error("获取报警等级失败");
        }
        return warnLevelMap;
    }

    private void buildDto(EquipmentInfoDto equipmentInfoDto){
        //报警状态
        Map<Integer, WarnConfigDetailDto> warnLevelMap = this.getWarnMap();
        equipmentInfoDto.setIotStatusName(this.getIotStatusName(warnLevelMap, equipmentInfoDto.getIotStatus()));
        if(null != equipmentInfoDto.getRunningStatus()){
            equipmentInfoDto.setRunningStatusName(RunningStatusType.getNameByValue(equipmentInfoDto.getRunningStatus()));
        }
        if(null != equipmentInfoDto.getHealthStatus()){
            equipmentInfoDto.setHealthStatusName(HealthStatusType.getNameByValue(equipmentInfoDto.getHealthStatus()));
        }
        List<String> categoryIds = StringUtils.isNotBlank(equipmentInfoDto.getCategoryId()) ? Collections.singletonList(equipmentInfoDto.getCategoryId()) : null;
        //概览图是否显示
        Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(categoryIds, 2);
        if(StringUtils.isNotBlank(equipmentInfoDto.getCategoryId())){
            equipmentInfoDto.setEnableOverview(overviewEnableMap.get(equipmentInfoDto.getCategoryId()));
        }

        //类型全路径名称
        Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
        //设备特种设备集合，不包含自身
        Map<String, String> equipmentNameMap = this.getDepthName(Collections.singletonList(equipmentInfoDto.getId()), true);
        equipmentInfoDto.setCategoryAllName(categoryNameMap.get(equipmentInfoDto.getCategoryId()));
        equipmentInfoDto.setParentAllName(equipmentNameMap.get(equipmentInfoDto.getId()));

        if(equipmentInfoDto.getType() != StaticValue.ONE){
            Map<String, String> parentNameMap = this.getNameMapByIds(Collections.singletonList(equipmentInfoDto.getParentId()));
            //主设备查询位置
            equipmentInfoDto.setParentName(parentNameMap.get(equipmentInfoDto.getParentId()));
        }
        List<String> supplierIds = new ArrayList<>();
        if(StringUtils.isNotBlank(equipmentInfoDto.getManufacturerId())){
            supplierIds.add(equipmentInfoDto.getManufacturerId());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getSupplierId())){
            supplierIds.add(equipmentInfoDto.getSupplierId());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getDesignCompanyId())){
            supplierIds.add(equipmentInfoDto.getDesignCompanyId());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getInstallCompanyId())){
            supplierIds.add(equipmentInfoDto.getInstallCompanyId());
        }
        if(CollectionUtils.isNotEmpty(supplierIds)) {
            RestResponse<Map<String, ManufacturerSupplierDto>> res = baseServiceClient.getListByIds(ArrayUtil.toArray(supplierIds, String.class));
            if (res.isOk()) {
                ManufacturerSupplierDto supplierDto;
                if(StringUtils.isNotBlank(equipmentInfoDto.getManufacturerId())){
                    supplierDto = res.getData().get(equipmentInfoDto.getManufacturerId());
                    equipmentInfoDto.setManufacturerName(null != supplierDto ? supplierDto.getName() : null);
                }
                if(StringUtils.isNotBlank(equipmentInfoDto.getSupplierId())){
                    supplierDto = res.getData().get(equipmentInfoDto.getSupplierId());
                    equipmentInfoDto.setSupplierName(null != supplierDto ? supplierDto.getName() : null);
                }
                if(StringUtils.isNotBlank(equipmentInfoDto.getDesignCompanyId())){
                    supplierDto = res.getData().get(equipmentInfoDto.getDesignCompanyId());
                    equipmentInfoDto.setDesignCompanyName(null != supplierDto ? supplierDto.getName() : null);
                }
                if(StringUtils.isNotBlank(equipmentInfoDto.getInstallCompanyId())){
                    supplierDto = res.getData().get(equipmentInfoDto.getInstallCompanyId());
                    equipmentInfoDto.setInstallCompanyName(null != supplierDto ? supplierDto.getName() : null);
                }
            } else {
                log.info("获取厂商失败");
            }
        }
        // 获取设备维护人员关联id列表
        if (null != equipmentInfoDto.getMaintainerIds() && equipmentInfoDto.getMaintainerIds().length > 0) {
            // 获取并设置人员名称
            RestResponse<String> personNameRes = baseServiceClient.getPersonNamesByIds(equipmentInfoDto.getMaintainerIds());
            if (personNameRes.isOk()) {
                String data = personNameRes.getData();
                if (data != null) {
                    equipmentInfoDto.setMaintainerNames(data);
                }
            }
        }

        // 获取设备班组关联id列表
        if (null != equipmentInfoDto.getTeamIds() && equipmentInfoDto.getTeamIds().length > 0) {
            // 获取并设置班组名称
            RestResponse<String> teamRes = baseServiceClient.getTeamNamesByIds(equipmentInfoDto.getTeamIds());
            if (teamRes.isOk()) {
                String data = teamRes.getData();
                if (data != null) {
                    equipmentInfoDto.setTeamNames(data);
                }
            }
        }

        List<String> orgCodes = new ArrayList<>();
        if(StringUtils.isNotBlank(equipmentInfoDto.getManageDepart())){
            orgCodes.add(equipmentInfoDto.getManageDepart());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getUseDepart())){
            orgCodes.add(equipmentInfoDto.getUseDepart());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getCostCenter())){
            orgCodes.add(equipmentInfoDto.getCostCenter());
        }
        Map<String, String> orgMap = this.getOrgMapByCodes(orgCodes);

        List<String> staffUids = new ArrayList<>();
        if(StringUtils.isNotBlank(equipmentInfoDto.getManagePrincipal())){
            staffUids.add(equipmentInfoDto.getManagePrincipal());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getUsePrincipal())){
            staffUids.add(equipmentInfoDto.getUsePrincipal());
        }
        Map<String, String> staffMap = this.getMapByUids(staffUids);

        if(StringUtils.isNotBlank(equipmentInfoDto.getCostCenter())){
            String name = orgMap.get(equipmentInfoDto.getCostCenter());
            equipmentInfoDto.setCostCenterName(name);
            //拼接部门(code/name)
            equipmentInfoDto.setCostCenter(StringUtils.isNoneBlank(name) ? equipmentInfoDto.getCostCenter() + StringPool.SLASH + name : equipmentInfoDto.getCostCenter());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getManageDepart())){
            String name = orgMap.get(equipmentInfoDto.getManageDepart());
            equipmentInfoDto.setManageDepartName(name);
            equipmentInfoDto.setManageDepart(StringUtils.isNoneBlank(name) ? equipmentInfoDto.getManageDepart() + StringPool.SLASH + name : equipmentInfoDto.getManageDepart());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getManagePrincipal())){
            String name = staffMap.get(equipmentInfoDto.getManagePrincipal());
            equipmentInfoDto.setManagePrincipalName(name);
            equipmentInfoDto.setManagePrincipal(StringUtils.isNoneBlank(name) ? equipmentInfoDto.getManagePrincipal() + StringPool.SLASH + name : equipmentInfoDto.getManagePrincipal());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getUseDepart())){
            String name = orgMap.get(equipmentInfoDto.getUseDepart());
            equipmentInfoDto.setUseDepartName(name);
            equipmentInfoDto.setUseDepart(StringUtils.isNoneBlank(name) ? equipmentInfoDto.getUseDepart() + StringPool.SLASH + name : equipmentInfoDto.getUseDepart());
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getUsePrincipal())){
            String name = staffMap.get(equipmentInfoDto.getUsePrincipal());
            equipmentInfoDto.setUsePrincipalName(name);
            equipmentInfoDto.setUsePrincipal(StringUtils.isNoneBlank(name) ? equipmentInfoDto.getUsePrincipal() + StringPool.SLASH + name : equipmentInfoDto.getUsePrincipal());
        }

        //获取客户信息
        //buildCustomer(equipmentInfoDto);
        //构造扩展属性
        equipmentInfoDto.setPropDtos(equipmentInfoPropService.getListByInfoId(equipmentInfoDto.getId(), equipmentInfoDto.getCategoryId()));
        //特种设备
        if(null != equipmentInfoDto.getSpecialInfo() && equipmentInfoDto.getSpecialInfo()){
            equipmentInfoDto.setSpecialDto(specialService.getDtoByInfoId(equipmentInfoDto.getId()));
        }
    }

    /**
     * 根据组织编码获取组织code,名称键值对
     * @param orgCodes
     * @return
     */
    private Map<String, String> getOrgMapByCodes(List<String> orgCodes){
        Map<String, String> orgMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(orgCodes)){
            RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(StringUtils.join(orgCodes.toArray(), StringPool.COMMA), "0");
            if(orgRes.isOk()){
                orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
            }else{
                log.error("获取组织失败");
            }
        }
        return orgMap;
    }

    /**
     * 根据uid集合获取uid,名称键值对
     * @param uids
     * @return
     */
    private Map<String, String> getMapByUids(List<String> uids){
        Map<String, String> uidMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(uids)){
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(uids);
            RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if (staffRes.isOk()) {
                uidMap = staffRes.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));
            }else{
                log.error("获取用户失败");
            }
        }
        return uidMap;
    }

    private void buildCustomer(EquipmentInfoDto equipmentInfoDto) {
        String[] equipmentIds = new String[]{equipmentInfoDto.getId()};
        RestResponse<Map<String, EquipmentCustomerDto>> restResponse = systemClient.getMapByEquipmentIds(equipmentIds);
        if (restResponse.isOk()) {
            Map<String, EquipmentCustomerDto> customerDtoMap = restResponse.getData();
            EquipmentCustomerDto customerDto = customerDtoMap.get(equipmentInfoDto.getId());
            if (null != customerDto) {
                equipmentInfoDto.setCustomerId(customerDto.getId());
                equipmentInfoDto.setCustomerName(customerDto.getName());
            }
        } else {
            log.info("获取客户信息失败");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public EquipmentInfoAppDto getAppDtoById(String id) {
        EquipmentInfoDto equipmentInfoDto = equipmentInfoMapper.getAppDtoById(id);
        if (equipmentInfoDto == null) {
            return new EquipmentInfoAppDto();
        }
        buildDto(equipmentInfoDto);
        Map<String, DictionaryItemDto> importanceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> importanceResponse = baseServiceClient.getItemMapByCode("importance");
        if(importanceResponse.isOk()){
            importanceMap = importanceResponse.getData();
        }else{
            log.info("获取重要程度失败");
        }

        Map<String, DictionaryItemDto> equipmentSourceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> equipmentSourceResponse = baseServiceClient.getItemMapByCode("equipment_source");
        if(equipmentSourceResponse.isOk()){
            equipmentSourceMap = equipmentSourceResponse.getData();
        }else{
            log.info("获取设备来源失败");
        }

        Map<String, DictionaryItemDto> assetStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> assetStatusResponse = baseServiceClient.getItemMapByCode("asset_status");
        if(assetStatusResponse.isOk()){
            assetStatusMap = assetStatusResponse.getData();
        }else{
            log.info("获取资产状态失败");
        }

        Map<String, DictionaryItemDto> checkStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> checkStatusResponse = baseServiceClient.getItemMapByCode("inspection_status");
        if(checkStatusResponse.isOk()){
            checkStatusMap = checkStatusResponse.getData();
        }else{
            log.info("获取检验状态失败");
        }

        if(StringUtils.isNotBlank(equipmentInfoDto.getImportance())){
            equipmentInfoDto.setImportance(null != importanceMap.get(equipmentInfoDto.getImportance()) ? importanceMap.get(equipmentInfoDto.getImportance()).getName() : null);
        }

        if(StringUtils.isNotBlank(equipmentInfoDto.getAssetStatus())){
            equipmentInfoDto.setAssetStatus(null != assetStatusMap.get(equipmentInfoDto.getAssetStatus()) ? assetStatusMap.get(equipmentInfoDto.getAssetStatus()).getName() : null);
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getSource())){
            equipmentInfoDto.setSource(null != equipmentSourceMap.get(equipmentInfoDto.getSource()) ? equipmentSourceMap.get(equipmentInfoDto.getSource()).getName() : null);
        }
        if(StringUtils.isNotBlank(equipmentInfoDto.getCheckStatus())){
            equipmentInfoDto.setCheckStatus(null != checkStatusMap.get(equipmentInfoDto.getCheckStatus()) ? checkStatusMap.get(equipmentInfoDto.getCheckStatus()).getName() : null);
        }

        EquipmentInfoAppDto equipmentInfoAppDto = CopyDataUtil.copyObject(equipmentInfoDto, EquipmentInfoAppDto.class);

        String picIds = equipmentInfoAppDto.getPicId();
        if (StringUtils.isNotBlank(picIds)) {
            String[] picStrs = picIds.split(StringPool.COMMA);
            Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(picStrs));
            AttachmentClientDto attachmentClientDto = map.get(picStrs[0]);
            if(null != attachmentClientDto){
                equipmentInfoAppDto.setPicUrl(attachmentClientDto.getUrl());
            }
        }
        return equipmentInfoAppDto;
    }

    private List<AttachmentDetailDto> buildPic(String[] picStrs, Map<String, AttachmentClientDto> map){
        List<AttachmentDetailDto> picUrls = new ArrayList<>(picStrs.length);
        for(String picId : picStrs){
            AttachmentDetailDto attachmentClientDto = CopyDataUtil.copyObject(map.get(picId), AttachmentDetailDto.class);
            if(null != attachmentClientDto) {
                picUrls.add(attachmentClientDto);
            }
        }
        return picUrls;
    }

    @Override
    public Boolean updateDoc(EquipmentDocEditParam editParam){
        EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(editParam, EquipmentInfo.class);
        return updateById(equipmentInfo);
    }

    @Override
    public EquipmentDocDto getDocByInfoId(String equipmentId){
        EquipmentDocDto equipmentDocDto = equipmentInfoMapper.getDocByInfoId(equipmentId);
        if(equipmentDocDto.getType() != StaticValue.ONE){
            Map<String, String> parentNameMap = this.getNameMapByIds(Collections.singletonList(equipmentDocDto.getParentId()));
            //主设备查询位置
            equipmentDocDto.setParentName(parentNameMap.get(equipmentDocDto.getParentId()));
        }
        if(StringUtils.isNotBlank(equipmentDocDto.getCommonDocIds())){
            String[] picStrs = equipmentDocDto.getCommonDocIds().split(StringPool.COMMA);
            Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(picStrs));
            equipmentDocDto.setCommonDocUrls(buildPic(picStrs, map));
        }
        if(StringUtils.isNotBlank(equipmentDocDto.getInfoDocIds())){
            String[] picStrs = equipmentDocDto.getInfoDocIds().split(StringPool.COMMA);
            Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(picStrs));
            equipmentDocDto.setInfoDocUrls(buildPic(picStrs, map));
        }
        return equipmentDocDto;
    }

    /**
     * 生成含设备ID的二维码图片(base64)
     *
     * @param equipmentId 设备ID
     * @return
     * @throws WriterException
     */
    private String createQrCodeById(String equipmentId){
        try {
            MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
            // 设置QR二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
            // 设置QR二维码的纠错级别（H为最高级别）具体级别信息
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            // 设置编码方式
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.MARGIN, 0);

            String equipmentQrCode = "equipmentId:" + equipmentId;

            // 参数顺序分别为：二维码内容，编码类型，生成图片宽度，生成图片高度，设置参数
            BitMatrix bm = multiFormatWriter.encode(equipmentQrCode, BarcodeFormat.QR_CODE, OR_LENGTH, OR_LENGTH, hints);
            BufferedImage image = new BufferedImage(OR_LENGTH, OR_LENGTH, BufferedImage.TYPE_INT_RGB);

            // 开始利用二维码数据创建Bitmap图片，分别设为黑（0xFFFFFFFF）白（0xFF000000）两色
            for (int x = 0; x < OR_LENGTH; x++) {
                for (int y = 0; y < OR_LENGTH; y++) {
                    image.setRGB(x, y, bm.get(x, y) ? QRCOLOR : BGWHITE);
                }
            }

            return bufferedImageToBase64(image);
        }catch (Exception e){
            log.error("获取二维码失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Override
    public Integer getCountByLocationId(List<String> equipmentLocationIds, List<String> customerInfoIds) {
        if (CollectionUtils.isEmpty(equipmentLocationIds)) {
            return 0;
        }
        return equipmentInfoMapper.getCountByLocationId(equipmentLocationIds, customerInfoIds);
    }

    @Override
    public Integer getCountByTypeId(List<String> equipmentTypeIds, List<String> customerInfoIds) {
        if (CollectionUtils.isEmpty(equipmentTypeIds)) {
            return 0;
        }
        return equipmentInfoMapper.getCountByTypeId(equipmentTypeIds, customerInfoIds);
    }

    @Override
    public Boolean checkSupplierUsed(String relatedId) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.<EquipmentInfo>lambdaQuery();
        // 设置查询需要返回的字段
        wrapper.select(EquipmentInfo::getId);
        wrapper.and(w -> w.eq(EquipmentInfo::getSupplierId, relatedId)
                .or()
                .eq(EquipmentInfo::getManufacturerId, relatedId)
                .or()
                .eq(EquipmentInfo::getInstallCompanyId, relatedId)
                .or()
                .eq(EquipmentInfo::getDesignCompanyId, relatedId));
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        return equipmentInfoMapper.selectCount(wrapper) > 0;

    }

    @Override
    public List<String> checkPartUsed(String[] partIds) {
        if(null!=partIds){
            //备件-设备类型 以及备件-设备使用情况
            /*List<String> relPartIds = categoryPartRelService.checkPartUsed(partIds);
            if (CollectionUtils.isNotEmpty(relPartIds)) {
                return relPartIds;
            }*/
        }

        return null;
    }

    @Override
    public Map<String, EquipmentListDto> getListByIds(String[] equipmentIds) {
        if(null == equipmentIds || equipmentIds.length <= 0){
            return new HashMap<>(StaticValue.ONE);
        }
        List<EquipmentListDto> listDtos = equipmentInfoMapper.getListByIds(equipmentIds);
        if (CollectionUtils.isEmpty(listDtos)) {
            return new HashMap<>(StaticValue.ONE);
        }
        List<String> staffUids = listDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getManagePrincipal())).map(EquipmentListDto::getManagePrincipal).distinct().collect(Collectors.toList());

        Map<String, String> staffMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(staffUids)){
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(staffUids);
            RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if (staffRes.isOk()) {
                staffMap = staffRes.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));
            }else{
                log.error("获取用户失败");
            }
        }
        //type 为1是主设备,父节点为位置
        List<String> infoIds = listDtos.stream().filter(dto -> dto.getEquipmentType() != StaticValue.ONE)
                .map(EquipmentListDto::getParentId).distinct().collect(Collectors.toList());
        Map<String, String> infoNameMap = this.getNameMapByIds(infoIds);

        Map map = new HashMap(listDtos.size());
        List<String> picIdList = listDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicId()))
                .map(EquipmentListDto::getPicId).collect(Collectors.toList());
        Map<String, AttachmentClientDto> picMap = this.getAttachmentMap(picIdList);

        List<String> categoryIds = listDtos.stream().map(EquipmentListDto::getCategoryId).distinct().collect(Collectors.toList());
        Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
        //设备父级节点名称集合，不包含自身
        Map<String, String> equipmentNameMap = this.getDepthName(Arrays.asList(equipmentIds), true);

        for (EquipmentListDto dto : listDtos) {
            dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
            dto.setParentAllName(equipmentNameMap.get(dto.getEquipmentId()));
            if(dto.getEquipmentType() != StaticValue.ONE) {
                //子设备，父节点名称切换为设备名称
                dto.setParentName(infoNameMap.get(dto.getParentId()));
            }
            if(StringUtils.isNotBlank(dto.getPicId())){
                //取第一张图片
                String[] picIds = dto.getPicId().split(StringPool.COMMA);
                dto.setPicId(picIds[0]);
                AttachmentClientDto attachmentClientDto = picMap.get(picIds[0]);
                dto.setPicUrl(null != attachmentClientDto ? attachmentClientDto.getUrl() : null);
            }
            if(StringUtils.isNotBlank(dto.getManagePrincipal())){
                dto.setManagePrincipalUid(dto.getManagePrincipal());
                dto.setManagePrincipal(staffMap.get(dto.getManagePrincipal()));
            }
            map.put(dto.getEquipmentId(), dto);
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<EquipmentInfoDto> createQrCodeByIds(String[] ids){
        List<EquipmentInfo> equipments = this.listByIds(ListUtil.toList(ids));
        if (CollUtil.isEmpty(equipments)) {
            throw new ServiceException(ResultCode.NOT_FOUND);
        }

        List<EquipmentInfoDto> result = new ArrayList<>();
        for (EquipmentInfo equipmentInfo : equipments) {
            String base64 = createQrCodeById(equipmentInfo.getId());
            EquipmentInfoDto equipmentInfoDto = new EquipmentInfoDto();
            equipmentInfoDto.setBase64(base64);
            equipmentInfoDto.setId(equipmentInfo.getId());
            equipmentInfoDto.setName(equipmentInfo.getName());
            equipmentInfoDto.setCode(equipmentInfo.getCode());
            /*RestResponse<Map<String, EquipmentCustomerDto>> response = systemClient.getMapByEquipmentIds(equipmentInfo.getId().split(StringPool.COMMA));
            if (response.isOk()){
                EquipmentCustomerDto customerDto = response.getData().get(equipmentInfo.getId());
                equipmentInfoDto.setCustomerName(null != customerDto ? customerDto.getName() : "");
            }*/
            result.add(equipmentInfoDto);
        }
        return result;
    }

    @Override
    public EquipmentLabelDto qrLabel(){
        EquipmentLabelDto equipmentLabelDto = new EquipmentLabelDto();
        equipmentLabelDto.setQrPic(qrPic);
        equipmentLabelDto.setQrLabel(qrLabel);
        equipmentLabelDto.setQrBottom(qrBottom);
        return equipmentLabelDto;
    }

    /**
     * bufferedImage转base64
     *
     * @param bufferedImage
     * @return
     */
    private String bufferedImageToBase64(BufferedImage bufferedImage) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "jpg", baos);
        byte[] bytes = baos.toByteArray();
        String jpg_base64 = Base64.encodeBase64String(bytes).trim();
        //删除 \r\n
        jpg_base64 = jpg_base64.replaceAll("\n", "").replaceAll("\r", "");
        return "data:image/jpg;base64," + jpg_base64;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean removeByIds(String[] ids) {
        //维护计划、维护工单
        RestResponse<List<String>> taskResponse = taskClient.checkEquipmentUsed(ids);
        if (taskResponse.isOk()) {
            List<String> equipmentIds = taskResponse.getData().stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(equipmentIds)) {
                String equipmentNames = this.getNamesByIds(equipmentIds);
                if (StringUtils.isNotBlank(equipmentNames)) {
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("objects_referenced", new Object[]{equipmentNames}, LocaleContextHolder.getLocale())));
                }
            }
        } else {
            log.error("校验工单-设备关联失败，请联系管理员");
        }

        RestResponse<List<String>> restResponse = parameterClient.haveDataSourceInfoIds(ids);
        if(restResponse.isOk()){
            List<String> equipmentIds = restResponse.getData();
            if(CollectionUtils.isNotEmpty(equipmentIds)){
                String equipmentNames = this.getNamesByIds(equipmentIds);
                if(StringUtils.isNotBlank(equipmentNames)){
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("objs_has_datasource", new Object[]{equipmentNames.toString()}, LocaleContextHolder.getLocale())));
                }
            }
        }else{
            log.error("iot-service连接失败");
        }
        RestResponse<List<String>> scadaRes = scadaClient.checkInfoIdsUsed(ids);
        if(scadaRes.isOk()){
            List<String> equipmentIds = scadaRes.getData();
            if(CollectionUtils.isNotEmpty(equipmentIds)){
                String equipmentNames = this.getNamesByIds(equipmentIds);
                if(StringUtils.isNotBlank(equipmentNames)){
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("objs_has_studio", new Object[]{equipmentNames.toString()}, LocaleContextHolder.getLocale())));
                }
            }
        }else{
            log.error("scada-service连接失败");
        }
        List<String> haveChildInfoNames = equipmentInfoMapper.haveChildInfoNames(ids);
        if(CollectionUtils.isNotEmpty(haveChildInfoNames)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("objs_has_children", new Object[]{haveChildInfoNames.toString()}, LocaleContextHolder.getLocale())));
        }

        //删除特种设备,会删掉对应部件下的所有特种设备
        specialService.deleteByInfoIds(ids);
        clearCache();
        Assert.notEmpty(ResultCode.PARAM_VALID_ERROR, ids);

        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, ids);

        return this.remove(wrapper);
    }

    @Override
    public Boolean deleteByCodes(List<String> codes, String tenantId) {
        return equipmentInfoMapper.updateByCodes(codes, tenantId) > 0;
    }

    @Override
    public List<EquipmentPartAppDto> getAppPartById(String id){
        List<EquipmentPartAppDto> equipmentPartAppDtos = new ArrayList<>();
        List<EquipmentPartRelDto> equipmentPartRelDtos = new ArrayList<>();//this.getPartDtoById(id);
        if(CollectionUtils.isNotEmpty(equipmentPartRelDtos)){
            equipmentPartAppDtos = CopyDataUtil.copyList(equipmentPartRelDtos, EquipmentPartAppDto.class);
            List<String> picIdList = equipmentPartAppDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicIds()))
                    .map(EquipmentPartAppDto::getPicIds).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(picIdList)) {
                List<String> allPicIds = new ArrayList<>();
                for (String picIdStr : picIdList) {
                    String[] strs = picIdStr.split(StringPool.COMMA);
                    allPicIds.add(strs[0]);
                    allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
                }
                RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
                if (restResponse.isOk()) {
                    Map<String, AttachmentClientDto> map = restResponse.getData();
                    for(EquipmentPartAppDto dto : equipmentPartAppDtos){
                        String picIds = dto.getPicIds();
                        if (StringUtils.isNotBlank(picIds)) {
                            String[] picStrs = picIds.split(StringPool.COMMA);
                            //取第一张
                            AttachmentClientDto attachmentClientDto = map.get(picStrs[0]);
                            if (null != attachmentClientDto) {
                                dto.setPicUrl(attachmentClientDto.getUrl());
                            }
                        }
                    }
                } else {
                    log.info("获取附件失败");
                }
            }
        }
        return equipmentPartAppDtos;
    }

    @Override
    public String getQrByInfoId(String id){
        return createQrCodeById(id);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<EquipmentMaintainerDto> getMaintainerListByIds(String[] equipmentIds) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getMaintainerIds, EquipmentInfo::getTeamIds);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);

        List<EquipmentMaintainerDto> result = new ArrayList<>(equipmentIds.length);
        Map<String, EquipmentInfo> map = equipmentInfos.stream().collect(Collectors.toMap(EquipmentInfo::getId, v -> v, (v1,v2) -> v1));
        for (String equipmentId : equipmentIds) {
            EquipmentMaintainerDto equipmentMaintainerDto = new EquipmentMaintainerDto();
            equipmentMaintainerDto.setEquipmentId(equipmentId);
            EquipmentInfo equipmentInfo = map.get(equipmentId);
            equipmentMaintainerDto.setMaintainerIds(null != equipmentInfo ? equipmentInfo.getMaintainerIds() : null);
            equipmentMaintainerDto.setTeamIds(null != equipmentInfo ? equipmentInfo.getTeamIds() : null);
            result.add(equipmentMaintainerDto);
        }
        return result;
    }

    @Override
    public List<String> getAllIds() {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getId);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(equipmentInfos)) {
            return equipmentInfos.stream().map(EquipmentInfo::getId).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public List<String> getEquipmentIdsByParam(EquipmentInfoSearchDto param) {
        if(StringUtils.isNotBlank(param.getLocationId())){
            List<String> locationIds = CollectionUtils.isNotEmpty(param.getLocationIds()) ? param.getLocationIds() : new ArrayList<>();
            locationIds.add(param.getLocationId());
            param.setLocationIds(locationIds);
        }
        if(StringUtils.isNotBlank(param.getCategoryId())){
            List<String> categoryIds = CollectionUtils.isNotEmpty(param.getCategoryIds()) ? param.getCategoryIds() : new ArrayList<>();
            categoryIds.add(param.getCategoryId());
            param.setCategoryIds(categoryIds);
        }
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            return new ArrayList<>();
        }else{
            param.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }
        return equipmentInfoMapper.getEquipmentIdsByParam(param);
    }

    @Override
    public List<String> getEquipmentIdsByParamNonAuth(EquipmentInfoSearchDto param) {
        if(StringUtils.isNotBlank(param.getLocationId())){
            List<String> locationIds = CollectionUtils.isNotEmpty(param.getLocationIds()) ? param.getLocationIds() : new ArrayList<>();
            locationIds.add(param.getLocationId());
            param.setLocationIds(locationIds);
        }
        if(StringUtils.isNotBlank(param.getCategoryId())){
            List<String> categoryIds = CollectionUtils.isNotEmpty(param.getCategoryIds()) ? param.getCategoryIds() : new ArrayList<>();
            categoryIds.add(param.getCategoryId());
            param.setCategoryIds(categoryIds);
        }
        return equipmentInfoMapper.getEquipmentIdsByParam(param);
    }

    @Override
    public Boolean checkExists(String code, String name) {
        if (StringUtils.isEmpty(code) && StringUtils.isEmpty(name)) {
            throw new ServiceException(ResultCode.PARAM_VALID_ERROR);
        }
        QueryWrapper<EquipmentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        queryWrapper.and(StringUtils.isNotEmpty(name), equipmentInfoQueryWrapper -> equipmentInfoQueryWrapper.eq("name", name));
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(queryWrapper);
        return !equipmentInfos.isEmpty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCalibrationDate(String equipmentId, Integer result, Date calibrationDate, String certificateNo) {
        EquipmentInfo equipmentInfo = (EquipmentInfo) this.getById(equipmentId);
        if (null == equipmentInfo) {
            log.error("未找到此设备");
            return false;
        }
        //合格的设备不需要修改状态
        if (result != StaticValue.ONE) {
            equipmentInfo.setHealthStatus(result);
            equipmentInfoMapper.updateById(equipmentInfo);
        }

        //禁用的设备不需要更改校准日期
        if (result != StaticValue.FIVE) {
            LambdaUpdateWrapper<EquipmentInfoProp> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EquipmentInfoProp::getDefaultValue, DateUtil.format(calibrationDate, "yyyy-MM-dd"));
            wrapper.eq(EquipmentInfoProp::getEquipmentId, equipmentId);
            wrapper.eq(EquipmentInfoProp::getName, "校准日期");
            equipmentInfoPropService.update(wrapper);

            if (StringUtils.isNotBlank(certificateNo)) {
                // 修改证书编号
                LambdaUpdateWrapper<EquipmentInfoProp> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(EquipmentInfoProp::getDefaultValue, certificateNo);
                wrapper.eq(EquipmentInfoProp::getEquipmentId, equipmentId);
                updateWrapper.eq(EquipmentInfoProp::getName, "证书编号");
                equipmentInfoPropService.update(updateWrapper);
            }

            EquipmentCategory equipmentCategory = (EquipmentCategory) equipmentCategoryService
                    .getById(equipmentInfo.getCategoryId());
            createCalibrationTaskOrder(equipmentInfo, equipmentCategory, calibrationDate);
        }

        return true;
    }

    @Override
    public List<EquipmentInfo> getEquipmentListByCategoryId(String categoryId) {
        return equipmentInfoMapper.getEquipmentListByCategoryId(categoryId);
    }

    //@Async
    @Override
    public void createCalibrationTaskOrderBatch(EquipmentCategory equipmentCategory, List<EquipmentInfo> equipmentInfoList) {
        log.info("批量生成仪表校准工单开始");
        String[] equipmentIds = equipmentInfoList.stream().map(EquipmentInfo::getId).toArray(String[]::new);
        List<EquipmentInfoProp> equipmentInfoPropList = equipmentInfoPropService.getListByEquipmentIds(equipmentIds);
        Map<String, String> map = equipmentInfoPropList.stream()
                .filter(prop -> "校准日期".equals(prop.getName()))
                .filter(prop -> prop.getDefaultValue() != null)
                .collect(Collectors.toMap(EquipmentInfoProp::getEquipmentId, EquipmentInfoProp::getDefaultValue));

        for (EquipmentInfo equipmentInfo : equipmentInfoList) {
            String value = map.get(equipmentInfo.getId());
            if (StrUtil.isNotBlank(value)) {
                createCalibrationTaskOrder(equipmentInfo, equipmentCategory, DateUtil.parseDate(value));
            }
        }
        log.info("批量生成仪表校准工单结束");
    }

    @Override
    public Map<String, List<String>> getComboMap(){
        Map<String, List<String>> map = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> importanceResponse = baseServiceClient.getItemMapByCode("importance");
        if(importanceResponse.isOk()){
            List<String> importanceList = importanceResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("importance", importanceList);
        }else{
            log.info("获取重要程度失败");
        }

        //运行状态
        List<String> runningList = Arrays.stream(RunningStatusType.values()).map(RunningStatusType::getName).collect(Collectors.toList());
        map.put("runningStatus", runningList);

        RestResponse<Map<String, DictionaryItemDto>> equipmentSourceResponse = baseServiceClient.getItemMapByCode("equipment_source");
        if(equipmentSourceResponse.isOk()){
            List<String> sourceList = equipmentSourceResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("source", sourceList);
        }else{
            log.info("获取设备来源失败");
        }

        //健康状态
        List<String> healthList = Arrays.stream(HealthStatusType.values()).map(HealthStatusType::getName).collect(Collectors.toList());
        map.put("healthStatus", healthList);

        RestResponse<Map<String, DictionaryItemDto>> assetStatusResponse = baseServiceClient.getItemMapByCode("asset_status");
        if(assetStatusResponse.isOk()){
            List<String> assetList = assetStatusResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("assetStatus", assetList);
        }else{
            log.info("获取资产状态失败");
        }
        RestResponse<Map<String, DictionaryItemDto>> checkStatusResponse = baseServiceClient.getItemMapByCode("inspection_status");
        if(checkStatusResponse.isOk()){
            List<String> checkStatusList = checkStatusResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("checkStatus", checkStatusList);
        }else{
            log.info("获取检验状态失败");
        }

        return map;
    }

    @Override
    public Map<String, String> getShowFields(Boolean exported){
        Map<String, String> showFiles = new HashMap<>();
        RestResponse<List<FormConfDetailDto>> restResponse = baseServiceClient.displayDetail("equipment", exported);
        if(restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())){
            for(FormConfDetailDto detailDto : restResponse.getData()){
                showFiles.put(detailDto.getShowFieldName(), StringUtils.isNotBlank(detailDto.getCustomZh()) ? detailDto.getCustomZh() : null);
            }
        }else{
            throw new GlobalServiceException(GlobalResultMessage.of("没有配置的表单"));
        }
        showFiles.put("extendProp", null);
        return showFiles;
    }

    @Override
    public List<EquipmentInfoExcel> getExcelList(EquipmentSpecialQueryParam queryParam) {
        //部门权限控制
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            return Collections.emptyList();
        }else{
            queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }
        List<EquipmentSpecialExcel> exportList = equipmentInfoMapper.getExportList(queryParam);

        if (CollUtil.isNotEmpty(exportList)) {
            buildExcelDtos(exportList, queryParam.getCategoryId());
        }
        return CopyDataUtil.copyList(exportList, EquipmentInfoExcel.class);
    }

    @Override
    public List<EquipmentExportJsonExcel> getExcelInfoList(EquipmentSpecialQueryParam queryParam){
        //部门权限控制
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            return Collections.emptyList();
        }else{
            queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }
        List<EquipmentSpecialExcel> exportList = equipmentInfoMapper.getExportList(queryParam);

        if (CollUtil.isNotEmpty(exportList)) {
            buildExcelDtos(exportList, null);
        }
        return CopyDataUtil.copyList(exportList, EquipmentExportJsonExcel.class);
    }

    @Override
    public List<EquipmentSpecialExcel> getSpecialExcelList(EquipmentSpecialQueryParam queryParam){
        //部门权限控制
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            return Collections.emptyList();
        }else{
            queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }
        List<EquipmentSpecialExcel> exportList = equipmentInfoMapper.getSpecialExportList(queryParam);

        if (CollUtil.isNotEmpty(exportList)) {
            buildExcelDtos(exportList, queryParam.getCategoryId());
        }

        return exportList;
    }

    private void buildExcelDtos(List<EquipmentSpecialExcel> records, String categoryId) {
        List<String> equipmentIds = records.stream().map(EquipmentSpecialExcel::getId).distinct().collect(Collectors.toList());

        /*List<String> infoParentIds = records.stream().filter(dto -> dto.getType() != StaticValue.ONE)
                .map(EquipmentSpecialExcel::getParentId).distinct().collect(Collectors.toList());*/
        List<String> categoryIds = records.stream().map(EquipmentSpecialExcel::getCategoryId).distinct().collect(Collectors.toList());
        Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
        //设备父级节点名称集合，不包含自身
        Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds,true);
        //Map<String, String> infoNameMap = this.getNameMapByIds(infoParentIds);
        List<String> firmIds = new ArrayList<>();
        List<String> manufacturerIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getManufacturerId()))
                .map(EquipmentSpecialExcel::getManufacturerId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(manufacturerIds)){
            firmIds.addAll(manufacturerIds);
        }
        List<String> supplierIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getSupplierId()))
                .map(EquipmentSpecialExcel::getSupplierId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(supplierIds)){
            firmIds.addAll(supplierIds);
        }
        List<String> designCompanyIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getDesignCompanyId()))
                .map(EquipmentSpecialExcel::getDesignCompanyId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(designCompanyIds)){
            firmIds.addAll(designCompanyIds);
        }
        List<String> installCompanyIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getInstallCompanyId()))
                .map(EquipmentSpecialExcel::getInstallCompanyId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(installCompanyIds)){
            firmIds.addAll(installCompanyIds);
        }
        Map<String, ManufacturerSupplierDto> firmMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(firmIds)) {
            RestResponse<Map<String, ManufacturerSupplierDto>> res = baseServiceClient.getListByIds(ArrayUtil.toArray(firmIds, String.class));
            if (res.isOk()) {
                firmMap = res.getData();
            } else {
                log.info("获取厂商失败");
            }
        }

        List<String> orgCodes = new ArrayList<>();
        List<String> costCenterCodes = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getCostCenter()))
                .map(EquipmentSpecialExcel::getCostCenter).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(costCenterCodes)){
            orgCodes.addAll(costCenterCodes);
        }
        List<String> manageDepartCodes = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getManageDepart()))
                .map(EquipmentSpecialExcel::getManageDepart).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(manageDepartCodes)){
            orgCodes.addAll(manageDepartCodes);
        }
        List<String> useDepartCodes = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getUseDepart()))
                .map(EquipmentSpecialExcel::getUseDepart).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(useDepartCodes)){
            orgCodes.addAll(useDepartCodes);
        }
        Map<String, String> orgMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(orgCodes)){
            RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(StringUtils.join(orgCodes.toArray(), StringPool.COMMA), "0");
            if(orgRes.isOk()){
                orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
            }else{
                log.error("获取组织失败");
            }
        }

        List<String> staffUids = new ArrayList<>();
        List<String> managePrincipalUids = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getManagePrincipal()))
                .map(EquipmentSpecialExcel::getManagePrincipal).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(managePrincipalUids)){
            staffUids.addAll(managePrincipalUids);
        }
        List<String> usePrincipalUids = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getUsePrincipal()))
                .map(EquipmentSpecialExcel::getUsePrincipal).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(usePrincipalUids)){
            staffUids.addAll(usePrincipalUids);
        }
        Map<String, String> staffMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(staffUids)){
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(staffUids);
            RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if (staffRes.isOk()) {
                staffMap = staffRes.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));
            }else{
                log.error("获取用户失败");
            }
        }

        List<String> maintainerIds = new ArrayList<>();
        List<String> teamIds = new ArrayList<>();
        for(EquipmentSpecialExcel listDto : records){
            if(null != listDto.getTeamIds() && listDto.getTeamIds().length > 0){
                teamIds.addAll(Arrays.asList(listDto.getTeamIds()));
            }
            if(null != listDto.getMaintainerIds() && listDto.getMaintainerIds().length > 0){
                maintainerIds.addAll(Arrays.asList(listDto.getMaintainerIds()));
            }
        }
        // 获取设备维护人员关联id列表
        List<MaintPersonDto> personDtos = null;
        if (CollUtil.isNotEmpty(maintainerIds)) {
            maintainerIds = maintainerIds.stream().distinct().collect(Collectors.toList());
            RestResponse<List<MaintPersonDto>> response = baseServiceClient
                    .getMaintainerListByIds(ArrayUtil.toArray(maintainerIds, String.class));
            if (response.isOk()) {
                personDtos = response.getData();
            } else {
                log.info("获取维护人员失败");
            }
        }
        // 获取设备班组关联id列表
        List<MaintTeamDto> teamDtos = null;
        if (CollUtil.isNotEmpty(teamIds)) {
            teamIds = teamIds.stream().distinct().collect(Collectors.toList());
            RestResponse<List<MaintTeamDto>> response = baseServiceClient
                    .getMaintTeamListByIds(ArrayUtil.toArray(teamIds, String.class));
            if (response.isOk()) {
                teamDtos = response.getData();
            } else {
                log.info("获取维护班组失败");
            }
        }

        Map<String, DictionaryItemDto> importanceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> importanceResponse = baseServiceClient.getItemMapByCode("importance");
        if(importanceResponse.isOk()){
            importanceMap = importanceResponse.getData();
        }else{
            log.info("获取重要程度失败");
        }

        Map<String, DictionaryItemDto> equipmentSourceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> equipmentSourceResponse = baseServiceClient.getItemMapByCode("equipment_source");
        if(equipmentSourceResponse.isOk()){
            equipmentSourceMap = equipmentSourceResponse.getData();
        }else{
            log.info("获取设备来源失败");
        }

        Map<String, DictionaryItemDto> assetStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> assetStatusResponse = baseServiceClient.getItemMapByCode("asset_status");
        if(assetStatusResponse.isOk()){
            assetStatusMap = assetStatusResponse.getData();
        }else{
            log.info("获取资产状态失败");
        }

        Map<String, DictionaryItemDto> checkStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> checkStatusResponse = baseServiceClient.getItemMapByCode("inspection_status");
        if(checkStatusResponse.isOk()){
            checkStatusMap = checkStatusResponse.getData();
        }else{
            log.info("获取检验状态失败");
        }

        Map<String, List<EquipmentPropDetailDto>> propMap = equipmentInfoPropService.getPropMap(equipmentIds, categoryId, true);
        Map<Integer, WarnConfigDetailDto> warnLevelMap = this.getWarnMap();
        for (EquipmentSpecialExcel record : records) {
            //报警状态
            record.setIotStatusName(this.getIotStatusName(warnLevelMap, record.getIotStatus()));
            record.setCategoryName(categoryNameMap.get(record.getCategoryId()));
            record.setParentName(equipmentNameMap.get(record.getId()));
            /*if(record.getType() != StaticValue.ONE){
                record.setParentName(infoNameMap.get(record.getParentId()));
            }*/
            if (CollectionUtils.isNotEmpty(personDtos) && null != record.getMaintainerIds() && record.getMaintainerIds().length > 0) {
                String[] names = personDtos.stream().filter(dto -> Arrays.asList(record.getMaintainerIds()).contains(dto.getId()))
                        .map(MaintPersonDto::getName).distinct().toArray(String[]::new);
                record.setMaintainerNames(null != names ? StringUtils.join(names, StringPool.COMMA) : null);
            }
            if (CollectionUtils.isNotEmpty(teamDtos) && null != record.getTeamIds() && record.getTeamIds().length > 0) {
                String[] names = teamDtos.stream().filter(dto -> Arrays.asList(record.getTeamIds()).contains(dto.getId()))
                        .map(MaintTeamDto::getName).distinct().toArray(String[]::new);
                record.setTeamNames(null != names ? StringUtils.join(names, StringPool.COMMA) : null);
            }
            ManufacturerSupplierDto supplierDto;
            if(StringUtils.isNotBlank(record.getManufacturerId())){
                supplierDto = firmMap.get(record.getManufacturerId());
                record.setManufacturerName(null != supplierDto ? supplierDto.getName() : null);
            }
            if(StringUtils.isNotBlank(record.getSupplierId())){
                supplierDto = firmMap.get(record.getSupplierId());
                record.setSupplierName(null != supplierDto ? supplierDto.getName() : null);
            }
            if(StringUtils.isNotBlank(record.getDesignCompanyId())){
                supplierDto = firmMap.get(record.getDesignCompanyId());
                record.setDesignCompanyName(null != supplierDto ? supplierDto.getName() : null);
            }
            if(StringUtils.isNotBlank(record.getInstallCompanyId())){
                supplierDto = firmMap.get(record.getInstallCompanyId());
                record.setInstallCompanyName(null != supplierDto ? supplierDto.getName() : null);
            }
            if(StringUtils.isNotBlank(record.getCostCenter())){
                record.setCostCenterName(orgMap.get(record.getCostCenter()));
            }
            if(StringUtils.isNotBlank(record.getManageDepart())){
                record.setManageDepartName(orgMap.get(record.getManageDepart()));
            }
            if(StringUtils.isNotBlank(record.getManagePrincipal())){
                record.setManagePrincipalName(staffMap.get(record.getManagePrincipal()));
            }
            if(StringUtils.isNotBlank(record.getUseDepart())){
                record.setUseDepartName(orgMap.get(record.getUseDepart()));
            }
            if(StringUtils.isNotBlank(record.getUsePrincipal())){
                record.setUsePrincipalName(staffMap.get(record.getUsePrincipal()));
            }
            if(null == record.getSpecialInfo() || record.getSpecialInfo().equals("0")) {
                record.setSpecialInfo("0");
                record.setCertificateNo(null);
                record.setRegistrationCode(null);
                record.setInspectionDate(null);
                record.setNextInspectionDate(null);
                record.setReportNo(null);
                record.setInspectionOrg(null);
                record.setIssuingAuthority(null);
                record.setUseOrg(null);
            }
            if(null == record.getChecked() || record.getChecked().equals("0")) {
                record.setChecked("0");
                record.setCheckUser(null);
                record.setCheckStatus(null);
                record.setCheckDate(null);
            }
            if(null == record.getNetworked()){
                record.setNetworked("0");
            }
            if(null == record.getMonitored()){
                record.setMonitored("0");
            }
            if(StringUtils.isNotBlank(record.getImportance())){
                record.setImportance(null != importanceMap.get(record.getImportance()) ? importanceMap.get(record.getImportance()).getName() : null);
            }
            if(null != record.getRunningStatus()){
                record.setRunningStatusName(RunningStatusType.getNameByValue(record.getRunningStatus()));
            }
            if(null != record.getHealthStatus()){
                record.setHealthStatusName(HealthStatusType.getNameByValue(record.getHealthStatus()));
            }
            if(StringUtils.isNotBlank(record.getAssetStatus())){
                record.setAssetStatus(null != assetStatusMap.get(record.getAssetStatus()) ? assetStatusMap.get(record.getAssetStatus()).getName() : null);
            }
            if(StringUtils.isNotBlank(record.getSource())){
                record.setSource(null != equipmentSourceMap.get(record.getSource()) ? equipmentSourceMap.get(record.getSource()).getName() : null);
            }
            if(StringUtils.isNotBlank(record.getCheckStatus())){
                record.setCheckStatus(null != checkStatusMap.get(record.getCheckStatus()) ? checkStatusMap.get(record.getCheckStatus()).getName() : null);
            }

            List<EquipmentPropDetailDto> propList = propMap.get(record.getId());
            if(CollectionUtils.isNotEmpty(propList)){
                List<String> propValue = propList.stream().map(EquipmentPropDetailDto::getDefaultValue).collect(Collectors.toList());
                record.setExtendProp(JSONObject.toJSONString(propValue));
            }
        }
    }


    @Override
    public Map<String, EquipmentStatisticsDto> getEquipmentStatistics(InfoSearchDto param) {
        if(CollectionUtils.isNotEmpty(param.getCategoryIds())) {
            param.setCategoryIds(equipmentCategoryService.getCategoryIdsByParentId(param.getCategoryIds()));
        }
        if(CollectionUtils.isNotEmpty(param.getLocationIds())) {
            param.setLocationIds(equipmentLocationService.getLocationIdsByParentId(param.getLocationIds(), true));
        }
        List<EquipmentStatisticsDto> equipmentStatisticsDtos = equipmentInfoMapper.getEquipmentStatistics(param);
        return equipmentStatisticsDtos.stream().collect(Collectors.toMap(EquipmentStatisticsDto::getEquipmentId, v -> v, (v1,v2) -> v1));
    }

    /**
     * 获取所有设备编码
     * @return
     */
    private List<String> getAllCodes(){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.isNotNull(EquipmentInfo::getCode);
        wrapper.select(EquipmentInfo::getCode);
        return equipmentInfoMapper.selectList(wrapper).stream().map(EquipmentInfo::getCode).distinct().collect(Collectors.toList());
    }
    /**
     * 获取中台组织名称，code键值对
     * @return
     */
    private Map<String, String> getOrgMap(){
        Map<String, String> map = new HashMap<>();
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        RestResponse<List<OrgTreeDto>> restResponse = porosClient.getOrgTree(user.getTenantId());
        if(restResponse.isOk()){
            List<OrgTreeDto> allList = new ArrayList<>();
            getOrgList(restResponse.getData(), allList);
            map = allList.stream().collect(Collectors.toMap(OrgTreeDto::getName, OrgTreeDto::getCode, (v1, v2) -> v1));
        }else{
            log.error("获取中台组织失败");
        }
        return map;
    }

    private void getOrgList(List<OrgTreeDto> treeDtos, List<OrgTreeDto> allList){
        if(CollectionUtils.isEmpty(treeDtos)) {
            return;
        }
        for(OrgTreeDto orgTreeDto : treeDtos){
            allList.add(orgTreeDto);
            getOrgList(orgTreeDto.getChildren(), allList);
        }
    }
    private Map<String, String> getUserMap(){
        Map<String, String> map = new HashMap<>();
        PorosStaffSearchDto staffSearchDto = new PorosStaffSearchDto();
        RestResponse<List<PorosSecStaffDto>> restResponse = porosClient.getUserList(staffSearchDto);
        if(restResponse.isOk()){
            map = restResponse.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getName, PorosSecStaffDto::getUid, (v1, v2) -> v1));
        }else{
            log.error("获取租户下用户失败");
        }
        return map;
    }

    private List<EquipmentSpecialExcel> delExcelNullData(List<EquipmentSpecialExcel> excels){
        List<EquipmentSpecialExcel> newExcels = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(excels)){
            for (EquipmentSpecialExcel excel : excels) {
                //剔除掉空行
                if (StringUtils.isNotBlank(excel.getName()) || StringUtils.isNotBlank(excel.getParentName())) {
                    newExcels.add(excel);
                }
            }
        }
        return newExcels;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String excelImport(List<EquipmentSpecialExcel> rows, Boolean propExported) {
        rows = this.delExcelNullData(rows);
        if(CollectionUtils.isEmpty(rows)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("data_null_error", null, LocaleContextHolder.getLocale())));
        }

        // 设备位置
        Map<String, EquipmentLocation> locationMap = equipmentLocationService.getAllNameIdMap();
        List<String> categoryNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryName())).map(EquipmentSpecialExcel::getCategoryName).distinct().collect(Collectors.toList());
        Map<String, EquipmentCategory> categoryMap = equipmentCategoryService.getMapByNames(categoryNames);
        EquipmentCategory equipmentCategory = StringUtils.isNotBlank(rows.get(0).getCategoryName()) ? categoryMap.get(rows.get(0).getCategoryName()) : null;
        if(null == equipmentCategory){
            log.error("未找到设备类型");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("category_error", null, LocaleContextHolder.getLocale())));
        }
        String result = checkExcelField(rows, locationMap, equipmentCategory.getCode());
        if (StrUtil.isNotBlank(result)) {
            throw new GlobalServiceException(GlobalResultMessage.of(result));
        }
        // 设备类别

        // 厂商/制造商/设计单位/安装单位
        List<String> firmNames = new ArrayList<>();
        List<String> manufacturerNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getManufacturerName()))
                .map(EquipmentSpecialExcel::getManufacturerName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(manufacturerNames)){
            firmNames.addAll(manufacturerNames);
        }
        List<String> supplierNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getSupplierName()))
                .map(EquipmentSpecialExcel::getSupplierName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(supplierNames)){
            firmNames.addAll(supplierNames);
        }
        List<String> designCompanyNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getDesignCompanyName()))
                .map(EquipmentSpecialExcel::getDesignCompanyName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(designCompanyNames)){
            firmNames.addAll(designCompanyNames);
        }
        List<String> installCompanyNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getInstallCompanyName()))
                .map(EquipmentSpecialExcel::getInstallCompanyName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(installCompanyNames)){
            firmNames.addAll(installCompanyNames);
        }
        Map<String, ManufacturerSupplierDto> firmMap = getSupplierDtoMap(firmNames);
        // 维护班组
        List<String> teamRows = rows.stream().map(EquipmentSpecialExcel::getTeamNames).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, MaintTeamDto> teamMap = getMaintTeamDtoMap(teamRows);
        // 维护人员
        List<String> personRows = rows.stream().map(EquipmentSpecialExcel::getMaintainerNames).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, MaintPersonDto> personMap = getMaintPersonDtoMap(personRows);
        //业务组织
        //Map<String, String> porosOrgMap = this.getOrgMap();
        //用户
        Map<String, String> userMap = this.getUserMap();

        Map<String, String> importanceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> importanceResponse = baseServiceClient.getItemMapByCode("importance");
        if(importanceResponse.isOk()){
            importanceMap = importanceResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取重要程度失败");
        }

        Map<String, String> equipmentSourceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> equipmentSourceResponse = baseServiceClient.getItemMapByCode("equipment_source");
        if(equipmentSourceResponse.isOk()){
            equipmentSourceMap = equipmentSourceResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取设备来源失败");
        }

        Map<String, String> assetStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> assetStatusResponse = baseServiceClient.getItemMapByCode("asset_status");
        if(assetStatusResponse.isOk()){
            assetStatusMap = assetStatusResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取资产状态失败");
        }

        Map<String, String> checkStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> checkStatusResponse = baseServiceClient.getItemMapByCode("inspection_status");
        if(checkStatusResponse.isOk()){
            checkStatusMap = checkStatusResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取检验状态失败");
        }

        // 检查各种名称是否存在
        /*String check = checkName(rows, categoryMap, locationMap, supplierMap, teamMap, personMap);
        if (StrUtil.isNotBlank(check)) {
            return check;
        }*/


        List<EquipmentInfo> equipmentInfoList = new ArrayList<>(rows.size());
        List<EquipmentInfoSpecial> specials = new ArrayList<>();
        List<EquipmentInfoProp> equipmentInfoProps = new ArrayList<>();
        List<EquipmentCategoryPropDetailDto> categoryDeatilDtos = categoryPropService.getDetailListByCategoryId(equipmentCategory.getId(), false, propExported);
        // 保存设备主表
        List<String> equipmentIds = new ArrayList<>();
        for (EquipmentSpecialExcel excel : rows) {
            EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(excel, EquipmentInfo.class);
            String equipmentId = UUID.randomUUID().toString().replace("-","");
            equipmentInfo.setId(equipmentId);
            equipmentIds.add(equipmentId);
            equipmentInfo.setType(1);
            equipmentInfo.setLayerCode(equipmentId);
            // 设备类别id
            equipmentInfo.setCategoryId(equipmentCategory.getId());
            equipmentInfo.setPicId(equipmentCategory.getPicIds());
            // 设备位置id
            EquipmentLocation equipmentLocation = locationMap.get(excel.getParentName().replace("（", "(").replace("）",")"));
            String locationId = null;
            if(null != equipmentLocation){
                locationId = equipmentLocation.getId();
            }
            equipmentInfo.setParentId(locationId);
            equipmentInfo.setLocationId(equipmentInfo.getParentId());

            equipmentInfo.setImportance(StringUtils.isNotBlank(excel.getImportance()) ? importanceMap.get(excel.getImportance()) : null);
            equipmentInfo.setRunningStatus(StringUtils.isNotBlank(excel.getRunningStatusName()) ? RunningStatusType.getValueByName(excel.getRunningStatusName()) : null);
            equipmentInfo.setHealthStatus(StringUtils.isNotBlank(excel.getHealthStatusName()) ? HealthStatusType.getValueByName(excel.getHealthStatusName()) : null);
            equipmentInfo.setAssetStatus(StringUtils.isNotBlank(excel.getAssetStatus()) ? assetStatusMap.get(excel.getAssetStatus()) : null);
            equipmentInfo.setSource(StringUtils.isNotBlank(excel.getSource()) ? equipmentSourceMap.get(excel.getSource()) : null);

            //Boolean类型导入poros-common有bug，只能转换String
            equipmentInfo.setMonitored(StringUtils.isNotBlank(excel.getMonitored()) ? excel.getMonitored().equals("1") ? true : false : null);
            equipmentInfo.setNetworked(StringUtils.isNotBlank(excel.getNetworked()) ? excel.getNetworked().equals("1") ? true : false : null);

            // 制造商、供应商、设计单位、安装单位
            ManufacturerSupplierDto firmDto;
            if(StringUtils.isNotBlank(excel.getManufacturerName())){
                firmDto = firmMap.get(excel.getManufacturerName());
                equipmentInfo.setManufacturerId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getSupplierName())){
                firmDto = firmMap.get(excel.getSupplierName());
                equipmentInfo.setSupplierId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getDesignCompanyName())){
                firmDto = firmMap.get(excel.getDesignCompanyName());
                equipmentInfo.setDesignCompanyId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getInstallCompanyName())){
                firmDto = firmMap.get(excel.getInstallCompanyName());
                equipmentInfo.setInstallCompanyId(null != firmDto ? firmDto.getId() : null);
            }
           /* if(StringUtils.isNotBlank(excel.getCostCenterName())){
                equipmentInfo.setCostCenter(porosOrgMap.get(excel.getCostCenterName()));
            }
            if(StringUtils.isNotBlank(excel.getManageDepartName())){
                equipmentInfo.setManageDepart(porosOrgMap.get(excel.getManageDepartName()));
            }
            if(StringUtils.isNotBlank(excel.getManagePrincipalName())){
                equipmentInfo.setManagePrincipal(userMap.get(excel.getManagePrincipalName()));
            }
            if(StringUtils.isNotBlank(excel.getUseDepartName())){
                equipmentInfo.setUseDepart(porosOrgMap.get(excel.getUseDepartName()));
            }*/
            if(StringUtils.isNotBlank(excel.getUsePrincipalName())){
                equipmentInfo.setUsePrincipal(userMap.get(excel.getUsePrincipalName()));
            }


            if(StringUtils.isNotBlank(excel.getTeamNames())){
                List<String> teamIds = new ArrayList<>();
                String[] teamNames = excel.getTeamNames().split(StringPool.COMMA);
                for(String teamName : teamNames){
                    MaintTeamDto teamDto = teamMap.get(teamName);
                    if(null != teamDto){
                        teamIds.add(teamDto.getId());
                    }
                }
                if(CollectionUtils.isNotEmpty(teamIds)){
                    equipmentInfo.setTeamIds(teamIds.toArray(new String[teamIds.size()]));
                }
            }
            if(StringUtils.isNotBlank(excel.getMaintainerNames())){
                List<String> personIds = new ArrayList<>();
                String[] personNames = excel.getMaintainerNames().split(StringPool.COMMA);
                for(String personName : personNames){
                    MaintPersonDto personDto = personMap.get(personName);
                    if(null != personDto){
                        personIds.add(personDto.getId());
                    }
                }
                if(CollectionUtils.isNotEmpty(personIds)){
                    equipmentInfo.setMaintainerIds(personIds.toArray(new String[personIds.size()]));
                }
            }


            if(StringUtils.isNotBlank(excel.getSpecialInfo()) && excel.getSpecialInfo().equals("1")){
                equipmentInfo.setSpecialInfo(true);
                EquipmentInfoSpecial special = new EquipmentInfoSpecial();
                special.setEquipmentId(equipmentId);
                special.setCertificateNo(excel.getCertificateNo());
                special.setRegistrationCode(excel.getRegistrationCode());
                special.setInspectionDate(excel.getInspectionDate());
                special.setNextInspectionDate(excel.getNextInspectionDate());
                special.setReportNo(excel.getReportNo());
                special.setInspectionOrg(excel.getInspectionOrg());
                special.setIssuingAuthority(excel.getIssuingAuthority());
                special.setUseOrg(excel.getUseOrg());
                specials.add(special);
            }else{
                equipmentInfo.setSpecialInfo(false);
            }
            if(StringUtils.isNotBlank(excel.getChecked()) && excel.getChecked().equals("1")){
                equipmentInfo.setChecked(true);
                equipmentInfo.setCheckDate(excel.getCheckDate());
                equipmentInfo.setCheckStatus(StringUtils.isNotBlank(excel.getCheckStatus()) ? checkStatusMap.get(excel.getCheckStatus()) : null);
                equipmentInfo.setCheckUser(excel.getCheckUser());
            }else{
                equipmentInfo.setChecked(false);
            }

            if(StringUtils.isNotBlank(equipmentInfo.getCategoryId())
                    && CollectionUtils.isNotEmpty(categoryDeatilDtos)
                    && StringUtils.isNotBlank(excel.getExtendProp())){
                List<String> extendPropList = JSONObject.parseArray(excel.getExtendProp(), String.class);
                int minSize = categoryDeatilDtos.size() < extendPropList.size() ? categoryDeatilDtos.size() : extendPropList.size();
                for(int i = 0; i < minSize; i++){
                    EquipmentCategoryPropDetailDto categoryProp = categoryDeatilDtos.get(i);
                    EquipmentInfoProp equipmentInfoProp = CopyDataUtil.copyObject(categoryProp, EquipmentInfoProp.class);
                    equipmentInfoProp.setId(null);
                    equipmentInfoProp.setEquipmentId(equipmentId);
                    equipmentInfoProp.setDefaultValue(StringUtils.isNotBlank(extendPropList.get(i)) ? extendPropList.get(i) : categoryProp.getDefaultValue());
                    equipmentInfoProps.add(equipmentInfoProp);
                }
            }

            equipmentInfoList.add(equipmentInfo);
        }
        this.saveBatch(equipmentInfoList);

        if(CollectionUtils.isNotEmpty(specials)){
            specialService.saveBatch(specials);
        }
        if(CollectionUtils.isNotEmpty(equipmentInfoProps)){
            equipmentInfoPropService.saveBatch(equipmentInfoProps);
        }

        //导入成功清理缓存
        clearCache();
        return "成功导入" + equipmentInfoList.size() + "条";
    }

    private List<EquipmentImportJsonExcel> delNullData(List<EquipmentImportJsonExcel> excels){
        List<EquipmentImportJsonExcel> newExcels = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(excels)){
            for (EquipmentImportJsonExcel excel : excels) {
                //剔除掉空行
                if (StringUtils.isNotBlank(excel.getName()) || StringUtils.isNotBlank(excel.getParentName())) {
                    newExcels.add(excel);
                }
            }
        }
        return newExcels;
    }

    @Override
    public String importPropJson(List<EquipmentImportJsonExcel> rows){
        rows = this.delNullData(rows);
        if(CollectionUtils.isEmpty(rows)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("data_null_error", null, LocaleContextHolder.getLocale())));
        }

        // 设备位置
        Map<String, EquipmentLocation> locationMap = equipmentLocationService.getAllNameIdMap();
        Map<String, EquipmentCategory> categoryMap = equipmentCategoryService.getAllNameIdMap();

        //校验
        List<String> categoryIds = new ArrayList<>();
        int i = 0;
        StringBuilder builder = new StringBuilder();
        for(EquipmentImportJsonExcel excel : rows){
            excel.setId(UUID.randomUUID().toString().replace("-",""));
            EquipmentCategory equipmentCategory = StringUtils.isNotBlank(excel.getCategoryName()) ? categoryMap.get(excel.getCategoryName().replace("（", "(").replace("）", ")")) : null;
            if (null != equipmentCategory) {
                excel.setCategoryId(equipmentCategory.getId());
                excel.setPicId(equipmentCategory.getPicIds());
                categoryIds.add(excel.getCategoryId());
            }

            // 设备位置id
            EquipmentLocation equipmentLocation = StringUtils.isNotBlank(excel.getParentName()) ? locationMap.get(excel.getParentName().replace("（", "(").replace("）",")")) : null;
            String locationId = null;
            if(null != equipmentLocation){
                locationId = equipmentLocation.getId();
                excel.setParentId(locationId);
                excel.setLocationId(locationId);
            }

            boolean flag = StringUtils.isBlank(excel.getName()) || equipmentLocation == null;
            if (flag) {
                builder.append("第 ").append(i + 2).append(" 行数据").append(StringPool.COLON);
                if (StringUtils.isBlank(excel.getName())) {
                    builder.append("请填写设备名称").append(StringPool.COMMA);
                }
                if (equipmentLocation == null) {
                    builder.append("系统中该设备位置不存在,请校验").append(StringPool.COMMA);
                }
                builder.deleteCharAt(builder.length() - 1);
                builder.append(StringPool.SEMICOLON);
            }
            i++;
        }
        if(StringUtils.isNotBlank(builder.toString())){
            throw new GlobalServiceException(GlobalResultMessage.of(builder.toString()));
        }

        // 厂商/制造商/设计单位/安装单位
        List<String> firmNames = new ArrayList<>();
        List<String> manufacturerNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getManufacturerName()))
                .map(EquipmentImportJsonExcel::getManufacturerName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(manufacturerNames)){
            firmNames.addAll(manufacturerNames);
        }
        List<String> supplierNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getSupplierName()))
                .map(EquipmentImportJsonExcel::getSupplierName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(supplierNames)){
            firmNames.addAll(supplierNames);
        }
        List<String> designCompanyNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getDesignCompanyName()))
                .map(EquipmentImportJsonExcel::getDesignCompanyName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(designCompanyNames)){
            firmNames.addAll(designCompanyNames);
        }
        List<String> installCompanyNames = rows.stream().filter(dto -> StringUtils.isNotBlank(dto.getInstallCompanyName()))
                .map(EquipmentImportJsonExcel::getInstallCompanyName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(installCompanyNames)){
            firmNames.addAll(installCompanyNames);
        }
        Map<String, ManufacturerSupplierDto> firmMap = getSupplierDtoMap(firmNames);

        Map<String, String> importanceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> importanceResponse = baseServiceClient.getItemMapByCode("importance");
        if(importanceResponse.isOk()){
            importanceMap = importanceResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取重要程度失败");
        }

        Map<String, String> equipmentSourceMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> equipmentSourceResponse = baseServiceClient.getItemMapByCode("equipment_source");
        if(equipmentSourceResponse.isOk()){
            equipmentSourceMap = equipmentSourceResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取设备来源失败");
        }

        Map<String, String> assetStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> assetStatusResponse = baseServiceClient.getItemMapByCode("asset_status");
        if(assetStatusResponse.isOk()){
            assetStatusMap = assetStatusResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取资产状态失败");
        }

        Map<String, String> checkStatusMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> checkStatusResponse = baseServiceClient.getItemMapByCode("inspection_status");
        if(checkStatusResponse.isOk()){
            checkStatusMap = checkStatusResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));
        }else{
            log.info("获取检验状态失败");
        }
        List<String> teamRows = rows.stream().map(EquipmentImportJsonExcel::getTeamNames).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        // 维护班组
        Map<String, MaintTeamDto> teamMap = getMaintTeamDtoMap(teamRows);
        // 维护人员
        List<String> personRows = rows.stream().map(EquipmentImportJsonExcel::getMaintainerNames).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, MaintPersonDto> personMap = getMaintPersonDtoMap(personRows);

        List<EquipmentInfo> equipmentInfoList = new ArrayList<>(rows.size());
        List<EquipmentInfoSpecial> specials = new ArrayList<>();
        List<EquipmentInfoProp> equipmentInfoProps = new ArrayList<>();
        Map<String,List<EquipmentCategoryPropDetailDto>> categoryDeatilMap = categoryPropService.getDetailMapByCategoryIds(categoryIds);
        // 保存设备主表
        for (EquipmentImportJsonExcel excel : rows) {
            EquipmentInfo equipmentInfo = CopyDataUtil.copyObject(excel, EquipmentInfo.class);
            String equipmentId = excel.getId();
            equipmentInfo.setType(1);
            equipmentInfo.setLayerCode(equipmentId);

            equipmentInfo.setImportance(StringUtils.isNotBlank(excel.getImportance()) ? importanceMap.get(excel.getImportance()) : null);
            equipmentInfo.setRunningStatus(StringUtils.isNotBlank(excel.getRunningStatusName()) ? RunningStatusType.getValueByName(excel.getRunningStatusName()) : null);
            equipmentInfo.setHealthStatus(StringUtils.isNotBlank(excel.getHealthStatusName()) ? HealthStatusType.getValueByName(excel.getHealthStatusName()) : null);
            equipmentInfo.setAssetStatus(StringUtils.isNotBlank(excel.getAssetStatus()) ? assetStatusMap.get(excel.getAssetStatus()) : null);
            equipmentInfo.setSource(StringUtils.isNotBlank(excel.getSource()) ? equipmentSourceMap.get(excel.getSource()) : null);

            //Boolean类型导入poros-common有bug，只能转换String
            equipmentInfo.setMonitored(StringUtils.isNotBlank(excel.getMonitored()) ? excel.getMonitored().equals("1") ? true : false : null);
            equipmentInfo.setNetworked(StringUtils.isNotBlank(excel.getNetworked()) ? excel.getNetworked().equals("1") ? true : false : null);
            equipmentInfo.setCostCenter(excel.getCostCenterName());
            equipmentInfo.setManageDepart(excel.getManageDepartName());
            equipmentInfo.setManagePrincipal(excel.getManagePrincipalName());
            equipmentInfo.setUseDepart(excel.getUseDepartName());
            equipmentInfo.setUsePrincipal(excel.getUsePrincipalName());
            // 制造商、供应商、设计单位、安装单位
            ManufacturerSupplierDto firmDto;
            if(StringUtils.isNotBlank(excel.getManufacturerName())){
                firmDto = firmMap.get(excel.getManufacturerName());
                equipmentInfo.setManufacturerId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getSupplierName())){
                firmDto = firmMap.get(excel.getSupplierName());
                equipmentInfo.setSupplierId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getDesignCompanyName())){
                firmDto = firmMap.get(excel.getDesignCompanyName());
                equipmentInfo.setDesignCompanyId(null != firmDto ? firmDto.getId() : null);
            }
            if(StringUtils.isNotBlank(excel.getInstallCompanyName())){
                firmDto = firmMap.get(excel.getInstallCompanyName());
                equipmentInfo.setInstallCompanyId(null != firmDto ? firmDto.getId() : null);
            }
            if (StringUtils.isNotBlank(excel.getTeamNames())) {
                List<String> teamIds = new ArrayList<>();
                String[] teamNames = excel.getTeamNames().split(StringPool.COMMA);
                for (String teamName : teamNames) {
                    MaintTeamDto teamDto = teamMap.get(teamName);
                    if (null != teamDto) {
                        teamIds.add(teamDto.getId());
                    }
                }
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    equipmentInfo.setTeamIds(teamIds.toArray(new String[teamIds.size()]));
                }
            }
            if (StringUtils.isNotBlank(excel.getMaintainerNames())) {
                List<String> personIds = new ArrayList<>();
                String[] personNames = excel.getMaintainerNames().split(StringPool.COMMA);
                for (String personName : personNames) {
                    MaintPersonDto personDto = personMap.get(personName);
                    if (null != personDto) {
                        personIds.add(personDto.getId());
                    }
                }
                if (CollectionUtils.isNotEmpty(personIds)) {
                    equipmentInfo.setMaintainerIds(personIds.toArray(new String[personIds.size()]));
                }
            }

            if(StringUtils.isNotBlank(excel.getSpecialInfo()) && excel.getSpecialInfo().equals("1")){
                equipmentInfo.setSpecialInfo(true);
                EquipmentInfoSpecial special = new EquipmentInfoSpecial();
                special.setEquipmentId(equipmentId);
                special.setCertificateNo(excel.getCertificateNo());
                special.setRegistrationCode(excel.getRegistrationCode());
                special.setInspectionDate(excel.getInspectionDate());
                special.setNextInspectionDate(excel.getNextInspectionDate());
                special.setReportNo(excel.getReportNo());
                special.setInspectionOrg(excel.getInspectionOrg());
                special.setIssuingAuthority(excel.getIssuingAuthority());
                special.setUseOrg(excel.getUseOrg());
                specials.add(special);
            }else{
                equipmentInfo.setSpecialInfo(false);
            }
            if(StringUtils.isNotBlank(excel.getChecked()) && excel.getChecked().equals("1")){
                equipmentInfo.setChecked(true);
                equipmentInfo.setCheckDate(excel.getCheckDate());
                equipmentInfo.setCheckStatus(StringUtils.isNotBlank(excel.getCheckStatus()) ? checkStatusMap.get(excel.getCheckStatus()) : null);
                equipmentInfo.setCheckUser(excel.getCheckUser());
            }else{
                equipmentInfo.setChecked(false);
            }
            if(StringUtils.isNotBlank(equipmentInfo.getCategoryId())) {
                List<EquipmentCategoryPropDetailDto> categoryDeatilDtos = categoryDeatilMap.get(equipmentInfo.getCategoryId());
                if (CollectionUtils.isNotEmpty(categoryDeatilDtos)
                        && StringUtils.isNotBlank(excel.getExtendProp())) {
                    Map<String, EquipmentCategoryPropDetailDto> detailDtoMap = categoryDeatilDtos.stream().collect(Collectors.toMap(EquipmentCategoryPropDetailDto::getAllName, v -> v, (v1, v2) -> v1));
                    //扩展设备格式   属性组名-属性名称:xxxx；
                    String[] extendProps = excel.getExtendProp().replace("；", ";").split(";");
                    for (String extendProp : extendProps) {
                        String[] props = extendProp.replace("：", ":").split(":");
                        EquipmentCategoryPropDetailDto categoryProp = detailDtoMap.get(props[0]);
                        if (null != categoryProp) {
                            EquipmentInfoProp equipmentInfoProp = CopyDataUtil.copyObject(categoryProp, EquipmentInfoProp.class);
                            equipmentInfoProp.setId(null);
                            equipmentInfoProp.setEquipmentId(equipmentId);
                            equipmentInfoProp.setDefaultValue(props.length == 2 ? props[1] : categoryProp.getDefaultValue());
                            equipmentInfoProps.add(equipmentInfoProp);
                        }
                    }
                }
            }

            equipmentInfoList.add(equipmentInfo);
        }
        this.saveBatch(equipmentInfoList);

        if(CollectionUtils.isNotEmpty(specials)){
            specialService.saveBatch(specials);
        }
        if(CollectionUtils.isNotEmpty(equipmentInfoProps)){
            equipmentInfoPropService.saveBatch(equipmentInfoProps);
        }

        //导入成功清理缓存
        clearCache();
        return "成功导入" + equipmentInfoList.size() + "条";
    }

    @Override
    public Boolean updateOnlineStatus(List<IotDetailDto> iotDetailDtos) {
        List<EquipmentOnlineEditParam> onlineEditParams = new ArrayList<>(iotDetailDtos.size());
        for (IotDetailDto iotDetailDto : iotDetailDtos) {
            //设备状态不为空才需要更新
            if(null != iotDetailDto.getDeviceStatus()) {
                EquipmentOnlineEditParam equipmentOnlineEditParam = new EquipmentOnlineEditParam();
                equipmentOnlineEditParam.setDeviceId(iotDetailDto.getDeviceId());
                equipmentOnlineEditParam.setOnlineStatus(iotDetailDto.getDeviceStatus());
                onlineEditParams.add(equipmentOnlineEditParam);
            }
        }
        if (CollectionUtils.isNotEmpty(onlineEditParams)) {
            return equipmentInfoMapper.updateOnlineStatus(onlineEditParams);
        }
        return false;
    }

    @Override
    public PageResult<StudioResDetailDto> getStudioList(StudioQueryParam studioQueryParam) {
        studioQueryParam.setType(StaticValue.ZERO);
        studioQueryParam.setStatus(StaticValue.ONE);
        StudioResDto studioResDto = IotUtils.queryStudio(studioQueryParam);
        return Optional.ofNullable(PageResult.<StudioResDetailDto>builder()
                .records(studioResDto.getContent())
                .total(null != studioResDto.getTotal() ? studioResDto.getTotal() : 0)
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public Integer equipmentCount(ClientHomeDto clientHomeDto) {
        return equipmentInfoMapper.equipmentCount(clientHomeDto);
    }

    @Override
    public Integer equipmentAllCount() {
        return equipmentInfoMapper.equipmentAllCount();
    }

    @Override
    public Integer equipmentJoinCount() {
        return equipmentInfoMapper.equipmentJoinCount();
    }

    @Override
    public List<String> getInfoIdsByName(String equipmentName) {
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())){
            //未查询到部门权限内设备
            return new ArrayList<>();
        }
        return equipmentInfoMapper.getInfoIdsByName(equipmentName, buildInfoSearchDto.getEquipmentIds());
    }

    @Override
    public String getNamesByIds(List<String> equipmentIds) {
        StringBuffer sb = new StringBuffer();
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.<EquipmentInfo>lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getName, EquipmentInfo::getId, EquipmentInfo::getCode);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        for(EquipmentInfo equipmentInfo : equipmentInfos){
            sb.append(equipmentInfo.getName());
            if(StringUtils.isNotBlank(equipmentInfo.getCode())){
                sb.append(StringPool.LEFT_BRACKET).append(equipmentInfo.getCode()).append(StringPool.RIGHT_BRACKET);
            }
            sb.append(StringPool.COMMA);
        }
        return sb.length() > 0 ? sb.substring(0, sb.length() - 1) : sb.toString();
    }

    /**
     * 构建排序sql
     *
     * @param sortPros
     */
    private String buildEquipmentSort(Map<String, String> sortPros) {

        if (null == sortPros || sortPros.isEmpty()) {
            return " ORDER BY ei.update_time DESC";
        }
        //是否已有排序字段
        Boolean flag = false;
        StringBuffer sb = new StringBuffer(" ORDER BY ");
        for (Map.Entry<String, String> entity : sortPros.entrySet()) {
            if (flag) {
                sb.append(", ");
            }
            if (entity.getKey().equals("createTime")) {
                flag = true;
                sb.append("create_time ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentCode")) {
                flag = true;
                sb.append("CONVERT(ei.code USING gbk) ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentName")) {
                flag = true;
                sb.append("CONVERT(ei.name USING gbk) ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentCategory")) {
                flag = true;
                sb.append("CONVERT(ec.name USING gbk) ").append(entity.getValue());
            }
        }
        if (flag) {
            return sb.toString();
        } else {
            return " ORDER BY ei.update_time DESC";
        }

    }

    /**
     * 检查导入的excel字段中名称是否存在
     *
     * @param rows
     * @param categoryMap
     * @param locationMap
     * @param supplierMap
     * @param teamMap
     * @param personMap
     */
    private String checkName(List<EquipmentInfoExcel> rows, Map<String, String> categoryMap,
                             Map<String, String> locationMap, Map<String, ManufacturerSupplierDto> supplierMap,
                             Map<String, MaintTeamDto> teamMap, Map<String, MaintPersonDto> personMap) {
        StringBuilder builder = new StringBuilder();
        /*for (EquipmentInfoExcel excel : rows) {
            String categoryId = categoryMap.get(excel.getCategoryName());
            String locationId = locationMap.get(excel.getParentName());
            ManufacturerSupplierDto manufacturerSupplierDto = supplierMap.get(rows.get(i).getSupplierName());
            String notExistTeam = getNotExistTeam(rows.get(i).getMaintTeamNames(), teamMap);
            String notExistPerson = getNotExistPerson(rows.get(i).getMaintainerNames(), personMap);

            boolean flag = (equipmentCategory == null) || (equipmentLocation == null)
                    || (StrUtil.isNotBlank(rows.get(i).getSupplierName()) && manufacturerSupplierDto == null)
                    || StrUtil.isNotBlank(notExistTeam)
                    || StrUtil.isNotBlank(notExistPerson);
            if (flag) {
                builder.append("第 ").append(i + 2).append(" 行数据").append(StringPool.COLON);
            }

            if (equipmentCategory == null) {
                builder.append("系统中该设备类别不存在,请先新增").append(StringPool.COMMA);
            }
            if (equipmentLocation == null) {
                builder.append("系统中该设备位置不存在,请先新增").append(StringPool.COMMA);
            }
            if (StrUtil.isNotBlank(rows.get(i).getSupplierName()) && manufacturerSupplierDto == null) {
                builder.append("系统中该厂商不存在,请先新增").append(StringPool.COMMA);
            }
            if (StrUtil.isNotBlank(notExistTeam)) {
                builder.append("系统中维护班组[").append(notExistTeam).append("]不存在,请先新增").append(StringPool.COMMA);
            }
            if (StrUtil.isNotBlank(notExistPerson)) {
                builder.append("系统中维护人员[").append(notExistPerson).append("]不存在,请先新增").append(StringPool.COMMA);
            }
            if (flag) {
                builder.deleteCharAt(builder.length() - 1);
                builder.append(StringPool.SEMICOLON);
            }
        }*/
        return builder.toString();
    }

    private String getNotExistPerson(String personNames, Map<String, MaintPersonDto> personMap) {
        StringBuilder notExistPerson = new StringBuilder();
        for (String personName : personNames.split(StringPool.COMMA)) {
            MaintPersonDto maintPersonDto = personMap.get(personName);
            if (StrUtil.isNotBlank(personName) && maintPersonDto == null) {
                notExistPerson.append(personName).append(StringPool.COMMA);
            }
        }
        if (notExistPerson.length() > 1) {
            notExistPerson.deleteCharAt(notExistPerson.length() - 1);
        }
        return notExistPerson.toString();
    }

    private String getNotExistTeam(String maintTeamNames, Map<String, MaintTeamDto> teamMap) {
        StringBuilder notExistTeam = new StringBuilder();
        for (String teamName : maintTeamNames.split(StringPool.COMMA)) {
            MaintTeamDto maintTeamDto = teamMap.get(teamName);
            if (StrUtil.isNotBlank(teamName) && maintTeamDto == null) {
                notExistTeam.append(teamName).append(StringPool.COMMA);
            }
        }
        if (notExistTeam.length() > 1) {
            notExistTeam.deleteCharAt(notExistTeam.length() - 1);
        }
        return notExistTeam.toString();
    }

    private Map<String, MaintPersonDto> getMaintPersonDtoMap(List<String> personRows) {
        Map<String, MaintPersonDto> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(personRows)) {
            List<String> personUids = new ArrayList<>();
            for (String personRow : personRows) {
                String[] split = personRow.split(StringPool.COMMA);
                for (String s : split) {
                    if (!personUids.contains(s)) {
                        personUids.add(s);
                    }
                }
            }
            RestResponse<Map<String, MaintPersonDto>> personRes = baseServiceClient.getMaintainerListByUids(personUids);
            if (personRes.isOk()) {
                map = personRes.getData();
            } else {
                log.error("获取维护人员失败");
                //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
        }
        return map;
    }

    private Map<String, MaintTeamDto> getMaintTeamDtoMap(List<String> teamRows) {
        Map<String, MaintTeamDto> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(teamRows)) {
            List<String> teamNames = new ArrayList<>();
            for (String teamRow : teamRows) {
                String[] split = teamRow.split(StringPool.COMMA);
                for (String s : split) {
                    if (!teamNames.contains(s)) {
                        teamNames.add(s);
                    }
                }
            }
            RestResponse<Map<String, MaintTeamDto>> teamRes = baseServiceClient.getMaintTeamListByNames(teamNames);
            if (teamRes.isOk()) {
                map = teamRes.getData();
            } else {
                log.error("获取维护班组失败");
                //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
        }
        return map;
    }

    private Map<String, ManufacturerSupplierDto> getSupplierDtoMap(List<String> firmNames) {
        Map<String, ManufacturerSupplierDto> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(firmNames)) {
            RestResponse<Map<String, ManufacturerSupplierDto>> firmRes = baseServiceClient.getListByNames(firmNames);
            if (firmRes.isOk()) {
                map =  firmRes.getData();
            } else {
                log.error("查询厂商失败");
                //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
        }
        return map;
    }

    /**
     * 获取当前类型下最大编码
     * @param categoryCode
     * @return
     */
    private int maxCodeNum(String categoryCode){
        String maxCode = equipmentInfoMapper.getMaxCode(categoryCode);
        int begin = (maxCode.length() - 4 > 0 ? maxCode.length() - 4 : 0);
        maxCode = maxCode.substring(begin);
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        Boolean numFlag = pattern.matcher(maxCode).matches();
        if(numFlag){
            return Integer.valueOf(maxCode) + 1;
        }else{
            return 1;
        }
    }

    /**
     * 校验导入EXCEL设备表的字段
     *
     * @param rows
     * @return
     */
    private String checkExcelField(List<EquipmentSpecialExcel> rows, Map<String, EquipmentLocation> locationMap, String categoryCode) {
        List<String> allCodes = this.getAllCodes();
        StringBuffer builder = new StringBuffer();
        Integer i = 1;
        Integer codeNum = 0;
        // 校验必填字段
        for (EquipmentSpecialExcel row : rows) {
            String name = row.getName();
            String code = row.getCode();
            if(StringUtils.isBlank(code)){
                if(codeNum <= 0) {
                    codeNum = this.maxCodeNum(categoryCode);
                }
                //四位，不足补0
                String num = String.format("%04d", codeNum);
                code = categoryCode + num;
                codeNum++;
                row.setCode(code);
            }
            String locationName = row.getParentName();
            String categoryName = row.getCategoryName();
            StringBuffer sb = new StringBuffer();

            i++;
            //第一行为表头
            sb.append("第 ").append(i).append(" 行数据").append(StringPool.COLON);
            Boolean flag = false;
            if (StringUtils.isBlank(name)) {
                sb.append("名称不能为空").append(StringPool.COMMA);
                flag = true;
            }
            if (StringUtils.isBlank(locationName)) {
                sb.append("设备位置名称不能为空").append(StringPool.COMMA);
                flag = true;
            }else{
                EquipmentLocation equipmentLocation = locationMap.get(locationName.replace("（", "(").replace("）",")"));

                if(null == equipmentLocation){
                    sb.append("未找到对应设备位置").append(StringPool.COMMA);
                    flag = true;
                }
            }
            if (StringUtils.isBlank(categoryName)) {
                sb.append("设备类型名称不能为空").append(StringPool.COMMA);
                flag = true;
            }
            if(StringUtils.isBlank(code)){
                sb.append("设备编码不能为空").append(StringPool.COMMA);
                flag = true;
            }else if(CollectionUtils.isNotEmpty(allCodes) && allCodes.contains(code)){
                sb.append("设备编码已存在").append(StringPool.COMMA);
                flag = true;
            }

            if (flag) {
                sb.deleteCharAt(sb.length() - 1);
                builder.append(sb.toString()).append(StringPool.SEMICOLON);
            }
        }
        return builder.toString();
    }

    @Override
    public List<EquipmentSummaryDto> findByLocationId(String locationId, String keyword) {
        LambdaQueryWrapper<EquipmentInfo> queryWrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(locationId)) {
            queryWrapper.eq(EquipmentInfo::getParentId, locationId);
        }
        if(StringUtils.isNotBlank(keyword)){
            queryWrapper.like(EquipmentInfo::getName, keyword);
        }
        queryWrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        queryWrapper.select(EquipmentInfo::getName, EquipmentInfo::getId);
        List<EquipmentInfo> list = super.list(queryWrapper);
        return list.stream().map(equipmentInfo -> {
            EquipmentSummaryDto equipmentSummaryDto = new EquipmentSummaryDto();
            equipmentSummaryDto.setEquipmentId(equipmentInfo.getId());
            equipmentSummaryDto.setEquipmentName(equipmentInfo.getName());
            return equipmentSummaryDto;
        }).collect(Collectors.toList());
    }

    @Override
    public int getTotalCount() {
        return equipmentInfoMapper.equipmentTotalCount();
    }

    @Override
    public Map<String, EquipmentSummaryDto> getSummaryMap(EquipmentInfoSearchDto searchDto){
        List<EquipmentSummaryDto> summaryDtos = equipmentInfoMapper.getEquipmentSummary(searchDto);
        if(CollectionUtils.isNotEmpty(summaryDtos)){
            List<String> categoryIds = summaryDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getCategoryId()))
                    .map(EquipmentSummaryDto::getCategoryId).distinct().collect(Collectors.toList());
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            List<String> equipmentIds = summaryDtos.stream().map(EquipmentSummaryDto::getEquipmentId).distinct().collect(Collectors.toList());
            Map<String, EquipmentParentNameDto> equipmentAllNameMap = this.getDepthAllName(equipmentIds,  true);

            for(EquipmentSummaryDto summaryDto : summaryDtos){
                summaryDto.setCategoryAllName(StringUtils.isNotBlank(summaryDto.getCategoryId()) ?
                        categoryNameMap.get(summaryDto.getCategoryId()) : "");
                EquipmentParentNameDto equipmentParentNameDto = equipmentAllNameMap.get(summaryDto.getEquipmentId());
                summaryDto.setParentAllName(null != equipmentParentNameDto ? equipmentParentNameDto.getParentAllName() : null);
                summaryDto.setEquipmentAllName(null != equipmentParentNameDto ? equipmentParentNameDto.getEquipmentAllName() : null);
            }
        }
        return summaryDtos.stream().collect(Collectors.toMap(EquipmentSummaryDto::getEquipmentId,  v -> v, (v1, v2) -> v1));
    }

    @Override
    public EquipmentStudioDto getStudioByInfoId(String id) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, id);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getPcStudioId, EquipmentInfo::getAppStudioId, EquipmentInfo::getPcStudioUrl, EquipmentInfo::getAppStudioUrl);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        EquipmentStudioDto equipmentStudioDto = CopyDataUtil.copyObject(equipmentInfo, EquipmentStudioDto.class);
        if (null != equipmentStudioDto) {
            StudioQueryParam studioQueryParam = new StudioQueryParam();
            if (StringUtils.isNotBlank(equipmentStudioDto.getPcStudioId())) {
                studioQueryParam.setConfigToolId(equipmentStudioDto.getPcStudioId());
                StudioResDetailDto pcStudio = IotUtils.getStudioById(studioQueryParam);
                if (null != pcStudio) {
                    equipmentStudioDto.setPcStudioName(pcStudio.getStudioName());
                    equipmentStudioDto.setPcLengthWidth(pcStudio.getLengthWidth());
                    String pcStudioUrl = iotUrl + "/interface/interface_preview?id=" + equipmentStudioDto.getPcStudioId();
                    equipmentStudioDto.setPcStudioUrl(pcStudioUrl);
                }
            }
            if (StringUtils.isNotBlank(equipmentStudioDto.getAppStudioId())) {
                studioQueryParam.setConfigToolId(equipmentStudioDto.getAppStudioId());
                StudioResDetailDto appStudio = IotUtils.getStudioById(studioQueryParam);
                if (null != appStudio) {
                    equipmentStudioDto.setAppStudioName(appStudio.getStudioName());
                    equipmentStudioDto.setAppLengthWidth(appStudio.getLengthWidth());
                    String appStudioUrl = iotUrl + "/interface/interface_preview?id=" + equipmentStudioDto.getAppStudioId();
                    equipmentStudioDto.setAppStudioUrl(appStudioUrl);
                }
            }
        }
        return equipmentStudioDto;
    }

    @Override
    public String pcStudioUrl(String id){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, id);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        return null != equipmentInfo ? equipmentInfo.getPcStudioUrl() : null;
    }

    @Override
    public Boolean updateInfoStudio(StudioEditParam studioEditParam) {
        LambdaQueryWrapper<EquipmentInfo> queryWrapper = Wrappers.lambdaQuery();
        if (studioEditParam.getAppType().equals("2")) {
            queryWrapper.eq(EquipmentInfo::getPcStudioId, studioEditParam.getStudioId());
        } else if (studioEditParam.getAppType().equals("1")) {
            queryWrapper.eq(EquipmentInfo::getAppStudioId, studioEditParam.getStudioId());
        }

        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        if (studioEditParam.getAppType().equals("2")) {
            wrapper.set(EquipmentInfo::getPcStudioId, studioEditParam.getStudioId());
        } else if (studioEditParam.getAppType().equals("1")) {
            wrapper.set(EquipmentInfo::getAppStudioId, studioEditParam.getStudioId());
        }
        wrapper.eq(EquipmentInfo::getId, studioEditParam.getEquipmentId());
        return update(wrapper);
    }

    @Override
    public Map<String, String> getNameMapByIds(List<String> equipmentIds){
        Map<String, String> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(equipmentIds)) {
            LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentInfo::getId, equipmentIds);
            wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
            wrapper.select(EquipmentInfo::getId, EquipmentInfo::getName);
            map = equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentInfo::getId, EquipmentInfo::getName));
        }
        return map;
    }

    @Override
    public String getRootInfoId(String equipmentId) {
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.select(EquipmentInfo::getLayerCode);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        if (null != equipmentInfo && StringUtils.isNotBlank(equipmentInfo.getLayerCode())) {
            return equipmentInfo.getLayerCode().substring(0, 32);
        }
        return null;
    }

    @Override
    public Map<String, SpecialEquipmentDto> getSpecialListByIds(String[] equipmentIds){
        Map<String, SpecialEquipmentDto> map = new HashMap<>();
        if(null != equipmentIds && equipmentIds.length > 0) {
            List<SpecialEquipmentDto> listDtos = equipmentInfoMapper.getSpecialListByIds(equipmentIds);
            if (CollectionUtils.isEmpty(listDtos)) {
                return map;
            }
            //type 为1是主设备,父节点为位置
            List<String> infoIds = listDtos.stream().filter(dto -> dto.getEquipmentType() != StaticValue.ONE)
                    .map(SpecialEquipmentDto::getParentId).distinct().collect(Collectors.toList());
            Map<String, String> infoNameMap = this.getNameMapByIds(infoIds);
            for (SpecialEquipmentDto dto : listDtos) {
                if (dto.getEquipmentType() != StaticValue.ONE) {
                    dto.setParentName(infoNameMap.get(dto.getParentId()));
                }
            }
            map = listDtos.stream().collect(Collectors.toMap(SpecialEquipmentDto::getEquipmentId, v -> v, (v1, v2) -> v1));
        }
        return map;
    }

    private List<EquipmentInfo> getEntitiesByIds(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getName,EquipmentInfo::getLocationId, EquipmentInfo::getParentId, EquipmentInfo::getType, EquipmentInfo::getLayerCode);
        return  equipmentInfoMapper.selectList(wrapper);
    }

    @Override
    public Map<String, EquipmentParentNameDto> getDepthAllName(List<String> equipmentIds, Boolean removeOur){
        Map<String, EquipmentParentNameDto> map = new HashMap<>();
        List<EquipmentInfo> equipmentInfos = this.getEntitiesByIds(equipmentIds);
        if(CollectionUtils.isNotEmpty(equipmentInfos)){
            List<String> allParentIds = new ArrayList<>();
            for(EquipmentInfo equipmentInfo : equipmentInfos){
                if (StringUtils.isEmpty(equipmentInfo.getLayerCode())){
                    continue;
                }
                String[] parentIds = equipmentInfo.getLayerCode().split(StringPool.SLASH);
                for(String parentId : parentIds){
                    if(!allParentIds.contains(parentId)){
                        allParentIds.add(parentId);
                    }
                }
            }
            //只搜索自身及其父节点
            equipmentInfos = this.getEntitiesByIds(allParentIds);
            Map<String, EquipmentInfo> equipmentMap = equipmentInfos.stream().collect(Collectors.toMap(EquipmentInfo::getId, v -> v, (v1, v2) -> v1));
            //所有位置名称集合
            List<String> locationIds = equipmentInfos.stream().map(EquipmentInfo::getLocationId).distinct().collect(Collectors.toList());
            Map<String, String> locationNameMap = equipmentLocationService.getDepthName(locationIds);
            for(String equipmentId : equipmentIds){
                EquipmentInfo equipmentInfo = equipmentMap.get(equipmentId);
                if(null != equipmentInfo){
                    List<String> equipmentNames = new ArrayList<>();
                    if (StringUtils.isEmpty(equipmentInfo.getLayerCode())){
                        continue;
                    }
                    String[] parentIds = equipmentInfo.getLayerCode().split(StringPool.SLASH);
                    int length = removeOur ? parentIds.length - 1 : parentIds.length;

                    for (int i = 0; i < length; i++) {
                        EquipmentInfo parentEquipment = equipmentMap.get(parentIds[i]);
                        if (null != parentEquipment) {
                                equipmentNames.add(parentEquipment.getName());
                            } else {
                                break;
                            }
                    }
                    EquipmentParentNameDto equipmentParentNameDto = new EquipmentParentNameDto();
                    equipmentParentNameDto.setEquipmentName(equipmentInfo.getName());
                    String locationAllName = locationNameMap.get(equipmentInfo.getLocationId());
                    if(StringUtils.isNotBlank(locationAllName)) {
                        String[] locationNames = locationAllName.split(StringPool.SLASH);
                        equipmentParentNameDto.setLocationName(locationNames[locationNames.length - 1]);
                        equipmentParentNameDto.setLocationAllName(locationAllName);
                        String equipmentAllName = StringUtils.join(equipmentNames, StringPool.SLASH);
                        equipmentParentNameDto.setEquipmentAllName(equipmentAllName);
                        String parentAllName = StringUtils.isNotBlank(equipmentAllName) ? locationAllName + StringPool.SLASH + equipmentAllName : locationAllName;
                        equipmentParentNameDto.setParentAllName(parentAllName);
                    }
                    map.put(equipmentId, equipmentParentNameDto);
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, String> getDepthName(List<String> equipmentIds, Boolean removeOur){
        Map<String, String> map = new HashMap<>();
        Map<String, EquipmentParentNameDto> allNameMap = this.getDepthAllName(equipmentIds, removeOur);
        for(Map.Entry<String, EquipmentParentNameDto> entity : allNameMap.entrySet()){
            map.put(entity.getKey(), entity.getValue().getParentAllName());
        }
        return map;
    }

    @Override
    public Boolean checkCategoryUsed(String categoryId){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getCategoryId, categoryId);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        return equipmentInfoMapper.selectCount(wrapper) > 0;
    }

    @Override
    public void userExcelImport(List<SDSHUserExcel> rows){
        if(CollectionUtils.isNotEmpty(rows)){
            //业务组织
            //Map<String, String> porosOrgMap = this.getOrgMap();
            List<SecStaffAddParam> users = new ArrayList<>(rows.size());
            for(SDSHUserExcel row : rows){
                //审核通过新增中台用户
                SecStaffAddParam secStaffAddParam = new SecStaffAddParam();
                secStaffAddParam.setName(row.getName());
                secStaffAddParam.setUid(row.getUid());
                secStaffAddParam.setMobile(row.getMobile());
                secStaffAddParam.setEmail(row.getEmail());
                secStaffAddParam.setDepartmentNumber(row.getOrgCode());
                //secStaffAddParam.setDepartmentNumber(porosOrgMap.get(row.getOrgName()));
                secStaffAddParam.setRoleIds(new ArrayList<>());
                if(StringUtils.isNotEmpty(row.getRoleCode())) {
                    String[] roleCodes = row.getRoleCode().split(StringPool.COMMA);
                    secStaffAddParam.setRoleCodeList(Arrays.asList(roleCodes));
                }
                users.add(secStaffAddParam);
            }
            RestResponse<List<String>> restResponse = porosSecStaffClient.batchAdd(users);
            log.info("中台返回数据 ->" + JSONObject.toJSON(restResponse));
        }
    }

    @Override
    public String getCategoryId(String equipmentId){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getId,EquipmentInfo::getCategoryId);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        if(null != equipmentInfo){
            return equipmentInfo.getCategoryId();
        }
        return null;
    }

    @Override
    public Boolean updateEquipmentRate(EquipmentRateDto equipmentRateDto){
        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentInfo::getId, equipmentRateDto.getEquipmentId());
        wrapper.set(EquipmentInfo::getMaxRate, equipmentRateDto.getMaxRate());
        wrapper.set(EquipmentInfo::getMinRate, equipmentRateDto.getMinRate());
        return update(wrapper);
    }

    @Override
    public EquipmentRateDto getRate(String equipmentId){
        EquipmentRateDto equipmentRateDto = new EquipmentRateDto();
        equipmentRateDto.setEquipmentId(equipmentId);
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.select(EquipmentInfo::getMaxRate, EquipmentInfo::getMinRate);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        if(null != equipmentInfo){
            equipmentRateDto.setMaxRate(null != equipmentInfo.getMaxRate() ? equipmentInfo.getMaxRate() : 0);
            equipmentRateDto.setMinRate(null != equipmentInfo.getMinRate() ? equipmentInfo.getMinRate() : 0);
        }
        return equipmentRateDto;
    }

    @Override
    public Map<String, EquipmentRateDto> getRateMap(String[] equipmentIds){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getMaxRate, EquipmentInfo::getMinRate);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        Map<String, EquipmentRateDto> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(equipmentInfos)){
            for(EquipmentInfo equipmentInfo : equipmentInfos){
                EquipmentRateDto rateDto = new EquipmentRateDto();
                rateDto.setEquipmentId(equipmentInfo.getId());
                rateDto.setMaxRate(null != equipmentInfo.getMaxRate() ? equipmentInfo.getMaxRate() : 0);
                rateDto.setMinRate(null != equipmentInfo.getMinRate() ? equipmentInfo.getMinRate() : 0);
                map.put(equipmentInfo.getId(), rateDto);
            }
        }
        return map;
    }

    @Override
    public RunningEquipmentDto getRunningInfoNum(InfoStatisticsSearchDto dto){
        RunningEquipmentDto runningEquipmentDto = new RunningEquipmentDto();

        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            List<CategoryInfoDto> categoryInfoDtos = equipmentCategoryService.getDtosIncludeChild(dto.getCategoryIds());
            if (CollectionUtils.isEmpty(categoryInfoDtos)) {
                return runningEquipmentDto;
            }
            if(CollectionUtils.isNotEmpty(dto.getLocationIds())) {
                List<String> locationIds = equipmentLocationService.getLocationIdsByParentId(dto.getLocationIds(), true);
                if(CollectionUtils.isEmpty(locationIds)){
                    return runningEquipmentDto;
                }else{
                    dto.setLocationIds(locationIds);
                }
            }
            List<RunningEquipmentCountDto> runningEquipmentCountDtos = new ArrayList<>();
            Integer totalQty = StaticValue.ZERO;
            List<String> categoryIds = categoryInfoDtos.stream().map(CategoryInfoDto::getId).distinct().collect(Collectors.toList());
            EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
            searchDto.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            searchDto.setCategoryIds(categoryIds);
            searchDto.setLocationIds(dto.getLocationIds());
            searchDto.setRunningStatus(RunningStatusType.NORMAL.getValue());
            List<EquipmentSummaryDto> summaryDtos = equipmentInfoMapper.getEquipmentSummary(searchDto);
            Map<String, Integer> categoryCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(summaryDtos)){
                for(EquipmentSummaryDto info : summaryDtos){
                    String categoryLayerCode = info.getCategoryLayerCode();
                    if(StringUtils.isBlank(categoryLayerCode)){
                        continue;
                    }
                    totalQty++;
                    //设备对应的类型，将该类型与其父类型计数加1
                    String[] layerCodes = categoryLayerCode.split(StringPool.SLASH);
                    String allLayerCode = "";
                    for(String layerCode : layerCodes){
                        allLayerCode = StringUtils.isNotBlank(allLayerCode) ? allLayerCode + StringPool.SLASH + layerCode : layerCode;
                        Integer categoryCount = categoryCountMap.get(allLayerCode);
                        categoryCount = null != categoryCount ? categoryCount + 1 : 1;
                        categoryCountMap.put(allLayerCode, categoryCount);
                    }
                }

            }

            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            for(CategoryInfoDto categoryInfoDto : categoryInfoDtos){
                if(categoryInfoDto.getId().equals("0")){
                    //剔除根节点
                    continue;
                }
                RunningEquipmentCountDto countDto = new RunningEquipmentCountDto();
                countDto.setCategoryId(categoryInfoDto.getId());
                countDto.setCategoryName(categoryInfoDto.getName());
                countDto.setCategoryAllName(categoryNameMap.get(categoryInfoDto.getId()));
                Integer num = categoryCountMap.get(categoryInfoDto.getLayerCode());
                countDto.setNum(null != num ? num : 0);
                runningEquipmentCountDtos.add(countDto);
            }
            runningEquipmentDto.setTotalQty(totalQty);
            runningEquipmentDto.setData(runningEquipmentCountDtos);
        }
        return runningEquipmentDto;
    }

    @Override
    public String equipmentPicUrl(String id){
        EquipmentInfo equipmentInfo = (EquipmentInfo) this.getById(id);
        if(null != equipmentInfo && StringUtils.isNotBlank(equipmentInfo.getPicId())) {
            String picIds = equipmentInfo.getPicId();
            if (StringUtils.isNotBlank(picIds)) {
                String[] picStrs = picIds.split(StringPool.COMMA);
                Map<String, AttachmentClientDto> map = getAttachmentMap(Arrays.asList(picStrs));
                AttachmentClientDto attachmentClientDto = map.get(picStrs[0]);
                if (null != attachmentClientDto) {
                    return attachmentClientDto.getUrl();
                }
            }
        }
        return null;
    }

    @Override
    public CardEquipmentTimeDto currentEquipmentCard(String equipmentId){
        CardEquipmentTimeDto timeDto = new CardEquipmentTimeDto();
        List<String> equipmentIds = new ArrayList<>();
        equipmentIds.add(equipmentId);
        Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds, true);
        timeDto.setLocationName(equipmentNameMap.get(equipmentId));
        EquipmentInfo equipmentInfo = (EquipmentInfo)this.getById(equipmentId);
        if(null != equipmentInfo) {
            timeDto.setStatus(equipmentInfo.getRunningStatus());
            timeDto.setStatusName(RunningStatusType.getNameByValue(timeDto.getStatus()));

            Date completionTime = null != equipmentInfo.getCompletionTime() ? equipmentInfo.getCompletionTime() : equipmentInfo.getCreateTime();
            //累积运行时间
            double dayAllTime = 3600 * 24 * 1000d;
            double runningTime = (new Date().getTime() - completionTime.getTime()) / dayAllTime;
            BigDecimal runningTimeDecimal = new BigDecimal(runningTime).setScale(2, BigDecimal.ROUND_HALF_UP);
            timeDto.setRunningTime(runningTimeDecimal.doubleValue() + "d");
            //无故障运行时间
            List<EquipmentStatus> stopStatuses = statusService.getList(equipmentId, null, null, new Integer[]{RunningStatusType.STOP.getValue(),RunningStatusType.OFF_LINE.getValue()});

            if (CollectionUtils.isNotEmpty(stopStatuses)) {
                double stopDay = 0d;
                for (EquipmentStatus stopStatus : stopStatuses) {
                    Date endTime = null != stopStatus.getEndTime() ? stopStatus.getEndTime() : new Date();
                    double stopTime = (endTime.getTime() - stopStatus.getMarkTime().getTime()) / dayAllTime;
                    stopDay += stopTime;
                }
                BigDecimal stopTimeDecimal = runningTimeDecimal.subtract(new BigDecimal(stopDay)).setScale(2, BigDecimal.ROUND_HALF_UP);
                timeDto.setNoFaultTime(stopTimeDecimal.doubleValue() + "d");
            } else {
                timeDto.setNoFaultTime(timeDto.getRunningTime());
            }
        }
        return timeDto;
    }

    @Override
    public EquipmentProgressDto equipmentProgress(String equipmentId, Date beginTime, Date endTime){
        EquipmentProgressDto equipmentProgressDto = new EquipmentProgressDto();
        equipmentProgressDto.setBeginTime(beginTime);
        equipmentProgressDto.setEndTime(endTime);

        List<EquipmentStatus> equipmentStatuses = statusService.getList(equipmentId, beginTime, endTime, null);
        Map<Integer, Double> statusHourCountMap = new HashMap<>();
        double hourAllTime = 3600 *1000d;
        double total = (endTime.getTime() - beginTime.getTime()) / hourAllTime;

        List<EquipmentProgressDetailDto> statusList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(equipmentStatuses)){
            for(int i = 0; i < equipmentStatuses.size(); i++){
                EquipmentStatus equipmentStatus = equipmentStatuses.get(i);
                if(i == 0 && beginTime.compareTo(equipmentStatus.getMarkTime()) < 0){
                    //最小记录时间比开始时间大
                    EquipmentStatus minEquipmentStatus = new EquipmentStatus();
                    minEquipmentStatus.setMarkTime(beginTime);
                    minEquipmentStatus.setEndTime(equipmentStatus.getMarkTime());
                    minEquipmentStatus.setStatus(RunningStatusType.NORMAL.getValue());
                    EquipmentProgressDetailDto firstNormalDto = buildDetail(minEquipmentStatus, hourAllTime, beginTime, endTime, statusHourCountMap, total);

                    if(equipmentStatus.getStatus() == RunningStatusType.NORMAL.getValue()){
                        //最小记录也是正常，增加时间统计,增加第一条正常数据比例
                        EquipmentProgressDetailDto normalDto = buildDetail(equipmentStatus, hourAllTime, beginTime, endTime, statusHourCountMap, total);
                        firstNormalDto.setEndTime(normalDto.getEndTime());
                        firstNormalDto.setRate(firstNormalDto.getRate() + normalDto.getRate());
                        firstNormalDto.setHourTime(firstNormalDto.getHourTime() + normalDto.getHourTime());
                        statusList.add(firstNormalDto);
                    }else{
                        //先把首次补齐的正常状态时间加进去
                        statusList.add(firstNormalDto);
                        statusList.add(buildDetail(equipmentStatus, hourAllTime, beginTime, endTime, statusHourCountMap, total));
                    }
                }else {
                    statusList.add(buildDetail(equipmentStatus, hourAllTime, beginTime, endTime, statusHourCountMap, total));
                }
            }

        }else{
            //未查询到记录，说明设备一直正常
            EquipmentStatus equipmentStatus = new EquipmentStatus();
            equipmentStatus.setMarkTime(beginTime);
            equipmentStatus.setEndTime(endTime);
            equipmentStatus.setStatus(RunningStatusType.NORMAL.getValue());
            statusList.add(buildDetail(equipmentStatus, hourAllTime, beginTime, endTime, statusHourCountMap, total));
        }
        equipmentProgressDto.setStatusList(statusList);

        equipmentProgressDto.setStatusCount(buildStatusCount(statusHourCountMap, total));
        return equipmentProgressDto;
    }

    private EquipmentProgressDetailDto buildDetail(EquipmentStatus equipmentStatus, double hourAllTime, Date beginTime, Date endTime, Map<Integer, Double> statusHourCountMap, double total){
        //开始时间再记录区间中央
        Date statusBeginTime = beginTime.compareTo(equipmentStatus.getMarkTime()) > 0 ? beginTime : equipmentStatus.getMarkTime();
        //最后一条记录结束时间无/结束时间比选中截止时间大
        Date statusEndTime = null != equipmentStatus.getEndTime() ?
                endTime.compareTo(equipmentStatus.getEndTime()) > 0 ? equipmentStatus.getEndTime() : endTime : endTime;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        EquipmentProgressDetailDto detailDto = new EquipmentProgressDetailDto();
        detailDto.setName(sdf.format(statusBeginTime));
        detailDto.setStatus(equipmentStatus.getStatus());

        RunningStatusType statusType = RunningStatusType.getEnumByValue(equipmentStatus.getStatus());
        if(null != statusType){
            detailDto.setDescription(statusType.getName());
            detailDto.setColor(statusType.getColor());
        }

        //计算持续时间(H)
        double currentHourTime = (statusEndTime.getTime() - statusBeginTime.getTime()) / hourAllTime;
        Double statusHour = statusHourCountMap.get(equipmentStatus.getStatus());
        Double newStatusHour = null == statusHour ? currentHourTime : statusHour + currentHourTime;

        BigDecimal statusRate = new BigDecimal(currentHourTime * 100).divide(new BigDecimal(total), 2,BigDecimal.ROUND_HALF_UP);
        detailDto.setRate(statusRate.doubleValue());
        detailDto.setBeginTime(statusBeginTime.getTime());
        detailDto.setEndTime(statusEndTime.getTime());
        detailDto.setHourTime(currentHourTime);
        statusHourCountMap.put(equipmentStatus.getStatus(), newStatusHour);

        return detailDto;
    }

    private List<EquipmentProgressDetailDto> buildStatusCount(Map<Integer, Double> statusHourCountMap, double total){
        List<EquipmentProgressDetailDto> statusCount = new ArrayList<>();
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            EquipmentProgressDetailDto count = new EquipmentProgressDetailDto();
            count.setStatus(runningStatusType.getValue());
            count.setDescription(runningStatusType.getName());
            count.setColor(runningStatusType.getColor());
            Double hourTime = null != statusHourCountMap.get(runningStatusType.getValue()) ? statusHourCountMap.get(runningStatusType.getValue()) : 0d;
            BigDecimal hourTimeDecimal = new BigDecimal(hourTime).setScale(2, BigDecimal.ROUND_HALF_UP);
            count.setHourTime(hourTimeDecimal.doubleValue());
            BigDecimal rate = new BigDecimal(hourTime * 100).divide(new BigDecimal(total), 2,BigDecimal.ROUND_HALF_UP);
            count.setRate(rate.doubleValue());
            statusCount.add(count);
        }
        return statusCount;
    }

    @Override
    public WarnCountDto cardWarnTrend(String equipmentId){
        WarnCountDto warnCountDto = new WarnCountDto();
        List<String> equipmentIds = new ArrayList<>();
        equipmentIds.add(equipmentId);
        OverviewStatisticDto dto = new OverviewStatisticDto();
        dto.setEquipmentIds(equipmentIds);
        dto.setTrendType(StaticValue.TWO);
        RestResponse<WarnCountDto> restResponse = parameterClient.getCardWarnTrend(dto);
        if (restResponse.isOk()) {
            warnCountDto = restResponse.getData();
        } else {
            log.error("连接iot获取设备概览报警趋势数据失败");
        }
        return warnCountDto;
    }

    @Override
    public List<InfoParamWarnDto> cardWarnList(String equipmentId){
        List<InfoParamWarnDto> infoParamWarnDtos = new ArrayList<>();
        List<String> equipmentIds = new ArrayList<>();
        equipmentIds.add(equipmentId);
        OverviewStatisticDto dto = new OverviewStatisticDto();
        dto.setNum(3);
        dto.setEquipmentIds(equipmentIds);
        RestResponse<List<InfoParamWarnDto>> restResponse = parameterClient.getCardWarnList(dto);
        if (restResponse.isOk()) {
            infoParamWarnDtos = restResponse.getData();
        } else {
            log.error("连接iot获取设备概览报警列表数据失败");
        }
        return infoParamWarnDtos;
    }

    @Override
    public List<Double> equipmentEfficiency(String equipmentId){
        List<Double> efficiency = new ArrayList<>();
        Date endTime = DateUtil.endOfMonth(new Date());
        Date beginTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(endTime, -5));
        List<EquipmentStatus> equipmentStatuses = statusService.getList(equipmentId, beginTime, endTime, new Integer[]{RunningStatusType.OFF_LINE.getValue(), RunningStatusType.STOP.getValue(), RunningStatusType.STAY.getValue()});
        if(CollectionUtils.isNotEmpty(equipmentStatuses)){
            Map<Integer, Long> monthNotNormalMap = new HashMap<>();
            for(EquipmentStatus equipmentStatus : equipmentStatuses){
                //开始时间再记录区间中央
                Date statusBeginTime = beginTime.compareTo(equipmentStatus.getMarkTime()) > 0 ? beginTime : equipmentStatus.getMarkTime();
                //最后一条记录结束时间无
                Date statusEndTime = null != equipmentStatus.getEndTime() ? equipmentStatus.getEndTime() : new Date();

                int beginMonth = DateUtil.month(statusBeginTime);
                int endMonth = DateUtil.month(statusEndTime);

                Date currentMonthDate = statusBeginTime;
                Date start;
                Date end;
                //跨月份的数据，多次计算
                for(int i = beginMonth; i <= endMonth; i++){
                    if(i != beginMonth){
                        start = DateUtil.beginOfMonth(currentMonthDate);
                    }else{
                        start = statusBeginTime;
                    }
                    if(i != endMonth){
                        end = DateUtil.endOfMonth(currentMonthDate);
                    }else{
                        end = statusEndTime;
                    }
                    long notNormalTime = null != monthNotNormalMap.get(i) ? monthNotNormalMap.get(i) : 0L;
                    notNormalTime += end.getTime() - start.getTime();
                    monthNotNormalMap.put(i, notNormalTime);
                    //增加一个月
                    currentMonthDate = DateUtil.offsetMonth(currentMonthDate, 1);
                }
            }

            //计算每个月
            while(beginTime.compareTo(endTime) < 0){
                int month = DateUtil.month(beginTime);
                Long monthNotNormal = null != monthNotNormalMap.get(month) ? monthNotNormalMap.get(month) : 0L;
                Long monthAll = DateUtil.endOfMonth(beginTime).getTime() - beginTime.getTime();
                BigDecimal warnRate = new BigDecimal((monthAll - monthNotNormal) * 100).divide(new BigDecimal(monthAll), 2,BigDecimal.ROUND_HALF_UP);
                efficiency.add(warnRate.doubleValue());
                beginTime = DateUtil.offsetMonth(beginTime, 1);
            }

        }else{
            //全正常，运行效率100%
            for(int i = 0; i< 6; i++){
                efficiency.add(100d);
            }
        }
        return efficiency;
    }

    @Override
    public List<EquipmentRunningStatusDto> getEquipmentRunningStatusCount(Boolean auth, List<String> locationIds, List<String> categoryIds){
        List<EquipmentRunningStatusDto> list=new ArrayList<>();

        Map<Integer, Long> statusCountMap = new HashMap<>();
        Boolean haveInfo = true;
        if(CollectionUtils.isNotEmpty(categoryIds)) {
            categoryIds = equipmentCategoryService.getCategoryIdsByParentId(categoryIds);
            if (CollectionUtils.isEmpty(categoryIds)) {
                haveInfo = false;
            }
        }
        if(CollectionUtils.isNotEmpty(locationIds)) {
            locationIds = equipmentLocationService.getLocationIdsByParentId(locationIds, true);
            if(CollectionUtils.isEmpty(locationIds)){
                haveInfo = false;
            }
        }
        if(haveInfo) {
            if (null == auth || auth) {
                //默认过滤权限
                BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
                if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                    LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
                    wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), EquipmentInfo::getId, buildInfoSearchDto.getEquipmentIds());
                    wrapper.in(CollectionUtils.isNotEmpty(locationIds), EquipmentInfo::getLocationId, locationIds);
                    wrapper.in(CollectionUtils.isNotEmpty(categoryIds), EquipmentInfo::getCategoryId, categoryIds);
                    wrapper.select(EquipmentInfo::getId, EquipmentInfo::getRunningStatus);
                    statusCountMap = equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(dto -> dto.getRunningStatus(), Collectors.counting()));
                }
            } else {
                LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
                wrapper.in(CollectionUtils.isNotEmpty(locationIds), EquipmentInfo::getLocationId, locationIds);
                wrapper.in(CollectionUtils.isNotEmpty(categoryIds), EquipmentInfo::getCategoryId, categoryIds);
                wrapper.select(EquipmentInfo::getId, EquipmentInfo::getRunningStatus);
                statusCountMap = equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(dto -> dto.getRunningStatus(), Collectors.counting()));

            }
        }

        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            EquipmentRunningStatusDto status = new EquipmentRunningStatusDto();
            status.setName(runningStatusType.getName());
            status.setValue(runningStatusType.getValue());
            status.setColor(runningStatusType.getColor());
            Long count = statusCountMap.get(runningStatusType.getValue());
            status.setCount(null != count ? count.intValue() : StaticValue.ZERO);
            list.add(status);
        }
        return list;
    }

    @Override
    public List<EquipmentRunningStatusDto> equipmentIotStatusCount(Boolean auth){
        List<EquipmentRunningStatusDto> list=new ArrayList<>();
        List<WarnConfigDetailDto> warnLevelList = new ArrayList<>();
        RestResponse<List<WarnConfigDetailDto>> restResponse = parameterClient.getWarnLevelList();
        if(restResponse.isOk()){
            warnLevelList = restResponse.getData();
        }else{
            log.error("获取报警等级失败");
        }

        Map<Integer, Long> statusCountMap = new HashMap<>();
        if (null == auth || auth) {
            //默认过滤权限
            BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
            if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), EquipmentInfo::getId, buildInfoSearchDto.getEquipmentIds());
                wrapper.select(EquipmentInfo::getId, EquipmentInfo::getIotStatus);
                statusCountMap = equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(dto -> dto.getIotStatus(), Collectors.counting()));
            }
        }else {
            LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.select(EquipmentInfo::getId, EquipmentInfo::getIotStatus);
            statusCountMap = equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(dto -> dto.getIotStatus(), Collectors.counting()));

        }

        EquipmentRunningStatusDto normalIotStatus=new EquipmentRunningStatusDto();
        normalIotStatus.setName(IotParamStatusType.NORMAL.getName());
        normalIotStatus.setValue(IotParamStatusType.NORMAL.getValue());
        normalIotStatus.setColor(IotParamStatusType.NORMAL.getColor());
        Long normalCount = statusCountMap.get(IotParamStatusType.NORMAL.getValue());
        normalIotStatus.setCount(null != normalCount ? normalCount.intValue() : StaticValue.ZERO);
        list.add(normalIotStatus);

        Collections.reverse(warnLevelList);
        for(WarnConfigDetailDto warnConfigDetailDto : warnLevelList){
            EquipmentRunningStatusDto runningStatusDto =new EquipmentRunningStatusDto();
            Long count = statusCountMap.get(warnConfigDetailDto.getLevel());
            runningStatusDto.setName(warnConfigDetailDto.getName());
            runningStatusDto.setCount(null != count ? count.intValue() : StaticValue.ZERO);
            runningStatusDto.setValue(warnConfigDetailDto.getLevel());
            runningStatusDto.setColor(warnConfigDetailDto.getColor());
            list.add(runningStatusDto);
        }
        return list;
    }


    public List<EquipmentRateOfUtilizationDto> getEquipmentRateOfUtilization(){
        List<EquipmentRateOfUtilizationDto> list=new ArrayList<>();

        NumberFormat numberFormat = NumberFormat.getInstance();
        List<EquipmentLocationTreeDto> locations = equipmentLocationService.getRootChild();
        if(CollectionUtils.isNotEmpty(locations)) {
            for (EquipmentLocationTreeDto location : locations) {
                EquipmentRateOfUtilizationDto equipmentRateOfUtilizationDto = new EquipmentRateOfUtilizationDto();
                int all = equipmentInfoMapper.getEquipmentRateOfUtilizationAll(location.getId());
                int count = equipmentInfoMapper.getEquipmentRateOfUtilization(location.getId());
                equipmentRateOfUtilizationDto.setName(location.getName());
                equipmentRateOfUtilizationDto.setRateOfUtilization(all == 0 ? 0 : Float.valueOf(numberFormat.format((float) count / (float) all * 100)));
                list.add(equipmentRateOfUtilizationDto);
            }
        }
        return list;
    }

    @Override
    public TimeStatisticsDto getTimeStatistics(){
        NumberFormat numberFormat = NumberFormat.getInstance();
        LambdaQueryWrapper<EquipmentInfo> running = Wrappers.lambdaQuery();
        TimeStatisticsDto timeStatisticsDto=new TimeStatisticsDto();
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //系统累计运行时间
        int goneLive=this.countTwoDate("2022-11-01 00:00:00",sdf.format(d));
        int all=this.count();
        int runningStatus=this.count(running.eq(EquipmentInfo::getRunningStatus, StaticValue.ZERO));
        int warnCount= equipmentInfoMapper.getWranCount();
        timeStatisticsDto.setTotalUsage(all==0 ? 0 : Float.valueOf(numberFormat.format((float)runningStatus/(float)all*100)));
        timeStatisticsDto.setTotalFailureRate(all==0 ? 0 : Float.valueOf(numberFormat.format((float)warnCount/(float)all*100)));
        timeStatisticsDto.setGoneLive(goneLive);
        timeStatisticsDto.setNonFailureOperationDays(0);
        timeStatisticsDto.setDownDays(0);
        return timeStatisticsDto;
    }

    @Override
    public List<EquipmentWarnTrendDto> getEquipmentAllStatus(){
        List<EquipmentWarnTrendDto> list=new ArrayList<>();
        List<EquipmentLocationTreeDto> locations = equipmentLocationService.getRootChild();
        if(CollectionUtils.isNotEmpty(locations)) {
            for (EquipmentLocationTreeDto location : locations) {
                EquipmentWarnTrendDto equipmentWarnTrendDto = new EquipmentWarnTrendDto();
                List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.getRunningStatusList(location.getId());
                Map<Integer, Long> runningStatusCountMap = equipmentInfos.stream().collect(Collectors.groupingBy(EquipmentInfo::getRunningStatus, Collectors.counting()));
                List<EquipmentWarnTrendCountDto> yWarnTrend = new ArrayList<>();
                for(RunningStatusType runningStatusType : RunningStatusType.values()){
                    EquipmentWarnTrendCountDto countDto = new EquipmentWarnTrendCountDto();
                    countDto.setAll(equipmentInfos.size());
                    Long count = runningStatusCountMap.get(runningStatusType.getValue());
                    countDto.setCount(null != count ? count.intValue() : 0);
                    countDto.setName(runningStatusType.getName());
                    countDto.setColor(runningStatusType.getColor());
                    yWarnTrend.add(countDto);
                }

                equipmentWarnTrendDto.setYWarnTrend(yWarnTrend);
                equipmentWarnTrendDto.setLocation(location.getName());
                list.add(equipmentWarnTrendDto);
            }
        }
        return list;
    }

    @Override
    public WarnCountDto getEquipmentWarnTrend(){
        WarnCountDto warnCountDto = new WarnCountDto();
        //List<String> equipmentIds = new ArrayList<>();
        //List<EquipmentInfo> equipmentInfo=this.list();
        //equipmentIds=equipmentInfo.stream().map(EquipmentInfo::getId).collect(Collectors.toList());
        OverviewStatisticDto dto = new OverviewStatisticDto();
        dto.setEquipmentIds(null);
        dto.setTrendType(StaticValue.TWO);
        dto.setFirstPage(StaticValue.ONE);
        RestResponse<WarnCountDto> restResponse = parameterClient.getCardWarnTrend(dto);
        if (restResponse.isOk()) {
            warnCountDto = restResponse.getData();
        } else {
            log.error("连接iot获取设备概览报警趋势数据失败");
        }
        return warnCountDto;
    }

    public int countTwoDate(String startDate, String endDate)
    {
            Date start=DateUtil.parse(startDate,"yyyy-MM-dd HH:mm:ss");
            Date end = DateUtil.parse(endDate,"yyyy-MM-dd HH:mm:ss");
            Calendar cal=Calendar.getInstance();
            cal.setTime(start);
            long time1=cal.getTimeInMillis();
            cal.setTime(end);
            long time2=cal.getTimeInMillis();
            long between_days=(time2-time1)/(1000*3600*24);
            return Integer.parseInt(String.valueOf(between_days));
    }

    @Override
    public BuildInfoSearchDto getCurrentUserInfoIds(){
        BuildInfoSearchDto buildInfoSearchDto = BuildInfoSearchDto.builder().flag(false).build();
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        //log.info("-----------" + JSONObject.toJSON(userBaseInfo));
        if(null != userBaseInfo && !userBaseInfo.getUid().equals("admin") && authEnabled) {
            List<String> equipmentIds = new ArrayList<>();
            List<String> allLocationIds = new ArrayList<>();
            RestResponse<PorosSecStaffDto> userRes = porosSecStaffClient.getByUid(userBaseInfo.getUid(), null);
            if(userRes.isOk()){
                PorosSecStaffDto user = userRes.getData();
                List<OrgPosRelDto> relDtoList = user.getRelDtoList();
                if(CollectionUtils.isNotEmpty(relDtoList)) {
                    List<String> allOrgCode = relDtoList.stream().map(OrgPosRelDto::getOrgCode).distinct().collect(Collectors.toList());
                    List<String> locationIds = orgAuthService.getLocationIdsByOrgCodes(allOrgCode);
                    if (CollectionUtils.isNotEmpty(locationIds)) {
                        //获取设备位置根节点
                        List<String> tenantIds = new ArrayList<>();
                        tenantIds.add(userBaseInfo.getTenantId());
                        String rootId = equipmentLocationService.getRootIdMap(tenantIds).get(userBaseInfo.getTenantId());
                        if(locationIds.contains(rootId)){
                            //配置了位置根节点，不过滤权限
                            return buildInfoSearchDto;
                        }
                        allLocationIds = equipmentLocationService.getParentIdsByLocationIds(locationIds);
                        List<String> childLocationIds = equipmentLocationService.getLocationIdsByParentId(locationIds, true);
                        if(CollectionUtils.isNotEmpty(childLocationIds)){
                            //查询位置需要补上直属父级以及所有子集
                            allLocationIds.addAll(childLocationIds);
                        }
                        EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
                        //查询设备只取位置及其子节点
                        searchDto.setLocationIds(locationIds);
                        equipmentIds = equipmentInfoMapper.getEquipmentIdsByParam(searchDto);

                        //log.info("---------------equipmentIds:" + equipmentIds.size());
                    }else{
                        //什么都未配置的情况下，默认显示位置根节点
                        List<LocationEquipmentTreeDto> rootDtos = equipmentLocationService.getTreeListByParentId("0");
                        allLocationIds.addAll(rootDtos.stream().map(LocationEquipmentTreeDto::getId).collect(Collectors.toList()));
                    }
                }
            }else {
                log.error("获取用户信息失败");
            }
            buildInfoSearchDto = BuildInfoSearchDto.builder().equipmentIds(equipmentIds).locationIds(allLocationIds).flag(true).build();
        }
        return buildInfoSearchDto;
    }

    @Override
    public StatisticsLineDto failureRate(InfoStatisticsSearchDto dto){
        StatisticsLineDto statisticsLineDto = new StatisticsLineDto();
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            List<CategoryInfoDto> categoryInfoDtos = equipmentCategoryService.getDtosIncludeChild(dto.getCategoryIds());
            if (CollectionUtils.isEmpty(categoryInfoDtos)) {
                return statisticsLineDto;
            }
            if(CollectionUtils.isNotEmpty(dto.getLocationIds())) {
                List<String> locationIds = equipmentLocationService.getLocationIdsByParentId(dto.getLocationIds(), true);
                if(CollectionUtils.isEmpty(locationIds)){
                    return statisticsLineDto;
                }else{
                    dto.setLocationIds(locationIds);
                }
            }
            List<String> categoryIds = categoryInfoDtos.stream().map(CategoryInfoDto::getId).distinct().collect(Collectors.toList());
            EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
            searchDto.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            searchDto.setCategoryIds(categoryIds);
            searchDto.setLocationIds(dto.getLocationIds());
            List<EquipmentSummaryDto> summaryDtos = equipmentInfoMapper.getEquipmentSummary(searchDto);
            Map<String, Integer> categoryCountMap = new HashMap<>();
            Map<String, Integer> categoryFaultMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(summaryDtos)){
                for(EquipmentSummaryDto info : summaryDtos){
                    String categoryLayerCode = info.getCategoryLayerCode();
                    if(StringUtils.isBlank(categoryLayerCode)){
                        continue;
                    }

                    //设备对应的类型，将该类型与其父类型计数加1
                    String[] layerCodes = categoryLayerCode.split(StringPool.SLASH);
                    String allLayerCode = "";
                    for(String layerCode : layerCodes){
                        allLayerCode = StringUtils.isNotBlank(allLayerCode) ? allLayerCode + StringPool.SLASH + layerCode : layerCode;
                        Integer categoryCount = categoryCountMap.get(allLayerCode);
                        categoryCount = null != categoryCount ? categoryCount + 1 : 1;
                        categoryCountMap.put(allLayerCode, categoryCount);

                        if(info.getIotStatus() != IotParamStatusType.NORMAL.getValue()){
                            //参数引起的设备报警,计数
                            Integer categoryFaultCount = categoryFaultMap.get(allLayerCode);
                            categoryFaultCount = null != categoryFaultCount ? categoryFaultCount + 1 : 1;
                            categoryFaultMap.put(allLayerCode, categoryFaultCount);
                        }
                    }
                }

            }
            List<WarnCountXAxisDto> xaxisDtos = new ArrayList<>();
            List<String> yaxisDtos  = new ArrayList<>();
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            for(CategoryInfoDto categoryInfoDto : categoryInfoDtos){
                if(categoryInfoDto.getId().equals("0")){
                    //剔除根节点
                    continue;
                }
                WarnCountXAxisDto xAxisDto = new WarnCountXAxisDto();
                xAxisDto.setKey(categoryInfoDto.getName());
                xAxisDto.setCategoryId(categoryInfoDto.getId());
                xAxisDto.setCategoryAllName(categoryNameMap.get(categoryInfoDto.getId()));
                xaxisDtos.add(xAxisDto);
                Integer count = categoryCountMap.get(categoryInfoDto.getLayerCode());
                Integer faultCount = null != categoryFaultMap.get(categoryInfoDto.getLayerCode()) ? categoryFaultMap.get(categoryInfoDto.getLayerCode()) : 0;
                if(null != count && null != faultCount && faultCount > 0){
                    String rate = new BigDecimal(faultCount * 100).divide(new BigDecimal(count), StaticValue.TWO, BigDecimal.ROUND_HALF_UP).toString();
                    yaxisDtos.add(rate);
                }else{
                    yaxisDtos.add("0");
                }
            }
            statisticsLineDto.setXaxisDtos(xaxisDtos);
            statisticsLineDto.setYaxisDtos(yaxisDtos);
        }
        return statisticsLineDto;
    }

    @Override
    public List<FaultEquipmentNumDto> getFaultInfo(InfoStatisticsSearchDto searchDto){
        List<FaultEquipmentNumDto> dtos = new ArrayList<>();
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            if(CollectionUtils.isNotEmpty(searchDto.getCategoryIds())) {
                List<String> categoryIds = equipmentCategoryService.getCategoryIdsByParentId(searchDto.getCategoryIds());
                if (CollectionUtils.isEmpty(categoryIds)) {
                    return dtos;
                }else{
                    searchDto.setCategoryIds(categoryIds);
                }
            }
            if(CollectionUtils.isNotEmpty(searchDto.getLocationIds())) {
                List<String> locationIds = equipmentLocationService.getLocationIdsByParentId(searchDto.getLocationIds(), true);
                if(CollectionUtils.isEmpty(locationIds)){
                    return dtos;
                }else{
                    searchDto.setLocationIds(locationIds);
                }
            }
            EquipmentInfoSearchDto infoSearchDto = new EquipmentInfoSearchDto();
            infoSearchDto.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            infoSearchDto.setCategoryIds(searchDto.getCategoryIds());
            infoSearchDto.setLocationIds(searchDto.getLocationIds());
            List<EquipmentSummaryDto> summaryDtos = equipmentInfoMapper.getEquipmentSummary(infoSearchDto);
            if(CollectionUtils.isEmpty(summaryDtos)){
                return dtos;
            }

            Date endDate = DateUtil.endOfDay(new Date());
            Date beginDate = DateUtil.beginOfDay(DateUtil.offsetMonth(new Date(), -1));
            FaultEquipmentSearchDto faultEquipmentSearchDto = new FaultEquipmentSearchDto();
            faultEquipmentSearchDto.setNum(StaticValue.FIVE);
            faultEquipmentSearchDto.setBeginDate(beginDate);
            faultEquipmentSearchDto.setEndDate(endDate);
            faultEquipmentSearchDto.setEquipmentIds(summaryDtos.stream().map(EquipmentSummaryDto::getEquipmentId).distinct().collect(Collectors.toList()));
            RestResponse<List<FaultEquipmentNumDto>> faultRes = parameterClient.getFaultInfo(faultEquipmentSearchDto);
            if (faultRes.isOk()) {
                dtos = faultRes.getData();
                if (CollectionUtils.isNotEmpty(dtos)) {
                    List<String> equipmentIds = dtos.stream().map(FaultEquipmentNumDto::getEquipmentId).distinct().collect(Collectors.toList());
                    Map<String, String> nameMap = this.getNameMapByIds(equipmentIds);
                    Map<String, String> allNameMap = this.getDepthName(equipmentIds, true);
                    for (FaultEquipmentNumDto dto : dtos) {
                        dto.setEquipmentName(nameMap.get(dto.getEquipmentId()));
                        dto.setParentAllName(allNameMap.get(dto.getEquipmentId()));
                    }
                }
            } else {
                log.error("获取故障设备失败");
            }
        }
        return dtos;
    }

    @Override
    public WarnCountDto getRunningStatusCount(InfoStatisticsSearchDto dto){
        WarnCountDto warnCountDto = new WarnCountDto();
        BuildInfoSearchDto buildInfoSearchDto = this.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            List<CategoryInfoDto> categoryInfoDtos = equipmentCategoryService.getDtosIncludeChild(dto.getCategoryIds());
            if (CollectionUtils.isEmpty(categoryInfoDtos)) {
                return warnCountDto;
            }
            if(CollectionUtils.isNotEmpty(dto.getLocationIds())) {
                List<String> locationIds = equipmentLocationService.getLocationIdsByParentId(dto.getLocationIds(), true);
                if(CollectionUtils.isEmpty(locationIds)){
                    return warnCountDto;
                }else{
                    dto.setLocationIds(locationIds);
                }
            }
            List<String> categoryIds = categoryInfoDtos.stream().map(CategoryInfoDto::getId).distinct().collect(Collectors.toList());
            EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
            searchDto.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
            searchDto.setCategoryIds(categoryIds);
            searchDto.setLocationIds(dto.getLocationIds());
            List<EquipmentSummaryDto> summaryDtos = equipmentInfoMapper.getEquipmentSummary(searchDto);
            Map<String, Map<Integer, Integer>> categoryRunningStatusCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(summaryDtos)){
                for(EquipmentSummaryDto info : summaryDtos){
                    String categoryLayerCode = info.getCategoryLayerCode();
                    if(StringUtils.isBlank(categoryLayerCode)){
                        continue;
                    }

                    //设备对应的类型，将该类型与其父类型计数加1
                    String[] layerCodes = categoryLayerCode.split(StringPool.SLASH);
                    String allLayerCode = "";
                    for(String layerCode : layerCodes){
                        //类型层级编码拼接状态
                        allLayerCode = StringUtils.isNotBlank(allLayerCode) ? allLayerCode + StringPool.SLASH + layerCode : layerCode;
                        Map<Integer, Integer> runningStatusMap = categoryRunningStatusCountMap.get(allLayerCode);
                        if(null == runningStatusMap){
                            runningStatusMap = new HashMap<>();
                        }

                        //设备类型对应状态计数加1
                        Integer count = runningStatusMap.get(info.getRunningStatus());
                        count = null != count ? count + 1 : 1;
                        runningStatusMap.put(info.getRunningStatus(), count);

                        categoryRunningStatusCountMap.put(allLayerCode, runningStatusMap);
                    }
                }

            }

            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            //按参数类型聚合
            Map<Integer, List<Integer>> runningStatusAllCountMap = new HashMap<>();
            warnCountDto.setXaxisDtos(buildXAxisList(categoryInfoDtos,categoryNameMap, categoryRunningStatusCountMap, runningStatusAllCountMap));
            warnCountDto.setYaxisDtos(buildYAxisList(runningStatusAllCountMap));
        }

        return warnCountDto;
    }

    /**
     * 构造x轴
     * @param categoryInfoDtos 类型集合
     * @param categoryNameMap 包含类型全名称
     * @param categoryRunningStatusCountMap 类型对应状态统计
     * @param runningStatusAllCountMap 按照参数类型数量，对应聚合每种状态的数量list
     * @return
     */
    private List<WarnCountXAxisDto> buildXAxisList(List<CategoryInfoDto> categoryInfoDtos, Map<String, String> categoryNameMap,
         Map<String, Map<Integer, Integer>> categoryRunningStatusCountMap, Map<Integer, List<Integer>> runningStatusAllCountMap){
        List<WarnCountXAxisDto> xaxisDtos = new ArrayList<>();
        for(CategoryInfoDto categoryInfoDto : categoryInfoDtos){
            if(categoryInfoDto.getId().equals("0")){
                //剔除根节点
                continue;
            }
            WarnCountXAxisDto xAxisDto = new WarnCountXAxisDto();
            xAxisDto.setKey(categoryInfoDto.getName());
            xAxisDto.setCategoryId(categoryInfoDto.getId());
            xAxisDto.setCategoryAllName(categoryNameMap.get(categoryInfoDto.getId()));
            xaxisDtos.add(xAxisDto);

            //当前设备类型下，所有状态对应数量
            //按照参数类型数量，对应聚合每种状态的数量list
            Map<Integer, Integer> runningStatusCountMap = categoryRunningStatusCountMap.get(categoryInfoDto.getLayerCode());
            for(RunningStatusType runningStatusType : RunningStatusType.values()){
                Integer status = runningStatusType.getValue();
                List<Integer> statusCountList = runningStatusAllCountMap.get(status);
                if(CollectionUtils.isEmpty(statusCountList)){
                    statusCountList = new ArrayList<>();
                }
                if(null != runningStatusCountMap) {
                    Integer runningStatusCount = runningStatusCountMap.get(status);
                    statusCountList.add(null != runningStatusCount ? runningStatusCount : StaticValue.ZERO);
                }else{
                    statusCountList.add(StaticValue.ZERO);
                }
                runningStatusAllCountMap.put(status, statusCountList);
            }

        }
        return xaxisDtos;
    }

    /**
     * 构造y轴
     * @param runningStatusAllCountMap 状态对应集合
     * @return
     */
    private List<WarnCountYAxisDto> buildYAxisList(Map<Integer, List<Integer>> runningStatusAllCountMap){
        List<WarnCountYAxisDto> yaxisDtos = new ArrayList<>();
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            WarnCountYAxisDto warnCountYAxisDto = new WarnCountYAxisDto();
            warnCountYAxisDto.setName(runningStatusType.getName());
            warnCountYAxisDto.setLevel(runningStatusType.getValue());
            warnCountYAxisDto.setColor(runningStatusType.getColor());
            warnCountYAxisDto.setData(runningStatusAllCountMap.get(runningStatusType.getValue()));
            yaxisDtos.add(warnCountYAxisDto);
        }
        return yaxisDtos;
    }

    @Override
    public List<EquipmentInfo> getInfosByCategoryId(String categoryId){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getCategoryId, categoryId);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getRunningStatus, EquipmentInfo::getHealthStatus, EquipmentInfo::getIotStatus);
        return equipmentInfoMapper.selectList(wrapper);
    }

    @Override
    public PageResult<OeeDetailListDto> oeeEquipmentPageList(OeeQueryParam queryParam){
        Page<OeeQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        BuildInfoSearchDto searchDto = this.getCurrentUserInfoIds();
        if(searchDto.getFlag() && CollectionUtils.isEmpty(searchDto.getEquipmentIds())){
            //过滤过，且设备id集合为空，直接返回空字符集
            return new PageResult<>();
        }else {
            queryParam.setEquipmentIds(searchDto.getEquipmentIds());
        }
        IPage<OeeDetailListDto> pageResult = equipmentInfoMapper.getOeeInfoPageList(page, queryParam);
        List<OeeDetailListDto> records = pageResult.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            List<String> categoryIds = records.stream().map(OeeDetailListDto::getCategoryId).distinct().collect(Collectors.toList());
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            List<String> equipmentIds = records.stream().map(OeeDetailListDto::getEquipmentId).distinct().collect(Collectors.toList());
            //设备父级节点名称集合，不包含自身
            Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds, true);
            for(OeeDetailListDto dto : records){
                dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
                dto.setParentAllName(equipmentNameMap.get(dto.getEquipmentId()));
                dto.setRunningStatusName(RunningStatusType.getNameByValue(dto.getRunningStatus()));
            }
        }
        return Optional.ofNullable(PageResult.<OeeDetailListDto>builder()
                        .records(records)
                        .total(pageResult.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @Override
    public List<OeeDetailListDto> oeeEquipmentList(OeeQueryParam queryParam){
        BuildInfoSearchDto searchDto = this.getCurrentUserInfoIds();
        if(searchDto.getFlag() && CollectionUtils.isEmpty(searchDto.getEquipmentIds())){
            //过滤过，且设备id集合为空，直接返回空字符集
            return new ArrayList<>();
        }else {
            queryParam.setEquipmentIds(searchDto.getEquipmentIds());
        }
        List<OeeDetailListDto> records = equipmentInfoMapper.getOeeInfoList(queryParam);
        if(CollectionUtils.isNotEmpty(records)){
            List<String> categoryIds = records.stream().map(OeeDetailListDto::getCategoryId).distinct().collect(Collectors.toList());
            Map<String, String> categoryNameMap = equipmentCategoryService.getDepthName(categoryIds);
            List<String> equipmentIds = records.stream().map(OeeDetailListDto::getEquipmentId).distinct().collect(Collectors.toList());
            //设备父级节点名称集合，不包含自身
            Map<String, String> equipmentNameMap = this.getDepthName(equipmentIds, true);
            for(OeeDetailListDto dto : records){
                dto.setCategoryAllName(categoryNameMap.get(dto.getCategoryId()));
                dto.setParentAllName(equipmentNameMap.get(dto.getEquipmentId()));
                dto.setRunningStatusName(RunningStatusType.getNameByValue(dto.getRunningStatus()));
            }
        }
        return records;
    }

    @Override
    public List<String> getOeeOpenInfoIds(){
        return equipmentInfoMapper.getOeeOpenInfoIds();
    }

    @Override
    public Boolean updateEquipmentStopEnable(String equipmentId, Boolean enabled){
        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.set(EquipmentInfo::getParamStopEnable, enabled);
        return this.update(wrapper);
    }

    @Override
    public EquipmentSummaryDto getInfoParamStop(String equipmentId){
        EquipmentSummaryDto equipmentSummaryDto = null;
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getParamStopEnable, EquipmentInfo::getRunningStatus,
                EquipmentInfo::getIotStatus, EquipmentInfo::getMaxRate, EquipmentInfo::getMinRate);
        EquipmentInfo equipmentInfo = equipmentInfoMapper.selectOne(wrapper);
        if(null != equipmentInfo){
            equipmentSummaryDto = CopyDataUtil.copyObject(equipmentInfo, EquipmentSummaryDto.class);
            equipmentSummaryDto.setEquipmentId(equipmentInfo.getId());
        }
        return equipmentSummaryDto;
    }

    @Override
    public List<String> getExistParentIds(List<String> parentIds){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, parentIds);
        wrapper.eq(EquipmentInfo::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentInfo::getId);
        List<String> existParentIds = equipmentInfoMapper.selectList(wrapper).stream().map(EquipmentInfo::getId).distinct().collect(Collectors.toList());
        List<String> existLocationIds = equipmentLocationService.getExistParentIds(parentIds);
        if(CollectionUtils.isNotEmpty(existLocationIds)){
            existParentIds.addAll(existLocationIds);
        }
        return existParentIds;
    }

    @Override
    public Boolean changeEquipmentParent(String equipmentId, Integer toParentType, String toParentId, String oldInfoLayerCode){
        if(StringUtils.isNotBlank(oldInfoLayerCode)) {
            this.clearCache();
            String locationId;
            Integer type;
            if (toParentType == 1) {
                type = StaticValue.ONE;
                locationId = toParentId;
            } else {
                EquipmentInfo parentEquipment = (EquipmentInfo) this.getById(toParentId);
                locationId = parentEquipment.getLocationId();
                type = StaticValue.TWO;
            }
            String layerCode = this.getLayerCode(toParentId, equipmentId);
            List<EquipmentBomTreeDto> dtos = equipmentInfoMapper.getListByLayerCode(oldInfoLayerCode);
            List<EquipmentInfo> entities = new ArrayList<>(dtos.size());
            for (EquipmentBomTreeDto dto : dtos) {
                EquipmentInfo entity = new EquipmentInfo();
                String oldLayerCode = dto.getLayerCode();
                String newLayerCode = oldLayerCode.replaceFirst(oldInfoLayerCode, layerCode);
                entity.setLayerCode(newLayerCode);
                entity.setLocationId(locationId);
                entity.setId(dto.getId());
                if (entity.getId().equals(equipmentId)) {
                    entity.setLocationId(locationId);
                    entity.setParentId(toParentId);
                    entity.setType(type);
                }
                entities.add(entity);
            }
            //将设备下子设备层级编码全部更新
            this.updateBatchById(entities);
        }
        return true;
    }

    @Override
    public Map<String, String> getLayerCodeMap(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getId, equipmentIds);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getLayerCode);
        return equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentInfo::getId, EquipmentInfo::getLayerCode));
    }

    @Override
    public void updateSort(List<EquipmentSortEditParam> param) {
        List<EquipmentInfo> result = Lists.newArrayList();
        for (EquipmentSortEditParam temp : param) {
            EquipmentInfo equipmentInfo = new EquipmentInfo();
            equipmentInfo.setId(temp.getId());
            equipmentInfo.setSort(temp.getSort());
            result.add(equipmentInfo);
        }
        this.updateBatchById(result);
    }

    @Override
    public String getStatisticsOfRemainingLifeCount(String nearMonth){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        switch (nearMonth){
            case "1": wrapper.lt(EquipmentInfo::getRemainingLife, 30); break;
            case "3": wrapper.ge(EquipmentInfo::getRemainingLife, 30).lt(EquipmentInfo::getRemainingLife, 90); break;
            case "6": wrapper.ge(EquipmentInfo::getRemainingLife, 90).lt(EquipmentInfo::getRemainingLife, 180); break;
            default : wrapper.eq(EquipmentInfo::getId, "-1"); break;
        }
        return "" + equipmentInfoMapper.selectCount(wrapper);
    }

    @Override
    public HealthEstimateDto equipmentHealthEstimate(String equipmentId){
        HealthEstimateDto estimateDto = new HealthEstimateDto();
        EquipmentInfo equipmentInfo = (EquipmentInfo) this.getById(equipmentId);
        if(null != equipmentInfo){
            estimateDto.setHealthStatus(equipmentInfo.getHealthStatus());
            estimateDto.setHealthIndex(equipmentInfo.getHealthIndex());
            estimateDto.setRemainingLife(equipmentInfo.getRemainingLife());
        }
        estimateDto.setHealthStatusName(HealthStatusType.getNameByValue(estimateDto.getHealthStatus()));
        estimateDto.setRemainingLifeStr(this.buildRemainingLife(estimateDto.getRemainingLife()));
        return estimateDto;
    }

    @Override
    public Double equipmentHealthIndex(String equipmentId){
        EquipmentInfo equipmentInfo = (EquipmentInfo) this.getById(equipmentId);
        if(null != equipmentInfo){
            return equipmentInfo.getHealthIndex();
        }
        return 100d;
    }

    @Override
    public Boolean clearRemainingLife(String equipmentId){
        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentInfo::getId, equipmentId);
        wrapper.set(EquipmentInfo::getRemainingLife, null);
        wrapper.set(EquipmentInfo::getRemainingLifeUpdateTime, null);
        return update(wrapper);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean algorithmUpdateEquipment(InfoDefaultParamDto dto){
        EquipmentInfo equipmentInfo = new EquipmentInfo();
        equipmentInfo.setId(dto.getEquipmentId());
        equipmentInfo.setHealthStatus(dto.getHealthStatus());
        if(null != dto.getHealthIndex()) {
            healthIndexService.updateHealthIndex(dto.getHealthIndex(), dto.getEquipmentId(), dto.getDate());
            equipmentInfo.setHealthIndex(dto.getHealthIndex());
            equipmentInfo.setHealthIndexUpdateTime(dto.getDate());
        }
        if(null != dto.getRemainingLife()) {
            equipmentInfo.setRemainingLife(dto.getRemainingLife());
            equipmentInfo.setRemainingLifeUpdateTime(dto.getDate());
        }
        this.updateById(equipmentInfo);
        return true;
    }

    public List<RunningStatusDto> getHealthStatus(StatusSearchDto statusSearchDto){
        List<RunningStatusDto> runningStatusDtos = new ArrayList<>();
        if(null != statusSearchDto.getType() && StringUtils.isNotBlank(statusSearchDto.getParentId())) {
            List<String> parentIds = new ArrayList<>();
            parentIds.add(statusSearchDto.getParentId());
            if (statusSearchDto.getType() == 1) {
                parentIds = equipmentLocationService.getLocationIdsByParentId(parentIds, true);
            }
            statusSearchDto.setParentIds(parentIds);
        }
        if (StrUtil.isNotBlank(statusSearchDto.getCategoryId())) {
            List<String> categoryIds = CollectionUtils.isNotEmpty(statusSearchDto.getCategoryIds()) ? statusSearchDto.getCategoryIds() : new ArrayList<>();
            categoryIds.addAll(Arrays.asList(statusSearchDto.getCategoryId().split(StringPool.COMMA)));
            statusSearchDto.setCategoryIds(categoryIds);
        }
        Map<Integer, List<EquipmentInfo>> statusCountMap = new HashMap<>();
        //判定是否需要校验设备部门权限
        BuildInfoSearchDto authSearchDto = this.getCurrentUserInfoIds();
        if(!authSearchDto.getFlag() || CollectionUtils.isNotEmpty(authSearchDto.getEquipmentIds())){
            statusSearchDto.setEquipmentIds(authSearchDto.getEquipmentIds());
            statusCountMap = equipmentInfoMapper.getHealthStatus(statusSearchDto).stream().collect(Collectors.groupingBy(EquipmentInfo::getHealthStatus));
        }
        for(HealthStatusType healthStatusType : HealthStatusType.values()){
            RunningStatusDto runningStatusDto = new RunningStatusDto();
            runningStatusDto.setValue(healthStatusType.getValue()+"");
            runningStatusDto.setName(healthStatusType.getName());
            runningStatusDto.setColor(healthStatusType.getColor());
            runningStatusDto.setCount(CollectionUtils.isNotEmpty(statusCountMap.get(healthStatusType.getValue())) ?
                    statusCountMap.get(healthStatusType.getValue()).size() : 0);
            runningStatusDtos.add(runningStatusDto);
        }
        return runningStatusDtos;
    }

    public List<RunningStatusDto> getIotStatus(StatusSearchDto statusSearchDto){
        List<RunningStatusDto> runningStatusDtos = new ArrayList<>();
        if(null != statusSearchDto.getType() && StringUtils.isNotBlank(statusSearchDto.getParentId())) {
            List<String> parentIds = new ArrayList<>();
            parentIds.add(statusSearchDto.getParentId());
            if (statusSearchDto.getType() == 1) {
                parentIds = equipmentLocationService.getLocationIdsByParentId(parentIds, true);
            }
            statusSearchDto.setParentIds(parentIds);
        }
        if (StrUtil.isNotBlank(statusSearchDto.getCategoryId())) {
            List<String> categoryIds = CollectionUtils.isNotEmpty(statusSearchDto.getCategoryIds()) ? statusSearchDto.getCategoryIds() : new ArrayList<>();
            categoryIds.addAll(Arrays.asList(statusSearchDto.getCategoryId().split(StringPool.COMMA)));
            statusSearchDto.setCategoryIds(categoryIds);
        }
        Map<Integer, List<EquipmentInfo>> statusCountMap = new HashMap<>();
        //判定是否需要校验设备部门权限
        BuildInfoSearchDto authSearchDto = this.getCurrentUserInfoIds();
        if(!authSearchDto.getFlag() || CollectionUtils.isNotEmpty(authSearchDto.getEquipmentIds())){
            statusSearchDto.setEquipmentIds(authSearchDto.getEquipmentIds());
            statusCountMap = equipmentInfoMapper.getIotStatus(statusSearchDto).stream().collect(Collectors.groupingBy(EquipmentInfo::getIotStatus));
        }
        for(IotParamStatusType iotParamStatusType : IotParamStatusType.values()){
            RunningStatusDto runningStatusDto = new RunningStatusDto();
            runningStatusDto.setValue(iotParamStatusType.getValue()+"");
            runningStatusDto.setName(iotParamStatusType.getName());
            runningStatusDto.setColor(iotParamStatusType.getColor());
            runningStatusDto.setCount(CollectionUtils.isNotEmpty(statusCountMap.get(iotParamStatusType.getValue())) ?
                    statusCountMap.get(iotParamStatusType.getValue()).size() : 0);
            runningStatusDtos.add(runningStatusDto);
        }
        Map<Integer, WarnConfigDetailDto> warnMap = this.getWarnMap();
        for (Integer key : warnMap.keySet()) {
            RunningStatusDto runningStatusDto = new RunningStatusDto();
            runningStatusDto.setValue(key+"");
            runningStatusDto.setName(warnMap.get(key).getName());
            runningStatusDto.setColor(warnMap.get(key).getColor());
            runningStatusDto.setCount(CollectionUtils.isNotEmpty(statusCountMap.get(key)) ?
                    statusCountMap.get(key).size() : 0);
            runningStatusDtos.add(runningStatusDto);
        }
        return runningStatusDtos;
    }

    @Override
    public EquipmentSummaryDto getEquipmentInfoByCode(String equipmentCode){
        EquipmentSummaryDto equipmentSummaryDto = null;
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfo::getCode, equipmentCode);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getParamStopEnable, EquipmentInfo::getRunningStatus,
                EquipmentInfo::getIotStatus, EquipmentInfo::getMaxRate, EquipmentInfo::getMinRate);
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentInfos)){
            equipmentSummaryDto = CopyDataUtil.copyObject(equipmentInfos.get(0), EquipmentSummaryDto.class);
            equipmentSummaryDto.setEquipmentId(equipmentInfos.get(0).getId());
        }
        return equipmentSummaryDto;
    }

    @Override
    public Map<String, String> getIdByCode(List<String> codes){
        LambdaQueryWrapper<EquipmentInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfo::getCode, codes);
        wrapper.select(EquipmentInfo::getId, EquipmentInfo::getCode);
        return equipmentInfoMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentInfo::getCode, EquipmentInfo::getId));
    }

    @Override
    public String getFirstIdByCode(String code) {
        List<EquipmentInfo> equipmentInfos = equipmentInfoMapper.selectList(new LambdaQueryWrapper<EquipmentInfo>().eq(EquipmentInfo::getCode, code));
        return equipmentInfos.size() == 0 ? "" : equipmentInfos.get(0).getId();
    }
}
