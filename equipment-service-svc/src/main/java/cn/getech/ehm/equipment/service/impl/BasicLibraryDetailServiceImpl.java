package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.refect.ReflectUtil;
import cn.getech.ehm.equipment.dto.bearing.*;
import cn.getech.ehm.equipment.entity.bearing.BasicLibrary;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryDetail;
import cn.getech.ehm.equipment.entity.bearing.BasicLibraryField;
import cn.getech.ehm.equipment.entity.bearing.BearingDb;
import cn.getech.ehm.equipment.mapper.BasicLibraryDetailMapper;
import cn.getech.ehm.equipment.mapper.BearingDbMapper;
import cn.getech.ehm.equipment.service.IBasicLibraryDetailService;
import cn.getech.ehm.equipment.service.IBasicLibraryFieldService;
import cn.getech.ehm.equipment.service.IBasicLibraryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础库详情服务实现类
 */
@Slf4j
@Service
public class BasicLibraryDetailServiceImpl extends BaseServiceImpl<BasicLibraryDetailMapper, BasicLibraryDetail> implements IBasicLibraryDetailService {

    @Autowired
    private BasicLibraryDetailMapper detailMapper;
    @Autowired
    private IBasicLibraryFieldService fieldService;
    @Autowired
    private IBasicLibraryService libraryService;
    @Autowired
    private BearingDbMapper bearingDbMapper;

    @Override
    public PageResult detailList(BasicLibraryQueryParam queryParam) {
        Page<BasicLibraryQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        IPage<BasicLibraryDetailDto> result = detailMapper.pageList(page, queryParam);
        List<BasicLibraryDetailDto> records = result.getRecords();
        List<Object> objects = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(records)){
            for (BasicLibraryDetailDto dto : records) {
                Map<String, Object> detailMap = JSONObject.parseObject(dto.getDetail(), Map.class);
                Object obj = ReflectUtil.getObject(dto, detailMap);
                objects.add(obj);
            }
        }
        return Optional.ofNullable(PageResult.builder()
                        .records(objects)
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @Override
    public Boolean editDetail(Object dto) {
        Map<String, Object> map = (Map)dto;
        String basicLibraryId = (String)map.get("basicLibraryId");
        if(StringUtils.isBlank(basicLibraryId)){
            throw new GlobalServiceException(GlobalResultMessage.of("未绑定基础库"));
        }else{
            BasicLibraryDetail basicLibraryDetail  = ReflectUtil.copyFieldValue(map, BasicLibraryDetail.class);
            if(StringUtils.isNotBlank(basicLibraryDetail.getFactoryId()) && StringUtils.isNotBlank(basicLibraryDetail.getModelId())
                    && check(basicLibraryDetail.getId(), basicLibraryDetail.getFactoryId(), basicLibraryDetail.getModelId(), basicLibraryId)){
                throw new GlobalServiceException(GlobalResultMessage.of("该品牌型号下已存在该信息"));
            }

            List<BasicLibraryFieldDto> fieldDtos = fieldService.getList(basicLibraryId, false, null);
            if(CollectionUtils.isNotEmpty(fieldDtos)){
                List<String> fieldNames = fieldDtos.stream().map(BasicLibraryFieldDto::getFieldName).collect(Collectors.toList());
                Map<String, Object> detailMap = new LinkedHashMap<>();
                for(String fieldName : fieldNames){
                    detailMap.put(fieldName, map.get(fieldName));
                }
                basicLibraryDetail.setDetail(JSONObject.toJSONString(detailMap));
            }
            if(StringUtils.isBlank(basicLibraryDetail.getId())){
                basicLibraryDetail.setSort(detailMapper.getMaxSort(basicLibraryDetail.getBasicLibraryId()) + 1);
            }
            saveOrUpdate(basicLibraryDetail);
        }
        return true;
    }

    /**
     * 校验型号+品牌是否已经存在对应基础库
     */
    private Boolean check(String id, String factoryId, String modelId, String basicLibraryId){
        LambdaQueryWrapper<BasicLibraryDetail> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(BasicLibraryDetail::getId, id);
        }
        wrapper.eq(BasicLibraryDetail::getFactoryId, factoryId);
        wrapper.eq(BasicLibraryDetail::getModelId, modelId);
        wrapper.eq(BasicLibraryDetail::getBasicLibraryId, basicLibraryId);
        return detailMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Boolean deleteDetail(String id) {
        return this.removeById(id);
    }

    @Override
    public Boolean addBearingDb(){
        List<BearingDb> bearingDbs = bearingDbMapper.selectList(null);
        if(CollectionUtils.isNotEmpty(bearingDbs)){
            Map<String, String> libraryIdMap = new HashMap<>();
            String bearingId = null;
            List<BasicLibrary> basicLibraries = libraryService.getDefaultLibrary();
            if(CollectionUtils.isNotEmpty(basicLibraries)){
                for(BasicLibrary basicLibrary : basicLibraries){
                    String oldId = basicLibrary.getId();
                    String id = UUID.randomUUID().toString().replace("-","");
                    if(basicLibrary.getFieldName().equals("bearing")){
                        bearingId = id;
                    }
                    basicLibrary.setId(id);
                    basicLibrary.setTenantId(null);
                    libraryIdMap.put(oldId, id);
                }
                libraryService.saveBatch(basicLibraries);
            }
            List<BasicLibraryField> basicLibraryFields = fieldService.getDefaultFields();
            if(CollectionUtils.isNotEmpty(basicLibraryFields)){
                for(BasicLibraryField basicLibraryField : basicLibraryFields){
                    basicLibraryField.setId(null);
                    basicLibraryField.setTenantId(null);
                    basicLibraryField.setBasicLibraryId(libraryIdMap.get(basicLibraryField.getBasicLibraryId()));
                }
                fieldService.saveBatch(basicLibraryFields);
            }


            List<BasicLibraryDetail> detailList = new ArrayList<>(bearingDbs.size());
            int sort= 1;
            for(BearingDb bearingDb : bearingDbs){
                BearingDetailDto bearingDetailDto = CopyDataUtil.copyObject(bearingDb, BearingDetailDto.class);
                BasicLibraryDetail detail = new BasicLibraryDetail();
                detail.setBasicLibraryId(bearingId);
                detail.setFactoryId(bearingDb.getFactoryId());
                detail.setModelId(bearingDb.getModelId());
                detail.setSort(sort++);
                detail.setDetail(JSONObject.toJSONString(bearingDetailDto));
                detailList.add(detail);
            }
            this.saveBatch(detailList);
        }
        return true;
    }

    @Override
    public List<BasicLibraryDetail> getDefaultDetails(){
        return detailMapper.getDefaultDetails();
    }

    @Override
    public Map<String, Object> getDetailMap(String id){
        Map<String, Object> map = new HashMap<>();
        if(StringUtils.isNotBlank(id)){
            BasicLibraryDetail detail = detailMapper.selectById(id);
            if(detail != null && StringUtils.isNotBlank(detail.getDetail())){
                map = JSONObject.parseObject(detail.getDetail(), Map.class);
            }
        }
        return map;
    }
}
