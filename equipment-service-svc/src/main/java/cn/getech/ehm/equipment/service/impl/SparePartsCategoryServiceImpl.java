package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryDto;
import cn.getech.ehm.equipment.dto.runningparam.SparePartsCategoryQueryParam;
import cn.getech.ehm.equipment.entity.SparePartsCategory;
import cn.getech.ehm.equipment.mapper.SparePartsCategoryMapper;
import cn.getech.ehm.equipment.service.IRunningParameterService;
import cn.getech.ehm.equipment.service.ISparePartsCategoryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 零部件类型 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class SparePartsCategoryServiceImpl extends BaseServiceImpl<SparePartsCategoryMapper, SparePartsCategory> implements ISparePartsCategoryService {

    @Autowired
    private SparePartsCategoryMapper sparePartsCategoryMapper;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private IRunningParameterService runningParameterService;

    @Override
    public PageResult<SparePartsCategoryDto> pageDto(SparePartsCategoryQueryParam queryParam) {
        Page<SparePartsCategoryQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        IPage<SparePartsCategoryDto> result = sparePartsCategoryMapper.pageList(page, queryParam);
        List<SparePartsCategoryDto> records = result.getRecords();
        if(CollectionUtils.isNotEmpty(records)) {
            List<String> ids = records.stream().map(SparePartsCategoryDto::getId).collect(Collectors.toList());
            Map<String, Long> featureNumMap = runningParameterService.getFeatureCountMap(ids);
            for(SparePartsCategoryDto categoryDto : records){
                Long num = featureNumMap.get(categoryDto.getId());
                categoryDto.setFeatureNum(null != num ? num : 0);
            }
        }

        return Optional.ofNullable(PageResult.<SparePartsCategoryDto>builder()
                .records(records)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public List<SparePartsCategoryDto> getList(SparePartsCategoryQueryParam queryParam){
        queryParam.setPageNo(1);
        queryParam.setLimit(20);
        Page<SparePartsCategoryQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        return sparePartsCategoryMapper.pageList(page, queryParam).getRecords();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(SparePartsCategoryDto dto) {
        if(check(null, dto.getName(), dto.getType())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        SparePartsCategory sparePartsCategory = CopyDataUtil.copyObject(dto, SparePartsCategory.class);
        save(sparePartsCategory);
        if(StringUtils.isNotBlank(sparePartsCategory.getBasicLibraryId())){
            runningParameterService.editBasicLibraryDefaultParam(sparePartsCategory.getId(), sparePartsCategory.getBasicLibraryId());
        }
        return true;
    }

    private boolean check(String id, String name, Integer type){
        LambdaQueryWrapper<SparePartsCategory> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(SparePartsCategory::getId, id);
        }
        wrapper.eq(SparePartsCategory::getType, type);
        wrapper.eq(SparePartsCategory::getName, name);
        wrapper.eq(SparePartsCategory::getDeleted, DeletedType.NO.getValue());
        return sparePartsCategoryMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean updateByParam(SparePartsCategoryDto dto) {
        if(check(dto.getId(), dto.getName(), dto.getType())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        SparePartsCategory sparePartsCategory = CopyDataUtil.copyObject(dto, SparePartsCategory.class);
        SparePartsCategory old = (SparePartsCategory) this.getById(dto.getId());
        if(StringUtils.isNotBlank(dto.getBasicLibraryId()) && !dto.getBasicLibraryId().equals(old.getBasicLibraryId())){
            runningParameterService.editBasicLibraryDefaultParam(sparePartsCategory.getId(), sparePartsCategory.getBasicLibraryId());
        }
        return updateById(sparePartsCategory);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean copy(String id, String name){
        SparePartsCategory old = (SparePartsCategory) this.getById(id);
        if(null != old){
            if(check(null, name, old.getType())){
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
            }
            String newId = UUID.randomUUID().toString().replace("-","");
            old.setId(newId);
            old.setName(name);
            this.save(old);

            runningParameterService.copy(id, newId);
        }
        return true;
    }

    @Override
    public Boolean deleteById(String id) {
        LambdaUpdateWrapper<SparePartsCategory> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(SparePartsCategory::getId, id);
        wrapper.set(SparePartsCategory::getDeleted, DeletedType.YES.getValue());
        return update(wrapper);
    }

    @Override
    public SparePartsCategoryDto getDtoById(String id){
        return CopyDataUtil.copyObject(this.getById(id), SparePartsCategoryDto.class);
    }
}
