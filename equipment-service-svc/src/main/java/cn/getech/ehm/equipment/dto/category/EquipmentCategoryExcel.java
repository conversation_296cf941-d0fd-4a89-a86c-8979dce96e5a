package cn.getech.ehm.equipment.dto.category;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备类型导入
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentTypeDto", description = "设备类型导入")
public class EquipmentCategoryExcel {

    @ApiModelProperty(value = "类型编码")
    @Excel(name="编码",cellType = Excel.ColumnType.STRING )
    private String code;

    @ApiModelProperty(value = "类型名称")
    @Excel(name="名称",cellType = Excel.ColumnType.STRING )
    private String name;
}