package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.service.IBearingModelService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 轴承型号控制器
 * <AUTHOR>
 * @since 2021-04-07
 */
@RestController
@RequestMapping("/bearingModel")
@Api(tags = "轴承型号服务接口")
public class BearingModelController {
    @Autowired
    private IBearingModelService modelService;

    /**
     * 分页获取轴承型号列表
     */
    @ApiOperation("分页获取型号轴承列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<FactoryModelDto>> bearingPageList(@RequestBody FactoryModelQueryParam queryParam){
        return RestResponse.ok(modelService.pageDto(queryParam));
    }

    /**
     * 新增轴承型号
     */
    @ApiOperation("新增轴承型号")
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody FactoryModelDto dto) {
        return RestResponse.ok(modelService.saveByParam(dto));
    }

    /**
     * 修改轴承型号
     */
    @ApiOperation(value="修改轴承型号")
    @PostMapping("/edit")
    //@Permission("rule:template:update")
    public RestResponse<Boolean> update(@RequestBody FactoryModelDto dto) {
        return RestResponse.ok(modelService.updateByParam(dto));
    }

    /**
     * 根据id删除轴承型号
     */
    @ApiOperation(value="根据id删除轴承型号")
    @AuditLog(title = "轴承型号",desc = "轴承型号",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:location:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(modelService.deleteById(id));
    }

    /**
     * 获取轴承生产厂家列表
     */
    @ApiOperation("获取轴承型号列表")
    @GetMapping("/modelList")
    public RestResponse<List<FactoryModelDto>> modelList(@RequestParam(value = "keyword", required = false) String keyword){
        return RestResponse.ok(modelService.getList(keyword));
    }
}
