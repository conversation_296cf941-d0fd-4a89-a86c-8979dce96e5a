package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.info.EquipmentPropDto;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsAddParam;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsDto;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsEditParam;
import cn.getech.ehm.equipment.entity.EquipmentSpareParts;
import cn.getech.ehm.equipment.mapper.SparePartsMapper;
import cn.getech.ehm.equipment.service.ISparePartsService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备零件
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@Slf4j
@Service
public class SparePartsServiceImpl extends BaseServiceImpl<SparePartsMapper, EquipmentSpareParts> implements ISparePartsService {

    @Autowired
    private SparePartsMapper sparePartsMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Override
    public String saveByParam(SparePartsAddParam addParam) {
        if(check(null, addParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentSpareParts spareParts = CopyDataUtil.copyObject(addParam, EquipmentSpareParts.class);
        Integer sort = sparePartsMapper.getMaxSort(addParam.getEquipmentId()) + StaticValue.ONE;
        spareParts.setSort(sort);
        save(spareParts);
        return spareParts.getId();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveBatchParams(List<SparePartsAddParam> addParams){
        //删除旧零件
        this.deleteByInfoId(addParams.get(StaticValue.ZERO).getEquipmentId());

        //校验编码
        List<String> codes = addParams.stream().map(SparePartsAddParam::getCode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentSpareParts::getCode, codes);
        wrapper.select(EquipmentSpareParts::getCode);
        List<EquipmentSpareParts> entities = sparePartsMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(entities)){
            List<String> haveCodes = entities.stream().map(EquipmentSpareParts::getCode).distinct().collect(Collectors.toList());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("codes_exists", new Object[]{haveCodes.toString()}, LocaleContextHolder.getLocale())));
        }
        List<EquipmentSpareParts> equipmentSpareParts = new ArrayList<>(addParams.size());
        Integer index = 1;
        for(SparePartsAddParam addParam : addParams){
            EquipmentSpareParts entity = CopyDataUtil.copyObject(addParam, EquipmentSpareParts.class);
            entity.setSort(index++);
            equipmentSpareParts.add(entity);
        }
        return saveBatch(equipmentSpareParts);
    }

    private Boolean deleteByInfoId(String equipmentId){
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentSpareParts::getEquipmentId, equipmentId);
        return sparePartsMapper.delete(wrapper) > StaticValue.ZERO;
    }

    private Boolean check(String id, String code){
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(EquipmentSpareParts::getId, id);
        }
        wrapper.eq(EquipmentSpareParts::getCode, code);
        return sparePartsMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    @Override
    public SparePartsDto getDtoById(String id) {
        EquipmentSpareParts spareParts = sparePartsMapper.selectById(id);
        SparePartsDto sparePartsDto = CopyDataUtil.copyObject(spareParts, SparePartsDto.class);
        if(null != sparePartsDto && StringUtils.isNotBlank(spareParts.getProperty())){
            List<EquipmentPropDto> propDtos = JSONObject.parseArray(spareParts.getProperty(), EquipmentPropDto.class);
            sparePartsDto.setPropDtos(propDtos);
        }
        return sparePartsDto;
    }

    @Override
    public boolean updateByParam(SparePartsEditParam editParam) {
        if(check(editParam.getId(), editParam.getCode())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentSpareParts spareParts = CopyDataUtil.copyObject(editParam, EquipmentSpareParts.class);
        if(CollectionUtils.isNotEmpty(editParam.getPropDtos())){
            String property = JSONObject.toJSONString(editParam.getPropDtos());
            spareParts.setProperty(property);
        }else{
            spareParts.setProperty("");
        }
        return updateById(spareParts);
    }

    @Override
    public Boolean deleteById(String id){
        return sparePartsMapper.deleteById(id) > StaticValue.ZERO;
    }

    @Override
    public Map<String, List<SparePartsDto>> getMapByInfoIds(List<String> equipmentIds){
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentSpareParts::getEquipmentId, equipmentIds);
        wrapper.select(EquipmentSpareParts::getName, EquipmentSpareParts::getCode, EquipmentSpareParts::getRelationId,
                EquipmentSpareParts::getFactoryName, EquipmentSpareParts::getModelName, EquipmentSpareParts::getProportion,
                EquipmentSpareParts::getMaxSpeed,EquipmentSpareParts::getMinSpeed, EquipmentSpareParts::getEquipmentId);
        List<SparePartsDto> sparePartsDtos = CopyDataUtil.copyList(sparePartsMapper.selectList(wrapper), SparePartsDto.class);
        return sparePartsDtos.stream().collect(Collectors.groupingBy(SparePartsDto::getEquipmentId));
    }

    @Override
    public List<SparePartsDto> getListByInfoId(String equipmentId){
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentSpareParts::getEquipmentId, equipmentId);
        wrapper.select(EquipmentSpareParts::getId,EquipmentSpareParts::getName, EquipmentSpareParts::getCode,
                EquipmentSpareParts::getRelationId, EquipmentSpareParts::getFactoryName, EquipmentSpareParts::getModelName,
                EquipmentSpareParts::getProportion,EquipmentSpareParts::getMaxSpeed,EquipmentSpareParts::getMinSpeed,
                EquipmentSpareParts::getEquipmentId, EquipmentSpareParts::getType);
        return CopyDataUtil.copyList(sparePartsMapper.selectList(wrapper), SparePartsDto.class);
    }

    @Override
    public List<String> havePartsInfoIds(List<String> equipmentIds){
        if(CollectionUtils.isEmpty(equipmentIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EquipmentSpareParts> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentSpareParts::getEquipmentId, equipmentIds);
        wrapper.select(EquipmentSpareParts::getEquipmentId);
        return sparePartsMapper.selectList(wrapper).stream().map(EquipmentSpareParts::getEquipmentId).distinct().collect(Collectors.toList());
    }
}
