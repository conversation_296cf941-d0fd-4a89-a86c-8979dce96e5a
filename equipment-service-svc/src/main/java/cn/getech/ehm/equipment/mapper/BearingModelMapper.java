package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.bearing.BearingModel;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 轴承型号Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface BearingModelMapper  extends BaseMapper<BearingModel> {

    /**
     * 校验名称是否存在
     * @param id
     * @param name
     * @return
     */
    Integer checkNameExits(@Param("id") String id, @Param("name") String name);


    /**
     * 获取默认geek下轴承型号库
     * @return
     */
    @SqlParser(filter = true)
    List<BearingModel> getDefaultModel();
}
