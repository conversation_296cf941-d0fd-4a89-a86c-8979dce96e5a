package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备告警
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_warn")
public class EquipmentWarn extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * iot关联设备id
     */
    @TableField("iot_equipment_id")
    private String iotEquipmentId;

    /**
     * 1预警2报警
     */
    @TableField("level")
    private Integer level;

    /**
     * 1告警中3结束
     */
    @TableField("status")
    private Integer status;

    /**
     * 告警时间
     */
    @TableField("warn_time")
    private Date warnTime;

    /**
     * 修复时间
     */
    @TableField("repair_time")
    private Date repairTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 机架
     */
    @TableField("frame")
    private String frame;

    /**
     * 卡件/卡槽
     */
    @TableField("fixture")
    private String fixture;

}
