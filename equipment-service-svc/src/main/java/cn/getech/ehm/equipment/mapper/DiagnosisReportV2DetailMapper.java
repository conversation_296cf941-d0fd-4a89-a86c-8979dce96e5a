package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.diagnosis.ReportV2DetailDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReportDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 诊断报告V2详情 Mapper 接口
 */
@Repository
public interface DiagnosisReportV2DetailMapper extends BaseMapper<EquipmentDiagnosisReportDetail> {
    /**
     *
     * @param reportId
     * @return
     */
    List<ReportV2DetailDetailDto> getListByReportId(@Param("reportId") String reportId);
}
