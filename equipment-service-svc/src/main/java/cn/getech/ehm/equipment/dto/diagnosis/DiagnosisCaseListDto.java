package cn.getech.ehm.equipment.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 诊断案例返回列表
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "DiagnosisCaseListDto", description = "诊断案例返回列表")
public class DiagnosisCaseListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "创建人uid")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}