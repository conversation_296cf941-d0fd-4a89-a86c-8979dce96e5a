package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备结构树
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentTreeDetailDto", description = "设备小窗口查询结构树")
public class EquipmentTreeDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    /**
     * 用于编辑选择框是否展开
     * 子设备不能新增节点，子节点全是子设备不需要展开
     */
    @ApiModelProperty(value = "子节点的是否全是子设备")
    private Boolean allChildInfo = true;

    @ApiModelProperty(value = "类别装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("title", "title");}};

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "主设备位置id")
    private String locationId;

    @ApiModelProperty(value = "主设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "上级节点")
    private String parentId;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "使用部门code")
    private String useDepart;

    @ApiModelProperty(value = "使用部门名称")
    private String useDepartName;

    @ApiModelProperty(value = "管理部门code")
    private String manageDepart;

    @ApiModelProperty(value = "管理部门名称")
    private String manageDepartName;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "首张图片图片url")
    private String picUrl;

    @ApiModelProperty(value = "三级名称")
    private String parentAllName;

}