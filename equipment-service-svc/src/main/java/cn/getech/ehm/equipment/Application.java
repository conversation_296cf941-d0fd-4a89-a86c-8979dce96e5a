package cn.getech.ehm.equipment;

import com.thebeastshop.forest.springboot.annotation.ForestScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages={"cn.getech"})
//@ForestScan("cn.getech.**.request")
@EnableFeignClients(basePackages={"cn.getech"})
@MapperScan("cn.getech.ehm.equipment.mapper")
@EnableScheduling
@EnableAsync
@EnableKafka
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
