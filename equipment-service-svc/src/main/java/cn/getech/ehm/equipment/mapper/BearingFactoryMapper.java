package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.bearing.BearingFactory;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 轴承生产厂家Mapper
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Repository
public interface BearingFactoryMapper  extends BaseMapper<BearingFactory> {
    /**
     * 校验名称是否存在
     * @param id
     * @param name
     * @return
     */
    Integer checkNameExits(@Param("id") String id, @Param("name") String name);

    /**
     * 获取默认geek下轴承轴承生产厂家(品牌)库
     * @return
     */
    @SqlParser(filter = true)
    List<BearingFactory> getDefaultFactory();
}
