package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SDSH用户导入
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "SDSHUserExcel", description = "SDSH用户导入")
public class SDSHUserExcel {

    @ApiModelProperty(value = "用户名")
    @FormExcel(name="用户名",cellType = FormExcel.ColumnType.STRING )
    private String uid;

    @ApiModelProperty(value = "姓名")
    @FormExcel(name="姓名",cellType = FormExcel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "手机号")
    @FormExcel(name="手机号",cellType = FormExcel.ColumnType.STRING )
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    @FormExcel(name="邮箱",cellType = FormExcel.ColumnType.STRING )
    private String email;

    @ApiModelProperty(value = "业务组织")
    @FormExcel(name="业务组织",cellType = FormExcel.ColumnType.STRING )
    private String orgCode;

    @ApiModelProperty(value = "角色")
    @FormExcel(name="角色",cellType = FormExcel.ColumnType.STRING )
    private String roleCode;
}