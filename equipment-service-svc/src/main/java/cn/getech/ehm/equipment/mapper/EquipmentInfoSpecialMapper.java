package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentInfoSpecial;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 特种设备 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@Repository
public interface EquipmentInfoSpecialMapper extends BaseMapper<EquipmentInfoSpecial> {
    /**
     * 分页查询
     * @param page
     * @param queryParam
     * @return
     */
    IPage<EquipmentSpecialListDto> getPageList(Page<EquipmentSpecialQueryParam> page,
                                               @Param("param") EquipmentSpecialQueryParam queryParam);

    /**
     * 特种设备导出列表查询
     * @param queryParam
     * @return
     */
    List<EquipmentSpecialListDto> getExportSpecialEq(@Param("param") EquipmentSpecialQueryParam queryParam);

    /**
     * 列表查询
     * @param queryParam
     * @return
     */
    List<EquipmentSpecialListDto> getStructureList(@Param("param") EquipmentSpecialQueryParam queryParam);

    /**
     * 获取设备类型id集合
     * @return
     */
    List<String> getEquipmentCategoryIds();

    /**
     * 获取备件id集合
     * @return
     */
    List<String> getStructurePartIds();
}
