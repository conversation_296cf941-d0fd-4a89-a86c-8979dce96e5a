package cn.getech.ehm.equipment.dto.manager;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 安全管理人员
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "SpecialManagerDto", description = "安全管理人员")
public class SpecialManagerDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "登记证号")
    private String certificateNo;

    @ApiModelProperty(value = "作业项目")
    private String jobItem;

    @ApiModelProperty(value = "发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issueDate;

    @ApiModelProperty(value = "复审日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reviewDate;

    @ApiModelProperty(value = "证件有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateValidity;

    @ApiModelProperty(value = "发证机关")
    private String issuingAuthority;

    @ApiModelProperty(value = "聘用单位")
    private String employer;

    @ApiModelProperty(value = "所属车间code")
    private String workshop;

    @ApiModelProperty(value = "所属车间名称")
    private String workshopName;

    @ApiModelProperty(value = "附件ids")
    private String docIds;

    @ApiModelProperty(value = "附件Dtos")
    private List<AttachmentClientDto> docDtos;

    @ApiModelProperty("提前预警天数")
    private Integer deadlineDays;

    @ApiModelProperty("证书名称")
    private String certificateName;

    @ApiModelProperty("证书类型")
    private String certificateType;

    @ApiModelProperty("身份证号")
    private String idNumber;

    private Boolean isOverTime;

}