package cn.getech.ehm.equipment.dto.category;

import cn.getech.ehm.common.constant.StaticValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备类型 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentCategoryTreeDto", description = "设备类型树数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentCategoryTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "设备数量")
    private Integer total = StaticValue.ZERO;

    @ApiModelProperty(value = "子类别数量")
    private Integer child = StaticValue.ZERO;

    @ApiModelProperty(value = "是否是叶子节点")
    private Boolean isLeaf = true;

    @ApiModelProperty(value = "类别装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("title", "title");}};

    @ApiModelProperty(value = "是否固定0否1是")
    private Integer fixed = StaticValue.ZERO;

    @ApiModelProperty(value = "类型0其他1仪器仪表")
    private Integer type = StaticValue.ZERO;

    @ApiModelProperty(value = "校准周期")
    private Integer intervalPeriod;

    @ApiModelProperty(value = "子节点")
    private List<EquipmentCategoryTreeDto> children = new ArrayList<>();

}