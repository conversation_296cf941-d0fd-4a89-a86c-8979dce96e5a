package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备(扩展属性为json)导出
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentImportJsonExcel", description = "设备(扩展属性为json)导入导出")
public class EquipmentImportJsonExcel {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "编码")
    @FormExcel(name="设备编码",cellType = FormExcel.ColumnType.STRING )
    private String code;

    @ApiModelProperty(value = "设备位号")
    @FormExcel(name="设备位号",cellType = FormExcel.ColumnType.STRING )
    private String itemNo;

    @ApiModelProperty(value = "名称")
    @FormExcel(name="设备名称",cellType = FormExcel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    @FormExcel(name="设备类型",cellType = FormExcel.ColumnType.STRING )
    private String categoryName;

    @ApiModelProperty(value = "图片")
    private String picId;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "上级节点名称(设备位置/上级设备)")
    @FormExcel(name="设备位置",cellType = FormExcel.ColumnType.STRING )
    private String parentName;

    @ApiModelProperty(value = "规格型号")
    @FormExcel(name="规格型号",cellType = FormExcel.ColumnType.STRING )
    private String specification;

    @ApiModelProperty(value = "投产日期")
    @FormExcel(name="投产日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd")
    private Date completionTime;

    @ApiModelProperty(value = "重要程度")
    @FormExcel(name="重要程度",cellType = FormExcel.ColumnType.STRING)
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    @FormExcel(name="设备品牌",cellType = FormExcel.ColumnType.STRING )
    private String brand;

    @ApiModelProperty(value = "扩展字段X")
    @FormExcel(name="扩展字段X",cellType = FormExcel.ColumnType.STRING )
    private String alternateFieldX;

    @ApiModelProperty(value = "扩展字段Y")
    @FormExcel(name="扩展字段Y",cellType = FormExcel.ColumnType.STRING )
    private String alternateFieldY;

    @ApiModelProperty(value = "是否特种设备")
    @FormExcel(name="是否特种设备",cellType = FormExcel.ColumnType.STRING, readConverterExp = "1=是,0=否", combo = {"是","否"} )
    private String specialInfo;

    @ApiModelProperty(value = "登记证号")
    @FormExcel(name="登记证号",cellType = FormExcel.ColumnType.STRING )
    private String certificateNo;

    @ApiModelProperty(value = "注册代码")
    @FormExcel(name="注册代码",cellType = FormExcel.ColumnType.STRING )
    private String registrationCode;

    @ApiModelProperty(value = "特种设备检验日期")
    @FormExcel(name="特种设备检验日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd" )
    private Date inspectionDate;

    @ApiModelProperty(value = "下次检验日期")
    @FormExcel(name="下次检验日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd" )
    private Date nextInspectionDate;

    @ApiModelProperty(value = "检验报告编号")
    @FormExcel(name="检验报告编号",cellType = FormExcel.ColumnType.STRING )
    private String reportNo;

    @ApiModelProperty(value = "校验机构")
    @FormExcel(name="校验机构",cellType = FormExcel.ColumnType.STRING )
    private String inspectionOrg;

    @ApiModelProperty(value = "发证机关")
    @FormExcel(name="发证机关",cellType = FormExcel.ColumnType.STRING )
    private String issuingAuthority;

    @ApiModelProperty(value = "使用单位")
    @FormExcel(name="使用单位",cellType = FormExcel.ColumnType.STRING )
    private String useOrg;

    @ApiModelProperty(value = "是否校验设备")
    @FormExcel(name="是否校验设备",cellType = FormExcel.ColumnType.STRING, readConverterExp = "1=是,0=否", combo = {"是","否"} )
    private String checked;

    @ApiModelProperty(value = "校验日期")
    @FormExcel(name="校验日期",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd")
    private Date checkDate;

    @ApiModelProperty(value = "校验人员")
    @FormExcel(name="校验人员",cellType = FormExcel.ColumnType.STRING )
    private String checkUser;

    @ApiModelProperty(value = "检验状态")
    @FormExcel(name="检验状态",cellType = FormExcel.ColumnType.STRING )
    private String checkStatus;

    @ApiModelProperty(value = "运行状态值")
    private Integer runningStatus;

    @ApiModelProperty(value = "运行状态")
    @FormExcel(name="运行状态",cellType = FormExcel.ColumnType.STRING )
    private String runningStatusName;

    @ApiModelProperty(value = "健康状态值")
    private Integer healthStatus;

    @ApiModelProperty(value = "健康状态")
    @FormExcel(name="健康状态",cellType = FormExcel.ColumnType.STRING )
    private String healthStatusName;

    @ApiModelProperty(value = "是否联网")
    @FormExcel(name="是否联网",cellType = FormExcel.ColumnType.STRING, readConverterExp = "1=是,0=否", combo = {"是","否"})
    private String networked;

    @ApiModelProperty(value = "是否监控")
    @FormExcel(name="是否监控",cellType = FormExcel.ColumnType.STRING, readConverterExp = "1=是,0=否", combo = {"是","否"} )
    private String monitored;

    @ApiModelProperty(value = "制造商id")
    private String manufacturerId;

    @ApiModelProperty(value = "制造商名称")
    @FormExcel(name="制造商",cellType = FormExcel.ColumnType.STRING )
    private String manufacturerName;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    @FormExcel(name="供应商",cellType = FormExcel.ColumnType.STRING )
    private String supplierName;

    @ApiModelProperty(value = "设计单位id")
    private String designCompanyId;

    @ApiModelProperty(value = "设计单位名称")
    @FormExcel(name="设计单位",cellType = FormExcel.ColumnType.STRING )
    private String designCompanyName;

    @ApiModelProperty(value = "安装单位id")
    private String installCompanyId;

    @ApiModelProperty(value = "安装单位名称")
    @FormExcel(name="安装单位",cellType = FormExcel.ColumnType.STRING )
    private String installCompanyName;

    @ApiModelProperty(value = "出厂编码")
    @FormExcel(name="出厂编码",cellType = FormExcel.ColumnType.STRING )
    private String factoryCode;

    @ApiModelProperty(value = "出厂日期")
    @FormExcel(name="出厂日期",cellType = FormExcel.ColumnType.STRING , dateFormat = "yyyy-MM-dd")
    private Date productionTime;

    @ApiModelProperty(value = "设计年限")
    @FormExcel(name="设计年限",cellType = FormExcel.ColumnType.STRING , dateFormat = "yyyy-MM-dd")
    private Date designPeriod;

    @ApiModelProperty(value = "保修年限")
    @FormExcel(name="保修年限",cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd" )
    private Date warrantyPeriod;

    @ApiModelProperty(value = "资产编码")
    @FormExcel(name="资产编码",cellType = FormExcel.ColumnType.STRING )
    private String assetCode;

    @ApiModelProperty(value = "资产状态")
    @FormExcel(name="资产状态",cellType = FormExcel.ColumnType.STRING )
    private String assetStatus;

    @ApiModelProperty(value = "成本中心code")
    private String costCenter;

    @ApiModelProperty(value = "成本中心名称")
    @FormExcel(name="成本中心",cellType = FormExcel.ColumnType.STRING )
    private String costCenterName;

    @ApiModelProperty(value = "来源")
    @FormExcel(name="来源",cellType = FormExcel.ColumnType.STRING )
    private String source;

    @ApiModelProperty(value = "资产原值")
    @FormExcel(name="资产原值",cellType = FormExcel.ColumnType.NUMERIC )
    private Double originalAsset;

    @ApiModelProperty(value = "资产净值")
    @FormExcel(name="资产净值",cellType = FormExcel.ColumnType.NUMERIC )
    private Double netAsset;

    @ApiModelProperty(value = "管理部门code")
    private String manageDepart;

    @ApiModelProperty(value = "管理部门名称")
    @FormExcel(name="管理部门",cellType = FormExcel.ColumnType.STRING )
    private String manageDepartName;

    @ApiModelProperty(value = "管理责任人uid")
    private String managePrincipal;

    @ApiModelProperty(value = "管理责任人名称")
    @FormExcel(name="管理责任人",cellType = FormExcel.ColumnType.STRING )
    private String managePrincipalName;

    @ApiModelProperty(value = "使用部门code")
    private String useDepart;

    @ApiModelProperty(value = "使用部门名称")
    @FormExcel(name="使用部门",cellType = FormExcel.ColumnType.STRING )
    private String useDepartName;

    @ApiModelProperty(value = "使用责任人uid")
    private String usePrincipal;

    @ApiModelProperty(value = "使用责任人名称")
    @FormExcel(name="使用责任人",cellType = FormExcel.ColumnType.STRING )
    private String usePrincipalName;

    @ApiModelProperty(value = "维保责任人ids")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维保责任人uid")
    @FormExcel(name="维保责任人",cellType = FormExcel.ColumnType.STRING )
    private String maintainerNames;

    @ApiModelProperty(value = "维保班组ids")
    private String[] teamIds;

    @ApiModelProperty(value = "维保班组名称")
    @FormExcel(name="维保班组",cellType = FormExcel.ColumnType.STRING )
    private String teamNames;

    @ApiModelProperty(value = "报警状态值")
    private Integer iotStatus;

    @ApiModelProperty(value = "报警状态名称")
    @FormExcel(name="报警状态",cellType = FormExcel.ColumnType.STRING )
    private String iotStatusName;

    @ApiModelProperty(value = "扩展属性")
    @FormExcel(name="扩展属性",cellType = FormExcel.ColumnType.STRING)
    private String extendProp;
}