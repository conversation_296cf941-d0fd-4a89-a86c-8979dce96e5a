package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderDto;
import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderPageDto;
import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskOrderQueryParam;
import cn.getech.ehm.equipment.entity.CalibrationTaskOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 校准工单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Repository
public interface CalibrationTaskOrderMapper extends BaseMapper<CalibrationTaskOrder> {
    /**
     * 分页查询
     * @param page
     * @param queryParam
     * @return
     */
    Page<CalibrationTaskOrderPageDto> pageDto(@Param("page") Page<CalibrationTaskOrderPageDto> page,
                                              @Param("queryParam") CalibrationTaskOrderQueryParam queryParam);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    CalibrationTaskOrderDto getDtoById(@Param("id") String id);

    /**
     * 获取当前设备本年最大编码
     */
    String getMaxCode(@Param("year") String year);

    /**
     * 获取外校合格的设备ids
     * @return
     */
    List<String> getEquipmentIds();
}
