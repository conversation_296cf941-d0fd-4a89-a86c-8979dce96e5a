package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.service.IBearingFactoryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 轴承生产厂家(品牌)控制器
 * <AUTHOR>
 * @since 2021-04-07
 */
@RestController
@RequestMapping("/bearingFactory")
@Api(tags = "轴承生产厂家(品牌)服务接口")
public class BearingFactoryController {
    @Autowired
    private IBearingFactoryService factoryService;

    /**
     * 分页获取轴承生产厂家(品牌)列表
     */
    @ApiOperation("分页获取轴承生产厂家(品牌)列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<FactoryModelDto>> bearingPageList(@RequestBody FactoryModelQueryParam queryParam){
        return RestResponse.ok(factoryService.pageDto(queryParam));
    }

    /**
     * 新增轴承生产厂家(品牌)
     */
    @ApiOperation("新增轴承生产厂家(品牌)")
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody FactoryModelDto dto) {
        return RestResponse.ok(factoryService.saveByParam(dto));
    }

    /**
     * 修改轴承
     */
    @ApiOperation(value="修改轴承生产厂家(品牌)")
    @PostMapping("/edit")
    //@Permission("rule:template:update")
    public RestResponse<Boolean> update(@RequestBody FactoryModelDto dto) {
        return RestResponse.ok(factoryService.updateByParam(dto));
    }

    /**
     * 根据id删除生产厂家(品牌)
     */
    @ApiOperation(value="根据id删除生产厂家(品牌)")
    @AuditLog(title = "生产厂家(品牌)",desc = "生产厂家(品牌)",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:location:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(factoryService.deleteById(id));
    }

    /**
     * 获取轴承生产厂家(品牌)列表
     */
    @ApiOperation("获取轴承生产厂家(品牌)列表")
    @GetMapping("/factoryList")
    public RestResponse<List<FactoryModelDto>> factoryList(@RequestParam(value = "keyword", required = false) String keyword){
        return RestResponse.ok(factoryService.getList(keyword));
    }

}
