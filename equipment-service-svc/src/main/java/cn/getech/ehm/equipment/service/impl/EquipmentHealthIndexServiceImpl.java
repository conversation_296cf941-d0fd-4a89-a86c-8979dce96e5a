package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.overview.HealthIndexDto;
import cn.getech.ehm.equipment.entity.EquipmentHealthIndex;
import cn.getech.ehm.equipment.entity.EquipmentStatus;
import cn.getech.ehm.equipment.mapper.EquipmentHealthIndexMapper;
import cn.getech.ehm.equipment.service.IEquipmentHealthIndexService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备健康指数
 */
@Slf4j
@Service
public class EquipmentHealthIndexServiceImpl extends BaseServiceImpl<EquipmentHealthIndexMapper, EquipmentHealthIndex> implements IEquipmentHealthIndexService {

    @Autowired
    private EquipmentHealthIndexMapper healthIndexMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public List<Double> equipmentHealthTrend(String equipmentId){
        List<Double> healthIndexs = new ArrayList<>();
        Date endTime = DateUtil.endOfMonth(new Date());
        Date beginTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(endTime, -6));
        LambdaQueryWrapper<EquipmentHealthIndex> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentHealthIndex::getEquipmentId, equipmentId);
        wrapper.between(EquipmentHealthIndex::getMarkTime, beginTime, endTime);
        Map<Date, List<HealthIndexDto>> map = CopyDataUtil.copyList(healthIndexMapper.selectList(wrapper), HealthIndexDto.class).stream().collect(Collectors.groupingBy(HealthIndexDto::getMarkTime));
        Date month = beginTime;
        for(int i = 0; i < 6; i++){
            List<HealthIndexDto> dtos = map.get(beginTime);
            if(CollectionUtils.isNotEmpty(dtos)){
                Double values = dtos.stream().map(HealthIndexDto::getValue).reduce(0d, Double::sum);
                Double value = new BigDecimal(values).divide(new BigDecimal(dtos.size()), 1, RoundingMode.HALF_UP).doubleValue();
                healthIndexs.add(value);
            }else{
                healthIndexs.add(100d);
            }
            beginTime = DateUtil.offsetMonth(month, 1);
        }
        return healthIndexs;
    }

    @Override
    public Boolean updateHealthIndex(Double value, String equipmentId, Date markTime){
        EquipmentHealthIndex equipmentHealthIndex = new EquipmentHealthIndex();
        equipmentHealthIndex.setEquipmentId(equipmentId);
        equipmentHealthIndex.setMarkTime(markTime);
        equipmentHealthIndex.setValue(value);
        return save(equipmentHealthIndex);
    }
}
