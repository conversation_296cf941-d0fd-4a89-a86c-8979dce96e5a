package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.info.BomParentDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Repository
public interface EquipmentInfoMapper extends BaseMapper<EquipmentInfo> {
    /**
     * 获取设备位置下设备数量
     * @param equipmentLocationIds
     * @return
     */
    Integer getCountByLocationId(@Param("equipmentLocationIds") List<String> equipmentLocationIds,@Param("customerInfoIds") List<String> customerInfoIds);

    /**
     * 获取设备类型下设备数量
     * @param equipmentTypeIds
     * @return
     */
    Integer getCountByTypeId(@Param("equipmentTypeIds") List<String> equipmentTypeIds,@Param("customerInfoIds") List<String> customerInfoIds);

    /**
     * 根据id集合获取设备列表
     * @param equipmentIds
     * @return
     */
    List<EquipmentListDto> getListByIds(@Param("equipmentIds") String[] equipmentIds);

    /**
     * 根据parentId获取列表
     * @param parentId
     * @return
     */
    List<LazyEquipmentTreeDto> getTreeListByParentId(@Param("parentId") String parentId, @Param("keyword") String keyword, @Param("equipmentIds") List<String> equipmentIds);

    /**
     * 获取全部设备
     * @return
     */
    List<LocationEquipmentTreeDto> getAllTreeList(@Param("equipmentIds") List<String> equipmentIds);

    /**
     * 分页查询，返回Dto
     * @param param
     * @return
     */
    IPage<EquipmentTreeDetailDto> listTreeDetailDto(Page<EquipmentInfoQueryParam> page,
                                    @Param("param") InfoSearchDto param);

    /**
     *小窗口查询设备、子设备
     * @param parentId
     * @return
     */
    List<EquipmentTreeDetailDto> secondaryInfoList(String parentId);

    /**
     * 分页查询
     * @param page
     * @param infoAppQueryParam
     * @return
     */
    IPage<EquipmentInfoAppDetailDto> getAppPageList(Page<InfoAppQueryParam> page,
                                              @Param("param") InfoAppQueryParam infoAppQueryParam);

    /**
     * 分页查询
     * @param page
     * @param queryParam
     * @return
     */
    IPage<EquipmentInfoListDto> getPageList(Page<EquipmentInfoQueryParam> page,
                                        @Param("param") EquipmentInfoQueryParam queryParam);

    List<EquipmentInfoListDto> getAllListByParam(@Param("param") EquipmentInfoQueryParam queryParam);

    /**
     * 弹框分页获取作业标准详细信息
     * @param page
     * @param queryParam
     * @return
     */
    IPage<EquipmentInfoDetailDto> detailList(Page<DetailQueryParam> page,
                                            @Param("param") DetailQueryParam queryParam);

    /**
     * 运行状态统计
     * @param statusSearchDto
     * @return
     */
    List<EquipmentInfo> getRunningStatus(@Param("param") StatusSearchDto statusSearchDto);

    /**
     * 根据类别id获取设备列表
     * @param categoryId
     * @return
     */
    List<EquipmentInfo> getEquipmentListByCategoryId(String categoryId);

    /** 查询导出excel数据列表
     * @param queryParam
     * @return
     */
    List<EquipmentSpecialExcel> getExportList(@Param("param") EquipmentSpecialQueryParam queryParam);

    /** 查询导出特种设备excel数据列表
     * @param queryParam
     * @return
     */
    List<EquipmentSpecialExcel> getSpecialExportList(@Param("param") EquipmentSpecialQueryParam queryParam);

    /**
     * 根据类型ids、位置ids获取设备信息集合
     * @return
     */
    List<EquipmentStatisticsDto> getEquipmentStatistics(@Param("param") InfoSearchDto param);

    /**
     * 获取设备集合
     * @param searchDto
     * @return
     */
    List<EquipmentSummaryDto> getEquipmentSummary(@Param("searchDto") EquipmentInfoSearchDto searchDto);

    EquipmentInfoDto getDtoById(@Param("id") String id);

    EquipmentInfoDto getDtoByPositionTagId(@Param("id") String id);

    /**
     * 面包屑信息
     * @param id
     * @return
     */
    BomParentDto getBomDtoById(@Param("id") String id);

    /**
     * app根据id查询
     * @param id
     * @return
     */
    EquipmentInfoDto getAppDtoById(@Param("id") String id);

    /**
     * 设备离线在线
     * @param equipmentOnlineEditParams
     * @return
     */
    @SqlParser(filter = true)
    Boolean updateOnlineStatus(@Param("params") List<EquipmentOnlineEditParam> equipmentOnlineEditParams);

    /**
     * 获取客户端设备数量
     * @return
     */
    Integer equipmentCount(@Param("param") ClientHomeDto clientHomeDto);

    /**
     * 获取所有可用设备数量
     * @return
     */
    Integer equipmentAllCount();

    /**
     * 获取设备总数量
     * @return
     */
    @SqlParser(filter = true)
    Integer equipmentTotalCount();
    /**
     * 查询接入数量
     * @return
     */
    Integer equipmentJoinCount();

    /**
     * 根据id获取设备文档
     * @param equipmentId
     * @return
     */
    EquipmentDocDto getDocByInfoId(@Param("equipmentId") String equipmentId);

    List<String> getInfoIdsByName(@Param("equipmentName") String equipmentName, @Param("equipmentIds") List<String> equipmentIds);

    /**
     * 获取父节点layerCode
     * @param id
     * @return
     */
    String getLayerCode(@Param("id") String id);

    /**
     * 获取对应位置layerCode
     * @param id
     * @return
     */
    String getLocationLayerCode(@Param("id") String id);

    /**
     * 根据layerCode获取设备集合
     * @param layerCode
     * @return
     */
    List<EquipmentBomTreeDto> getListByLayerCode(String layerCode);

    /**
     * 根据位置ids获取纵深位置树对应的主设备
     * @param locationIds
     * @return
     */
    List<String> getMainInfoIdByLocation(@Param("locationIds") List<String> locationIds);

    /**
     * 根据查询条件获取设备ids
     * @return
     */
    List<String> getEquipmentIdsByParam(@Param("param") EquipmentInfoSearchDto param);

    /**
     * 拥有子节点设备名称
     * @param equipmentIds
     * @return
     */
    List<String> haveChildInfoNames(@Param("equipmentIds") String[] equipmentIds);

    /**
     * 根据id集合获取特种设备列表
     * @param equipmentIds
     * @return
     */
    List<SpecialEquipmentDto> getSpecialListByIds(@Param("equipmentIds") String[] equipmentIds);

    /**
     * 获取导入自动生成编码最大值
     * @param categoryCode
     * @return
     */
    String getMaxCode(@Param("categoryCode") String categoryCode);

    /**
     * poros删除位置
     * @param codes
     * @return
     */
    @SqlParser(filter = true)
    Integer updateByCodes(@Param("codes") List<String> codes, @Param("tenantId") String tenantId);

    /**
     * 获取设备使用率
     * @return
     */
    int getEquipmentRateOfUtilization(@Param("locationId") String locationId);

    /**
     * 获取总设备使用率
     * @return
     */
    int getEquipmentRateOfUtilizationAll(@Param("locationId") String locationId);

    /**
     * 获取车间名
     * @return
     */
    String getLocationName(@Param("locationId") String locationId);

    /**
     * 获取设备信息
     * @return
     */
    List<EquipmentInfo> getRunningStatusList(@Param("locationId") String locationId);

    /**
     * 获取报警设备数量
     * @return
     */
    int getWranCount();

    /**
     * 分页查询oee数据
     * @param param
     * @return
     */
    IPage<OeeDetailListDto> getOeeInfoPageList(Page<OeeQueryParam> page,
                                                    @Param("param") OeeQueryParam param);

    /**
     * 查询oee数据
     * @param param
     * @return
     */
    List<OeeDetailListDto> getOeeInfoList(@Param("param") OeeQueryParam param);

    /**
     * 获取开启oee的设备ids
     * @return
     */
    List<String> getOeeOpenInfoIds();

    List<EquipmentInfo> getHealthStatus(@Param("param") StatusSearchDto statusSearchDto);

    List<EquipmentInfo> getIotStatus(@Param("param") StatusSearchDto statusSearchDto);
}
