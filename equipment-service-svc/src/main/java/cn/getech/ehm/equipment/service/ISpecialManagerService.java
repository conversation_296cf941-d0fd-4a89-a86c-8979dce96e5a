package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.manager.*;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-1-8
 */
public interface ISpecialManagerService {
    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<SpecialManagerDto> pageList(SpecialManagerQueryParam queryParam);

    /**
     * 获取所有数据
     * @param queryParam
     * @return
     */
    List<SpecialManagerExcelDto> getExcelList(SpecialManagerQueryParam queryParam);

    /**
     * 新增
     * @param addParam
     * @return
     */
    Boolean saveByParam(SpecialManagerAddParam addParam);

    /**
     * 编辑
     * @param editParam
     * @return
     */
    Boolean editByParam(SpecialManagerEditParam editParam);

    /**
     * 删除
     * @param id
     * @return
     */
    Boolean delete(String id);

    /**
     * 查询
     * @param id
     * @return
     */
    SpecialManagerDto getDtoById(String id);

    /**
     * 导入
     * @param rows
     * @return
     */
    String excelImport(List<SpecialManagerExcelDto> rows);

    String getSpecialWarningCountOfNext(Integer offsetDays);

    String getSpecialWarningCountOfOverRemind();

    String getSpecialWarningCountOfOverNext();
}
