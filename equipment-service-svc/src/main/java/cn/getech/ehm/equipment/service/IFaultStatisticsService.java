package cn.getech.ehm.equipment.service;


import cn.getech.ehm.equipment.dto.EquipmentFaultStatisticsDto;
import cn.getech.ehm.task.dto.FaultStatisticsDto;
import cn.getech.ehm.task.dto.FaultStatisticsParam;

import java.util.List;

/**
 * 统计报表服务类
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
public interface IFaultStatisticsService {

    /**
     * 故障设备统计
     * @return
     */
    List<EquipmentFaultStatisticsDto> faultEquipmentStatistics(FaultStatisticsParam param);

    /**
     * 故障类型统计
     * @param param
     * @return
     */
    List<FaultStatisticsDto> faultCategoryStatistics(FaultStatisticsParam param);

}