package cn.getech.ehm.equipment.entity.bearing;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础库字段
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_library_field")
public class BasicLibraryField extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 基础库id
     */
    @TableField("basic_library_id")
    private String basicLibraryId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 字段名(用于保存)
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 字段名(用于展示:品牌型号等需要id/name做保存展示切换)
     */
    @TableField("show_field_name")
    private String showFieldName;

    /**
     * 字段类型
     */
    @TableField("field_type")
    private String fieldType;

    /**
     * 单位key
     */
    @TableField("unit")
    private String unit;

    /**
     * 是否默认基础参数
     */
    @TableField("default_param")
    private Boolean defaultParam;

    /**
     * 组件类型(text/number/searchSelect下拉框)
     */
    @TableField("components_type")
    private String componentsType;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
