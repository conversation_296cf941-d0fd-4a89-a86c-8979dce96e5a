package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.category.CategoryStructureDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryStructure;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 设备类型部件 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Repository
public interface InfoCategoryStructureMapper extends BaseMapper<EquipmentCategoryStructure> {

    /**
     * 获取类型部件
     * @param categoryId
     * @return
     */
    List<CategoryStructureDto> getListByCategoryId(@Param("categoryId") String categoryId, @Param("copyStructureIds") List<String> copyStructureIds);

    /**
     * 获取类型下最大排序
     * @param categoryId
     * @return
     */
    Integer getMaxSort(@Param("categoryId") String categoryId);
}
