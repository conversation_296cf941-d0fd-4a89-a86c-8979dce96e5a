package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.category.StructureSortDto;
import cn.getech.ehm.iot.dto.SynCategoryStructureDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.entity.EquipmentStructure;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 设备部件 服务类
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
public interface IEquipmentStructureService extends IBaseService<EquipmentStructure> {

        /**
         * 根据equipmentId查询集合
         * @param equipmentId
         * @return
         */
        List<EquipmentStructureDto> getListByEquipmentId(String equipmentId,String keyword);

        /**
         * 获取监测中所有特种参数
         * @param equipmentId
         * @return
         */
        List<FeatureParameterDto> getFeatureParameter(String equipmentId);

        /**
         * 获取特征参数频率图表
         * @param equipmentId
         * @param rpm 转速平均值
         * @return
         */
        List<List<String>> getFeatureParameterFFT(String equipmentId, Double rpm);

        /**
         * 获取特征参数阶次图表
         * @param equipmentId
         * @return
         */
        List<List<String>> getFeatureParameterOS(String equipmentId);

        /**
         * 获取部件的键值对
         * @param equipmentId
         * @return
         */
        List<EquipmentStructureDetailDto> getRootStructureDetail(String equipmentId);

        /**
         * 根据id集合获取设备部件名称集合
         * @return
         */
        Map<String, String> getStructureMapByIds(String[] structureIds);

        /**
         * 新增设备部件
         * @param addParam
         * @return
         */
        String addStructure(EquipmentStructureAddParam addParam);

        /**
         * 编辑设备部件
         * @param editParam
         * @return
         */
        Boolean editStructure(EquipmentStructureEditParam editParam);

        /**
         * 删除零部件
         * @param id
         * @return
         */
        Boolean deleteStructure(String id);

        /**
         * 删除
         * @param equipmentIds
         * @return
         */
        Boolean deleteByEquipmentIds(List<String> equipmentIds);

        /**
         * 继承类型部件
         * @param equipmentId
         * @return
         */
        Boolean quoteCategoryStructure(String equipmentId,String categoryId);

        /**
         * 同步类型部件
         * @return
         */
        Boolean synCategoryStructure(SynCategoryStructureDto dto);

        /**
         * 参数监控部件树
         * @param relSearchDto
         * @return
         */
        List<EquipmentTreeDto> paramStructureTree(RelSearchDto relSearchDto);

        /**
         * 获取组态部件树
         * @param relSearchDto
         * @return
         */
        List<StudioEquipmentBomDto> getStudioStructureBomList(RelSearchDto relSearchDto, List<StudioEquipmentBomDto> studioEquipmentBomDtos);

        /**
         * 根据id集合获取特种部件详细信息
         * @param structureIds
         * @return
         */
        Map<String, SpecialStructureDto> getSpecialListByIds(String[] structureIds);

        /**
         * 获取零部件对应名称
         * @param structureIds
         * @return
         */
        Map<String, String> getStructureNameMap(String[] structureIds);

        public List<EquipmentStructureDto> getListByStructureId(List<String> structureIds);

        /**
         * 重新排序
         * @return
         */
        Boolean updateStructureSort(List<StructureSortDto> dtos);

        /**
         * 设备同步选中类型部件
         * @param dto
         * @return
         */
        Map<String, String> quoteSelectCategoryStructure(QuoteCategoryStructureDto dto);
}