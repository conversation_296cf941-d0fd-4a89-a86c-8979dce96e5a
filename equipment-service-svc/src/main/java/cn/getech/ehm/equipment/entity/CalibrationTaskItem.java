package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 校准标准项目
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calibration_task_item")
public class CalibrationTaskItem extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("title")
    private String title;

    /**
     * 校准单id
     */
    @TableField("calibration_id")
    private String calibrationId;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 对比值
     */
    @TableField("value")
    private String value;

    /**
     * 允许误差
     */
    @TableField("error")
    private String error;

    /**
     * 校准值
     */
    @TableField("calibration_value")
    private String calibrationValue;

    /**
     * 自动判定结果0:不合格，1:合格
     */
    @TableField("auto_result")
    private Integer autoResult;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
