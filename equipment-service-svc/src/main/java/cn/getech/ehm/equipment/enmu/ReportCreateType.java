package cn.getech.ehm.equipment.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 诊断报告类型枚举
 */
public enum ReportCreateType {
    ARTIFICIAL(1,"人工报告"),
    AUTOMATIC(2,"智能诊断报告");


    ReportCreateType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value){
        if(null != value) {
            for (ReportCreateType sourceType : ReportCreateType.values()) {
                if (sourceType.getValue() == value) {
                    return sourceType.getName();
                }
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(ReportCreateType sourceType : ReportCreateType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(sourceType.value);
            enumListDto.setName(sourceType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
