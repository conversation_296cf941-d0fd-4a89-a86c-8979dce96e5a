package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.service.IDiagnosisReportService;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.util.Arrays;

/**
 * 诊断报告控制器
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@RestController
@RequestMapping("/diagnosisReport")
@Api(tags = "诊断报告服务接口")
public class DiagnosisReportController {

    @Autowired
    private IDiagnosisReportService diagnosisReportService;

    /**
     * 分页获取诊断案例列表
     */
    @ApiOperation("分页获取诊断报告列表")
    @GetMapping("/pageList")
    //@Permission("diagnosis:case:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<PageResult<DiagnosisReportListDto>> pageList(@Valid DiagnosisReportQueryParam queryParam){
        return RestResponse.ok(diagnosisReportService.pageDto(queryParam));
    }

    /**
     * 新增诊断案例
     */
    @ApiOperation("新增诊断报告")
    @AuditLog(title = "诊断报告",desc = "新增诊断报告",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("diagnosis:case:update")
    public RestResponse<Boolean> add(@RequestBody @Valid DiagnosisReportAddParam addParam) {
        return RestResponse.ok(diagnosisReportService.saveByParam(addParam));
    }

    /**
     * 修改诊断案例
     */
    @ApiOperation(value="修改诊断报告")
    @AuditLog(title = "诊断报告",desc = "修改诊断报告",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("diagnosis:case:update")
    public RestResponse<Boolean> update(@RequestBody @Valid DiagnosisReportEditParam editParam) {
        return RestResponse.ok(diagnosisReportService.updateByParam(editParam));
    }

    /**
     * 根据id删除诊断报告
     */
    @ApiOperation(value="根据id删除诊断报告")
    @AuditLog(title = "诊断报告",desc = "诊断报告",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("diagnosis:case:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(diagnosisReportService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 根据id获取诊断案例
     */
    @ApiOperation(value = "根据id获取诊断报告")
    @GetMapping(value = "/{id}")
    //@Permission("diagnosis:case:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<DiagnosisReportDto> get(@PathVariable("id")  String id) {
        return RestResponse.ok(diagnosisReportService.getDtoById(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "/exportWord")
    public void exportWord(@RequestParam("id") String id, @RequestParam("url") String url, HttpServletResponse response) {
        File file = diagnosisReportService.exportWord(id, url);
        WordDownloadUtil.download(response, file, "");
        if (file.exists()) {
            file.delete();
        }
    }
}
