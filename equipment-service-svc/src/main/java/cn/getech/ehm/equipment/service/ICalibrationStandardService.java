package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.category.CalibrationStandardDto;
import cn.getech.ehm.equipment.entity.CalibrationStandard;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 设备类型校准 服务类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ICalibrationStandardService extends IBaseService<CalibrationStandard> {

    /**
     * 新增
     * @param calibrationStandardDtoList
     * @return
     */
    Boolean updateList(List<CalibrationStandardDto> calibrationStandardDtoList, String categoryId);


    /**
     * 根据类型id获取校准项
     * @param categoryId
     * @return
     */
    List<CalibrationStandardDto> getByCategoryId(String categoryId);

}