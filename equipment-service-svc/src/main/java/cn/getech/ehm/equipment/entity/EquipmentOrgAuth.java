package cn.getech.ehm.equipment.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 诊断案例
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_org_auth")
public class EquipmentOrgAuth extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 部门编码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 设备id
     */
    @TableField(value = "location_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] locationIds;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
