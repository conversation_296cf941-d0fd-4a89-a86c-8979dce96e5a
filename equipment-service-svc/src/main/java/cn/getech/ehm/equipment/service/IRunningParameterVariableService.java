package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto;
import cn.getech.ehm.equipment.entity.RunningParameterVariable;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 零部件参数变量 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IRunningParameterVariableService extends IBaseService<RunningParameterVariable> {

    /**
     * 保存
     * @return
     */
    boolean saveOrUpdateByParam(List<ParameterVariableDto> dtos, String runningParameterId);

    /**
     * 获取列表
     * @return
     */
    List<ParameterVariableDto> getList(List<String> runningParameterIds);

    /**
     * 根据运行参数ids删除变量
     */
    Boolean deleteByParamIds(List<String> runningParameterIds);

}