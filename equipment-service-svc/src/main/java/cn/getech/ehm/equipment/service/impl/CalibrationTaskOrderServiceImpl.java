package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.ManufacturerSupplierDto;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.InfoSearchDto;
import cn.getech.ehm.equipment.dto.calibration.*;
import cn.getech.ehm.equipment.dto.category.CalibrationStandardDto;
import cn.getech.ehm.equipment.entity.CalibrationTaskOrder;
import cn.getech.ehm.equipment.mapper.CalibrationTaskOrderMapper;
import cn.getech.ehm.equipment.service.ICalibrationStandardService;
import cn.getech.ehm.equipment.service.ICalibrationTaskItemService;
import cn.getech.ehm.equipment.service.ICalibrationTaskOrderService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.errorprone.annotations.concurrent.LazyInit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校准工单 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Slf4j
@Service
public class CalibrationTaskOrderServiceImpl extends BaseServiceImpl<CalibrationTaskOrderMapper, CalibrationTaskOrder> implements ICalibrationTaskOrderService {

    @Autowired
    private CalibrationTaskOrderMapper calibrationTaskOrderMapper;
    @Autowired
    private ICalibrationTaskItemService calibrationTaskItemService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private ICalibrationStandardService calibrationStandardService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    private static final Integer DAY = 24 * 60 * 60 * 1000;

    @Override
    public PageResult<CalibrationTaskOrderPageDto> pageDto(CalibrationTaskOrderQueryParam calibrationTaskOrderQueryParam) {
        Page<CalibrationTaskOrderPageDto> page = new Page<>(calibrationTaskOrderQueryParam.getPageNo(), calibrationTaskOrderQueryParam.getLimit());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        try {
            calibrationTaskOrderQueryParam.setCurrentDate(sdf.parse(now));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        // 查找设备位置
        if (StringUtils.isNotBlank(calibrationTaskOrderQueryParam.getInnerEquipmentLocation())){
            EquipmentInfoSearchDto infoSearchDto = new EquipmentInfoSearchDto();
            String[] split = calibrationTaskOrderQueryParam.getInnerEquipmentLocation().split(",");
            infoSearchDto.setLocationIds(Arrays.asList(split));
            //todo   设备修改为树，需要修改

            calibrationTaskOrderQueryParam.setEquipmentIds(equipmentInfoService.getEquipmentIdsByParam(infoSearchDto));
        }

        calibrationTaskOrderQueryParam.setSortValue(buildCalibrationTaskSql(calibrationTaskOrderQueryParam.getSortPros()));

        Page<CalibrationTaskOrderPageDto> calibrationDtoPage = calibrationTaskOrderMapper.pageDto(page, calibrationTaskOrderQueryParam);
        List<CalibrationTaskOrderPageDto> calibrationDtos = calibrationDtoPage.getRecords();
        if(CollectionUtils.isNotEmpty(calibrationDtos)){
            calibrationDtos.stream().forEach(dto -> dto.setCountdown(countDate(dto.getPlanCalibrateDate())));
        }
        PageResult<CalibrationTaskOrderPageDto> result = PageResult.<CalibrationTaskOrderPageDto>builder()
                .records(calibrationDtoPage.getRecords()).total(calibrationDtoPage.getTotal()).build();
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    /**
     * 构建排序sql
     *
     * @param sortPros
     */
    private String buildCalibrationTaskSql(Map<String, String> sortPros) {
        if (null == sortPros || sortPros.isEmpty()) {
            return " ORDER BY calibration.create_time desc";
        }
        //是否已有排序字段
        Boolean flag = false;
        StringBuffer sb = new StringBuffer(" ORDER BY ");
        for (Map.Entry<String, String> entity : sortPros.entrySet()) {
            if (flag) {
                sb.append(", ");
            }
            if (entity.getKey().equals("code")) {
                flag = true;
                sb.append(" calibration.code ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentCode")) {
                flag = true;
                sb.append("equipment.code ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentName")) {
                flag = true;
                sb.append("equipment.name ").append(entity.getValue());
            } else if (entity.getKey().equals("planCalibrateDate")) {
                flag = true;
                sb.append("calibration.plan_calibrate_date ").append(entity.getValue());
            } else if (entity.getKey().equals("countdown")) {
                flag = true;
                sb.append("calibration.plan_calibrate_date ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentLocation")) {
                flag = true;
                sb.append("location.name ").append(entity.getValue());
            } else if (entity.getKey().equals("actualCalibrateDate")) {
                flag = true;
                sb.append("calibration.actual_calibrate_date ").append(entity.getValue());
            } else if (entity.getKey().equals("status")) {
                flag = true;
                sb.append("calibration.status ").append(entity.getValue());
            } else if (entity.getKey().equals("result")) {
                flag = true;
                sb.append("calibration.result ").append(entity.getValue());
            }
        }

        if (flag) {
            return sb.toString();
        } else {
            return " ORDER BY calibration.create_time desc";
        }
    }

    private Long countDate(Date planCalibrationDate){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdf.format(new Date());
        try {
            Date date = sdf.parse(dateStr);
            Long countdown = (planCalibrationDate.getTime() - date.getTime()) / DAY;
            if(countdown > 0) {
                return countdown;
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0L;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String saveCalibration(CalibrationTaskOrderAddParam calibrationTaskOrderAddParam) {
        log.info("新增或更新校准单:calibrationTaskOrderAddParam->" + JSON.toJSONString(calibrationTaskOrderAddParam));
        String calibrationId = calibrationTaskOrderAddParam.getCalibrationId();

        if(StringUtils.isBlank(calibrationId)) {
            calibrationId = buildNewCalibration(calibrationTaskOrderAddParam);
        }else{
            CalibrationTaskOrder calibrationTaskOrder = calibrationTaskOrderMapper.selectById(calibrationId);
            if(null == calibrationTaskOrder){
                calibrationId = buildNewCalibration(calibrationTaskOrderAddParam);
            }else {
                //如果有待校准的单，直接修改校准日期
                if (calibrationTaskOrder.getStatus().intValue() == StaticValue.ZERO) {
                    calibrationTaskOrder.setPlanCalibrateDate(calibrationTaskOrderAddParam.getPlanCalibrateDate());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String oldDisplayDate = sdf.format(calibrationTaskOrder.getDisplayDate());
                    String nowDate = sdf.format(new Date());
                    if(oldDisplayDate.compareTo(nowDate) > 0){
                        calibrationTaskOrder.setDisplayDate(calibrationTaskOrderAddParam.getDisplayDate());
                    }
                    updateById(calibrationTaskOrder);
                } else if(calibrationTaskOrder.getStatus().intValue() == StaticValue.ONE &&
                        calibrationTaskOrder.getResult().intValue() != StaticValue.FIVE){
                    //已校准的单，如果校准结果是禁用的，不生成新单,其余新增单
                    calibrationId = buildNewCalibration(calibrationTaskOrderAddParam);
                }
            }
        }
        log.info("新增或更新校准单结束");
        return calibrationId;
    }

    private String buildNewCalibration(CalibrationTaskOrderAddParam calibrationTaskOrderAddParam){
        CalibrationTaskOrder calibrationTaskOrder = CopyDataUtil.copyObject(calibrationTaskOrderAddParam, CalibrationTaskOrder.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, calibrationTaskOrder);
        calibrationTaskOrder.setCode(buildCode());
        calibrationTaskOrder.setStatus(StaticValue.ZERO);
        calibrationTaskOrder.setDeleted(DeletedType.NO.getValue());
        save(calibrationTaskOrder);
        return calibrationTaskOrder.getId();
    }

    private String buildCode(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        String year = String.valueOf(calendar.get(Calendar.YEAR));
        int index = year.length()- 2 > 0 ? year.length()- 2 : 0;
        String maxCode = calibrationTaskOrderMapper.getMaxCode(year.substring(index));

        StringBuffer prex = new StringBuffer(year.substring(index));
        if(StringUtils.isBlank(maxCode)){
            prex.append("0001");
            return prex.toString();
        }else{
            String suffix = maxCode.substring(2);
            try {
                Integer num = Integer.valueOf(suffix) + 1;
                String newCode = String.format("%04d", num);
                prex.append(newCode);
                return prex.toString();
            }catch (Exception e){
                log.error("生成编码失败");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_build", null, LocaleContextHolder.getLocale())));
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(CalibrationTaskOrderEditParam calibrationTaskOrderEditParam) {
        CalibrationTaskOrder calibrationTaskOrder = CopyDataUtil.copyObject(calibrationTaskOrderEditParam, CalibrationTaskOrder.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,calibrationTaskOrder);
        calibrationTaskOrder.setStatus(StaticValue.ONE);
        Boolean flag =  updateById(calibrationTaskOrder);
        if(calibrationTaskOrderEditParam.getType().intValue() == StaticValue.ZERO){
            calibrationTaskItemService.deleteByCalibrationId(calibrationTaskOrderEditParam.getId());
            if(CollectionUtils.isNotEmpty(calibrationTaskOrderEditParam.getItemDtos())){
                calibrationTaskItemService.saveList(calibrationTaskOrderEditParam.getItemDtos(), calibrationTaskOrderEditParam.getId());
            }
        }

        //更新设备状态、更新校准时间
        equipmentInfoService.updateCalibrationDate(calibrationTaskOrderEditParam.getEquipmentId()
                ,calibrationTaskOrderEditParam.getResult(), calibrationTaskOrderEditParam.getActualCalibrateDate(),
                calibrationTaskOrderEditParam.getCertificateNo());
        return flag;
    }

    @Override
    public List<String> getEquipmentIds(){
        List<String> equipmentIds =  calibrationTaskOrderMapper.getEquipmentIds();
        if(CollectionUtils.isNotEmpty(equipmentIds)){
            return equipmentIds.stream().distinct().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public CalibrationTaskOrderDto getDtoById(String id) {
        CalibrationTaskOrderDto calibrationTaskOrderDto = calibrationTaskOrderMapper.getDtoById(id);
        if(null == calibrationTaskOrderDto){
            log.error("未找到此校准单");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }
        calibrationTaskOrderDto.setEquipmentManufacturerName(getManufacturerName(calibrationTaskOrderDto.getEquipmentManufacturerId()));
        if(calibrationTaskOrderDto.getStatus().intValue() == StaticValue.ZERO){
            //未校准关联查询设备类型校准项
            List<CalibrationStandardDto> calibrationStandardDtos = calibrationStandardService.getByCategoryId(calibrationTaskOrderDto.getEquipmentCategoryId());
            List<CalibrationTaskItemDto> itemDtos = CopyDataUtil.copyList(calibrationStandardDtos, CalibrationTaskItemDto.class);
            calibrationTaskOrderDto.setItemDtos(itemDtos);
        }else{
            if(calibrationTaskOrderDto.getType().intValue() == StaticValue.ZERO){
                CalibrationTaskOrderDto refCalibration = calibrationTaskOrderMapper.getDtoById(calibrationTaskOrderDto.getRefCalibrationId());
                calibrationTaskOrderDto.setRefCalibrationId(refCalibration.getId());
                calibrationTaskOrderDto.setRefEquipmentId(refCalibration.getEquipmentId());
                calibrationTaskOrderDto.setRefEquipmentName(refCalibration.getEquipmentName());
                calibrationTaskOrderDto.setRefEquipmentSpecification(refCalibration.getEquipmentSpecification());
                calibrationTaskOrderDto.setRefEquipmentManufacturer(getManufacturerName(refCalibration.getEquipmentManufacturerId()));
                calibrationTaskOrderDto.setRefEquipmentFactoryCode(refCalibration.getEquipmentFactoryCode());
                calibrationTaskOrderDto.setRefCalibrateDate(refCalibration.getActualCalibrateDate());
                calibrationTaskOrderDto.setRefCertificateNo(refCalibration.getCertificateNo());
                calibrationTaskOrderDto.setRefEffectiveDate(equipmentInfoService.getEffectiveDate(refCalibration.getEquipmentId()));
                List<CalibrationTaskItemDto> itemDtos = calibrationTaskItemService.getByCalibrationId(calibrationTaskOrderDto.getId());
                calibrationTaskOrderDto.setItemDtos(itemDtos);
            }

        }
        return calibrationTaskOrderDto;
    }

    private String getManufacturerName(String manufacturerId){
        if(StringUtils.isBlank(manufacturerId)){
            return null;
        }
        RestResponse<ManufacturerSupplierDto> manufacturerRes =
                baseServiceClient.getManufacturerSupplierById(manufacturerId);
        if(!manufacturerRes.isSuccess()) {
            log.info("获取设备生产商失败");
        }else{
            ManufacturerSupplierDto manufacturerSupplierDto = manufacturerRes.getData();
            if(null != manufacturerSupplierDto) {
                return manufacturerSupplierDto.getName();
            }
        }
        return null;
    }

    @Override
    public RefEquipmentDto getRefEquipment(String equipmentId){
        RefEquipmentDto equipmentDto = new RefEquipmentDto();
        LambdaQueryWrapper<CalibrationTaskOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CalibrationTaskOrder::getEquipmentId, equipmentId);
        wrapper.eq(CalibrationTaskOrder::getStatus, StaticValue.ONE);
        wrapper.eq(CalibrationTaskOrder::getType, StaticValue.ONE);
        wrapper.ne(CalibrationTaskOrder::getResult, StaticValue.FIVE);
        wrapper.eq(CalibrationTaskOrder::getDeleted, DeletedType.NO.getValue());
        wrapper.select(CalibrationTaskOrder::getId);
        wrapper.orderByDesc(CalibrationTaskOrder::getUpdateTime);
        List<CalibrationTaskOrder> entities = calibrationTaskOrderMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(entities)){
            String calibrationId = entities.get(0).getId();
            CalibrationTaskOrderDto refCalibration = calibrationTaskOrderMapper.getDtoById(calibrationId);
            if(null == refCalibration){
                return equipmentDto;
            }
            equipmentDto.setRefCalibrationId(refCalibration.getId());
            equipmentDto.setRefEquipmentId(refCalibration.getEquipmentId());
            equipmentDto.setRefEquipmentName(refCalibration.getEquipmentName());
            equipmentDto.setRefEquipmentSpecification(refCalibration.getEquipmentSpecification());
            equipmentDto.setRefEquipmentManufacturerId(refCalibration.getEquipmentManufacturerId());
            equipmentDto.setRefEquipmentManufacturerName(getManufacturerName(refCalibration.getEquipmentManufacturerId()));
            equipmentDto.setRefEquipmentFactoryCode(refCalibration.getEquipmentFactoryCode());
            equipmentDto.setRefCalibrateDate(refCalibration.getActualCalibrateDate());
            equipmentDto.setRefCertificateNo(refCalibration.getCertificateNo());
            equipmentDto.setRefEffectiveDate(equipmentInfoService.getEffectiveDate(refCalibration.getEquipmentId()));
        }
        return equipmentDto;
    }
}
