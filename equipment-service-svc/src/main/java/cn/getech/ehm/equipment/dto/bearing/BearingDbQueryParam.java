package cn.getech.ehm.equipment.dto.bearing;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 轴承查询分页查询参数对象
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BearingDbQueryParam", description = "轴承查询参数")
public class BearingDbQueryParam extends PageParam {

    @ApiModelProperty(value = "生产厂家id")
    private String factoryId;

    @ApiModelProperty(value = "生产厂家名称")
    private String factoryName;

    @ApiModelProperty(value = "型号id")
    private String modelId;

    @ApiModelProperty(value = "型号名称")
    private String modelName;

}
