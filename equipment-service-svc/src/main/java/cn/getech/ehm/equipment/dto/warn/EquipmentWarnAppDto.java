package cn.getech.ehm.equipment.dto.warn;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 设备告警 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "EquipmentWarnAppDto", description = "设备告警返回数据模型")
public class EquipmentWarnAppDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "iot关联设备id")
    private String iotEquipmentId;

    @ApiModelProperty(value = "告警级别(1预警2报警)")
    private Integer level;

    @ApiModelProperty(value = "告警信息")
    private String remark;

    @ApiModelProperty(value = "告警状态(1告警中3结束)")
    private Integer status;

    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;

    @ApiModelProperty(value = "修复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repairTime;

    @ApiModelProperty(value = "图片ids")
    private String picIds;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}