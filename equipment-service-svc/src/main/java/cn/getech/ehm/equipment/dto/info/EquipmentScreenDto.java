package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 大屏设备列表
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "EquipmentScreenDto", description = "大屏设备列表")
public class EquipmentScreenDto {

    @ApiModelProperty(value = "设备id")
    private String id;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备编码")
    private String code;

    @ApiModelProperty(value = "告警状态(1告警中3结束)")
    private Integer status;

}