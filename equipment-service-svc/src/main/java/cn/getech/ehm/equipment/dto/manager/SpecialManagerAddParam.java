package cn.getech.ehm.equipment.dto.manager;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 管理人员新增
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SpecialManagerAddParam", description = "管理人员新增")
public class SpecialManagerAddParam extends ApiParam {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "登记证号")
    private String certificateNo;

    @ApiModelProperty(value = "作业项目")
    private String jobItem;

    @ApiModelProperty(value = "发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issueDate;

    @ApiModelProperty(value = "复审日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reviewDate;

    @ApiModelProperty(value = "证件有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateValidity;

    @ApiModelProperty(value = "发证机关")
    private String issuingAuthority;

    @ApiModelProperty(value = "聘用单位")
    private String employer;

    @ApiModelProperty(value = "所属车间")
    private String workshop;

    @ApiModelProperty(value = "附件ids")
    private String docIds;

    @ApiModelProperty("提前预警天数")
    private Integer deadlineDays;

    @ApiModelProperty("证书名称")
    private String certificateName;

    @ApiModelProperty("证书类型")
    private String certificateType;

    @ApiModelProperty("身份证号")
    private String idNumber;
}