package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 零部件运行参数变量
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_structure_variable")
public class EquipmentStructureVariable extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 零部件运行参数id
     */
    @TableField("structure_parameter_id")
    private String structureParameterId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 来源(1录入2参数)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 值(来源参数为运行参数id,否则为输入)
     */
    @TableField("value")
    private String value;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
}
