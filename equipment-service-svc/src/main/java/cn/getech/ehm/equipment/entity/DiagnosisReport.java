package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 诊断报告
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_diagnosis_report")
public class DiagnosisReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备名称
     */
    @TableField("code")
    private String code;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 分析图片信息
     */
    @TableField(value = "analyze_pic_info")
    private String analyzePicInfo;

    /**
     * 分析信息
     */
    @TableField("analyze_info")
    private String analyzeInfo;

    /**
     * 分析原因
     */
    @TableField("analyze_reason")
    private String analyzeReason;

    /**
     * 处理建议
     */
    @TableField("handling_suggestions")
    private String handlingSuggestions;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
