package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备零件
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_spare_parts")
public class EquipmentSpareParts extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;
    /**
     * 类型(1轴承2齿轮)
     */
    @TableField("type")
    private Integer type;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 关联id(轴承id、齿轮id)
     */
    @TableField("relation_id")
    private String relationId;

    /**
     * 轴承厂家
     */
    @TableField("factory_name")
    private String factoryName;

    /**
     * 型号
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 传动比
     */
    @TableField("proportion")
    private String proportion;

    /**
     * 最大转速
     */
    @TableField("max_speed")
    private Double maxSpeed;

    /**
     * 最小转速
     */
    @TableField("min_speed")
    private Double minSpeed;

    /**
     * 图片id
     */
    @TableField("pic_id")
    private String picId;

    /**
     * 扩展属性
     */
    @TableField("property")
    private String property;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

}
