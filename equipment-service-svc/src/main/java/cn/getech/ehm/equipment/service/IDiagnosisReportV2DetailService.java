package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.diagnosis.ReportV2DetailDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReportDetail;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 诊断报告v2详情 服务类
 */
public interface IDiagnosisReportV2DetailService extends IBaseService<EquipmentDiagnosisReportDetail> {

    /**
     * 初始化保存
     * @param reportId
     * @param equipmentIds
     */
    Boolean saveByParam(String reportId, List<String> equipmentIds);

    /**
     * 根据报告id查询
     * @param reportId
     * @return
     */
    List<ReportV2DetailDetailDto> getListByReportId(String reportId);

    /**
     * 更新设备信息
     */
    Boolean updateByParam(String reportId, List<ReportV2DetailDetailDto> followEquipments, List<ReportV2DetailDetailDto> equipmentList);

    /**
     * 删除
     * @param reportIds
     * @return
     */
    Boolean deleteByReportIds(List<String> reportIds);

    /**
     * 根据报告id查询设备iDs
     */
    List<String> getEquipmentIdByReportId(String reportId);
}