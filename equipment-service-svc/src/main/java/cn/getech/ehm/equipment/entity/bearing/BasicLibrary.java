package cn.getech.ehm.equipment.entity.bearing;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础库
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_library")
public class BasicLibrary extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 字段名
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 类型(1部件2零件)
     */
    @TableField("type")
    private Integer type;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
