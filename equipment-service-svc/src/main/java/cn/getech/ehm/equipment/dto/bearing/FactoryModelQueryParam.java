package cn.getech.ehm.equipment.dto.bearing;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 轴承品牌/型号查询参数
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FactoryModelQueryParam", description = "轴承品牌/型号查询参数")
public class FactoryModelQueryParam extends PageParam {

    @ApiModelProperty(value = "型号/品牌名称")
    private String name;

}
