package cn.getech.ehm.equipment.service;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.dto.app.SearchPageParam;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoEditParam;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoQueryParam;
import cn.getech.ehm.equipment.dto.iot.*;
import cn.getech.ehm.equipment.dto.location.LocationEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.overview.EquipmentProgressDto;
import cn.getech.ehm.equipment.dto.overview.CardEquipmentTimeDto;
import cn.getech.ehm.equipment.dto.overview.HealthEstimateDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.dto.warn.IotDetailDto;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.dto.RelSearchDto;
import cn.getech.ehm.equipment.dto.StudioEquipmentBomDto;
import cn.getech.ehm.equipment.dto.OeeDetailListDto;
import cn.getech.ehm.equipment.dto.OeeQueryParam;
import cn.getech.ehm.iot.dto.warn.FaultEquipmentNumDto;
import cn.getech.ehm.iot.dto.warn.InfoParamWarnDto;
import cn.getech.ehm.iot.dto.warn.StatisticsLineDto;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.param.PageParam;
import cn.getech.poros.framework.common.service.IBaseService;
import org.springframework.lang.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备表 服务类
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
public interface IEquipmentInfoService extends IBaseService<EquipmentInfo> {

        /**
         * 分页查询，返回Dto
         *
         * @param equipmentInfoQueryParam
         * @return
         */
        PageResult<EquipmentInfoListDto> pageDto(EquipmentInfoQueryParam equipmentInfoQueryParam);

        Boolean batchUpdate(EquipmentInfoBatchUpdateParam equipmentInfoBatchUpdateParam);

        /**
         * 弹框分页获取作业标准详细信息
         *
         * @param queryParam
         * @return
         */
        PageResult<EquipmentInfoDetailDto> detailList(DetailQueryParam queryParam);

        /**
         * 设备结构树
         * @param parentId
         * @return
         */
        List<LocationEquipmentTreeDto> equipmentTree(String parentId, String tenantId);

        /**
         * 获取设备懒加载结构树
         * @param parentId
         * @param type
         * @return
         */
        List<LazyEquipmentTreeDto> lazyEquipmentTree(String parentId, Integer type, String keyword);

        /**
         * 获取拥有子节点的ids
         * @param parentIds
         * @return
         */
        List<String> hasChildIds(List<String> parentIds);

        /**
         * 获取当前节点下设备运行统计
         * @param statusSearchDto
         * @return
         */
        List<RunningStatusDto> getRunningStatus(StatusSearchDto statusSearchDto);

        /**
         * 设备结构树新增
         * @param editDto
         * @return
         */
        String equipmentTreeAdd(EquipmentTreeEditDto editDto);

        /**
         * 设备结构树修改
         * @param editDto
         * @return
         */
        Boolean equipmentTreeEdit(EquipmentTreeEditDto editDto);

        /**
         * 获取全部设备
         * @return
         */
        List<LocationEquipmentTreeDto> getAllTreeList();

        /**
         * 设备结构树查询
         * @param relSearchDto
         * @return
         */
        List<EquipmentTreeDto> searchTree(RelSearchDto relSearchDto);

        /**
         * 参数监测设备结构树
         * @return
         */
        List<EquipmentTreeDto> paramEquipmentTree(RelSearchDto relSearchDto);

        /**
         * 获取设备下全部bom
         * @param relSearchDto
         * @return
         */
        List<StudioEquipmentBomDto> getStudioEquipmentBomList(RelSearchDto relSearchDto);

        /**
         * 设备BOM树
         * @param equipmentId
         * @return
         */
        EquipmentBomTreeDto equipmentBomTree(String equipmentId);

        /**
         * app分页查询，返回Dto
         *
         * @param infoAppQueryParam
         * @return
         */
        PageResult<EquipmentInfoAppDetailDto> appPageDto(InfoAppQueryParam infoAppQueryParam);

        /**
         * 判断是否客户/工程师/审核用户
         *
         * @return
         */
        CheckEquipmentIdDto getClientEquipmentIds();

        /**
         * 根据关键词搜索设备，模糊匹配code和name
         *
         * @param pageParam
         * @return
         */
        List<EquipmentListDto> search(PageParam pageParam);

        /**
         * 根据设备名称模糊查询设备id
         * @param equipmentName
         * @return
         */
        List<String> getInfoIdsByName(String equipmentName);
        List<EquipmentListDto> searchEquipment(SearchPageParam searchPageParam);

        /**
         * 获取base附件map
         *
         * @param picIdList
         * @return
         */
        Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList);

        /**
         * 分页查询主设备
         *
         * @param param
         * @return
         */
        PageResult<EquipmentTreeDetailDto> listPageDto(InfoSearchDto param);

        /**
         *小窗口查询设备、子设备
         * @param parentId
         * @return
         */
        List<EquipmentTreeDetailDto> secondaryInfoList(String parentId);

        /**
         * 获取大屏设备信息
         *
         * @param equipmentIds
         * @return
         */
        List<EquipmentScreenDto> getScreenInfos(List<String> equipmentIds);

        /**
         * 保存
         *
         * @param equipmentInfoAddParam
         * @return
         */
        String saveByParam(EquipmentInfoAddParam equipmentInfoAddParam);

        /**
         * 清理缓存
         * @return
         */
        Boolean clearCache();

        /**
         * 获取设备有效日期
         *
         * @param equipmentId
         * @return
         */
        Date getEffectiveDate(String equipmentId);

        /**
         * 根据id查询，转dto
         *
         * @param id
         * @return
         */
        EquipmentInfoDto getDtoById(String id);

        public EquipmentInfoDto getDtoByPositionTagId(String id);

        /**
         * app根据id查询
         *
         * @param id
         * @return
         */
        EquipmentInfoAppDto getAppDtoById(String id);

        /**
         * 根据id获取设备文档
         * @param equipmentId
         * @return
         */
        EquipmentDocDto getDocByInfoId(String equipmentId);

        /**
         * 编辑设备文档
         * @param editParam
         * @return
         */
        Boolean updateDoc(EquipmentDocEditParam editParam);

        /**
         * 更新
         *
         * @param equipmentInfoEditParam
         */
        boolean updateByParam(EquipmentInfoEditParam equipmentInfoEditParam);

        /**
         * 获取设备位置下设备数量
         *
         * @param equipmentLocationId
         * @return
         */
        Integer getCountByLocationId(List<String> equipmentLocationId, List<String> customerInfoIds);

        /**
         * 获取设备类型下设备数量
         *
         * @param equipmentTypeId
         * @return
         */
        Integer getCountByTypeId(List<String> equipmentTypeId, List<String> customerInfoIds);

        /**
         * 校验供应商厂商等是否被使用
         *
         * @param relatedId 相关id
         * @return
         */
        Boolean checkSupplierUsed(String relatedId);

        /**
         * 校验备件是否被设备相关使用
         *
         * @param partIds
         * @return
         */
        List<String> checkPartUsed(String[] partIds);

        /**
         * 根据id集合获取设备列表
         *
         * @param equipmentIds
         * @return
         */
        Map<String, EquipmentListDto> getListByIds(String[] equipmentIds);

        /**
         * 生成二维码图片
         *
         * @param ids
         * @return
         */
        List<EquipmentInfoDto> createQrCodeByIds(String[] ids);

        /**
         * 获取设备二维码配置信息
         * @return
         */
        EquipmentLabelDto qrLabel();

        /**
         * 根据id删除设备
         *
         * @param ids
         * @return
         */
        Boolean removeByIds(String[] ids);

        /**
         * 根据编码批量逻辑删除,不做校验
         * @param codes
         * @return
         */
        Boolean deleteByCodes(List<String> codes, String tenantId);

        /**
         * app根据设备ID获取备件列表dto
         *
         * @param id
         * @return
         */
        List<EquipmentPartAppDto> getAppPartById(String id);

        /**
         * 根据设备ID获取二维码
         *
         * @param id
         * @return
         */
        String getQrByInfoId(String id);

        /**
         * 根据id集合获取设备维护人员列表
         *
         * @param equipmentIds
         * @return
         */
        List<EquipmentMaintainerDto> getMaintainerListByIds(String[] equipmentIds);

        /**
         * 获取全部设备id
         *
         * @return
         */
        List<String> getAllIds();

        /**
         * 根据查询条件获取设备ids
         * @return
         */
        List<String> getEquipmentIdsByParam(EquipmentInfoSearchDto param);

        /**
         * 根据查询条件获取设备ids无权限
         * @return
         */
        List<String> getEquipmentIdsByParamNonAuth(EquipmentInfoSearchDto param);

        /**
         * 根据设备名称和设备编码，判设备是否存在
         *
         * @param code
         * @param name
         * @return
         */
        Boolean checkExists(String code, @Nullable String name);

        /**
         * 更新设备状态、更新校准时间
         *
         * @param equipmentId
         * @param result          1:正常4限用5禁用
         * @param calibrationDate
         * @return
         */
        Boolean updateCalibrationDate(String equipmentId, Integer result, Date calibrationDate, String certificateNo);

        /**
         * 根据类别id获取设备列表
         *
         * @param id
         * @return
         */
        List<EquipmentInfo> getEquipmentListByCategoryId(String id);


        /**
         * 批量生成校准工单
         *
         * @param equipmentCategory
         * @param equipmentInfoList
         */
        void createCalibrationTaskOrderBatch(EquipmentCategory equipmentCategory, List<EquipmentInfo> equipmentInfoList);

        /**
         * 根据类型ids、位置ids获取设备信息集合
         * @return
         */
        Map<String, EquipmentStatisticsDto> getEquipmentStatistics(InfoSearchDto param);

        /**
         * 导出设备列表数据(按类型)
         *
         * @param equipmentQueryParam
         * @return
         */
        List<EquipmentInfoExcel> getExcelList(EquipmentSpecialQueryParam equipmentQueryParam);

        /**
         * 导出设备列表数据(扩展属性为json)
         * @param equipmentQueryParam
         * @return
         */
        List<EquipmentExportJsonExcel> getExcelInfoList(EquipmentSpecialQueryParam equipmentQueryParam);


        /**
         * 导出特种设备列表数据
         *
         * @param queryParam
         * @return
         */
        List<EquipmentSpecialExcel> getSpecialExcelList(EquipmentSpecialQueryParam queryParam);

        /**
         * 获取表单配置的字段,别名
         * @param exported 是否过滤导出字段
         * @return
         */
        Map<String, String> getShowFields(Boolean exported);

        /**
         * 获取列表中选择项
         * @return
         */
        Map<String, List<String>> getComboMap();

        /**
         * 导入设备台账(按类型)
         * @param propExported 扩展属性是否导出标记
         * @param rows
         * @return
         */
        String excelImport(List<EquipmentSpecialExcel> rows, Boolean propExported);

        /**
         * 导入设备台账(扩展属性为大json)
         *
         * @param rows
         * @return
         */
        String importPropJson(List<EquipmentImportJsonExcel> rows);

        /**
         * 设备离线在线
         *
         * @param iotDetailDtos
         * @return
         */
        Boolean updateOnlineStatus(List<IotDetailDto> iotDetailDtos);

        /**
         * 获取当前登录用户系统数量、接入iot数量
         *
         * @return
         */
        Integer equipmentCount(ClientHomeDto clientHomeDto);

        /**
         * 求设备所有可用的数量
         * @return
         */
        Integer equipmentAllCount();

        /**
         * 查询系统接入数
         * @return
         */
        Integer equipmentJoinCount();

        String getNamesByIds(List<String> equipmentIds);

        /**
         * 根据位置ID获取设备列表
         * @param locationId
         * @return
         */
        List<EquipmentSummaryDto> findByLocationId(String locationId, String keyword);

        /**
         * 获取设备总数量
         * @return
         */
        int getTotalCount();

        /**
         * 获取设备概要信息列表
         * @param searchDto
         * @return
         */
        Map<String, EquipmentSummaryDto> getSummaryMap(EquipmentInfoSearchDto searchDto);

        /**
         * 获取组态列表
         *
         * @param studioQueryParam
         * @return
         */
        PageResult<StudioResDetailDto> getStudioList(StudioQueryParam studioQueryParam);

        /**
         * 根据id获取组态信息
         *
         * @param id
         * @return
         */
        EquipmentStudioDto getStudioByInfoId(String id);

        /**
         * 获取iot组态路径
         * @param id
         * @return
         */
        String pcStudioUrl(String id);

        /**
         * 修改设备组态信息
         *
         * @param studioEditParam
         * @return
         */
        Boolean updateInfoStudio(StudioEditParam studioEditParam);

        /**
         * 获取id/name键值对
         * @param equipmentIds
         * @return
         */
        Map<String, String> getNameMapByIds(List<String> equipmentIds);

        /**
         * 获取设备最顶级主设备id
         * @param equipmentId
         * @return
         */
        String getRootInfoId(String equipmentId);

        /**
         * 根据id集合获取设备列表
         *
         * @param equipmentIds
         * @return
         */
        Map<String, SpecialEquipmentDto> getSpecialListByIds(String[] equipmentIds);

        /**
         * 获取设备向上depth级的全名称集合
         * @param equipmentIds
         * @param removeOur 是否去除自己
         * @return
         */
        Map<String, EquipmentParentNameDto> getDepthAllName(List<String> equipmentIds, Boolean removeOur);

        /**
         * 获取设备向上depth级的名称集合
         * @param equipmentIds
         * @param removeOur 是否去除自己
         * @return
         */
        Map<String, String> getDepthName(List<String> equipmentIds, Boolean removeOur);

        /**
         * 校验设备类型是否被使用
         * @param categoryId
         * @return
         */
        Boolean checkCategoryUsed(String categoryId);

        /**
         * SDSH导入用户
         * @param rows
         * @return
         */
        void userExcelImport(List<SDSHUserExcel> rows);

        /**
         * 获取设备类型id
         * @param equipmentId
         * @return
         */
        String getCategoryId(String equipmentId);

        /**
         * 修改设备转速范围
         * @return
         */
        Boolean updateEquipmentRate(EquipmentRateDto equipmentRateDto);

        /**
         * 获取设备转速
         * @param equipmentId
         * @return
         */
        EquipmentRateDto getRate(String equipmentId);

        /**
         * 获取设备转速范围
         * @param equipmentIds
         * @return
         */
        Map<String, EquipmentRateDto> getRateMap(String[] equipmentIds);

        /**
         * 按类型获取在线设备数量
         * @return
         */
        RunningEquipmentDto getRunningInfoNum(InfoStatisticsSearchDto dto);

        /**
         * 获取设备首张图片
         * @param id
         * @return
         */
        String equipmentPicUrl(String id);

        /**
         * 卡片当前设备信息(设备)
         * @param equipmentId
         * @return
         */
        CardEquipmentTimeDto currentEquipmentCard(String equipmentId);

        /**
         * 运行历程卡片(设备)
         * @param equipmentId
         * @return
         */
        EquipmentProgressDto equipmentProgress(String equipmentId, Date beginTime, Date endTime);

        /**
         * 报警趋势卡片(设备)
         * @param equipmentId
         * @return
         */
        WarnCountDto cardWarnTrend(String equipmentId);

        /**
         * 报警列表卡片（3条）
         * @param equipmentId
         * @return
         */
        List<InfoParamWarnDto> cardWarnList(String equipmentId);

        /**
         * 运行时效率(设备)
         * @param equipmentId
         * @return
         */
        List<Double> equipmentEfficiency(String equipmentId);

        String getLayerCode(String parentId, String id);

        /**
         * 设备统计
         * @return
         */
        List<EquipmentRunningStatusDto> getEquipmentRunningStatusCount(Boolean auth, List<String> locationIds, List<String> categoryIds);

        /**
         * 设备报警状态统计
         * @param auth
         * @return
         */
        List<EquipmentRunningStatusDto> equipmentIotStatusCount(Boolean auth);

        /**
         * 设备使用率
         * @return
         */
        List<EquipmentRateOfUtilizationDto> getEquipmentRateOfUtilization();

        /**
         * 时间统计
         *
         */
        TimeStatisticsDto getTimeStatistics();

       /**
        * 设备所有状态
        *
        */
         List<EquipmentWarnTrendDto> getEquipmentAllStatus();

        /**
         * 设备报警趋势
         *
         */
        WarnCountDto getEquipmentWarnTrend();

        /**
         * 获取当前登录用户权限下设备ids
         * 定时器、多线程等需要注意是否存在当前用户信息
         * @return
         */
        BuildInfoSearchDto getCurrentUserInfoIds();

        /**
         * 故障率
         * @return
         */
        StatisticsLineDto failureRate(InfoStatisticsSearchDto dto);

        /**
         * 统计最近一个月设备报警数量前5名
         */
        List<FaultEquipmentNumDto> getFaultInfo(InfoStatisticsSearchDto dto);

        /**
         * 设备状态统计,取iotStatus
         * @return
         */
        WarnCountDto getRunningStatusCount(InfoStatisticsSearchDto dto);

        /**
         * 获取设备类型设备部分信息
         * @param categoryId
         * @return
         */
        List<EquipmentInfo> getInfosByCategoryId(String categoryId);

        /**
         * oee分页设备列表
         * @param queryParam
         * @return
         */
        PageResult<OeeDetailListDto> oeeEquipmentPageList(OeeQueryParam queryParam);

        /**
         * oee设备列表
         * @param queryParam
         * @return
         */
        List<OeeDetailListDto> oeeEquipmentList(OeeQueryParam queryParam);

        /**
         * 获取开启oee的设备ids
         * @return
         */
        List<String> getOeeOpenInfoIds();

        /**
         * 更新设备停机标识
         * @param equipmentId
         * @return
         */
        Boolean updateEquipmentStopEnable(String equipmentId, Boolean enabled);

        /**
         * 获取设备是否停机
         * @param equipmentId
         * @return
         */
        EquipmentSummaryDto getInfoParamStop(String equipmentId);

        /**
         * 判断父节点是否存在(父节点为设备或位置)
         * @param parentIds
         * @return
         */
        List<String> getExistParentIds(List<String> parentIds);

        /**
         * 设备切换父节点
         * @param toParentType 目标父节点类型1位置11设备12子设备
         * @param toParentId
         * @param oldInfoLayerCode 当前设备目前层级编码
         * @return
         */
        Boolean changeEquipmentParent(String equipmentId, Integer toParentType, String toParentId, String oldInfoLayerCode);

        /**
         * 获取设备层级编码
         * @param equipmentIds
         * @return
         */
        Map<String, String> getLayerCodeMap(List<String> equipmentIds);
        public void updateSort(List<EquipmentSortEditParam> param);

        /**
         * 获取不足X月的设备数量
         * @param nearMonth
         * @return
         */
        String getStatisticsOfRemainingLifeCount(String nearMonth);

        /**
         * 获取设备健康评估
         * @param equipmentId
         * @return
         */
        HealthEstimateDto equipmentHealthEstimate(String equipmentId);

        /**
         * 获取设备健康
         * @param equipmentId
         * @return
         */
        Double equipmentHealthIndex(String equipmentId);

        /**
         * 清空剩余寿命
         * @param equipmentId
         * @return
         */
        Boolean clearRemainingLife(String equipmentId);

        /**
         * 算法修改设备残余寿命/健康指数/健康状态
         */
        Boolean algorithmUpdateEquipment(InfoDefaultParamDto dto);

        public List<RunningStatusDto> getHealthStatus(StatusSearchDto statusSearchDto);

        public List<RunningStatusDto> getIotStatus(StatusSearchDto statusSearchDto);

        /**
         * 根据设备编码获取设备信息
         * @return
         */
        EquipmentSummaryDto getEquipmentInfoByCode(String equipmentCode);

        /**
         * 根据编码获取id
         * @param codes
         * @return
         */
        Map<String, String> getIdByCode(List<String> codes);

        String getFirstIdByCode(String code);
}