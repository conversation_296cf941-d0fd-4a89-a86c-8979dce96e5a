package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.EquipmentStructureDto;
import cn.getech.ehm.equipment.dto.info.SpecialStructureDto;
import cn.getech.ehm.equipment.entity.EquipmentStructure;
import cn.getech.ehm.equipment.dto.FeatureParameterDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备部件 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Repository
public interface EquipmentStructureMapper extends BaseMapper<EquipmentStructure> {
    /**
     * 获取设备部件
     * @param equipmentId
     * @return
     */
    List<EquipmentStructureDto> getListByEquipmentId(@Param("equipmentId") String equipmentId,@Param("keyword") String keyword);

    /**
     * 获取监测中所有特种参数
     * @param equipmentId
     * @return
     */
    List<FeatureParameterDto> getFeatureStructures(@Param("equipmentId") String equipmentId, @Param("structureId") String structureId);

    /**
     * 获取设备部件
     * @param structureIds
     * @return
     */
    List<SpecialStructureDto> getSpecialListByIds(@Param("structureIds") String[] structureIds);

    /**
     * 获取设备下最大排序
     * @param equipmentId
     * @return
     */
    Integer getMaxSort(@Param("equipmentId") String equipmentId);

    List<EquipmentStructureDto> getListByStructureId(@Param("structureIds") List<String> structureIds);
}
