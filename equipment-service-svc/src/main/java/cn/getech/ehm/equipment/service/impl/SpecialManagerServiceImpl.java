package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.dto.manager.*;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto;
import cn.getech.ehm.equipment.entity.EquipmentInfoSpecial;
import cn.getech.ehm.equipment.entity.SpecialManager;
import cn.getech.ehm.equipment.mapper.SpecialManagerMapper;
import cn.getech.ehm.equipment.service.ISpecialManagerService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecOrgClient;
import cn.getech.poros.permission.dto.PorosSecOrgDto;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 安全管理人员接口
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@Slf4j
@Service
public class SpecialManagerServiceImpl extends BaseServiceImpl<SpecialManagerMapper, SpecialManager> implements ISpecialManagerService {

    @Autowired
    private SpecialManagerMapper specialManagerMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private PorosSecOrgClient porosSecOrgClient;

    @Override
    public PageResult<SpecialManagerDto> pageList(SpecialManagerQueryParam queryParam) {
        Page<SpecialManagerQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), SpecialManager.class, null));
        IPage<SpecialManagerDto> result = specialManagerMapper.getPageList(page, queryParam);
        List<SpecialManagerDto> specialManagerDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            List<String> orgCodes = result.getRecords().stream().filter(dto -> StringUtils.isNotBlank(dto.getWorkshop()))
                    .map(SpecialManagerDto::getWorkshop).distinct().collect(Collectors.toList());

            Map<String, String> orgMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orgCodes)) {
                RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(StringUtils.join(orgCodes.toArray(), StringPool.COMMA), "0");
                if (orgRes.isOk()) {
                    orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
                } else {
                    log.error("获取组织失败");
                }
            }
            List<String> picIdList = result.getRecords().stream().filter(dto -> StringUtils.isNotBlank(dto.getDocIds())).map(SpecialManagerDto::getDocIds).distinct().collect(Collectors.toList());
            Map<String, AttachmentClientDto> docMap = getAttachmentMap(picIdList);
            for (SpecialManagerDto specialManagerDto : result.getRecords()) {
                if (StringUtils.isNotBlank(specialManagerDto.getWorkshop())) {
                    specialManagerDto.setWorkshopName(orgMap.get(specialManagerDto.getWorkshop()));
                }
                if (StringUtils.isNotBlank(specialManagerDto.getDocIds())) {
                    String[] picStrs = specialManagerDto.getDocIds().split(StringPool.COMMA);
                    List<AttachmentClientDto> docDocs = new ArrayList<>(picStrs.length);
                    for (String picStr : picStrs) {
                        AttachmentClientDto attachmentClientDto = docMap.get(picStr);
                        if (null != attachmentClientDto) {
                            docDocs.add(attachmentClientDto);
                        }
                    }
                    specialManagerDto.setDocDtos(docDocs);
                }
                specialManagerDtos.add(specialManagerDto);
                //处理过期时间
                Date now = new Date();
                for (SpecialManagerDto temp : result.getRecords()) {
                    temp.setIsOverTime(false);
                    if (temp.getCertificateValidity() != null) {
                        if (temp.getCertificateValidity().before(now)) {
                            temp.setIsOverTime(true);
                        }
                    }
                }
            }
        }

        return Optional.ofNullable(PageResult.<SpecialManagerDto>builder()
                        .records(specialManagerDtos)
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult(Collections.emptyList(), 0));
    }

    private Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }

    @Override
    public List<SpecialManagerExcelDto> getExcelList(SpecialManagerQueryParam queryParam) {
        List<SpecialManagerExcelDto> excelDtos = new ArrayList<>();
        LambdaQueryWrapper<SpecialManager> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(queryParam.getKeyword())) {
            wrapper.like(SpecialManager::getName, queryParam.getKeyword());
        }
        List<SpecialManager> specialManagers = specialManagerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(specialManagers)) {
            List<String> orgCodes = specialManagers.stream().filter(dto -> StringUtils.isNotBlank(dto.getWorkshop()))
                    .map(SpecialManager::getWorkshop).distinct().collect(Collectors.toList());

            Map<String, String> orgMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orgCodes)) {
                RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(StringUtils.join(orgCodes.toArray(), StringPool.COMMA), "0");
                if (orgRes.isOk()) {
                    orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
                } else {
                    log.error("获取组织失败");
                }
            }
            Map<String, DictionaryItemDto> jobItemMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobItemResponse = baseServiceClient.getItemMapByCode("job_item");
            if (jobItemResponse.isOk()) {
                jobItemMap = jobItemResponse.getData();
            } else {
                log.info("获取作业项目失败");
            }
            Map<String, DictionaryItemDto> employerMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> employerResponse = baseServiceClient.getItemMapByCode("employer");
            if (employerResponse.isOk()) {
                employerMap = employerResponse.getData();
            } else {
                log.info("获取聘用单位失败");
            }
            for (SpecialManager specialManager : specialManagers) {
                SpecialManagerExcelDto excelDtoDto = CopyDataUtil.copyObject(specialManager, SpecialManagerExcelDto.class);
                if (StringUtils.isNotBlank(excelDtoDto.getWorkshop())) {
                    excelDtoDto.setWorkshopName(orgMap.get(excelDtoDto.getWorkshop()));
                }
                if (StringUtils.isNotBlank(excelDtoDto.getJobItem())) {
                    excelDtoDto.setJobItemName(null != jobItemMap.get(excelDtoDto.getJobItem()) ? jobItemMap.get(excelDtoDto.getJobItem()).getName() : null);
                }
                if (StringUtils.isNotBlank(excelDtoDto.getEmployer())) {
                    excelDtoDto.setEmployerName(null != employerMap.get(excelDtoDto.getEmployer()) ? employerMap.get(excelDtoDto.getEmployer()).getName() : null);
                }
                excelDtos.add(excelDtoDto);
            }
        }
        return excelDtos;
    }

    @Override
    public Boolean saveByParam(SpecialManagerAddParam addParam) {
        SpecialManager specialManager = CopyDataUtil.copyObject(addParam, SpecialManager.class);
        if (StringUtils.isNotBlank(addParam.getWorkshop())) {
            specialManager.setWorkshop(addParam.getWorkshop().split(StringPool.SLASH)[0]);
        }
        return save(specialManager);
    }

    @Override
    public Boolean editByParam(SpecialManagerEditParam editParam) {
        SpecialManager specialManager = CopyDataUtil.copyObject(editParam, SpecialManager.class);
        if (StringUtils.isNotBlank(editParam.getWorkshop())) {
            specialManager.setWorkshop(editParam.getWorkshop().split(StringPool.SLASH)[0]);
        }
        return updateById(specialManager);
    }

    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    @Override
    public SpecialManagerDto getDtoById(String id) {
        SpecialManagerDto specialManagerDto = CopyDataUtil.copyObject(specialManagerMapper.selectById(id), SpecialManagerDto.class);
        if (StringUtils.isNotBlank(specialManagerDto.getDocIds())) {
            List<String> picIdList = new ArrayList<>(StaticValue.ONE);
            picIdList.add(specialManagerDto.getDocIds());
            Map<String, AttachmentClientDto> docMap = getAttachmentMap(picIdList);
            String[] picStrs = specialManagerDto.getDocIds().split(StringPool.COMMA);
            List<AttachmentClientDto> docDocs = new ArrayList<>(picStrs.length);
            for (String picStr : picStrs) {
                AttachmentClientDto attachmentClientDto = docMap.get(picStr);
                if (null != attachmentClientDto) {
                    docDocs.add(attachmentClientDto);
                }
            }
            specialManagerDto.setDocDtos(docDocs);
        }
        if (StringUtils.isNotBlank(specialManagerDto.getWorkshop())) {

            Map<String, String> orgMap = new HashMap<>();
            RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(specialManagerDto.getWorkshop(), "0");
            if (orgRes.isOk()) {
                orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
            } else {
                log.error("获取组织失败");
            }
            String name = orgMap.get(specialManagerDto.getWorkshop());
            specialManagerDto.setWorkshopName(name);
            specialManagerDto.setWorkshop(StringUtils.isNotBlank(name) ? specialManagerDto.getWorkshop() + StringPool.SLASH + name : specialManagerDto.getWorkshop());

        }
        return specialManagerDto;
    }

    @Override
    public String excelImport(List<SpecialManagerExcelDto> rows) {
        String result = checkExcelField(rows);
        if (StrUtil.isNotBlank(result)) {
            return result;
        }
        Map<String, String> jobItemNameMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> jobItemResponse = baseServiceClient.getItemMapByCode("job_item");
        if (jobItemResponse.isOk()) {
            Map<String, DictionaryItemDto> jobItemMap = jobItemResponse.getData();
            if (null != jobItemMap && jobItemMap.size() > 0) {
                List<DictionaryItemDto> jobItems = new ArrayList<>(jobItemMap.values());
                jobItemNameMap = jobItems.stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue, (v1, v2) -> v1));
            }
        } else {
            log.info("获取作业项目失败");
        }
        Map<String, String> emplyeeNameMap = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> employerResponse = baseServiceClient.getItemMapByCode("employer");
        if (employerResponse.isOk()) {
            Map<String, DictionaryItemDto> employerMap = employerResponse.getData();
            if (null != employerMap && employerMap.size() > 0) {
                List<DictionaryItemDto> employers = new ArrayList<>(employerMap.values());
                emplyeeNameMap = employers.stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue, (v1, v2) -> v1));
            }
        } else {
            log.info("获取聘用单位失败");
        }
        List<SpecialManager> specialManagers = new ArrayList<>(rows.size());
        for (SpecialManagerExcelDto excelDto : rows) {
            SpecialManager specialManager = CopyDataUtil.copyObject(excelDto, SpecialManager.class);
            specialManager.setEmployer(emplyeeNameMap.get(excelDto.getEmployerName()));
            specialManager.setJobItem(jobItemNameMap.get(excelDto.getJobItemName()));
            specialManager.setWorkshop(excelDto.getWorkshop());
            specialManagers.add(specialManager);
        }
        saveBatch(specialManagers);
        return null;
    }

    /**
     * 校验导入EXCEL设备表的字段
     *
     * @param rows
     * @return
     */
    private String checkExcelField(List<SpecialManagerExcelDto> rows) {
        StringBuilder builder = new StringBuilder();
        Integer i = 1;
        // 校验必填字段
        for (SpecialManagerExcelDto row : rows) {
            i++;
            String name = row.getName();

            boolean isBlank = StrUtil.isBlank(name);
            if (isBlank) {
                builder.append("第 ").append(i).append(" 行数据").append(StringPool.COLON);
            }

            if (StrUtil.isBlank(name)) {
                builder.append("名称不能为空").append(StringPool.SEMICOLON);
            }
        }
        return builder.toString();
    }

    public String getSpecialWarningCountOfNext(Integer offsetDays) {
        Date now = new Date();
        Date offsetDate = DateUtil.offset(now, DateField.DAY_OF_MONTH, offsetDays);
        Date nowBegin = DateUtil.beginOfDay(now);
        //获取从今日开始
        Integer count = this.count(new QueryWrapper<SpecialManager>().lambda()
                .le(SpecialManager::getCertificateValidity, offsetDate)
                .ge(SpecialManager::getCertificateValidity, nowBegin));
        return "" + count;
    }

    public String getSpecialWarningCountOfOverRemind() {
        Date now = new Date();
        Date nowBegin = DateUtil.beginOfDay(now);
        //获取从今日开始
        Integer count = this.count(new QueryWrapper<SpecialManager>().lambda()
                .apply("DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_SUB( certificate_validity, INTERVAL deadline_days DAY ) "));
        return "" + count;
    }

    public String getSpecialWarningCountOfOverNext() {
        Date now = new Date();
        Date nowBegin = DateUtil.beginOfDay(now);
        //获取从今日开始
        Integer count = this.count(new QueryWrapper<SpecialManager>().lambda()
                .apply("DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_FORMAT(certificate_validity,'%Y-%m-%d') "));
        return "" + count;
    }
}
