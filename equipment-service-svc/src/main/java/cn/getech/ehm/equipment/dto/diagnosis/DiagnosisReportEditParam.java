package cn.getech.ehm.equipment.dto.diagnosis;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 诊断报告编辑参数
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisReportEditParam", description = "诊断报告编辑参数")
public class DiagnosisReportEditParam extends ApiParam {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "分析图片信息集合")
    private List<AnalyzePicInfoDto> analyzePicInfoDtos;

    @ApiModelProperty(value = "分析信息")
    private String analyzeInfo;

    @ApiModelProperty(value = "分析原因")
    private String analyzeReason;

    @ApiModelProperty(value = "处理建议")
    private String handlingSuggestions;

}
