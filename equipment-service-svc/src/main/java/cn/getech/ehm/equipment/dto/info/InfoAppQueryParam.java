package cn.getech.ehm.equipment.dto.info;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备表 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InfoAppQueryParam", description = "app设备表查询参数")
public class InfoAppQueryParam extends PageParam {

    @ApiModelProperty(value = "编码/名称/设备规格/位号")
    private String keyword;

    @ApiModelProperty(value = "设备位置Id")
    private String locationId;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "父设备id")
    private String parentEquipmentId;

    @ApiModelProperty(value = "设备ids", hidden = true)
    private List<String> equipmentIds;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "是否高级搜索")
    private Boolean advancedSearch = false;

    @ApiModelProperty(value = "设备状态")
    private Integer iotStatus;

}
