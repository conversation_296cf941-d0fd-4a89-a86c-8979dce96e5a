package cn.getech.ehm.equipment.dto.iot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组态列表返回数据模型
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudioResDetailDto", description = "组态列表返回详情数据模型")
public class StudioResDetailDto {

    @ApiModelProperty(value = "组态id")
    private String studioId;

    @ApiModelProperty(value = "0-pc端1-手机")
    private Integer appType;

    @ApiModelProperty(value = "组态名称")
    private String studioName;

    @ApiModelProperty(value = "尺寸")
    private String lengthWidth;

}