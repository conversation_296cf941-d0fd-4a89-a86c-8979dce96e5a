package cn.getech.ehm.equipment.dto.bearing;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 轴承导入导出数据模型
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "BearingDbExcelDto", description = "轴承导入导出数据模型")
public class BearingDbExcelDto {
    @ApiModelProperty(value = "品牌")
    @FormExcel(name="品牌",cellType = FormExcel.ColumnType.STRING )
    private String factoryName;

    @ApiModelProperty(value = "型号")
    @FormExcel(name="型号",cellType = FormExcel.ColumnType.STRING )
    private String modelName;

    @ApiModelProperty(value = "内圈频率")
    @FormExcel(name="内圈频率",cellType = FormExcel.ColumnType.NUMERIC )
    private Double bpfi;

    @ApiModelProperty(value = "外圈频率")
    @FormExcel(name="外圈频率",cellType = FormExcel.ColumnType.NUMERIC )
    private Double bpfo;

    @ApiModelProperty(value = "内圈保持架频率")
    @FormExcel(name="内圈保持架频率",cellType = FormExcel.ColumnType.NUMERIC )
    private Double fifo;

    @ApiModelProperty(value = "外圈保持架频率")
    @FormExcel(name="外圈保持架频率",cellType = FormExcel.ColumnType.NUMERIC )
    private Double fifi;

    @ApiModelProperty(value = "滚动体频率")
    @FormExcel(name="滚动体频率",cellType = FormExcel.ColumnType.NUMERIC )
    private Double bsf;

    @ApiModelProperty(value = "滚动体个数")
    @FormExcel(name="滚动体个数",cellType = FormExcel.ColumnType.NUMERIC )
    private Integer nb;

    @ApiModelProperty(value = "接触角")
    @FormExcel(name="接触角",cellType = FormExcel.ColumnType.NUMERIC )
    private Integer contactAngle;

}
