package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 概览统计数据
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "CardAtlasCountDto", description = "概览统计数据")
public class CardAtlasCountDto {

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "位置层级编码")
    private String locationLayerCode;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "报警状态")
    private Integer warnStatus;
}