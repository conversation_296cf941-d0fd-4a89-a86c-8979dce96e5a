package cn.getech.ehm.equipment.dto.calibration;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 校准工单 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CalibrationTaskOrder查询", description = "校准工单查询参数")
public class CalibrationTaskOrderQueryParam extends PageParam {
    @ApiModelProperty(value = "校准状态,0:待校准，1:已校准")
    private Integer status;

    @ApiModelProperty(value = "校准结果1:正常4限用5禁用")
    private Integer result;

    @ApiModelProperty(value = "当天日期")
    private Date currentDate;

    @ApiModelProperty(value = "工单编号")
    private String innerCode;

    @ApiModelProperty(value = "当前设备编码")
    private String innerEquipmentCode;

    @ApiModelProperty(value = "当前设备名称")
    private String innerEquipmentName;

    @ApiModelProperty(value = "开始计划校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date innerStartPlanCalibrateDate;

    @ApiModelProperty(value = "结束计划校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date innerEndPlanCalibrateDate;

    @ApiModelProperty(value = "开始倒计时")
    private Integer innerStartCountdown;

    @ApiModelProperty(value = "结束倒计时")
    private Integer innerEndCountdown;

    @ApiModelProperty(value = "设备位置ids(逗号分隔)")
    private String innerEquipmentLocation;

    @ApiModelProperty(value = "开始实际校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date innerStartActualCalibrateDate;

    @ApiModelProperty(value = "结束实际校准日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date innerEndActualCalibrateDate;

    @ApiModelProperty(value = "校准状态,0:待校准，1:已校准 (多个逗号分开)")
    private String innerStatus;

    @ApiModelProperty(value = "校准结果1:正常4限用5禁用 (多个逗号分开)")
    private String innerResults;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "设备ids",hidden = true)
    private List<String> equipmentIds;
}
