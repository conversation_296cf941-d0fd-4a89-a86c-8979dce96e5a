package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备二维码标签配置信息
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentLabelDto", description = "设备二维码标签配置信息")
public class EquipmentLabelDto {

    @ApiModelProperty(value = "设备二维码左上图片")
    private String qrPic;

    @ApiModelProperty(value = "设备二维码顶部标签")
    private String qrLabel;

    @ApiModelProperty(value = "设备二维码底部标签")
    private String qrBottom;
}