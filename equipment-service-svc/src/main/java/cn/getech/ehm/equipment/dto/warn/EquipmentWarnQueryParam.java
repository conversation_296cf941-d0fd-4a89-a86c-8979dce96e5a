package cn.getech.ehm.equipment.dto.warn;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备告警 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentWarn查询", description = "设备告警查询参数")
public class EquipmentWarnQueryParam extends PageParam {

    @ApiModelProperty(value = "告警开始时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "告警结束时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "告警开始时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date innerBeginTime;

    @ApiModelProperty(value = "告警结束时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date innerEndTime;

    @ApiModelProperty(value = "恢复开始时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginRepairTime;

    @ApiModelProperty(value = "恢复结束时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endRepairTime;

    @ApiModelProperty(value = "设备id集合")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "是否客户平台(0否,1是)")
    private Integer isClient = StaticValue.ZERO;

    @ApiModelProperty(value = "告警级别(0未知,1预警,2报警)")
    private String level;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备名称")
    private String innerEquipmentName;

    @ApiModelProperty(value = "卡件名")
    private String frame;

    @ApiModelProperty(value = "卡槽名")
    private String fixture;

    @ApiModelProperty(value = "客户名称")
    private String innerCustomerName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "告警信息")
    private String remark;

    @ApiModelProperty(value = "告警状态(1告警中,3结束)，下拉框")
    private Integer status;

    @ApiModelProperty(value = "告警状态(1告警中,3结束)，多选")
    private String innerStatus;

    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;

    @ApiModelProperty(value = "恢复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repairTime;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

}
