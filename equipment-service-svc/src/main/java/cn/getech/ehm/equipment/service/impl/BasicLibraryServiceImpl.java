package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.BasicLibraryDto;
import cn.getech.ehm.equipment.entity.bearing.*;
import cn.getech.ehm.equipment.mapper.BasicLibraryMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 基础库服务实现类
 */
@Slf4j
@Service
public class BasicLibraryServiceImpl extends BaseServiceImpl<BasicLibraryMapper, BasicLibrary> implements IBasicLibraryService {

    @Autowired
    private BasicLibraryMapper libraryMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private IBearingFactoryService factoryService;
    @Autowired
    private IBearingModelService modelService;
    @Autowired
    private IBasicLibraryFieldService fieldService;
    @Autowired
    private IBasicLibraryDetailService detailService;

    @Override
    public List<BasicLibraryDto> getList(Integer type) {
        LambdaQueryWrapper<BasicLibrary> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BasicLibrary::getType, type);
        wrapper.orderByAsc(BasicLibrary::getSort);
        List<BasicLibraryDto> dtos = CopyDataUtil.copyList(libraryMapper.selectList(wrapper), BasicLibraryDto.class);
        return CollectionUtils.isNotEmpty(dtos) ? dtos : Lists.newArrayList();
    }

    @Override
    public Boolean edit(BasicLibraryDto dto) {
        if(check(dto.getId(), dto.getName(), null)){
            log.error("名称已存在");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        if(check(null, null, dto.getCode())){
            log.error("编码已存在");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        BasicLibrary basicLibrary = CopyDataUtil.copyObject(dto, BasicLibrary.class);
        if(StringUtils.isBlank(dto.getId())){
            basicLibrary.setSort(libraryMapper.getMaxSort() + 1);
        }
        return saveOrUpdate(basicLibrary);
    }

    private Boolean check(String id, String name, String code){
        LambdaQueryWrapper<BasicLibrary> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(StringUtils.isNotBlank(id), BasicLibrary::getId, id);
        wrapper.eq(StringUtils.isNotBlank(name), BasicLibrary::getName, name);
        wrapper.eq(StringUtils.isNotBlank(code), BasicLibrary::getCode, code);
        return libraryMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Boolean deleteById(String id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean initialization(String tenantId) {
        Map<String, String> modelIdMap = new HashMap<>();
        Map<String, String> factoryIdMap = new HashMap<>();
        Map<String, String> libraryIdMap = new HashMap<>();

        List<BearingModel> bearingModels = modelService.getDefaultModel();
        if(CollectionUtils.isNotEmpty(bearingModels)){
            for(BearingModel bearingModel : bearingModels){
                String oldId = bearingModel.getId();
                String id = UUID.randomUUID().toString().replace("-","");
                bearingModel.setId(id);
                bearingModel.setTenantId(tenantId);
                modelIdMap.put(oldId, id);
            }
            modelService.saveBatch(bearingModels);
        }

        List<BearingFactory> bearingFactories = factoryService.getDefaultFactory();
        if(CollectionUtils.isNotEmpty(bearingFactories)){
            for(BearingFactory bearingFactory : bearingFactories){
                String oldId = bearingFactory.getId();
                String id = UUID.randomUUID().toString().replace("-","");
                bearingFactory.setId(id);
                bearingFactory.setTenantId(tenantId);
                factoryIdMap.put(oldId, id);
            }
            factoryService.saveBatch(bearingFactories);
        }
        List<BasicLibrary> basicLibraries = libraryMapper.getDefaultLibrary();
        if(CollectionUtils.isNotEmpty(basicLibraries)){
            for(BasicLibrary basicLibrary : basicLibraries){
                String oldId = basicLibrary.getId();
                String id = UUID.randomUUID().toString().replace("-","");
                basicLibrary.setId(id);
                basicLibrary.setTenantId(tenantId);
                libraryIdMap.put(oldId, id);
            }
            this.saveBatch(basicLibraries);
        }
        List<BasicLibraryField> basicLibraryFields = fieldService.getDefaultFields();
        if(CollectionUtils.isNotEmpty(basicLibraryFields)){
            for(BasicLibraryField basicLibraryField : basicLibraryFields){
                basicLibraryField.setId(null);
                basicLibraryField.setTenantId(tenantId);
                basicLibraryField.setBasicLibraryId(libraryIdMap.get(basicLibraryField.getBasicLibraryId()));
            }
            fieldService.saveBatch(basicLibraryFields);
        }
        List<BasicLibraryDetail> basicLibraryDetails = detailService.getDefaultDetails();
        if(CollectionUtils.isNotEmpty(basicLibraryDetails)){
            for(BasicLibraryDetail basicLibraryDetail : basicLibraryDetails){
                basicLibraryDetail.setId(null);
                basicLibraryDetail.setTenantId(tenantId);
                basicLibraryDetail.setBasicLibraryId(libraryIdMap.get(basicLibraryDetail.getBasicLibraryId()));
                basicLibraryDetail.setFactoryId(factoryIdMap.get(basicLibraryDetail.getFactoryId()));
                basicLibraryDetail.setModelId(modelIdMap.get(basicLibraryDetail.getModelId()));
            }
            detailService.saveBatch(basicLibraryDetails);
        }
        return true;
    }

    @Override
    public List<BasicLibrary> getDefaultLibrary(){
        return libraryMapper.getDefaultLibrary();
    }
}
