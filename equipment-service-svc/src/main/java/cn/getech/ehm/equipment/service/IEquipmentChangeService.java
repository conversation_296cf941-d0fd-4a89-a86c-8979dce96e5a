package cn.getech.ehm.equipment.service;

import cn.getech.ehm.base.dto.TaskAuditParam;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeEditParam;
import cn.getech.ehm.equipment.dto.info.EquipmentInfoDto;
import cn.getech.ehm.equipment.entity.EquipmentChange;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeAddParam;
import cn.getech.ehm.equipment.dto.change.EquipmentChangeDto;
import java.util.List;

/**
 * 设备异动表 服务类
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
public interface IEquipmentChangeService extends IBaseService<EquipmentChange> {

    /**
     *
     * @param equipmentChangeAddParam
     * @return
     */
    Boolean saveBatchByParam(EquipmentChangeAddParam equipmentChangeAddParam);

    /**
     *
     * @param equipmentChangeEditParam
     * @return
     */
    Boolean updateBatchByParam(EquipmentChangeEditParam equipmentChangeEditParam);

    /**
     * 根据设备id列表获取设备备件列表
     * @param equipmentIds
     * @return
     */
    List<EquipmentInfoDto> getEquipmentInfoList(String[] equipmentIds);

    /**
     * 根据设备id列表获取异动列表
     * @param equipmentId
     * @return
     */
    List<EquipmentChangeDto> getEquipmentChangeList(String equipmentId);

    /**
     * 异动流程审核提交
     * @param taskAuditParam
     * @return
     */
    Boolean submitProcessTask(TaskAuditParam taskAuditParam);

    /**
     * 驳回以后，重新提交
     * @param taskAuditParam
     * @return
     */
    Boolean againSubmit(TaskAuditParam taskAuditParam);

    /**
     * 根据流程ID获取异动表单
     * @param processInstanceId
     * @return
     */
    EquipmentChangeDto getEquipChangeByProcessInstanceId(String processInstanceId);
}