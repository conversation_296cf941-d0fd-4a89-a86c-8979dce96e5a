package cn.getech.ehm.equipment.dto.diagnosis;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 智能诊断报告返回列表
 */
@Data
@ApiModel(value = "DiagnosisReportAutoDto", description = "智能诊断报告返回列表")
public class DiagnosisReportAutoDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否启动")
    private Boolean enable;

    @ApiModelProperty(value = "设备ids")
    private String[] equipmentIds;

    @ApiModelProperty(value = "描述")
    private String content;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}