package cn.getech.ehm.equipment.dto.calibration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 校准标准项目 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-24
 */
@Data
@ApiModel(value = "CalibrationTaskItemDto", description = "校准标准项目返回数据模型")
public class CalibrationTaskItemDto{

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 比对测试项目
     */
    @ApiModelProperty(value = "比对测试项目")
    private String title;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 对比值
     */
    @ApiModelProperty(value = "对比值")
    private String value;

    /**
     * 允许误差
     */
    @ApiModelProperty(value = "允许误差")
    private String error;

    /**
     * 校准值
     */
    @ApiModelProperty(value = "校准值")
    private String calibrationValue;

    /**
     * 自动判定结果0:不合格，1:合格
     */
    @ApiModelProperty(value = "自动判定结果0:不合格，1:合格")
    private Integer autoResult;

}