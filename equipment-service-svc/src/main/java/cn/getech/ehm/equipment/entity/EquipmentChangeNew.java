package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 新设备异动表
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_change_new")
public class EquipmentChangeNew extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备ID
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 异动类型(0-位置,1-状态)
     */
    @TableField("type")
    private Integer type;


    /**
     * 初始值
     */
    @TableField("initial_value")
    private String initialValue;

    /**
     * 目标值
     */
    @TableField("target_value")
    private String targetValue;

    /**
     * 位置异动的初始父节点id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 位置异动的目标父节点id
     */
    @TableField("target_parent_id")
    private String targetParentId;

    /**
     * 位置异动的目标类型1-位置 11主设备 12子设备
     */
    @TableField("target_parent_type")
    private Integer targetParentType;


    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 任务id
     */
    @TableField("activity_id")
    private String activityId;

    /**
     * 操作用户列表
     */
    @TableField("process_user")
    private String processUser;

    /**
     * 流程状态(0待审批 1审批通过 2审批驳回)
     */
    @TableField("status")
    private Integer status;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 申请人名称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 变动审批通过时间
     */
    @TableField("change_date")
    private Date changeDate;

    /**
     * 操作人名称
     */
    @TableField("process_user_name")
    private String processUserName;
}
