package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskItemDto;
import cn.getech.ehm.equipment.entity.CalibrationTaskItem;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 校准标准项目 服务类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ICalibrationTaskItemService extends IBaseService<CalibrationTaskItem> {
    /**
     * 新增
     * @param calibrationTaskItemDtos
     * @param calibrationId
     * @return
     */
    Boolean saveList(List<CalibrationTaskItemDto> calibrationTaskItemDtos, String calibrationId);


    /**
     * 根据校准单id获取校准项
     * @param calibrationId
     * @return
     */
    List<CalibrationTaskItemDto> getByCalibrationId(String calibrationId);

    /**
     * 删除校准项目
     * @param calibrationId
     * @return
     */
    Integer deleteByCalibrationId(String calibrationId);
}