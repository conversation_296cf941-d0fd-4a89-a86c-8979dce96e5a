package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.equipment.dto.bearing.*;
import cn.getech.ehm.equipment.service.IBearingDbService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 轴承控制器
 * <AUTHOR>
 * @since 2021-04-07
 */
@RestController
@RequestMapping("/bearing")
@Api(tags = "轴承服务接口")
public class BearingDbController {
    @Autowired
    private IBearingDbService bearingDbService;

    /**
     * 分页获取轴承列表
     */
    @ApiOperation("分页获取轴承列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<BearingDbDto>> bearingPageList(@RequestBody BearingDbQueryParam queryParam){
        return RestResponse.ok(bearingDbService.pageDto(queryParam));
    }

    @ApiOperation("根据id获取轴承")
    @GetMapping("/{id}")
    public BearingDbDto getDtoById(@PathVariable String id){
        return bearingDbService.getDtoById(id);
    }

    /**
     * 新增轴承
     */
    @ApiOperation("新增轴承")
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody BearingDbDto bearingDbDto) {
        return RestResponse.ok(bearingDbService.saveByParam(bearingDbDto));
    }

    /**
     * 修改轴承
     */
    @ApiOperation(value="修改轴承")
    @PostMapping("/edit")
    //@Permission("rule:template:update")
    public RestResponse<Boolean> update(@RequestBody BearingDbDto bearingDbDto) {
        return RestResponse.ok(bearingDbService.updateByParam(bearingDbDto));
    }

    /**
     * 根据id删除轴承
     */
    @ApiOperation(value="根据id删除轴承")
    @AuditLog(title = "轴承",desc = "轴承",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("equipment:location:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(bearingDbService.deleteById(id));
    }

    /**
     * 同步轴承mdb数据
     */
    @ApiOperation("同步轴承mdb数据")
    @GetMapping
    public RestResponse<Boolean> addDb() {
        return RestResponse.ok(bearingDbService.addDb());
    }

    /**
     * Excel导入轴承
     */
    @ApiOperation(value = "导入轴承")
    @AuditLog(title = "轴承",desc = "导入轴承",businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("equipment:import")
    public RestResponse<String> importPropJson(@RequestPart("file") MultipartFile file){
        FormExcelUtils<BearingDbExcelDto> util = new FormExcelUtils<>(BearingDbExcelDto.class);
        List<BearingDbExcelDto> rows = util.importExcel(file, null);
        return RestResponse.ok(bearingDbService.importExcel(rows));
    }

    /**
     * 导出轴承
     */
    @ApiOperation(value = "导出轴承")
    @AuditLog(title = "轴承",desc = "导出轴承",businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void exportPropJsonModel(@RequestBody BearingDbQueryParam queryParam, HttpServletResponse response){
        FormExcelUtils<BearingDbExcelDto> util = new FormExcelUtils<>(BearingDbExcelDto.class);
        util.exportExcel(bearingDbService.getExcelList(queryParam), "轴承列表", response,
                null, null, null);

    }

    /**
     * 同步geek下轴承相关数据
     */
    @ApiOperation("同步geek下轴承相关数据")
    @GetMapping("/initialization")
    public RestResponse<Boolean> initialization(@RequestParam String tenantId) {
        return RestResponse.ok(bearingDbService.initialization( tenantId));
    }
}
