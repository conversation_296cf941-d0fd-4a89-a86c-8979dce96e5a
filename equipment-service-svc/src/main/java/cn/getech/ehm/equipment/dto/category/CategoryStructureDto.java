package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备类型部件 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "CategoryStructureDto", description = "设备类型部件dto")
public class CategoryStructureDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "零部件类型id")
    private String sparePartsCategoryId;

    @ApiModelProperty(value = "零部件类型名称")
    private String sparePartsCategoryName;

    @ApiModelProperty(value = "零部件类型绑定基础库id")
    private String basicLibraryId;

    @ApiModelProperty(value = "基础库")
    private String basicLibraryName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

    @ApiModelProperty(value = "子零件")
    private List<CategoryStructureDto> children;

    @ApiModelProperty(value = "级别(1,2)")
    private Integer level = 2;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty("备件id")
    private String partId;

    @ApiModelProperty("备件名")
    private String partName;

    @ApiModelProperty("备件code")
    private String partCode;

    @ApiModelProperty("备件剩余库存")
    private BigDecimal partCurrentStock;

}