package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.equipment.dto.runningparam.*;
import cn.getech.ehm.equipment.enmu.StructureParamType;
import cn.getech.ehm.equipment.enmu.StructureSourceType;
import cn.getech.ehm.equipment.enums.FeatureParameterType;
import cn.getech.ehm.equipment.service.IRunningParameterService;
import cn.getech.ehm.equipment.service.ISparePartsCategoryService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 零部件类型控制器
 * <AUTHOR>
 * @since 2021-04-07
 */
@RestController
@RequestMapping("/spearPartsCategory")
@Api(tags = "结构类型服务接口")
public class SparePartsCategoryController {
    @Autowired
    private ISparePartsCategoryService sparePartsCategoryService;
    @Autowired
    private IRunningParameterService runningParameterService;

    @ApiOperation("分页获取零部件类型列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<SparePartsCategoryDto>> pageList(@RequestBody SparePartsCategoryQueryParam queryParam){
        return RestResponse.ok(sparePartsCategoryService.pageDto(queryParam));
    }

    @ApiOperation("获取零部件类型列表")
    @PostMapping("/list")
    public RestResponse<List<SparePartsCategoryDto>> getList(@RequestBody SparePartsCategoryQueryParam queryParam){
        return RestResponse.ok(sparePartsCategoryService.getList(queryParam));
    }

    @ApiOperation("新增零部件类型")
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody SparePartsCategoryDto dto) {
        return RestResponse.ok(sparePartsCategoryService.saveByParam(dto));
    }

    @ApiOperation(value="修改零部件类型")
    @PostMapping("/edit")
    public RestResponse<Boolean> update(@RequestBody SparePartsCategoryDto dto) {
        return RestResponse.ok(sparePartsCategoryService.updateByParam(dto));
    }

    @ApiOperation(value="复制零部件类型")
    @GetMapping("/copy")
    public RestResponse<Boolean> copy(@RequestParam String id, @RequestParam String name) {
        return RestResponse.ok(sparePartsCategoryService.copy(id, name));
    }

    @ApiOperation(value="根据id删除零部件类型")
    @AuditLog(title = "零部件类型",desc = "零部件类型",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(sparePartsCategoryService.deleteById(id));
    }

    @ApiOperation("获取参数分类枚举")
    @GetMapping("/getStructureParamType")
    public RestResponse<List<EnumListDto>> getStructureParamType(){
        return RestResponse.ok(StructureParamType.getList());
    }

    @ApiOperation("获取参数数据来源枚举")
    @GetMapping("/getStructureSourceType")
    public RestResponse<List<EnumListDto>> getStructureSourceType(){
        return RestResponse.ok(StructureSourceType.getList());
    }

    @ApiOperation("获取特征参数枚举")
    @GetMapping("/getFeatureParameter")
    public RestResponse<List<EnumListDto>> getFeatureParameter(){
        return RestResponse.ok(FeatureParameterType.getList());
    }

    @ApiOperation("获取零部件运行参数")
    @PostMapping("/getRunningParameterList")
    public RestResponse<List<RunningParameterListDto>> getRunningParameterList(@RequestBody @Validated RunningParameterQueryParam queryParam){
        return RestResponse.ok(runningParameterService.getList(queryParam));
    }

    @ApiOperation("新增零部件运行参数")
    @PostMapping("/addRunningParameter")
    public RestResponse<Boolean> addRunningParameter(@RequestBody RunningParameterDto dto) {
        return RestResponse.ok(runningParameterService.saveByParam(dto));
    }

    @ApiOperation(value="修改零部件运行参数")
    @PostMapping("/editRunningParameter")
    public RestResponse<Boolean> editRunningParameter(@RequestBody RunningParameterDto dto) {
        return RestResponse.ok(runningParameterService.updateByParam(dto));
    }

    @ApiOperation(value="根据id删除零部件运行参数")
    @AuditLog(title = "零部件运行参数",desc = "零部件运行参数",businessType = BusinessType.DELETE)
    @DeleteMapping("/delRunningParameter/{id}")
    public RestResponse<Boolean> delRunningParameter(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(runningParameterService.deleteById(id));
    }

    @ApiOperation(value="根据id获取零部件运行参数")
    @AuditLog(title = "零部件运行参数",desc = "零部件运行参数",businessType = BusinessType.QUERY)
    @GetMapping("/getRunningParameter/{id}")
    public RestResponse<RunningParameterDto> getRunningParameter(@PathVariable("id") @NotBlank String id) {
        return RestResponse.ok(runningParameterService.getDtoById(id));
    }
}
