package cn.getech.ehm.equipment.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 概览图卡片返回数据模型
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "OverviewModelCardDto", description = "概览图卡片返回数据模型")
public class OverviewModelCardDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "标识数字")
    private Integer num;

    @ApiModelProperty(value = "类型(1位置2类型)")
    private Integer type;

    @ApiModelProperty(value = "说明")
    private String remark;
}