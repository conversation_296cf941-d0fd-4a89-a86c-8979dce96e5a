package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备报警趋势
 * <AUTHOR>
 * @date 2022-11-09
 */
@Data
@ApiModel(value = "EquipmentWarnTrendDto", description = "设备报警趋势")
public class EquipmentWarnTrendDto {

    @ApiModelProperty(value = "设备位置")
    private String location;

    @ApiModelProperty(value = "设备趋势Y轴")
    List<EquipmentWarnTrendCountDto> yWarnTrend;
}