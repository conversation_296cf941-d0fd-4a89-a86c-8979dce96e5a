package cn.getech.ehm.equipment.dto.runningparam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 零部件运行参数数据模型
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@ApiModel(value = "RunningParameterDto", description = "零部件运行参数数据模型")
public class RunningParameterDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "零部件类型id")
    private String sparePartsId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码(特征参数/基础库)")
    private String code;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "参数分类值")
    private Integer paramType;

    @ApiModelProperty(value = "数据来源(1录入2计算)")
    private Integer sourceType;

    @ApiModelProperty(value = "是否生成参数表(0否1是)")
    private Integer createParam;

    @ApiModelProperty(value = "变量")
    private List<ParameterVariableDto> variables;

    @ApiModelProperty(value = "计算脚本")
    private String expression;

}
