package cn.getech.ehm.equipment.entity.bearing;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 轴承表
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bearing_db")
public class BearingDb extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 生产厂家id
     */
    @TableField("factory_id")
    private String factoryId;

    /**
     * 型号id
     */
    @TableField("model_id")
    private String modelId;

    /**
     * 滚动体个数
     */
    @TableField("nb")
    private Integer nb;

    /**
     * 外圈故障频率
     */
    @TableField("bpfo")
    private Double bpfo;

    /**
     * 内圈故障频率
     */
    @TableField("bpfi")
    private Double bpfi;

    /**
     * 外圈保持架故障频率
     */
    @TableField("fifi")
    private Double fifi;

    /**
     * 内圈保持架故障频率
     */
    @TableField("fifo")
    private Double fifo;

    /**
     * 滚动体故障频率
     */
    @TableField("bsf")
    private Double bsf;

    /**
     * 接触角
     */
    @TableField("contact_angle")
    private Integer contactAngle;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;
}
