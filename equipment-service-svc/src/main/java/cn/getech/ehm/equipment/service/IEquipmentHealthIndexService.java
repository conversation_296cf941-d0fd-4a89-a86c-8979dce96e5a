package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.entity.EquipmentHealthIndex;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 设备健康指数
 */
public interface IEquipmentHealthIndexService extends IBaseService<EquipmentHealthIndex> {

    /**
     * 获取健康指数趋势
     * @param equipmentId
     * @return
     */
    List<Double> equipmentHealthTrend(String equipmentId);

    /**
     * 更新值
     * @param value
     * @param equipmentId
     * @param markTime
     * @return
     */
    Boolean updateHealthIndex(Double value, String equipmentId, Date markTime);
}