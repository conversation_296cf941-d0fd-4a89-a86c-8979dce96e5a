package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.kafka.common.metrics.stats.Total;

/**
 * 时间统计
 * <AUTHOR>
 * @date 2022-11-10
 */
@Data
@ApiModel(value = "TimeStatisticsDto", description = "时间统计")
public class TimeStatisticsDto {

    @ApiModelProperty(value = "系统累计运行时间")
    private Integer goneLive;

    @ApiModelProperty(value = "无故障运行时间")
    private Integer nonFailureOperationDays;

    @ApiModelProperty(value = "停机时间")
    private Integer downDays;

    @ApiModelProperty(value = "设备总故障率")
    private float totalFailureRate;

    @ApiModelProperty(value = "设备总使用率")
    private float totalUsage;


}