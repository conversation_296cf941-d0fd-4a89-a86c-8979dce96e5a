package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.DiagnosisCaseAddParam;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.service.IDiagnosisCaseService;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import javax.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.Arrays;

/**
 * <p>
 * 诊断案例控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@RestController
@RequestMapping("/diagnosisCase")
@Api(tags = "诊断案例服务接口")
public class DiagnosisCaseController {

    @Autowired
    private IDiagnosisCaseService diagnosisCaseService;

    /**
     * 分页获取诊断案例列表
     */
    @ApiOperation("分页获取诊断案例列表")
    @GetMapping("/pageList")
    //@Permission("diagnosis:case:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<PageResult<DiagnosisCaseListDto>> pageList(@Valid DiagnosisCaseQueryParam diagnosisCaseQueryParam){
        return RestResponse.ok(diagnosisCaseService.pageDto(diagnosisCaseQueryParam));
    }

    /**
     * 新增诊断案例
     */
    @ApiOperation("新增诊断案例")
    @AuditLog(title = "诊断案例",desc = "新增诊断案例",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("diagnosis:case:update")
    public RestResponse<Boolean> add(@RequestBody @Valid DiagnosisCaseAddParam diagnosisCaseAddParam) {
        return RestResponse.ok(diagnosisCaseService.saveByParam(diagnosisCaseAddParam));
    }

    /**
     * 修改诊断案例
     */
    @ApiOperation(value="修改诊断案例")
    @AuditLog(title = "诊断案例",desc = "修改诊断案例",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("diagnosis:case:update")
    public RestResponse<Boolean> update(@RequestBody @Valid DiagnosisCaseEditParam diagnosisCaseEditParam) {
        return RestResponse.ok(diagnosisCaseService.updateByParam(diagnosisCaseEditParam));
    }

    /**
     * 根据id删除诊断案例
     */
    @ApiOperation(value="根据id删除诊断案例")
    @AuditLog(title = "诊断案例",desc = "诊断案例",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("diagnosis:case:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(diagnosisCaseService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 根据id获取诊断案例
     */
    @ApiOperation(value = "根据id获取诊断案例")
    @GetMapping(value = "/{id}")
    //@Permission("diagnosis:case:list")
    public RestResponse<DiagnosisCaseDto> get(@PathVariable("id")  String id) {
        return RestResponse.ok(diagnosisCaseService.getDtoById(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "/exportWord")
    public void exportWord(@RequestParam("id") String id, @RequestParam("url") String url, HttpServletResponse response) {
        File file = diagnosisCaseService.exportWord(id, url);
        WordDownloadUtil.download(response, file, "");
        if (file.exists()) {
            file.delete();
        }
    }
}
