package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.overview.OverviewModelDto;
import cn.getech.ehm.equipment.entity.OverviewModel;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 概览图 服务类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IOverviewModelService extends IBaseService<OverviewModel> {

        /**
         * 更新概览模板
         * @return
         */
        Boolean editOverviewModel(OverviewModelDto modelDto);

        /**
         * 查询概览模板
         * @return
         */
        OverviewModelDto getModelDto(String relationKey, Integer type);

        /**
         * 获取位置类型/设备类型对应的概览模板状态
         * @param relationKey
         * @param type
         * @return
         */
        Map<String, Boolean> getModelStatus(List<String> relationKeys, Integer type);
}