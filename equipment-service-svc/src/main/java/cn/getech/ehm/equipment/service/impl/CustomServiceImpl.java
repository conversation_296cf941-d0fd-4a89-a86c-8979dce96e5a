package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.custom.CustomRequest;
import cn.getech.ehm.equipment.dto.custom.CustomResponse;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;

@Service
@Slf4j
public class CustomServiceImpl {
    @Autowired
    @Lazy
    private IEquipmentInfoService equipmentInfoService;

    @Autowired
    EquipmentClient equipmentClient;

    public List<String> getEquipmentIdByLocation(List<String> locationIds) {
        List<String> equipmentIds = com.google.common.collect.Lists.newArrayList();
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(locationIds);
        RestResponse<List<String>> equipmentIdsByEquipmentInfo = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (!equipmentIdsByEquipmentInfo.isOk() || CollectionUtils.isEmpty(equipmentIdsByEquipmentInfo.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到相关设备"));
        }
        equipmentIds = equipmentIdsByEquipmentInfo.getData();
        return equipmentIds;
    }

    public CustomResponse healthIndex(CustomRequest request) {
        if (CollectionUtils.isNotEmpty(request.getLocationIds())) {
            EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
            param.setLocationIds(request.getLocationIds());
            List<String> equipmentIdByLocation = equipmentInfoService.getEquipmentIdsByParam(param);
            if (CollectionUtils.isNotEmpty(equipmentIdByLocation)) {
                request.setEquipmentIds(equipmentIdByLocation);
            } else {
                return CustomResponse.builder().valueTitle("健康指数").valueContent("0").build();
            }
        }
        List<EquipmentInfo> list = equipmentInfoService.list(new QueryWrapper<EquipmentInfo>().lambda().in(EquipmentInfo::getId, request.getEquipmentIds()));
        if (CollectionUtils.isEmpty(list)) {
            return CustomResponse.builder().valueTitle("健康指数").valueContent("0").build();
        }
        BigDecimal reduce = list.stream().map(item -> new BigDecimal(item.getHealthIndex())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal divide = reduce.divide(new BigDecimal(list.size()), 4, RoundingMode.HALF_UP);
        CustomResponse response = CustomResponse.builder().valueTitle("健康指数").valueContent(divide.toString()).build();
        return response;
    }
}
