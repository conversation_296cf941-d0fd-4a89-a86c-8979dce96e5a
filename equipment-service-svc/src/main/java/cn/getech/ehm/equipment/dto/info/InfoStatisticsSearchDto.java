package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 设备统计查询
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "InfoStatisticsSearchDto", description = "设备统计查询")
public class InfoStatisticsSearchDto {

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;
}
