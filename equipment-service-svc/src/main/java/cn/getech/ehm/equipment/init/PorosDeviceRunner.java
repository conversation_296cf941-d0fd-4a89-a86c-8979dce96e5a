package cn.getech.ehm.equipment.init;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.service.IEquipmentCategoryService;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.utils.PorosDeviceUtils;
import cn.getech.poros.device.client.DeviceInfoClient;
import cn.getech.poros.device.client.DeviceLocationClient;
import cn.getech.poros.device.client.DeviceTypeClient;
import cn.getech.poros.device.dto.infoclient.InfoDataSyncDto;
import cn.getech.poros.device.dto.locationclient.LocationDataSyncDto;
import cn.getech.poros.device.dto.typeclient.TypeDataSyncDto;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-01 17:29:10
 **/
@Slf4j
@Component
public class PorosDeviceRunner implements CommandLineRunner {
    @Value("${poros.device.sdk.enabled: false}")
    private Boolean porosDeviceEnabled;

    @Autowired
    private DeviceLocationClient deviceLocationClient;

    @Autowired
    private DeviceTypeClient deviceTypeClient;

    @Autowired
    private DeviceInfoClient deviceInfoClient;

    @Autowired
    private IEquipmentLocationService iEquipmentLocationService;

    @Autowired
    private IEquipmentCategoryService iEquipmentCategoryService;

    @Autowired
    private IEquipmentInfoService iEquipmentInfoService;

    @Autowired
    private ExecutorService executorService;//有可能未加载，后续测试下

    @Override
    public void run(String... args) {
        if (porosDeviceEnabled) {
            RestResponse<List<LocationDataSyncDto>> locationRestResponse = deviceLocationClient.getByVersionIncrementNoTenant(0L, null, null);
            if (locationRestResponse.isOk()) {
                List<LocationDataSyncDto> locationDataSyncDtos = locationRestResponse.getData();
                if (CollectionUtils.isNotEmpty(locationDataSyncDtos)) {
                    executorService.execute(() -> {
                        List<String> tenantIds = locationDataSyncDtos.stream().map(LocationDataSyncDto::getTenantId).distinct().collect(Collectors.toList());
                        Map<String, String> rootMap = iEquipmentLocationService.getRootIdMap(tenantIds);
                        List<EquipmentLocation> equipmentLocations = new ArrayList<>();
                        for (LocationDataSyncDto locationDataSyncDto : locationDataSyncDtos) {
                            if (locationDataSyncDto.getDeleted()) {
                                //删除的跳过
                                continue;
                            }
                            String rootId = rootMap.get(locationDataSyncDto.getTenantId());
                            //租户未查询到根节点，需要取租户下新增
                            EquipmentLocation equipmentLocation = PorosDeviceUtils.locationDataConvertor(locationDataSyncDto, StringUtils.isNotBlank(rootId) ? rootId : "porosSynchroRootId");
                            equipmentLocations.add(equipmentLocation);
                        }
                        Map<String, List<EquipmentLocation>> tenantMap = equipmentLocations.stream().collect(Collectors.groupingBy(EquipmentLocation::getTenantId));
                        for (Map.Entry<String, List<EquipmentLocation>> entity : tenantMap.entrySet()) {
                            List<EquipmentLocation> list = entity.getValue();
                            UserBaseInfo userBaseInfo = new UserBaseInfo();
                            userBaseInfo.setUid(list.get(0).getCreateBy());
                            userBaseInfo.setTenantId(entity.getKey());
                            UserContextHolder.switchContext(userBaseInfo);
                            iEquipmentLocationService.saveOrUpdateBatch(list);
                        }
                        iEquipmentLocationService.clearCache();
                    });
                }
            } else {
                log.error("获取设备位置数据失败{}", locationRestResponse.getMsg());
            }
            RestResponse<List<TypeDataSyncDto>> typeRestResponse = deviceTypeClient.getByVersionIncrementNoTenant(0L, null, null);
            if (typeRestResponse.isOk()) {
                List<TypeDataSyncDto> typeDataSyncDtos = typeRestResponse.getData();
                if (CollectionUtils.isNotEmpty(typeDataSyncDtos)) {
                    executorService.execute(() -> {
                        List<EquipmentCategory> equipmentCategories = new ArrayList<>();
                        for (TypeDataSyncDto typeDataSyncDto : typeDataSyncDtos) {
                            if (typeDataSyncDto.getDeleted()) {
                                continue;
                            }
                            EquipmentCategory equipmentCategory = PorosDeviceUtils.categoryDataConvertor(typeDataSyncDto);
                            equipmentCategories.add(equipmentCategory);
                        }
                        Map<String, List<EquipmentCategory>> tenantMap = equipmentCategories.stream().collect(Collectors.groupingBy(EquipmentCategory::getTenantId));
                        for (Map.Entry<String, List<EquipmentCategory>> entity : tenantMap.entrySet()) {
                            List<EquipmentCategory> list = entity.getValue();
                            UserBaseInfo userBaseInfo = new UserBaseInfo();
                            userBaseInfo.setUid(list.get(0).getCreateBy());
                            userBaseInfo.setTenantId(entity.getKey());
                            UserContextHolder.switchContext(userBaseInfo);
                            iEquipmentCategoryService.saveOrUpdateBatch(list);
                        }
                    });
                }
            } else {
                log.error("获取设备类型数据失败{}", typeRestResponse.getMsg());
            }
            RestResponse<List<InfoDataSyncDto>> infoRestResponse = deviceInfoClient.getSyncByVersionIncrementNoTenant(0L, PorosDeviceUtils.SYSTEM_LABEL, null, null);
            if (infoRestResponse.isOk()) {
                List<InfoDataSyncDto> infoDataSyncDtos = infoRestResponse.getData();
                if (CollectionUtils.isNotEmpty(infoDataSyncDtos)) {
                    executorService.execute(() -> {
                        List<EquipmentInfo> equipmentInfos = new ArrayList<>();
                        for (InfoDataSyncDto infoDataSyncDto : infoDataSyncDtos) {
                            if (infoDataSyncDto.getDeleted()) {
                                continue;
                            }
                            EquipmentInfo equipmentInfo = PorosDeviceUtils.infoDataConvertor(infoDataSyncDto);
                            equipmentInfos.add(equipmentInfo);
                        }
                        Map<String, List<EquipmentInfo>> tenantMap = equipmentInfos.stream().collect(Collectors.groupingBy(EquipmentInfo::getTenantId));
                        for (Map.Entry<String, List<EquipmentInfo>> entity : tenantMap.entrySet()) {
                            List<EquipmentInfo> list = entity.getValue();
                            UserBaseInfo userBaseInfo = new UserBaseInfo();
                            userBaseInfo.setUid(list.get(0).getCreateBy());
                            userBaseInfo.setTenantId(entity.getKey());
                            UserContextHolder.switchContext(userBaseInfo);
                            iEquipmentInfoService.saveOrUpdateBatch(list);
                        }
                        iEquipmentInfoService.clearCache();
                    });
                }
            } else {
                log.error("获取设备信息数据失败{}", infoRestResponse.getMsg());
            }
        }
    }
}
