package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.dto.BuildInfoSearchDto;
import cn.getech.ehm.equipment.dto.info.*;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialListDto;
import cn.getech.ehm.equipment.dto.special.EquipmentSpecialQueryParam;
import cn.getech.ehm.equipment.entity.EquipmentInfoSpecial;
import cn.getech.ehm.equipment.mapper.EquipmentInfoSpecialMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.PartDetailDto;
import cn.getech.ehm.part.dto.PartSearchDto;
import cn.getech.ehm.part.dto.PartSupplierDto;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecOrgClient;
import cn.getech.poros.permission.dto.PorosSecOrgDto;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 特种设备服务实现类
 * 目前去除部件级特种设备(旧版本部件关联备件信息),只预留设备级特种设备
 * <AUTHOR>
 * @since 2020-08-27
 */
@Slf4j
@Service
public class EquipmentInfoSpecialServiceImpl extends BaseServiceImpl<EquipmentInfoSpecialMapper, EquipmentInfoSpecial> implements IEquipmentInfoSpecialService {
    @Autowired
    private EquipmentInfoSpecialMapper specialMapper;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentStructureService structureService;
    @Autowired
    private IEquipmentCategoryService equipmentCategoryService;
    @Autowired
    private PartClient partClient;
    @Autowired
    private PorosSecOrgClient porosSecOrgClient;

    @Autowired
    private IEquipmentLocationService equipmentLocationService;

    @Override
    public PageResult<EquipmentSpecialListDto> pageDto(EquipmentSpecialQueryParam queryParam) {
        Page<EquipmentSpecialQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), EquipmentInfoSpecial.class, "special"));
        if (null != queryParam.getType() && queryParam.getType() == StaticValue.TWO && StringUtils.isNotBlank(queryParam.getCategoryId())) {
            RestResponse<List<String>> restResponse = partClient.getPartIdsByCategoryId(queryParam.getCategoryId());
            if (!restResponse.isOk()) {
                log.error("远程调用part-service出错");

            } else {
                List<String> partIds = restResponse.getData();
                if (CollectionUtils.isEmpty(partIds)) {
                    return new PageResult<>();
                }
                queryParam.setPartIds(partIds);
            }
        }
        BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
        if(buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())) {
            return new PageResult<>();
        }else{
            queryParam.setEquipmentIds(buildInfoSearchDto.getEquipmentIds());
        }

        IPage<EquipmentSpecialListDto> result = specialMapper.getPageList(page, queryParam);
        List<EquipmentSpecialListDto> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            //设备特种设备
            String[] equipmentIds = records.stream().filter(dto -> StringUtils.isBlank(dto.getStructureId())).map(EquipmentSpecialListDto::getEquipmentId).distinct().toArray(String[]::new);
            //部件特种设备
            //String[] structureIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getStructureId())).map(EquipmentSpecialListDto::getStructureId).distinct().toArray(String[]::new);
            Map<String, SpecialEquipmentDto> equipmentMap = equipmentInfoService.getSpecialListByIds(equipmentIds);
            //Map<String, SpecialStructureDto> structureMap = structureService.getSpecialListByIds(structureIds);

            Map<String, String> equipmentNameMap = new HashMap<>();
            if (null != equipmentIds && equipmentIds.length > 0) {
                //设备特种设备集合，不包含自身
                equipmentNameMap = equipmentInfoService.getDepthName(Arrays.asList(equipmentIds), true);
            }
           /* Map<String, String> structureEquipmentNameMap = new HashMap<>();
            List<String> structureEquipmentIds = records.stream().filter(dto -> StringUtils.isNotBlank(dto.getStructureId())).map(EquipmentSpecialListDto::getEquipmentId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(structureEquipmentIds)) {
                structureEquipmentNameMap = equipmentInfoService.getDepthName(structureEquipmentIds, 3, false);
            }*/
            for (EquipmentSpecialListDto dto : records) {
                if (StringUtils.isNotBlank(dto.getStructureId())) {
                    //部件特种设备
                    /*SpecialStructureDto specialStructureDto = structureMap.get(dto.getStructureId());
                    if (null != specialStructureDto) {
                        dto.setCode(specialStructureDto.getPartCode());
                        String name = StringUtils.isNotBlank(specialStructureDto.getLocation()) ? specialStructureDto.getPartName() + StringPool.DASH + specialStructureDto.getLocation() : specialStructureDto.getPartName();
                        dto.setName(name);
                        dto.setCategoryId(specialStructureDto.getPartCategoryId());
                        dto.setCategoryName(specialStructureDto.getPartCategoryName());
                        dto.setStructurePartId(specialStructureDto.getPartId());
                        dto.setType(StaticValue.TWO);

                        if (StringUtils.isNotBlank(specialStructureDto.getParentId()) && StringUtils.isNotBlank(specialStructureDto.getParentName())) {
                            //二级部件，parentName取父部件名称，补充上级设备名称
                            dto.setParentId(specialStructureDto.getParentId());
                            dto.setParentName(buildParentName(specialStructureDto.getParentName(), structureEquipmentNameMap.get(dto.getEquipmentId())));
                        } else {
                            //父节点为null，则为一级部件，直接补上级设备名
                            dto.setParentId(dto.getEquipmentId());
                            dto.setParentName(structureEquipmentNameMap.get(dto.getEquipmentId()));
                        }
                    }*/
            } else {
                //设备特种设备
                buildSpecialInfo(equipmentMap, equipmentNameMap, dto);
            }
            //处理过期类型
            Date now = new Date();
            for (EquipmentSpecialListDto temp : records) {
                temp.setIsOverTime(false);
                if (temp.getNextInspectionDate() != null) {
                    if (temp.getNextInspectionDate().before(now)) {
                        temp.setIsOverTime(true);
                    }
                }
            }
        }
    }

        return Optional.ofNullable(PageResult.<EquipmentSpecialListDto>builder()
                        .records(records)
                        .total(result.getTotal())
            .build())
            .orElse(new PageResult<>());
    }

    private String buildParentName(String parentStructureName, String parentEquipmentName) {
        String structureName = null;
        //二级部件，parentName取父部件名称，补充上级设备名称
        List<String> parentNames = new ArrayList<>();
        parentNames.add(parentStructureName);
        if (StringUtils.isNotBlank(parentEquipmentName)) {

            String[] parentNameStrs = parentEquipmentName.split(StringPool.SLASH);
            for (int i = parentNameStrs.length - 1; i >= (parentNameStrs.length >= 3 ? 1 : 0); i--) {
                //补充设备层级，最多截取两个层级
                parentNames.add(parentNameStrs[i]);
            }
            Collections.reverse(parentNames);
            structureName = StringUtils.join(parentNames.toArray(), StringPool.SLASH);
        }
        return structureName;
    }

    @Override
    public List<SpecialInfoCategoryDto> specialCategoryList() {
        List<SpecialInfoCategoryDto> list = new ArrayList<>();
        List<String> equipmentCategoryIds = specialMapper.getEquipmentCategoryIds();
        if (CollectionUtils.isNotEmpty(equipmentCategoryIds)) {
            Map<String, String> equipmentCategoryNameMap = equipmentCategoryService.getDepthName(equipmentCategoryIds);
            for (String equipmentCategoryId : equipmentCategoryIds) {
                String equipmentCategoryName = equipmentCategoryNameMap.get(equipmentCategoryId);
                if (StringUtils.isNotBlank(equipmentCategoryName)) {
                    SpecialInfoCategoryDto specialInfoCategoryDto = new SpecialInfoCategoryDto();
                    specialInfoCategoryDto.setCategoryId(equipmentCategoryId);
                    specialInfoCategoryDto.setCategoryName(equipmentCategoryName);
                    specialInfoCategoryDto.setType(StaticValue.ONE);
                    list.add(specialInfoCategoryDto);
                }
            }
        }
        /*List<String> structurePartIds = specialMapper.getStructurePartIds();
        //用于剔除重复的备件类型id
        List<String> partCategoryIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(structurePartIds)) {
            PartSearchDto searchDto = new PartSearchDto();
            searchDto.setPartIds(structurePartIds);
            searchDto.setObtainProp(false);
            RestResponse<Map<String, PartDetailDto>> listRestResponse = partClient.getPartMapByIds(searchDto);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用part-service出错");
            } else {
                Map<String, PartDetailDto> partMap = listRestResponse.getData();
                for (String structurePartId : structurePartIds) {
                    PartDetailDto partDetailDto = partMap.get(structurePartId);
                    if (null != partDetailDto && !partCategoryIds.contains(partDetailDto.getPartCategoryId()) && StringUtils.isNotBlank(partDetailDto.getPartAllCategoryName())) {
                        partCategoryIds.add(partDetailDto.getPartCategoryId());
                        SpecialInfoCategoryDto specialInfoCategoryDto = new SpecialInfoCategoryDto();
                        specialInfoCategoryDto.setCategoryId(partDetailDto.getPartCategoryId());
                        specialInfoCategoryDto.setCategoryName(partDetailDto.getPartAllCategoryName());
                        specialInfoCategoryDto.setType(StaticValue.TWO);
                        list.add(specialInfoCategoryDto);
                    }
                }
            }
        }*/

        return list;
    }

    @Override
    public String saveOrUpdateByParam(EquipmentSpecialDto dto, String equipmentId) {
        if (null == dto) {
            return null;
        }
        EquipmentInfoSpecial equipmentInfoSpecial = CopyDataUtil.copyObject(dto, EquipmentInfoSpecial.class);
        equipmentInfoSpecial.setEquipmentId(equipmentId);
        if (StringUtils.isNotBlank(equipmentInfoSpecial.getUseOrg())) {
            equipmentInfoSpecial.setUseOrg(equipmentInfoSpecial.getUseOrg().split(StringPool.SLASH)[0]);
        }
        saveOrUpdate(equipmentInfoSpecial);
        return equipmentInfoSpecial.getId();
    }

    @Override
    public EquipmentSpecialDto getDtoByInfoId(String equipmentId) {
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentInfoSpecial::getEquipmentId, equipmentId);
        wrapper.isNull(EquipmentInfoSpecial::getStructureId);

        return CopyDataUtil.copyObject(specialMapper.selectOne(wrapper), EquipmentSpecialDto.class);
    }

    @Override
    public Boolean deleteByInfoIds(String[] equipmentIds) {
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfoSpecial::getEquipmentId, equipmentIds);
        return specialMapper.delete(wrapper) > StaticValue.ZERO;
    }

    @Override
    public Boolean deleteByStructureIds(String[] structureIds) {
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentInfoSpecial::getStructureId, structureIds);
        return specialMapper.delete(wrapper) > StaticValue.ZERO;
    }

    @Override
    public Map<String, EquipmentSpecialDto> getListByStructureIds(List<String> structureIds) {
        if (CollectionUtils.isNotEmpty(structureIds)) {
            LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentInfoSpecial::getStructureId, structureIds);
            List<EquipmentInfoSpecial> equipmentInfoSpecials = specialMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(equipmentInfoSpecials)) {
                List<String> useOrgCodes = equipmentInfoSpecials.stream().filter(dto -> StringUtils.isNotBlank(dto.getUseOrg())).map(EquipmentInfoSpecial::getUseOrg).distinct().collect(Collectors.toList());
                Map<String, String> orgMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(useOrgCodes)) {
                    RestResponse<List<PorosSecOrgDto>> orgRes = porosSecOrgClient.getList(StringUtils.join(useOrgCodes.toArray(), StringPool.COMMA), "0");
                    if (orgRes.isOk()) {
                        orgMap = orgRes.getData().stream().collect(Collectors.toMap(PorosSecOrgDto::getCode, PorosSecOrgDto::getName, (v1, v2) -> v1));
                    } else {
                        log.error("获取组织失败");
                    }
                }
                List<EquipmentSpecialDto> equipmentSpecialDtos = new ArrayList<>(equipmentInfoSpecials.size());
                for (EquipmentInfoSpecial equipmentInfoSpecial : equipmentInfoSpecials) {
                    EquipmentSpecialDto dto = CopyDataUtil.copyObject(equipmentInfoSpecial, EquipmentSpecialDto.class);
                    if (StringUtils.isNotBlank(dto.getUseOrg())) {
                        String name = orgMap.get(dto.getUseOrg());
                        dto.setUseOrg(StringUtils.isNotBlank(name) ? dto.getUseOrg() + StringPool.SLASH + name : dto.getUseOrg());
                    }
                    equipmentSpecialDtos.add(dto);
                }

                return equipmentSpecialDtos.stream().collect(Collectors.toMap(EquipmentSpecialDto::getStructureId, v -> v, (v1, v2) -> v1));
            }
        }
        return new HashMap<>();
    }

    @Override
    public List<SpecialStructureExcelDto> getSpecialStructureList(EquipmentSpecialQueryParam queryParam) {
        List<SpecialStructureExcelDto> excelDtos = new ArrayList<>();
        RestResponse<List<String>> restResponse = partClient.getPartIdsByCategoryId(queryParam.getCategoryId());
        if (!restResponse.isOk()) {
            log.error("远程调用part-service出错");
        } else {
            List<String> partIds = restResponse.getData();
            if (CollectionUtils.isEmpty(partIds)) {
                return new ArrayList<>();
            }
            queryParam.setPartIds(partIds);
        }

        List<EquipmentSpecialListDto> result = specialMapper.getStructureList(queryParam);
        if (CollectionUtils.isNotEmpty(result)) {
            Map<String, String> structureEquipmentNameMap = new HashMap<>();
            List<String> structureEquipmentIds = result.stream().map(EquipmentSpecialListDto::getEquipmentId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(structureEquipmentIds)) {
                structureEquipmentNameMap = equipmentInfoService.getDepthName(structureEquipmentIds,false);
            }
            List<String> partIds = result.stream().map(EquipmentSpecialListDto::getStructurePartId).distinct().collect(Collectors.toList());
            List<String> parentPartIds = result.stream().filter(dto -> StringUtils.isNotBlank(dto.getStructureParentPartId())).map(EquipmentSpecialListDto::getStructureParentPartId).distinct().collect(Collectors.toList());
            partIds.addAll(parentPartIds);
            List<String> allPartIds = partIds.stream().distinct().collect(Collectors.toList());
            PartSearchDto searchDto = new PartSearchDto();
            searchDto.setPartIds(allPartIds);
            searchDto.setCategoryId(queryParam.getCategoryId());
            searchDto.setObtainProp(true);
            RestResponse<Map<String, PartDetailDto>> listRestResponse = partClient.getPartMapByIds(searchDto);
            Map<String, PartDetailDto> partMap = new HashMap<>();
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用part-service出错");
            } else {
                partMap = listRestResponse.getData();
            }
            for (EquipmentSpecialListDto dto : result) {
                PartDetailDto partDetailDto = partMap.get(dto.getStructurePartId());
                if (null != partDetailDto) {
                    SpecialStructureExcelDto structureExcelDto = CopyDataUtil.copyObject(dto, SpecialStructureExcelDto.class);
                    structureExcelDto.setCode(partDetailDto.getPartCode());
                    String name = StringUtils.isNotBlank(dto.getStructureLocation()) ? partDetailDto.getPartName() + StringPool.DASH + dto.getStructureLocation() : partDetailDto.getPartName();
                    structureExcelDto.setName(name);
                    structureExcelDto.setCategoryName(partDetailDto.getPartCategoryName());

                    if (StringUtils.isNotBlank(dto.getStructureParentPartId())) {
                        PartDetailDto parentPartDto = partMap.get(dto.getStructureParentPartId());
                        if (null != parentPartDto) {
                            dto.setParentName(buildParentName(parentPartDto.getPartName(), structureEquipmentNameMap.get(dto.getEquipmentId())));
                        } else {
                            //父节点未找到，直接补上级设备名
                            structureExcelDto.setParentName(structureEquipmentNameMap.get(dto.getEquipmentId()));
                        }
                    } else {
                        //父节点为null，则为一级部件，直接补上级设备名
                        structureExcelDto.setParentName(structureEquipmentNameMap.get(dto.getEquipmentId()));
                    }
                    structureExcelDto.setUnit(partDetailDto.getPartUnit());
                    structureExcelDto.setUnitName(StringUtils.isNotBlank(partDetailDto.getPartUnitName()) ? partDetailDto.getPartUnitName() : partDetailDto.getPartUnit());
                    structureExcelDto.setMaterial(partDetailDto.getPartMaterial());
                    if (CollectionUtils.isNotEmpty(partDetailDto.getPartSupplierDtos())) {
                        List<String> supplierNames = partDetailDto.getPartSupplierDtos().stream().map(PartSupplierDto::getSupplierName).distinct().collect(Collectors.toList());
                        structureExcelDto.setSupplierNames(StringUtils.join(supplierNames.toArray(), StringPool.COMMA));
                    }
                    structureExcelDto.setExtendProp(partDetailDto.getExtendProp());
                    excelDtos.add(structureExcelDto);
                }
            }
        }
        return excelDtos;
    }

    public String getSpecialWarningCountOfNext(Integer offsetDays) {
        Date now = new Date();
        Date offsetDate = DateUtil.offset(now, DateField.DAY_OF_MONTH, offsetDays);
        Date nowBegin = DateUtil.beginOfDay(now);
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = this.buildWrapper();
        wrapper.le(EquipmentInfoSpecial::getNextInspectionDate, offsetDate).ge(EquipmentInfoSpecial::getNextInspectionDate, nowBegin);
        //获取从今日开始
        Integer count = this.count(wrapper);
        return "" + count;
    }

    private LambdaQueryWrapper<EquipmentInfoSpecial> buildWrapper(){
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = Wrappers.lambdaQuery();
        BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
        if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
            wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), EquipmentInfoSpecial::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
        }else{
            wrapper.eq(EquipmentInfoSpecial::getEquipmentId, "-1");
        }
        return wrapper;
    }

    public String getSpecialWarningCountOfOverRemind() {
        Date now = new Date();
        Date nowBegin = DateUtil.beginOfDay(now);
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = this.buildWrapper();
        //获取从今日开始
        wrapper.apply("DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_SUB( next_inspection_date, INTERVAL deadline_days DAY ) ");
        Integer count = this.count(wrapper);
        return "" + count;
    }

    public String getSpecialWarningCountOfOverNext() {
        Date now = new Date();
        Date nowBegin = DateUtil.beginOfDay(now);
        LambdaQueryWrapper<EquipmentInfoSpecial> wrapper = this.buildWrapper();
        //获取从今日开始
        wrapper.apply("DATE_FORMAT(NOW(),'%Y-%m-%d') > DATE_FORMAT(next_inspection_date,'%Y-%m-%d') ");
        Integer count = this.count(wrapper);
        return "" + count;
    }

    @Override
    public List<EquipmentSpecialListDto> exportSpecialEq(EquipmentSpecialQueryParam queryParam) {
        if (StringUtils.isNotBlank(queryParam.getParentId())) {
            List<String> parentIds = equipmentLocationService.getChildIds(queryParam.getParentId());
            queryParam.setParentIds(parentIds);
        }
        List<EquipmentSpecialListDto> specialList = specialMapper.getExportSpecialEq(queryParam);
        if (CollectionUtils.isNotEmpty(specialList)) {
            String[] equipmentIds = specialList.stream().filter(dto -> StringUtils.isBlank(dto.getStructureId())).map(EquipmentSpecialListDto::getEquipmentId).distinct().toArray(String[]::new);
            Map<String, SpecialEquipmentDto> equipmentMap = equipmentInfoService.getSpecialListByIds(equipmentIds);
            Map<String, String> equipmentNameMap = new HashMap<>();
            if (equipmentIds.length > 0) {
                //设备特种设备集合，不包含自身
                equipmentNameMap = equipmentInfoService.getDepthName(Arrays.asList(equipmentIds), true);
            }
            Map<String, String> finalEquipmentNameMap = equipmentNameMap;
            specialList.forEach(dto -> buildSpecialInfo(equipmentMap, finalEquipmentNameMap, dto));
        }
        return specialList;
    }

    private void buildSpecialInfo(Map<String, SpecialEquipmentDto> equipmentMap, Map<String, String> finalEquipmentNameMap, EquipmentSpecialListDto dto) {
        //设备特种设备
        SpecialEquipmentDto specialEquipmentDto = equipmentMap.get(dto.getEquipmentId());
        if (null != specialEquipmentDto) {
            dto.setCode(specialEquipmentDto.getEquipmentCode());
            dto.setName(specialEquipmentDto.getEquipmentName());
            dto.setCategoryId(specialEquipmentDto.getCategoryId());
            dto.setCategoryName(specialEquipmentDto.getCategoryName());
            dto.setParentId(specialEquipmentDto.getParentId());
            dto.setParentName(finalEquipmentNameMap.get(specialEquipmentDto.getEquipmentId()));
            dto.setType(StaticValue.ONE);
            dto.setItemNo(specialEquipmentDto.getEquipmentItemNo());
        }
    }
}
