package cn.getech.ehm.equipment.dto.location;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 设备位置 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentLocation编辑", description = "设备位置编辑参数")
public class EquipmentLocationEditParam extends ApiParam {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "上级节点", required = true)
    @NotBlank(message = "上级节点不能为空")
    private String parentId;

    @ApiModelProperty(value = "位置类型", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "省编码")
    private String province;

    @ApiModelProperty(value = "市编码")
    private String city;

    @ApiModelProperty(value = "区编码")
    private String area;


    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片")
    private String picId;

}
