package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 运行状态设备总数量
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "RunningEquipmentDto", description = "运行状态设备总数量")
public class RunningEquipmentDto {

    @ApiModelProperty(value = "图表集合")
    List<RunningEquipmentCountDto> data = new ArrayList<>();

    @ApiModelProperty(value = "数量")
    private Integer totalQty = StaticValue.ZERO;
}