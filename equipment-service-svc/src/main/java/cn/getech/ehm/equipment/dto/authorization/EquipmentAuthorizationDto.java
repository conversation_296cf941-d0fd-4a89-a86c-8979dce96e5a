package cn.getech.ehm.equipment.dto.authorization;

import cn.getech.ehm.equipment.entity.EquipmentAuthorization;
import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;


/**
 * 授权设备DTO
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "EquipmentAuthorizationDto", description = "设备授权返回数据模型")
public class EquipmentAuthorizationDto extends ApiParam implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "组织code")
    private String code;

    @ApiModelProperty(value = "组织code path")
    private String codePath;

    @ApiModelProperty(value = "是否是人员")
    private Boolean staff;

    @ApiModelProperty(value = "组织名称")
    private String name;

    @ApiModelProperty(value = "上级组织code")
    private String parentCode;

    @ApiModelProperty(value = "上级组织名称")
    private String parentName;

    @ApiModelProperty(value = "授权类型(0跟随上级1自定义)")
    private Integer authType;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "上级设备id集合")
    private String[] parentEquipmentIds;

    @ApiModelProperty(value = "创建人标识")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新人标识")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    public static EquipmentAuthorization convert(EquipmentAuthorizationDto equipmentAuthorizationDto){
        EquipmentAuthorization equipmentAuthorization = new EquipmentAuthorization();
        equipmentAuthorization.setId(equipmentAuthorizationDto.id);
        equipmentAuthorization.setCode(equipmentAuthorizationDto.code);
        equipmentAuthorization.setParentCode(equipmentAuthorizationDto.parentCode);
        equipmentAuthorization.setEquipmentIds(equipmentAuthorizationDto.equipmentIds);
        equipmentAuthorization.setAuthType(equipmentAuthorizationDto.authType);
        equipmentAuthorization.setStaff(equipmentAuthorizationDto.staff);
        return equipmentAuthorization;
    }

}