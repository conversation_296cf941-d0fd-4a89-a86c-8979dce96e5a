package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportDto;
import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportListDto;
import cn.getech.ehm.equipment.dto.diagnosis.DiagnosisReportQueryParam;
import cn.getech.ehm.equipment.entity.DiagnosisReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 诊断报告 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Repository
public interface DiagnosisReportMapper extends BaseMapper<DiagnosisReport> {
    /**
     * 分页查询，返回Dto
     * @param param
     * @return
     */
    IPage<DiagnosisReportListDto> pageList(Page<DiagnosisReportQueryParam> page,
                                                    @Param("param") DiagnosisReportQueryParam param);

    /**
     * 获取详情
     * @param id
     * @return
     */
    DiagnosisReportDto getDtoById(@Param("id") String id);
}
