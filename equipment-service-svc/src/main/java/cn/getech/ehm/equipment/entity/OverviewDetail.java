package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 概览图详情
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("overview_detail")
public class OverviewDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 版式(1,2)
     */
    @TableField("model_type")
    private Integer modelType;

    /**
     * 类型(1位置2设备)
     */
    @TableField("type")
    private Integer type;

    /**
     * 类型对应标识(位置id；设备id)
     */
    @TableField("relation_key")
    private String relationKey;

    /**
     * 概览卡片图名称
     */
    @TableField("card_name")
    private String cardName;

    /**
     * 概览卡片图名称是否显示
     */
    @TableField("card_name_enable")
    private Boolean cardNameEnable;

    /**
     * 概览卡片图类型(0设备图片1组态2文件)
     */
    @TableField("card_type")
    private Integer cardType;

    /**
     * 组态页面类型(1Ehm2poros-iot
     */
    @TableField("studio_type")
    private Integer studioType;

    /**
     * 概览卡片图对应路由(iot组态链接;图片url;ehm组态id)
     */
    @TableField("card_url")
    private String cardUrl;

    /**
     * 概览卡片图组态名称
     */
    @TableField("card_studio_name")
    private String cardStudioName;

    /**
     * 概览卡片图组态缩略图
     */
    @TableField("card_studio_pic")
    private String cardStudioPic;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
