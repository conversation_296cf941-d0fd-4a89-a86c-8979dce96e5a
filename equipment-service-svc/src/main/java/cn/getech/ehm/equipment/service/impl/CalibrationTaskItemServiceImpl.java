package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.calibration.CalibrationTaskItemDto;
import cn.getech.ehm.equipment.entity.CalibrationTaskItem;
import cn.getech.ehm.equipment.mapper.CalibrationTaskItemMapper;
import cn.getech.ehm.equipment.service.ICalibrationTaskItemService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 校准标准项目 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Slf4j
@Service
public class CalibrationTaskItemServiceImpl extends BaseServiceImpl<CalibrationTaskItemMapper, CalibrationTaskItem> implements ICalibrationTaskItemService {

    @Autowired
    private CalibrationTaskItemMapper calibrationTaskItemMapper;

    @Override
    public Boolean saveList(List<CalibrationTaskItemDto> calibrationTaskItemDtos, String calibrationId) {
        List<CalibrationTaskItem> entities = CopyDataUtil.copyList(calibrationTaskItemDtos, CalibrationTaskItem.class);
        int i = StaticValue.ONE;
        for(CalibrationTaskItem entity : entities) {
            entity.setCalibrationId(calibrationId);
            entity.setSort(i++);
        }
        return this.saveBatch(entities);
    }

    @Override
    public List<CalibrationTaskItemDto> getByCalibrationId(String calibrationId) {
        LambdaQueryWrapper<CalibrationTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CalibrationTaskItem::getCalibrationId, calibrationId);
        wrapper.select(CalibrationTaskItem::getTitle, CalibrationTaskItem::getUnit, CalibrationTaskItem::getValue,
                CalibrationTaskItem::getError, CalibrationTaskItem::getId,CalibrationTaskItem::getCalibrationValue,
                CalibrationTaskItem::getAutoResult);
        wrapper.orderByAsc(CalibrationTaskItem::getSort);
        List<CalibrationTaskItem> entities = calibrationTaskItemMapper.selectList(wrapper);
        return CopyDataUtil.copyList(entities, CalibrationTaskItemDto.class);
    }

    @Override
    public Integer deleteByCalibrationId(String calibrationId){
        LambdaQueryWrapper<CalibrationTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CalibrationTaskItem::getCalibrationId, calibrationId);
        return calibrationTaskItemMapper.delete(wrapper);
    }
}
