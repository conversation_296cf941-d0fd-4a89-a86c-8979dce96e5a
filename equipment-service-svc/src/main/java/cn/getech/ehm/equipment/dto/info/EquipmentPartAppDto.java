package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备备件app返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
@ApiModel(value = "EquipmentPartAppDto", description = "设备备件app返回数据模型")
public class EquipmentPartAppDto {

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "备件编码")
    private String partCode;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型名称")
    private String partCategoryName;

    @ApiModelProperty(value = "规格型号")
    private String partSpecification;

    @ApiModelProperty(value = "库存")
    private Integer currentStock = StaticValue.ZERO;

    @ApiModelProperty(value = "图片ids")
    private String picIds;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}