package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.BuildInfoSearchDto;
import cn.getech.ehm.equipment.dto.LocationStatisticsDto;
import cn.getech.ehm.equipment.dto.LocationSummaryDto;
import cn.getech.ehm.equipment.dto.info.BomParentDto;
import cn.getech.ehm.equipment.dto.info.EquipmentSortEditParam;
import cn.getech.ehm.equipment.dto.overview.CardAtlasCountDto;
import cn.getech.ehm.equipment.dto.overview.EquipmentNumDto;
import cn.getech.ehm.equipment.dto.overview.IntactRateDto;
import cn.getech.ehm.equipment.dto.info.LazyEquipmentTreeDto;
import cn.getech.ehm.equipment.dto.location.*;
import cn.getech.ehm.equipment.dto.overview.ParamStatusDto;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.ehm.equipment.enums.RunningStatusType;
import cn.getech.ehm.equipment.mapper.EquipmentLocationMapper;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.dto.RelSearchDto;
import cn.getech.ehm.equipment.service.IOverviewModelService;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.warn.CardWarnStatisticDto;
import cn.getech.ehm.iot.dto.warn.OverviewStatisticDto;
import cn.getech.ehm.iot.dto.warn.WarnConfigDetailDto;
import cn.getech.ehm.iot.dto.warn.WarnCountDto;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备位置 服务实现类
 * <AUTHOR>
 * @since 2020-07-10
 */
@Slf4j
@Service
public class EquipmentLocationServiceImpl extends BaseServiceImpl<EquipmentLocationMapper, EquipmentLocation> implements IEquipmentLocationService {

    private static final String ZERO = "0";
    @Autowired
    private EquipmentLocationMapper equipmentLocationMapper;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private IOverviewModelService modelService;

    @Override
    public List<EquipmentLocationTreeDto> node(String parentId){
        List<EquipmentLocationTreeDto> resDtos = new ArrayList<>();
        saveRoot(parentId);
        if(StringUtils.isBlank(parentId)){
            parentId = "0";
        }
        BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
        if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getLocationIds())) {
            resDtos = this.getListByParentId(parentId, buildInfoSearchDto.getLocationIds());
        }
        return resDtos;
    }

    @Override
    public List<EquipmentLocationTreeDto> getListByParentId(String parentId, List<String> locationIds){
        List<EquipmentLocationTreeDto> resDtos = equipmentLocationMapper.getListByParentId(parentId, locationIds, null);
        if (CollectionUtils.isNotEmpty(resDtos)) {
            //概览图启动状态
            List<String> locationTypes = resDtos.stream().map(dto -> dto.getType().toString()).collect(Collectors.toList());
            Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(locationTypes, 1);
            for (EquipmentLocationTreeDto treeDto : resDtos) {
                treeDto.setEnableOverview(overviewEnableMap.get(treeDto.getType().toString()));
                List<EquipmentLocationTreeDto> equipmentLocations = equipmentLocationMapper.getListByParentId(treeDto.getId(), locationIds, null);
                if (equipmentLocations.size() > StaticValue.ZERO) {
                    treeDto.setIsLeaf(false);
                }
            }
        }
        return resDtos;
    }

    private void saveRoot(String parentId){
        if(StringUtils.isBlank(parentId) || parentId.equals(ZERO)) {
            List<EquipmentLocationTreeDto> resDtos = equipmentLocationMapper.getListByParentId(ZERO, null, null);
            if (CollectionUtils.isEmpty(resDtos)) {
                //第一层级父节点为0的数据列表为空，默认创建一个节点
                EquipmentLocation equipmentLocation = new EquipmentLocation();
                String id = UUID.randomUUID().toString().replace("-","");
                equipmentLocation.setId(id);
                equipmentLocation.setLayerCode(id);
                equipmentLocation.setType(StaticValue.ONE);
                equipmentLocation.setName("集团");
                equipmentLocation.setParentId(ZERO);
                equipmentLocation.setAddress("");
                save(equipmentLocation);
            }
        }
    }

    @Override
    public List<LazyEquipmentTreeDto> lazyLocationTree(String parentId, String keyword, List<String> authLocationIds){
        List<LazyEquipmentTreeDto> lazyEquipmentTreeDtos = new ArrayList<>();
        parentId = StringUtils.isNotBlank(parentId) ? parentId : ZERO;
        //构造集团根节点
        saveRoot(parentId);

        //获取当前节点下子节点
        List<EquipmentLocationTreeDto> children = equipmentLocationMapper.getListByParentId(parentId, authLocationIds, keyword);
        if(CollectionUtils.isNotEmpty(children)) {
            //概览图启动状态
            List<String> locationTypes = children.stream().map(dto -> dto.getType().toString() ).collect(Collectors.toList());
            Map<String, Boolean> overviewEnableMap = modelService.getModelStatus(locationTypes, 1);

            List<String> childIds = children.stream().map(EquipmentLocationTreeDto::getId).distinct().collect(Collectors.toList());
            //判断子节点是否拥有子位置
            LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
            wrapper.in(EquipmentLocation::getParentId, childIds);
            wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
            wrapper.select(EquipmentLocation::getId, EquipmentLocation::getParentId);
            List<String> hasChildLocationIds = equipmentLocationMapper.selectList(wrapper).stream().map(EquipmentLocation::getParentId).distinct().collect(Collectors.toList());

            //判断子节点是否拥有设备
            List<String> hasChildEquipIds = equipmentInfoService.hasChildIds(childIds);
            for(EquipmentLocationTreeDto treeDto : children){
                LazyEquipmentTreeDto lazyEquipmentTreeDto = new LazyEquipmentTreeDto();
                lazyEquipmentTreeDto.setId(treeDto.getId());
                lazyEquipmentTreeDto.setName(treeDto.getName());
                lazyEquipmentTreeDto.setLocationType(treeDto.getType());
                lazyEquipmentTreeDto.setParentId(parentId);
                lazyEquipmentTreeDto.setType(StaticValue.ONE);
                lazyEquipmentTreeDto.setSort(treeDto.getSort());
                lazyEquipmentTreeDto.setEnableOverview(overviewEnableMap.get(treeDto.getType().toString()));

                if(CollectionUtils.isNotEmpty(hasChildLocationIds) && hasChildLocationIds.contains(treeDto.getId())){
                    lazyEquipmentTreeDto.setIsLeaf(false);
                }
                if(CollectionUtils.isNotEmpty(hasChildEquipIds) && hasChildEquipIds.contains(treeDto.getId())){
                    lazyEquipmentTreeDto.setIsLeaf(false);
                }
                lazyEquipmentTreeDtos.add(lazyEquipmentTreeDto);
            }
        }
        return lazyEquipmentTreeDtos;
    }

    @Override
    public List<LocationEquipmentTreeDto> locationTree(Boolean auth){
        Map<String, List<LocationEquipmentTreeDto>> locationMap = new HashMap<>();
        if(null != auth && auth) {
            BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
            if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getLocationIds())) {
                locationMap = this.getAllTreeList(buildInfoSearchDto.getLocationIds()).stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));
            }
        }else{
            locationMap = this.getAllTreeList(null).stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));
        }
        return buildTree("0", locationMap, new HashMap<>());
    }

    @Override
    @Cacheable(value = "locationTreeCache", unless="#result == null", key = "#tenantId")
    public List<LocationEquipmentTreeDto> tree(String tenantId){
        Map<String, List<LocationEquipmentTreeDto>> locationMap = this.getAllTreeList(null).stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));
        Map<String, List<LocationEquipmentTreeDto>> equipmentMap = equipmentInfoService.getAllTreeList().stream().collect(Collectors.groupingBy(LocationEquipmentTreeDto::getParentId));

        return buildTree("0", locationMap, equipmentMap);
    }

    @Override
    public List<LocationEquipmentTreeDto> getTreeListByParentId(String parentId){
        if(parentId.equals("0")){
            saveRoot(null);
        }
        return equipmentLocationMapper.getTreeListByParentId(parentId);
    }

    @Override
    public List<LocationEquipmentTreeDto> getAllTreeList(List<String> locationIds){
        saveRoot(null);
        return equipmentLocationMapper.getAllTreeList(locationIds);
    }

    private List<LocationEquipmentTreeDto> buildTree(String parentId, Map<String, List<LocationEquipmentTreeDto>> locationMap, Map<String, List<LocationEquipmentTreeDto>> equipmentMap){
        List<LocationEquipmentTreeDto> resDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(locationMap.get(parentId))) {
            resDtos.addAll(locationMap.get(parentId));
        }
        if(CollectionUtils.isNotEmpty(equipmentMap.get(parentId))) {
            resDtos.addAll(equipmentMap.get(parentId));
        }

        if(CollectionUtils.isEmpty(resDtos)){
            return null;
        }
        for(LocationEquipmentTreeDto treeDto : resDtos) {
            List<LocationEquipmentTreeDto> children = buildTree(treeDto.getId(), locationMap, equipmentMap);
            if(CollectionUtils.isNotEmpty(children)) {
                treeDto.setIsLeaf(false);
                treeDto.setChildren(children);
            }
        }
        return resDtos;
    }

    @Override
    public BomParentDto getBomDtoById(String id){
        return equipmentLocationMapper.getBomDtoById(id);
    }

    @Override
    public List<EquipmentLocationDto> queryList(String keyword) {
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(keyword)){
            wrapper.like(EquipmentLocation::getName, keyword);
        }
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        return CopyDataUtil.copyList(equipmentLocationMapper.selectList(wrapper), EquipmentLocationDto.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public EquipmentLocationDto saveByParam(EquipmentLocationAddParam addParam) {
        if(check(addParam.getName(), addParam.getParentId(), null)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        clearCache();
        EquipmentLocation equipmentLocation = CopyDataUtil.copyObject(addParam, EquipmentLocation.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentLocation);
        String id = UUID.randomUUID().toString().replace("-","");
        String layerCode = this.getLayerCode(addParam.getParentId(),id);
        equipmentLocation.setLayerCode(layerCode);
        equipmentLocation.setId(id);
        equipmentLocation.setDeleted(DeletedType.NO.getValue());
        save(equipmentLocation);

        EquipmentLocationDto equipmentLocationDto = CopyDataUtil.copyObject(equipmentLocation, EquipmentLocationDto.class);
        return equipmentLocationDto;

    }

    @Override
    public void clearCache(){
        Cache cache = cacheManager.getCache("locationTreeCache");
        cache.clear();
        Cache infoTreeCache = cacheManager.getCache("equipmentTreeCache");
        infoTreeCache.clear();
    }

    public Boolean check(String name, String parentId, String id) {
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(EquipmentLocation::getId, id);
        }
        wrapper.eq(EquipmentLocation::getName, name);
        wrapper.eq(EquipmentLocation::getParentId, parentId);
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        return equipmentLocationMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    /**
     * 获取层级编码
     * @param parentId
     * @return
     */
    private String getLayerCode(String parentId, String id){
        log.info("生成层级编码");
        StringBuffer layerCode = new StringBuffer();
        String parentLayerCode = equipmentLocationMapper.getLayerCode(parentId);
        if(StringUtils.isNotBlank(parentLayerCode)){
            layerCode.append(parentLayerCode).append("/").append(id);
        }else{
            layerCode.append(id);
        }
        return layerCode.toString();
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateByParam(EquipmentLocationEditParam editParam) {
        if(check(editParam.getName(), editParam.getParentId(), editParam.getId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentLocation equipmentLocation = CopyDataUtil.copyObject(editParam, EquipmentLocation.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, equipmentLocation);

        /**上级节点修改，修改树结构下所有层级编码*/
        EquipmentLocation oldEquipmentLocation = (EquipmentLocation)this.getById(editParam.getId());
        if(!oldEquipmentLocation.getParentId().equals(editParam.getParentId())
                || !oldEquipmentLocation.getName().equals(editParam.getName())
                || !oldEquipmentLocation.getType().equals(editParam.getType())){
            clearCache();
        }
        if(!oldEquipmentLocation.getParentId().equals(editParam.getParentId())){
            String layerCode = this.getLayerCode(editParam.getParentId(), equipmentLocation.getId());
            List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.getListByLayerCode(oldEquipmentLocation.getLayerCode());
            equipmentLocations.stream().forEach(info -> {
                String oldLayerCode = info.getLayerCode();
                String newLayerCode = oldLayerCode.replaceFirst(oldEquipmentLocation.getLayerCode(), layerCode);
                info.setLayerCode(newLayerCode);
            });
            this.updateBatchById(equipmentLocations);
        }

        return updateById(equipmentLocation);
    }

    @Override
    public EquipmentLocationDto getDtoById(String id) {
        EquipmentLocationDto dto = equipmentLocationMapper.getById(id);
        if(null != dto) {
            /**获取当前节点下所有子节点列表*/
            
            List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.getListByLayerCode(dto.getLayerCode());
            List<String> ids = equipmentLocations.stream().map(EquipmentLocation::getId).distinct().collect(Collectors.toList());
            Integer num = equipmentInfoService.getCountByLocationId(ids, null);
            dto.setNum(num);
        }
        return dto;
    }

    @Override
    public List<String> getChildIds(String locationId){
        List<String> childIds = new ArrayList<>();
        String parentLayerCode = equipmentLocationMapper.getLayerCode(locationId);
        if(StringUtils.isNotBlank(parentLayerCode)){
            List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.getListByLayerCode(parentLayerCode);
            List<String> ids = equipmentLocations.stream().map(EquipmentLocation::getId).distinct().collect(Collectors.toList());
            childIds.addAll(ids);
        }
        return childIds;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteById(String id){
        EquipmentLocationDto dto = equipmentLocationMapper.getById(id);
        List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.getListByLayerCode(dto.getLayerCode());
        if(equipmentLocations.size() > StaticValue.ONE){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("obj_has_children", null, LocaleContextHolder.getLocale())));
        }
        clearCache();
        return equipmentLocationMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean deleteByCodes(List<String> codes, String tenantId){
        return equipmentLocationMapper.updateByCodes(codes, tenantId) > 0;
    }

    @Override
    public Map<String, String> getMapByNames(List<String> locationNames) {
        if(CollectionUtils.isEmpty(locationNames)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getName, locationNames);
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentLocation::getName, EquipmentLocation::getId);
        return equipmentLocationMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentLocation::getName, EquipmentLocation::getId, (v1, v2) -> v1));
    }

    @Override
    public Map<String, EquipmentLocation> getAllNameIdMap(){
        Map<String, EquipmentLocation> map = new HashMap<>();
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(EquipmentLocation::getParentId, "0");
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getLayerCode, EquipmentLocation::getName);
        List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentLocations)){
            Map<String, String> nameMap = equipmentLocations.stream().collect(Collectors.toMap(EquipmentLocation::getId, EquipmentLocation::getName, (v1,v2) -> v1));
            for(EquipmentLocation equipmentLocation : equipmentLocations){
                String[] allIds = equipmentLocation.getLayerCode().split(StringPool.SLASH);
                String allName = "";
                for(int i = 1; i < allIds.length; i++){
                    //第一位为根节点，剔除
                    String name = nameMap.get(allIds[i]);
                    if(StringUtils.isBlank(name)){
                        //未查询到全路径名称，跳过
                        break;
                    }
                    allName += name;
                    if(i != allIds.length - 1){
                        allName += StringPool.SLASH;
                    }
                }
                map.put(allName.replace("（", "(").replace("）",")"), equipmentLocation);
            }
        }
        return map;
    }

    @Override
    public Map<String, LocationStatisticsDto> getLocationStatistics(String[] locationIds){
        Map<String, LocationStatisticsDto> map = new HashMap<>();
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        if(null != locationIds) {
            for (String locationId : locationIds) {
                wrapper.or().like(EquipmentLocation::getLayerCode, locationId);
            }
        }
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.isNotNull(EquipmentLocation::getLayerCode);
        wrapper.select(EquipmentLocation::getName, EquipmentLocation::getLayerCode, EquipmentLocation::getId,
                EquipmentLocation::getParentId,EquipmentLocation::getCreateTime);
        List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentLocations)){
            equipmentLocations.stream().forEach(dto -> map.put(dto.getId(), CopyDataUtil.copyObject(dto,LocationStatisticsDto.class)));
        }
        return map;
    }

    @Override
    public List<String> getLocationIdsByParentId(List<String> locationIds, Boolean notDeleted) {
        LambdaQueryWrapper<EquipmentLocation> queryWrapper = Wrappers.lambdaQuery();
        if(CollectionUtils.isNotEmpty(locationIds)) {
            for (String locationId : locationIds) {
                queryWrapper.or().like(EquipmentLocation::getLayerCode, locationId);
            }
        }
        if(notDeleted) {
            queryWrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        }
        queryWrapper.select(EquipmentLocation::getId);
        return equipmentLocationMapper.selectList(queryWrapper).stream().map(EquipmentLocation::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean excelImport(List<EquipmentLocationExcel> excels){
        if(CollectionUtils.isNotEmpty(excels)){
            List<EquipmentLocation> equipmentLocations = new ArrayList<>(excels.size());
            Map<String, String> nameMap = new HashMap<>();
            //先查询根节点集团的id
            List<EquipmentLocationTreeDto> resDtos = equipmentLocationMapper.getListByParentId(ZERO, null, null);
            String rootId = null;
            if (CollectionUtils.isEmpty(resDtos)) {
                //第一层级父节点为0的数据列表为空，默认创建一个节点
                EquipmentLocation equipmentLocation = new EquipmentLocation();
                String id = UUID.randomUUID().toString().replace("-","");
                equipmentLocation.setId(id);
                equipmentLocation.setLayerCode(id);
                equipmentLocation.setType(StaticValue.ONE);
                equipmentLocation.setName("集团");
                equipmentLocation.setParentId(ZERO);
                equipmentLocation.setAddress("");
                save(equipmentLocation);
                rootId = id;
                nameMap.put("集团", id);
            }else{
                rootId = resDtos.get(0).getId();
                nameMap.put("集团", resDtos.get(0).getId());
            }
            //导入需保证父节点也在excel中定义，否则需要查询数据库或单独入库
            for(EquipmentLocationExcel excel : excels){
                //先把父节点定义id
                if(!nameMap.containsKey(excel.getParentName())){
                    String id = UUID.randomUUID().toString().replace("-","");
                    nameMap.put(excel.getParentName(), id);
                }
            }
            for(EquipmentLocationExcel excel : excels){
                EquipmentLocation equipmentLocation = new EquipmentLocation();
                //保存父节点
                String id = nameMap.get(excel.getName());
                if(StringUtils.isBlank(id)){
                    id = UUID.randomUUID().toString().replace("-","");
                }
                equipmentLocation.setId(id);
                equipmentLocation.setName(excel.getName());
                String parentId = nameMap.get(excel.getParentName());
                equipmentLocation.setParentId(StringUtils.isNoneBlank(parentId) ? parentId : "-1");
                equipmentLocation.setAddress(excel.getAddress());
                equipmentLocation.setType(Integer.valueOf(excel.getType()));
                equipmentLocation.setDeleted(DeletedType.NO.getValue());
                equipmentLocations.add(equipmentLocation);
            }

            Map<String, List<EquipmentLocation>> childMap = equipmentLocations.stream().collect(Collectors.groupingBy(EquipmentLocation::getParentId));
            List<EquipmentLocation> entities = new ArrayList<>();
            buildLayerCode(rootId, childMap, rootId, entities);

            saveBatch(entities);
            clearCache();
        }
        return true;
    }

    private void buildLayerCode(String parentId, Map<String, List<EquipmentLocation>> childMap, String parentLayerCode, List<EquipmentLocation> equipmentLocations){
        List<EquipmentLocation> children = childMap.get(parentId);
        if(CollectionUtils.isEmpty(children)){
            return;
        }
        for(EquipmentLocation child : children){
            String layerCode = parentLayerCode + StringPool.SLASH + child.getId();
            child.setLayerCode(layerCode);
            buildLayerCode(child.getId(), childMap, layerCode, equipmentLocations);
            equipmentLocations.add(child);
        }
    }

    @Override
    public Boolean excelImportCW(List<LocationCWExcel> excels){
        if(CollectionUtils.isNotEmpty(excels)){
            List<EquipmentLocation> equipmentLocations = new ArrayList<>();
            Map<String, EquipmentLocation> nameMap = new HashMap<>();
            Map<String, EquipmentLocation> locationMap = this.getAllNameIdMap();
            //先查询根节点集团的id
            List<EquipmentLocationTreeDto> resDtos = equipmentLocationMapper.getListByParentId(ZERO, null, null);
            String rootId = resDtos.get(0).getId();
            UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            for(LocationCWExcel excel : excels){
                String[] names = excel.getName().split(StringPool.SLASH);
                String parentId = rootId;
                String layerCode = rootId;
                String allName = "";
                Integer type = 2;
                for(String name : names){
                    //拼接全路径名称
                    allName = StringUtils.isNotBlank(allName) ? allName + StringPool.SLASH + name : name;
                    //查询本次是否已经使用过
                    EquipmentLocation equipmentLocation = nameMap.get(allName);
                    if (null != equipmentLocation) {
                        parentId = equipmentLocation.getId();
                        layerCode = equipmentLocation.getLayerCode();
                        type++;
                        continue;
                    }
                    //再次查询数据库中是否有
                    equipmentLocation = locationMap.get(allName);
                    if (null != equipmentLocation) {
                        parentId = equipmentLocation.getId();
                        layerCode = equipmentLocation.getLayerCode();
                        type++;
                        nameMap.put(allName, equipmentLocation);
                        continue;
                    }
                    //否则，为新增
                    equipmentLocation = new EquipmentLocation();
                    String id = UUID.randomUUID().toString().replace("-","");
                    equipmentLocation.setId(id);
                    equipmentLocation.setName(name);
                    equipmentLocation.setParentId(parentId);
                    equipmentLocation.setLayerCode(layerCode + StringPool.SLASH + id);
                    equipmentLocation.setType(type >= 4 ? 4 : type);
                    equipmentLocation.setDeleted(DeletedType.NO.getValue());
                    //手动修改时间，保证可以按照新增时间做排序
                    equipmentLocation.setCreateBy(userBaseInfo.getUid());
                    equipmentLocation.setCreateTime(calendar.getTime());
                    equipmentLocation.setUpdateBy(userBaseInfo.getUid());
                    equipmentLocation.setUpdateTime(calendar.getTime());
                    equipmentLocation.setTenantId(userBaseInfo.getTenantId());
                    calendar.add(Calendar.MINUTE, 1);
                    //标记当前新增的位置
                    parentId = equipmentLocation.getId();
                    layerCode = equipmentLocation.getLayerCode();
                    type++;
                    nameMap.put(allName, equipmentLocation);
                    equipmentLocations.add(equipmentLocation);
                }
            }

            equipmentLocationMapper.importCW(equipmentLocations);
            clearCache();
        }
        return true;
    }

    @Override
    public List<String> getLocationIdByInfoIds(RelSearchDto relSearchDto){
        List<String> locationIds = new ArrayList<>();
        List<String> layerCodes = equipmentLocationMapper.getLocationIdByInfoIds(relSearchDto);
        if(CollectionUtils.isNotEmpty(layerCodes)){
            for(String layerCode : layerCodes){
                List<String> ids = Arrays.asList(layerCode.split(StringPool.SLASH));
                locationIds.addAll(ids);
            }
            return locationIds.stream().distinct().collect(Collectors.toList());
        }
        return locationIds;
    }

    @Override
    public Boolean checkIsLocation(String id){
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentLocation::getId, id);
        return equipmentLocationMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Boolean updateName(String id, String parentId, String name){
        if(check(name, parentId, id)){
            LambdaUpdateWrapper<EquipmentLocation> wrapper = Wrappers.lambdaUpdate();
            wrapper.eq(EquipmentLocation::getId, id);
            wrapper.set(EquipmentLocation::getName, name);
            return update(wrapper);
        }
        return false;
    }

    @Override
    public String insertName(String parentId, String name, Integer type){
        if(check(name, parentId, null)){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        EquipmentLocation equipmentLocation = new EquipmentLocation();
        String id = UUID.randomUUID().toString().replace("-","");
        String layerCode = this.getLayerCode(parentId,id);
        equipmentLocation.setLayerCode(layerCode);
        equipmentLocation.setId(id);
        equipmentLocation.setDeleted(DeletedType.NO.getValue());
        equipmentLocation.setName(name);
        equipmentLocation.setType(type);
        equipmentLocation.setParentId(parentId);
        save(equipmentLocation);
        return equipmentLocation.getId();
    }

    private List<EquipmentLocation> getEntitiesByIds(List<String> locationIds){
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.in(EquipmentLocation::getId, locationIds);
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getName, EquipmentLocation::getLayerCode);
        return equipmentLocationMapper.selectList(wrapper);

    }

    @Override
    public Map<String, String> getDepthName(List<String> locationIds){
        Map<String, String> map = new HashMap<>();
        List<EquipmentLocation> equipmentLocations = this.getEntitiesByIds(locationIds);
        if(CollectionUtils.isNotEmpty(equipmentLocations)){
            List<String> allParentIds = new ArrayList<>();
            for(EquipmentLocation equipmentLocation : equipmentLocations){
                String[] parentIds = equipmentLocation.getLayerCode().split(StringPool.SLASH);
                for(String parentId : parentIds){
                    if(!allParentIds.contains(parentId)){
                        allParentIds.add(parentId);
                    }
                }
            }
            //只搜索自身及其父节点
            equipmentLocations = this.getEntitiesByIds(allParentIds);
            Map<String, EquipmentLocation> locationMap = equipmentLocations.stream().collect(Collectors.toMap(EquipmentLocation::getId, v -> v, (v1, v2) -> v1));

            for (String locationId : locationIds) {
                EquipmentLocation equipmentLocation = locationMap.get(locationId);
                if (null != equipmentLocation) {
                    List<String> locationNames = new ArrayList<>();
                    String[] parentIds = equipmentLocation.getLayerCode().split(StringPool.SLASH);
                    //非挂载于根位置下，剔除根位置展示
                    int beginIndex = parentIds.length > 1 ? 1 : 0;
                    for (int i = beginIndex; i < parentIds.length; i++) {
                        EquipmentLocation parentLocation = locationMap.get(parentIds[i]);
                        if (null != parentLocation) {
                            locationNames.add(parentLocation.getName());
                        } else {
                            break;
                        }
                    }
                    map.put(locationId, StringUtils.join(locationNames, StringPool.SLASH));
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, String> getMapByIds(String[] locationIds){
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getId, locationIds);
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getName);
        return equipmentLocationMapper.selectList(wrapper).stream().collect(Collectors.toMap(EquipmentLocation::getId, EquipmentLocation::getName));
    }

    @Override
    public Map<String,String> getRootIdMap(List<String> tenantIds) {
        Map<String, String> map = new HashMap<>();
        List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.getTenantRoot(tenantIds);
        if (CollectionUtils.isNotEmpty(equipmentLocations)) {
            map = equipmentLocations.stream().collect(Collectors.toMap(EquipmentLocation::getTenantId, EquipmentLocation::getId, (v1, v2) -> v1));
        }

        return map;
    }

    @Override
    public EquipmentNumDto cardEquipmentNum(String locationId){
        EquipmentNumDto equipmentNumDto = new EquipmentNumDto();
        List<ParamStatusDto> runningStatusCount = new ArrayList<>();
        List<ParamStatusDto> warnStatusCount = new ArrayList<>();
        List<WarnConfigDetailDto> warnLevelList = null;
        RestResponse<List<WarnConfigDetailDto>> restResponse = parameterClient.getWarnLevelList();
        if(restResponse.isOk()){
            warnLevelList = restResponse.getData();
        }else{
            log.error("获取报警等级失败");
        }
        List<CardAtlasCountDto> atlasCountDtos = this.getEquipmentIdsByLocationId(locationId);
        Integer total = atlasCountDtos.size();
        equipmentNumDto.setTotal(total);
        Map<Integer, List<CardAtlasCountDto>> runningStatusMap = atlasCountDtos.stream().collect(Collectors.groupingBy(CardAtlasCountDto::getRunningStatus));
        Map<Integer, List<CardAtlasCountDto>> warnStatusMap = atlasCountDtos.stream().collect(Collectors.groupingBy(CardAtlasCountDto::getWarnStatus));
        //运行状态
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            ParamStatusDto statusDto = new ParamStatusDto();
            statusDto.setName(runningStatusType.getName());
            statusDto.setStatus(runningStatusType.getValue());
            statusDto.setColor(runningStatusType.getColor());
            List<CardAtlasCountDto> countDtos = runningStatusMap.get(runningStatusType.getValue());
            statusDto.setCount(CollectionUtils.isNotEmpty(countDtos) ? countDtos.size() : StaticValue.ZERO);
            runningStatusCount.add(statusDto);
        }

        //报警状态
        if(CollectionUtils.isNotEmpty(warnLevelList)){
            for(WarnConfigDetailDto warnLevel : warnLevelList){
                ParamStatusDto warnStatusDto = new ParamStatusDto();
                warnStatusDto.setName(warnLevel.getName());
                warnStatusDto.setStatus(warnLevel.getLevel());
                warnStatusDto.setColor(warnLevel.getColor());
                warnStatusDto.setWarnConfigId(warnLevel.getId());
                if(CollectionUtils.isNotEmpty(atlasCountDtos)) {
                    List<CardAtlasCountDto> countDtos = warnStatusMap.get(warnLevel.getLevel());
                    warnStatusDto.setCount(CollectionUtils.isNotEmpty(countDtos) ? countDtos.size() : StaticValue.ZERO);
                }
                warnStatusCount.add(warnStatusDto);
            }
        }
        equipmentNumDto.setRunningStatusCount(runningStatusCount);
        equipmentNumDto.setWarnStatusCount(warnStatusCount);

        return equipmentNumDto;
    }

    @Override
    public List<IntactRateDto> equipmentIntactRate(String locationId){
        List<IntactRateDto> intactRateDtos = new ArrayList<>();
        List<EquipmentLocationTreeDto> treeDtos = equipmentLocationMapper.getListByParentId(locationId, null, null);
        if(CollectionUtils.isNotEmpty(treeDtos)){
            Map<String, EquipmentLocationTreeDto> layerCodeMap = new HashMap<>();
            List<String> layerCodes = new ArrayList<>();
            for(EquipmentLocationTreeDto treeDto : treeDtos){
                layerCodeMap.put(treeDto.getLayerCode(), treeDto);
                layerCodes.add(treeDto.getLayerCode());
            }
            int layerCodeLength = layerCodes.get(0).length();
            String parentLayerCode = layerCodes.get(0).substring(0, layerCodeLength - 33);
            //只选取子节点
            List<CardAtlasCountDto> countDtos = new ArrayList<>();
            BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
            if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                countDtos = equipmentLocationMapper.getInfoIdListByLocationId(parentLayerCode, layerCodeLength, true, buildInfoSearchDto.getEquipmentIds());
            }
            if(CollectionUtils.isNotEmpty(countDtos)) {
                List<String> nowWarnEquipmentIds = new ArrayList<>();
                List<String> lastWarnEquipmentIds = new ArrayList<>();
                RestResponse<List<String>> nowRestResponse = parameterClient.hasWarnEquipmentIds(0);
                if (nowRestResponse.isOk()) {
                    nowWarnEquipmentIds = nowRestResponse.getData();
                } else {
                    log.error("连接iot获取当前告警设备id失败");
                }
                RestResponse<List<String>> lastRestResponse = parameterClient.hasWarnEquipmentIds(1);
                if (lastRestResponse.isOk()) {
                    lastWarnEquipmentIds = lastRestResponse.getData();
                } else {
                    log.error("连接iot获取去年告警设备id失败");
                }
                Map<String, List<CardAtlasCountDto>> countDtoMap = countDtos.stream().collect(Collectors.groupingBy(CardAtlasCountDto::getLocationLayerCode));
                for(String layerCode : layerCodes){
                    EquipmentLocationTreeDto treeDto = layerCodeMap.get(layerCode);
                    List<CardAtlasCountDto> childCountDtos = countDtoMap.get(layerCode);
                    IntactRateDto intactRateDto = new IntactRateDto();

                    if(CollectionUtils.isNotEmpty(childCountDtos)) {
                        intactRateDto.setXaxis(treeDto.getName());
                        int allIds = 0;
                        int nowWarnIds = 0;
                        int lastWarnIds = 0;
                        for (CardAtlasCountDto child : childCountDtos) {
                            allIds++;
                            if (CollectionUtils.isNotEmpty(nowWarnEquipmentIds) && nowWarnEquipmentIds.contains(child.getEquipmentId())) {
                                nowWarnIds++;
                            }
                            if (CollectionUtils.isNotEmpty(lastWarnEquipmentIds) && lastWarnEquipmentIds.contains(child.getEquipmentId())) {
                                lastWarnIds++;
                            }
                        }
                        BigDecimal nowRate = new BigDecimal((allIds - nowWarnIds) * 100).divide(new BigDecimal(allIds), 2,BigDecimal.ROUND_HALF_UP);
                        intactRateDto.setYaxisOfNow(nowRate.doubleValue());
                        BigDecimal lastRate = new BigDecimal((allIds - lastWarnIds) * 100).divide(new BigDecimal(allIds), 2,BigDecimal.ROUND_HALF_UP);
                        intactRateDto.setYaxisOfLast(lastRate.doubleValue());
                        intactRateDtos.add(intactRateDto);
                    }
                }
            }
            //根据完好率、名称排序
            intactRateDtos = intactRateDtos.stream().sorted(Comparator.comparing(IntactRateDto::getYaxisOfNow).thenComparing(IntactRateDto::getXaxis).reversed()).collect(Collectors.toList());
            //取前12个
            int length = intactRateDtos.size() > 12 ? 12 : intactRateDtos.size();
            intactRateDtos = intactRateDtos.subList(0,length);
        }

        return intactRateDtos;
    }

    @Override
    public CardWarnStatisticDto cardWarnStatistic(String locationId){
        CardWarnStatisticDto cardWarnStatisticDto = new CardWarnStatisticDto();
        List<String> equipmentIds = this.getEquipmentIdsByLocationId(locationId).stream().map(CardAtlasCountDto::getEquipmentId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(equipmentIds)) {
            OverviewStatisticDto dto = new OverviewStatisticDto();
            dto.setEquipmentIds(equipmentIds);
            RestResponse<CardWarnStatisticDto> restResponse = parameterClient.getCardWarnStatistic(dto);
            if (restResponse.isOk()) {
                cardWarnStatisticDto = restResponse.getData();
            } else {
                log.error("连接iot获取位置概览报警卡片数据失败");
            }
        }
        return cardWarnStatisticDto;
    }

    private List<CardAtlasCountDto> getEquipmentIdsByLocationId(String locationId){
        List<CardAtlasCountDto> countDtos = new ArrayList<>();
        EquipmentLocationDto dto = equipmentLocationMapper.getById(locationId);
        if(null != dto){
            int layerCodeLength = dto.getLayerCode().length();
            BuildInfoSearchDto buildInfoSearchDto = equipmentInfoService.getCurrentUserInfoIds();
            if(!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                countDtos = equipmentLocationMapper.getInfoIdListByLocationId(dto.getLayerCode(), layerCodeLength, false, buildInfoSearchDto.getEquipmentIds());
            }

        }
        return countDtos;
    }

    @Override
    public WarnCountDto cardWarnTrend(String locationId){
        WarnCountDto warnCountDto = new WarnCountDto();
        List<String> equipmentIds = this.getEquipmentIdsByLocationId(locationId).stream().map(CardAtlasCountDto::getEquipmentId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(equipmentIds)) {
            OverviewStatisticDto dto = new OverviewStatisticDto();
            dto.setEquipmentIds(equipmentIds);
            dto.setTrendType(StaticValue.ONE);
            RestResponse<WarnCountDto> restResponse = parameterClient.getCardWarnTrend(dto);
            if (restResponse.isOk()) {
                warnCountDto = restResponse.getData();
            } else {
                log.error("连接iot获取位置概览报警趋势数据失败");
            }
        }
        return warnCountDto;
    }
    @Override
    public List<EquipmentLocationTreeDto> getRootChild(){
        List<EquipmentLocationTreeDto> dtos = new ArrayList<>();
        List<EquipmentLocationTreeDto> resDtos = equipmentLocationMapper.getListByParentId(ZERO, null, null);
        if (CollectionUtils.isNotEmpty(resDtos)) {
            dtos = equipmentLocationMapper.getListByParentId(resDtos.get(0).getId(), null, null);
        }

        return dtos;
    }

    @Override
    public String getNamesByIds(String[] locationIds){
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getId, locationIds);
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getName);
        List<String> names = equipmentLocationMapper.selectList(wrapper).stream().map(EquipmentLocation::getName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(names)){
            return StringUtils.join(names, StringPool.COMMA);
        }else{
            return null;
        }
    }

    @Override
    public List<String> getParentIdsByLocationIds(List<String> locationIds){
        List<String> parentIds = new ArrayList<>();
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getId, locationIds);
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getLayerCode);
        List<String> layerCodes = equipmentLocationMapper.selectList(wrapper).stream().map(EquipmentLocation::getLayerCode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(layerCodes)){
            for(String layerCode : layerCodes){
                List<String> ids = Arrays.asList(layerCode.split(StringPool.SLASH));
                parentIds.addAll(ids);
            }
            parentIds = parentIds.stream().distinct().collect(Collectors.toList());
        }
        return parentIds;
    }

    @Override
    public List<String> getExistParentIds(List<String> parentIds){
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getId, parentIds);
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentLocation::getId);
        return equipmentLocationMapper.selectList(wrapper).stream().map(EquipmentLocation::getId).distinct().collect(Collectors.toList());
    }
    public void updateSort(List<EquipmentSortEditParam> param) {
        List<EquipmentLocation> result = Lists.newArrayList();
        for (EquipmentSortEditParam temp : param) {
            EquipmentLocation equipmentLocation = new EquipmentLocation();
            equipmentLocation.setId(temp.getId());
            equipmentLocation.setSort(temp.getSort());
            result.add(equipmentLocation);
        }
        this.updateBatchById(result);
    }

    @Override
    public Map<String, LocationSummaryDto> getMapByLocationIds(String[] locationIds){
        Map<String, LocationSummaryDto> map = new HashMap<>();
        LambdaQueryWrapper<EquipmentLocation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentLocation::getId, locationIds);
        wrapper.eq(EquipmentLocation::getDeleted, DeletedType.NO.getValue());
        wrapper.select(EquipmentLocation::getId, EquipmentLocation::getName);
        Map<String, String> locationNameMap = this.getDepthName(Arrays.asList(locationIds));
        List<EquipmentLocation> equipmentLocations = equipmentLocationMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(equipmentLocations)){
            for(EquipmentLocation equipmentLocation : equipmentLocations){
                LocationSummaryDto dto = new LocationSummaryDto();
                dto.setId(equipmentLocation.getId());
                dto.setName(equipmentLocation.getName());
                dto.setAllName(locationNameMap.get(equipmentLocation.getId()));
                map.put(equipmentLocation.getId(), dto);
            }
        }
        return map;
    }
}
