package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.DiagnosisReport;
import cn.getech.ehm.equipment.mapper.DiagnosisReportMapper;
import cn.getech.ehm.equipment.service.IDiagnosisReportService;
import cn.getech.ehm.equipment.utils.WordDownloadUtil;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.util.BytePictureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 诊断报告 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class DiagnosisReportServiceImpl extends BaseServiceImpl<DiagnosisReportMapper, DiagnosisReport> implements IDiagnosisReportService {

    @Autowired
    private DiagnosisReportMapper diagnosisReportMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    /**
     * springboot当前运行的jar包目录
     */
    private String currentDirectory = new ApplicationHome(getClass()).getSource().getParentFile().toString() + File.separator;


    @Override
    public PageResult<DiagnosisReportListDto> pageDto(DiagnosisReportQueryParam queryParam) {
        Page<DiagnosisReportQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        IPage<DiagnosisReportListDto> result = diagnosisReportMapper.pageList(page, queryParam);
        return Optional.ofNullable(PageResult.<DiagnosisReportListDto>builder()
                        .records(result.getRecords())
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(DiagnosisReportAddParam addParam) {
        DiagnosisReport diagnosisReport = CopyDataUtil.copyObject(addParam, DiagnosisReport.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        diagnosisReport.setCode("ZD" + sdf.format(new Date()));
        if(CollectionUtils.isNotEmpty(addParam.getAnalyzePicInfoDtos())){
            diagnosisReport.setAnalyzePicInfo(JSONObject.toJSONString(addParam.getAnalyzePicInfoDtos()));
        }
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,diagnosisReport);
        return save(diagnosisReport);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(DiagnosisReportEditParam editParam) {
        DiagnosisReport diagnosisReport = CopyDataUtil.copyObject(editParam, DiagnosisReport.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,diagnosisReport);
        if(CollectionUtils.isNotEmpty(editParam.getAnalyzePicInfoDtos())){
            diagnosisReport.setAnalyzePicInfo(JSONObject.toJSONString(editParam.getAnalyzePicInfoDtos()));
        }
        return updateById(diagnosisReport);
    }

    @Override
    public File exportWord(String id, String url) {
        DiagnosisReportDto dto = diagnosisReportMapper.getDtoById(id);
        List<Map<String, Object>> analyzePicInfoMap = new ArrayList<>();
        PictureRenderData pictureRenderData = null;
        if(StringUtils.isNotBlank(dto.getEquipmentPicIds())){
            String picId = dto.getEquipmentPicIds().split(StringPool.COMMA)[0];
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(new String[]{picId});
            if (restResponse.isOk()) {
                AttachmentClientDto attachmentClientDto = restResponse.getData().get(picId);
                if(null != attachmentClientDto){
                    String[] fileTypes = attachmentClientDto.getFileType().split(StringPool.SLASH);
                    pictureRenderData = new PictureRenderData(600, 250, fileTypes.length >= 2 ? "." + fileTypes[1] : ".png", BytePictureUtils.getUrlBufferedImage(url + attachmentClientDto.getUrl()));
                }
            } else {
                log.error("获取设备图片失败");
            }
        }

        if(StringUtils.isNotBlank(dto.getAnalyzePicInfo())){
            List<AnalyzePicInfoDto> analyzePicInfoDtos = JSONObject.parseArray(dto.getAnalyzePicInfo(), AnalyzePicInfoDto.class);
            for(AnalyzePicInfoDto analyzePicInfoDto : analyzePicInfoDtos){
                Map<String, Object> map = new HashMap<>();
                map.put("name", analyzePicInfoDto.getName());
                map.put("timestamp", analyzePicInfoDto.getTimeStamp());
                map.put("url", new PictureRenderData(600, 250, ".png", BytePictureUtils.getUrlBufferedImage(url + analyzePicInfoDto.getUrl())));
                analyzePicInfoMap.add(map);
            }
        }
        RestResponse<PorosSecStaffDto> restResponse = porosSecStaffClient.getByUid(dto.getCreateBy(), null);
        if (restResponse.isOk()) {
            PorosSecStaffDto porosSecStaffDto = restResponse.getData();
            dto.setCreateBy(null != porosSecStaffDto ? porosSecStaffDto.getName() : dto.getCreateBy());
        } else {
            log.info("中台未获取到用户信息");
        }

        return WordDownloadUtil.fillDataIntoReportWord(dto, currentDirectory, analyzePicInfoMap, pictureRenderData);
    }


    @Override
    public DiagnosisReportDto getDtoById(String id) {
        DiagnosisReportDto dto = diagnosisReportMapper.getDtoById(id);
        if(null != dto){
            if(StringUtils.isNotBlank(dto.getAnalyzePicInfo())){
                List<AnalyzePicInfoDto> analyzePicInfoDtos = JSONObject.parseArray(dto.getAnalyzePicInfo(), AnalyzePicInfoDto.class);
                dto.setAnalyzePicInfoDtos(analyzePicInfoDtos);
            }
            if(StringUtils.isNotBlank(dto.getEquipmentPicIds())){
                String picId = dto.getEquipmentPicIds().split(StringPool.COMMA)[0];
                RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(new String[]{picId});
                if (restResponse.isOk()) {
                    AttachmentClientDto attachmentClientDto = restResponse.getData().get(picId);
                    dto.setEquipmentPicUrl(null != attachmentClientDto ? attachmentClientDto.getUrl() : null);
                } else {
                    log.error("获取设备图片失败");
                }
            }
        }
        return dto;
    }
}
