package cn.getech.ehm.equipment.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 运行状态统计
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "RunningStatusDto", description = "运行状态统计")
public class RunningStatusDto {

    @ApiModelProperty(value = "编码")
    private String value;

    @ApiModelProperty(value = "编码")
    private String name;

    @ApiModelProperty(value = "条数")
    private Integer count;

    private String color;
}