package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDetailDto;
import cn.getech.ehm.equipment.dto.category.EquipmentCategoryPropDto;
import cn.getech.ehm.equipment.entity.EquipmentCategoryProp;
import cn.getech.ehm.equipment.mapper.EquipmentCategoryPropMapper;
import cn.getech.ehm.equipment.service.IEquipmentCategoryPropService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备类型扩展属性 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Slf4j
@Service
public class EquipmentCategoryPropServiceImpl extends BaseServiceImpl<EquipmentCategoryPropMapper, EquipmentCategoryProp> implements IEquipmentCategoryPropService {

    @Autowired
    private EquipmentCategoryPropMapper equipmentCategoryPropMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveList(List<EquipmentCategoryPropDto> propDtos, String categoryId, Boolean isAdd) {
        List<EquipmentCategoryProp> equipmentCategoryProps = new ArrayList<>();
        int i = StaticValue.ONE;
        for(EquipmentCategoryPropDto prop : propDtos){
            for(EquipmentCategoryPropDetailDto detailDto : prop.getChildren()){
                EquipmentCategoryProp equipmentCategoryProp = CopyDataUtil.copyObject(detailDto, EquipmentCategoryProp.class);
                if (isAdd) {
                    equipmentCategoryProp.setId(null);
                }
                equipmentCategoryProp.setEquipmentCategoryId(categoryId);
                equipmentCategoryProp.setGroupName(prop.getGroupName());
                equipmentCategoryProp.setSort(i++);
                equipmentCategoryProps.add(equipmentCategoryProp);
            }
        }
        return this.saveBatch(equipmentCategoryProps);
    }

    @Override
    public Boolean initialization(List<EquipmentCategoryPropDto> equipmentCategoryPropDtos, String categoryId){
        List<EquipmentCategoryProp> infos = CopyDataUtil.copyList(equipmentCategoryPropDtos, EquipmentCategoryProp.class);
        int i = StaticValue.ONE;
        for(EquipmentCategoryProp info : infos){
            String id = UUID.randomUUID().toString().replace("-","");
            info.setId(id);
            info.setEquipmentCategoryId(categoryId);
            info.setSort(i++);
            info.setCreateBy("system");
            info.setCreateTime(new Date());
            info.setUpdateBy("system");
            info.setUpdateTime(new Date());
            equipmentCategoryPropMapper.saveDto(info);
        }
        return true;
    }

    @Override
    public List<EquipmentCategoryPropDto> getListByCategoryId(String categoryId, Boolean fixed, Boolean exported) {
        List<EquipmentCategoryPropDto> propDtos = new ArrayList<>();
        List<EquipmentCategoryPropDetailDto> detailDtos = this.getDetailListByCategoryId(categoryId, fixed, exported);
        if(CollectionUtils.isNotEmpty(detailDtos)){
            List<String> groupNames = detailDtos.stream().map(EquipmentCategoryPropDetailDto::getGroupName).distinct().collect(Collectors.toList());
            Map<String, List<EquipmentCategoryPropDetailDto>> map = detailDtos.stream().collect(Collectors.groupingBy(EquipmentCategoryPropDetailDto::getGroupName));
            for(String groupName : groupNames){
                EquipmentCategoryPropDto propDto = new EquipmentCategoryPropDto();
                propDto.setGroupName(groupName);
                propDto.setChildren(map.get(groupName));
                propDtos.add(propDto);
            }
        }

        return propDtos;
    }

    @Override
    public List<EquipmentCategoryPropDetailDto> getDetailListByCategoryId(String categoryId, Boolean fixed, Boolean exported){
        List<String> categoryIds = new ArrayList<>();
        categoryIds.add(categoryId);
        return equipmentCategoryPropMapper.getDetailListByCategoryIds(categoryIds, fixed, exported);
    }

    @Override
    public Map<String, List<EquipmentCategoryPropDetailDto>> getDetailMapByCategoryIds(List<String> categoryIds){
        if(CollectionUtils.isEmpty(categoryIds)){
            return Collections.emptyMap();
        }
        return equipmentCategoryPropMapper.getDetailListByCategoryIds(categoryIds, false, false)
                .stream().collect(Collectors.groupingBy(EquipmentCategoryPropDetailDto::getCategoryId));
    }

    @Override
    public List<String> getNameListByCategoryId(String categoryId, Boolean exported){
        return equipmentCategoryPropMapper.getNameListByCategoryId(categoryId, exported);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteByCategoryId(String categoryId) {
        LambdaQueryWrapper<EquipmentCategoryProp> wrapper = Wrappers.<EquipmentCategoryProp>lambdaQuery();
        wrapper.eq(EquipmentCategoryProp:: getEquipmentCategoryId, categoryId);
        return equipmentCategoryPropMapper.delete(wrapper) > 0;
    }
}
