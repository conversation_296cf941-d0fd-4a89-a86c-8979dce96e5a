package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.authorization.EquipmentAuthorizationDto;
import cn.getech.ehm.equipment.service.IEquipmentAuthorizationService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 设备授权控制器
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
@RestController
@RequestMapping("/equipmentAuthorization")
@Api(tags = "设备授权服务接口")
public class EquipmentAuthorizationController {

    @Autowired
    private IEquipmentAuthorizationService equipmentAuthorizationService;

    /**
     * 新增设备授权
     */
    @ApiOperation("新增设备授权")
    @AuditLog(title = "设备授权",desc = "新增设备授权",businessType = BusinessType.INSERT)
    @PostMapping
    //@Authorization("equipment:permission:update")
    public RestResponse<Boolean> save(@RequestBody @Valid EquipmentAuthorizationDto equipmentAuthorizationDto) {
        return RestResponse.ok(equipmentAuthorizationService.saveOrUpdate(EquipmentAuthorizationDto.convert(equipmentAuthorizationDto)));
    }

    /**
     * 获取设备授权
     */
    @ApiOperation(value = "获取设备授权")
    @GetMapping(value = "/get")
    /*@ApiImplicitParams({
            @ApiImplicitParam(name="code",value="0部门1人员code",dataType="string", paramType = "query"),
            @ApiImplicitParam(name="parentCode",value="父级code",dataType="string", paramType = "query"),
            @ApiImplicitParam(name="isStaff",value="是否是人员",dataType="string", paramType = "query")
    })*/
    //@Authorization("equipment:permission:list")
    public RestResponse<EquipmentAuthorizationDto> get(String code, String parentCode, Boolean isStaff) {
        return RestResponse.ok(equipmentAuthorizationService.getEquipmentAuthorization(code, parentCode, isStaff));
    }

    /**
     * 获取当前用户权限
     */
    @ApiOperation(value = "获取当前用户权限")
    @GetMapping(value = "/getCurrentUserAuz")
    public RestResponse<List<String>> getCurrentUserAuz() {
        return RestResponse.ok(equipmentAuthorizationService.getCurrentUserAuz());
    }

}
