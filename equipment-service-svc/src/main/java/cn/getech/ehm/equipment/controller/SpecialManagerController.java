package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.equipment.dto.manager.*;
import cn.getech.ehm.equipment.service.ISpecialManagerService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 安全管理人员接口
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/specialManager")
@Api(tags = "安全管理人员接口")
@Slf4j
public class SpecialManagerController {
    @Autowired
    private ISpecialManagerService specialManagerService;

    @ApiOperation("分页获取列表")
    @PostMapping("/pageList")
    //@Permission("equipment:list")
    public RestResponse<PageResult<SpecialManagerDto>> pageList(@RequestBody SpecialManagerQueryParam queryParam) {
        return RestResponse.ok(specialManagerService.pageList(queryParam));
    }

    @ApiOperation("新增安全管理人员")
    @AuditLog(title = "管理人员", desc = "新增安全管理人员", businessType = BusinessType.INSERT)
    @PostMapping
    public RestResponse<Boolean> add(@RequestBody @Valid SpecialManagerAddParam addParam) {
        return RestResponse.ok(specialManagerService.saveByParam(addParam));
    }

    /**
     * 修改安全管理人员
     */
    @ApiOperation(value = "修改安全管理人员")
    @AuditLog(title = "管理人员", desc = "修改安全管理人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public RestResponse<Boolean> edit(@RequestBody @Valid SpecialManagerEditParam editParam) {
        return RestResponse.ok(specialManagerService.editByParam(editParam));
    }

    /**
     * 根据id删除安全管理人员
     */
    @ApiOperation(value = "根据id删除安全管理人员")
    @AuditLog(title = "管理人员", desc = "删除安全管理人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> delete(@PathVariable String id) {
        return RestResponse.ok(specialManagerService.delete(id));
    }

    /**
     * 根据id获取安全管理人员
     */
    @ApiOperation(value = "根据id获取安全管理人员")
    @GetMapping(value = "/{id}")
    public RestResponse<SpecialManagerDto> get(@PathVariable String id) {
        return RestResponse.ok(specialManagerService.getDtoById(id));
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/exportModel")
    public void exportModel(HttpServletResponse response) {
        FormExcelUtils<SpecialManagerExcelDto> util = new FormExcelUtils<>(SpecialManagerExcelDto.class);
        util.exportExcel(new ArrayList<>(), "安全管理人员导入模板", response, null, null, null);
    }

    @ApiOperation(value = "导出")
    @AuditLog(title = "报表", desc = "导出安全管理人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @Permission("equipment:export")
    public void export(@RequestBody SpecialManagerQueryParam queryParam, HttpServletResponse response) {
        List<SpecialManagerExcelDto> exportDtos = specialManagerService.getExcelList(queryParam);
        FormExcelUtils<SpecialManagerExcelDto> util = new FormExcelUtils<>(SpecialManagerExcelDto.class);
        util.exportExcel(exportDtos, "安全管理人员", response, null, null, null);
    }

    @ApiOperation(value = "导入")
    @AuditLog(title = "报表", desc = "导入安全管理人员", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("fault:category:import")
    public RestResponse<String> excelImport(@RequestPart("file") MultipartFile file) {
        FormExcelUtils<SpecialManagerExcelDto> util = new FormExcelUtils<>(SpecialManagerExcelDto.class);
        List<SpecialManagerExcelDto> rows = util.importExcel(file, null);
        if (CollectionUtils.isEmpty(rows)) {
            return RestResponse.failed();
        }
        return RestResponse.ok(specialManagerService.excelImport(rows));
    }

    @ApiOperation("获取特种设备预警数量-下次检验日期")
    @GetMapping("/getSpecialWarningCount/next")
    public RestResponse<String> getSpecialWarningPersonCountOfNext(String param) {
        return RestResponse.ok(specialManagerService.getSpecialWarningCountOfNext(Integer.parseInt(param)));
    }

    @ApiOperation("获取特种设备预警数量-超过提醒日期")
    @GetMapping("/getSpecialWarningCount/over/remind")
    public RestResponse<String> getSpecialWarningPersonCountOfOverRemind(String param) {
        return RestResponse.ok(specialManagerService.getSpecialWarningCountOfOverRemind());
    }

    @ApiOperation("获取特种设备预警数量-超过下次检验日期")
    @GetMapping("/getSpecialWarningCount/over/next")
    public RestResponse<String> getSpecialWarningPersonCountOfOverNext(String param) {
        return RestResponse.ok(specialManagerService.getSpecialWarningCountOfOverNext());
    }
}
