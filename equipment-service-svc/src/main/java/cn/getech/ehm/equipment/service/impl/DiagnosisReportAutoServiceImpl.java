package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.diagnosis.*;
import cn.getech.ehm.equipment.entity.EquipmentDiagnosisReportAuto;
import cn.getech.ehm.equipment.mapper.DiagnosisReportAutoMapper;
import cn.getech.ehm.equipment.service.*;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 智能诊断报告 服务实现类
 */
@Slf4j
@Service
public class DiagnosisReportAutoServiceImpl extends BaseServiceImpl<DiagnosisReportAutoMapper, EquipmentDiagnosisReportAuto> implements IDiagnosisReportAutoService {

    @Autowired
    private DiagnosisReportAutoMapper autoMapper;

    @Override
    public PageResult<DiagnosisReportAutoDto> pageDto(DiagnosisReportAutoQueryParam queryParam) {
        LambdaQueryWrapper<EquipmentDiagnosisReportAuto> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), EquipmentDiagnosisReportAuto::getName, queryParam.getName());
        wrapper.orderByDesc(EquipmentDiagnosisReportAuto::getUpdateTime);
        PageResult<EquipmentDiagnosisReportAuto> result = this.page(queryParam, wrapper);
        return Optional.ofNullable(PageResult.<DiagnosisReportAutoDto>builder()
                        .records(CopyDataUtil.copyList(result.getRecords(), DiagnosisReportAutoDto.class))
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    @Override
    public Boolean editByParam(DiagnosisReportAutoDto dto) {
        EquipmentDiagnosisReportAuto entity = CopyDataUtil.copyObject(dto, EquipmentDiagnosisReportAuto.class);
        return saveOrUpdate(entity);
    }

    @Override
    public List<DiagnosisReportAutoDto> getList(Boolean enable) {
        LambdaQueryWrapper<EquipmentDiagnosisReportAuto> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(null != enable, EquipmentDiagnosisReportAuto::getEnable, enable);
        return CopyDataUtil.copyList(autoMapper.selectList(wrapper), DiagnosisReportAutoDto.class);
    }

    @Override
    public Boolean deleteByIds(List<String> ids) {
        LambdaQueryWrapper<EquipmentDiagnosisReportAuto> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EquipmentDiagnosisReportAuto::getId, ids);
        return remove(wrapper);
    }
}
