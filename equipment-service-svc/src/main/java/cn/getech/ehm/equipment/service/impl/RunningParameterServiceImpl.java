package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.BasicLibraryFieldDto;
import cn.getech.ehm.equipment.dto.runningparam.ParameterVariableDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterListDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterQueryParam;
import cn.getech.ehm.equipment.enmu.StructureParamType;
import cn.getech.ehm.equipment.enmu.StructureSourceType;
import cn.getech.ehm.equipment.entity.RunningParameter;
import cn.getech.ehm.equipment.entity.RunningParameterVariable;
import cn.getech.ehm.equipment.handler.CommonGetHandler;
import cn.getech.ehm.equipment.mapper.RunningParameterMapper;
import cn.getech.ehm.equipment.service.IBasicLibraryFieldService;
import cn.getech.ehm.equipment.service.IRunningParameterService;
import cn.getech.ehm.equipment.service.IRunningParameterVariableService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 零部件运行参数 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class RunningParameterServiceImpl extends BaseServiceImpl<RunningParameterMapper, RunningParameter> implements IRunningParameterService {

    @Autowired
    private RunningParameterMapper runningParameterMapper;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private IRunningParameterVariableService variableService;
    @Autowired
    private IBasicLibraryFieldService fieldService;
    @Autowired
    private CommonGetHandler commonGetHandler;

    @Override
    public List<RunningParameterListDto> getList(RunningParameterQueryParam queryParam) {
        List<RunningParameterListDto> runningParameterDtos = runningParameterMapper.getList(queryParam);
        if(CollectionUtils.isNotEmpty(runningParameterDtos)){
            Map<String, DictionaryItemDto> unitMap = commonGetHandler.getDictMap("unit");
            Map<String, String> basicLibraryUnitMap = new HashMap<>();
            if(StringUtils.isNotBlank(queryParam.getBasicLibraryId())) {
                basicLibraryUnitMap = fieldService.getCodeUnitMap(queryParam.getBasicLibraryId());
            }
            for(RunningParameterListDto dto : runningParameterDtos){
                dto.setSourceTypeName(StructureSourceType.getNameByValue(dto.getSourceType()));
                dto.setParamTypeName(StructureParamType.getNameByValue(dto.getParamType()));
                if(dto.getParamType() == StructureParamType.BASIC.getValue()){
                    dto.setUnit(basicLibraryUnitMap.get(dto.getCode()));
                }
                if(StringUtils.isNotBlank(dto.getUnit())){
                    DictionaryItemDto dictionaryItemDto = unitMap.get(dto.getUnit());
                    dto.setUnitName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                }
            }
        }

        return runningParameterDtos;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(RunningParameterDto dto) {
        if(check(null, dto.getName(), dto.getSparePartsId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        RunningParameter runningParameter = CopyDataUtil.copyObject(dto, RunningParameter.class);
        runningParameter.setSort(runningParameterMapper.getMaxSort(dto.getSparePartsId()) + 1);
        Boolean flag = save(runningParameter);
        if(null != dto.getSourceType() && dto.getSourceType() == StructureSourceType.COUNT.getValue()) {
            variableService.saveOrUpdateByParam(dto.getVariables(), runningParameter.getId());
        }
        return flag;
    }

    private boolean check(String id, String name, String sparePartsId){
        LambdaQueryWrapper<RunningParameter> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(id)){
            wrapper.ne(RunningParameter::getId, id);
        }
        wrapper.eq(RunningParameter::getSparePartsId, sparePartsId);
        wrapper.eq(RunningParameter::getName, name);
        return runningParameterMapper.selectCount(wrapper) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(RunningParameterDto dto) {
        if(check(dto.getId(), dto.getName(), dto.getSparePartsId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        RunningParameter runningParameter = CopyDataUtil.copyObject(dto, RunningParameter.class);
        if(dto.getParamType() == StructureParamType.BASIC.getValue()){
            runningParameter.setSourceType(0);
        }
        Boolean flag = updateById(runningParameter);
        RunningParameter old = (RunningParameter) this.getById(dto.getId());
        if(old.getSourceType() == StructureSourceType.COUNT.getValue()) {
            //删除旧详情
            variableService.deleteByParamIds(Collections.singletonList(dto.getId()));
        }
        if(null != runningParameter.getSourceType() && runningParameter.getSourceType() == StructureSourceType.COUNT.getValue()) {
            variableService.saveOrUpdateByParam(dto.getVariables(), runningParameter.getId());
        }
        return flag;
    }

    @Override
    public RunningParameterDto getDtoById(String id){
        RunningParameterDto runningParameterDto = CopyDataUtil.copyObject(this.getById(id), RunningParameterDto.class);
        if(runningParameterDto.getParamType() != StructureParamType.BASIC.getValue() && runningParameterDto.getSourceType() == StructureSourceType.COUNT.getValue()){
            List<String> runningParameterIds = new ArrayList<>();
            runningParameterIds.add(runningParameterDto.getId());
            runningParameterDto.setVariables(variableService.getList(runningParameterIds));
        }
        return runningParameterDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteById(String id) {
        variableService.deleteByParamIds(Collections.singletonList(id));
        return removeById(id);
    }

    @Override
    public List<RunningParameter> getEntitiesByCategoryId(String sparePartsCategoryId){
        LambdaQueryWrapper<RunningParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RunningParameter::getSparePartsId, sparePartsCategoryId);
        wrapper.orderByAsc(RunningParameter::getSort);
        return runningParameterMapper.selectList(wrapper);
    }

    @Override
    public List<String> getBasicLibraryCodes(String sparePartsId){
        LambdaQueryWrapper<RunningParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RunningParameter::getSparePartsId, sparePartsId);
        wrapper.eq(RunningParameter::getParamType, StructureParamType.BASIC.getValue());
        wrapper.select(RunningParameter::getCode);
        return runningParameterMapper.selectList(wrapper).stream().map(RunningParameter::getCode).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean editBasicLibraryDefaultParam(String sparePartsId, String basicLibraryId){
        LambdaQueryWrapper<RunningParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RunningParameter::getSparePartsId, sparePartsId);
        List<String> ids = runningParameterMapper.selectList(wrapper).stream().map(RunningParameter::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ids)){
            this.removeByIds(ids);
            variableService.deleteByParamIds(ids);
        }

        List<BasicLibraryFieldDto> fieldDtos = fieldService.getList(basicLibraryId, false, null);
        if(CollectionUtils.isNotEmpty(fieldDtos)){
            List<RunningParameter> runningParameters = new ArrayList<>();
            Integer sort = runningParameterMapper.getMaxSort(sparePartsId) + 1;
            for(BasicLibraryFieldDto fieldDto : fieldDtos){
                if(fieldDto.getDefaultParam()) {
                    RunningParameter runningParameter = new RunningParameter();
                    runningParameter.setSparePartsId(sparePartsId);
                    runningParameter.setCode(fieldDto.getFieldName());
                    runningParameter.setName(fieldDto.getName());
                    runningParameter.setUnit(fieldDto.getUnit());
                    runningParameter.setParamType(StructureParamType.BASIC.getValue());
                    runningParameter.setSort(sort++);
                    runningParameters.add(runningParameter);
                }
            }
            if(CollectionUtils.isNotEmpty(runningParameters)){
                this.saveBatch(runningParameters);
            }
        }

        return true;
    }

    @Override
    public Map<String, Long> getFeatureCountMap(List<String> sparePartsIds){
        LambdaQueryWrapper<RunningParameter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(RunningParameter::getSparePartsId, sparePartsIds);
        wrapper.eq(RunningParameter::getParamType, StructureParamType.FEATURE.getValue());
        wrapper.select(RunningParameter::getId, RunningParameter::getSparePartsId);
        return runningParameterMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(RunningParameter::getSparePartsId, Collectors.counting()));
    }

    @Override
    public Boolean copy(String oldSparePartsId, String newSparePartsId){
        List<RunningParameter> runningParameters = this.getEntitiesByCategoryId(oldSparePartsId);
        if(CollectionUtils.isNotEmpty(runningParameters)){
            List<RunningParameter> newRunningParameters = new ArrayList<>(runningParameters.size());
            List<RunningParameterVariable> newVariables = new ArrayList<>();

            //类型为计算的运行参数
            List<String> runningParameterIds = runningParameters.stream().filter(dto -> dto.getSourceType() == StructureSourceType.COUNT.getValue())
                    .map(RunningParameter::getId).distinct().collect(Collectors.toList());
            Map<String, List<ParameterVariableDto>> variableMap = variableService.getList(runningParameterIds).stream().collect(Collectors.groupingBy(ParameterVariableDto::getRunningParameterId));

            //运行参数id与部件参数id映射
            Map<String, String> idMap = new HashMap<>();
            for(RunningParameter runningParameter : runningParameters){
                //首先给参数设置对应的新id
                String id = UUID.randomUUID().toString().replace("-","");
                idMap.put(runningParameter.getId(), id);
            }

            for(RunningParameter runningParameter : runningParameters){
                RunningParameter newRunningParameter = CopyDataUtil.copyObject(runningParameter, RunningParameter.class);
                String newId = idMap.get(runningParameter.getId());
                newRunningParameter.setId(newId);
                newRunningParameter.setSparePartsId(newSparePartsId);
                newRunningParameters.add(newRunningParameter);
                List<ParameterVariableDto> parameterVariableDtos = variableMap.get(runningParameter.getId());
                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(parameterVariableDtos)){
                    for(ParameterVariableDto parameterVariableDto : parameterVariableDtos){
                        RunningParameterVariable newVariable = CopyDataUtil.copyObject(parameterVariableDto, RunningParameterVariable.class);
                        newVariable.setId(null);
                        newVariable.setRunningParameterId(newId);
                        if(parameterVariableDto.getSourceType() == 2){
                            //引用参数类型，需要修改绑定id
                            newVariable.setValue(idMap.get(parameterVariableDto.getValue()));
                        }
                        newVariables.add(newVariable);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(newRunningParameters)){
                saveBatch(newRunningParameters);
            }
            if(CollectionUtils.isNotEmpty(newVariables)){
                variableService.saveBatch(newVariables);
            }
        }
        return true;
    }
}
