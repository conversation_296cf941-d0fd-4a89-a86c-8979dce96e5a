package cn.getech.ehm.equipment.schedule;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.equipment.service.IDiagnosisReportAutoService;
import cn.getech.ehm.equipment.service.IDiagnosisReportV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;

/**
 * 定时生产智能诊断报告
 */
@Slf4j
@Component
public class DiagnosisReportAutoSchedule {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IDiagnosisReportAutoService autoService;
    @Autowired
    private IDiagnosisReportV2Service reportV2Service;

    private final static String PLAN_RELEASE_TASK_LOCK="EHM:DIAGNOSIS_REPORT_LOCK";

    @Scheduled(cron = "0 0 2 1 * ?")
    public void releaseReport() {
        log.info("定时生成智能诊断报告开始");
        if (!redisTemplate.opsForValue().setIfAbsent(PLAN_RELEASE_TASK_LOCK,"1",30, TimeUnit.SECONDS)){
            log.info("锁竞争失败，跳过释放");
            return;
        }
        UserContextHolder.defaultContext();
        reportV2Service.saveAuto(autoService.getList(true));

        log.info("定时生成智能诊断报告结束");
    }
}
