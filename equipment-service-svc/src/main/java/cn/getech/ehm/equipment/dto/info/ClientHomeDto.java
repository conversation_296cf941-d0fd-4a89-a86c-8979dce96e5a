package cn.getech.ehm.equipment.dto.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.List;


/**
 * 客户端工作台dto
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "ClientHomeDto", description = "客户端工作台dto")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClientHomeDto {
    @ApiModelProperty(value = "设备IDs")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "类型(0:总数量1接入iot数量)")
    private Integer type;

    @ApiModelProperty(value = "查询日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date beginTime;

    @ApiModelProperty(value = "查询日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "选中的客户id")
    private String customerId;
}