package cn.getech.ehm.equipment.dto.spareparts;

import cn.getech.ehm.equipment.dto.info.EquipmentPropDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 设备零件 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "SparePartsDto", description = "设备零件返回数据模型")
public class SparePartsDto {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型(1轴承2齿轮)")
    private Integer type;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    @ApiModelProperty(value = "轴承厂家")
    private String factoryName;

    @ApiModelProperty(value = "型号")
    private String modelName;

    @ApiModelProperty(value = "传动比")
    private String proportion;

    @ApiModelProperty(value = "最大转速")
    private Double maxSpeed;

    @ApiModelProperty(value = "最小转速")
    private Double minSpeed;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "扩展信息")
    private List<EquipmentPropDto> propDtos;

    @ApiModelProperty(value = "数据类型(1设备2零件)")
    private Integer bomType = 2;

}