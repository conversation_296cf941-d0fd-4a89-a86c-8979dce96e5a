package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.runningparam.RunningParameterDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterListDto;
import cn.getech.ehm.equipment.dto.runningparam.RunningParameterQueryParam;
import cn.getech.ehm.equipment.entity.RunningParameter;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 零部件参数 服务类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IRunningParameterService extends IBaseService<RunningParameter> {

    /**
     * 保存
     * @return
     */
    boolean saveByParam(RunningParameterDto dto);

    /**
     * 更新
     *
     */
    boolean updateByParam(RunningParameterDto dto);

    /**
     * 根据id获取dto
     * @param id
     * @return
     */
    RunningParameterDto getDtoById(String id);

    /**
     * 删除
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 获取列表
     * @return
     */
    List<RunningParameterListDto> getList(RunningParameterQueryParam queryParam);

    /**
     * 根据零部件类型id获取实体集合
     * @param sparePartsCategoryId
     * @return
     */
    List<RunningParameter> getEntitiesByCategoryId(String sparePartsCategoryId);

    /**
     * 获取基本库编码
     * @return
     */
    List<String> getBasicLibraryCodes(String sparePartsId);

    /**
     * 编辑结构默认基础参数
     */
    Boolean editBasicLibraryDefaultParam(String sparePartsId, String basicLibraryId);

    /**
     * 获取结构特征参数数量
     */
    Map<String, Long> getFeatureCountMap(List<String> sparePartsIds);

    /**
     * 复制
     */
    Boolean copy(String oldSparePartsId, String newSparePartsId);

}