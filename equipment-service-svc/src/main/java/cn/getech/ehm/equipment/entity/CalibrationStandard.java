package cn.getech.ehm.equipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型校准
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calibration_standard")
public class CalibrationStandard extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 类别ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 名称
     */
    @TableField("title")
    private String title;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 对比值
     */
    @TableField("value")
    private String value;

    /**
     * 允许误差
     */
    @TableField("error")
    private String error;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
