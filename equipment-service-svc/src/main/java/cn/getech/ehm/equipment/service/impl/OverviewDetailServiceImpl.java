package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.location.EquipmentLocationDto;
import cn.getech.ehm.equipment.dto.overview.OverviewDetailCardDto;
import cn.getech.ehm.equipment.dto.overview.OverviewDetailDto;
import cn.getech.ehm.equipment.dto.overview.OverviewModelDto;
import cn.getech.ehm.equipment.entity.OverviewDetail;
import cn.getech.ehm.equipment.mapper.OverviewDetailMapper;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentLocationService;
import cn.getech.ehm.equipment.service.IOverviewDetailService;
import cn.getech.ehm.equipment.service.IOverviewModelService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 概览图详情 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Slf4j
@Service
public class OverviewDetailServiceImpl extends BaseServiceImpl<OverviewDetailMapper, OverviewDetail> implements IOverviewDetailService {

    @Autowired
    private OverviewDetailMapper detailMapper;
    @Autowired
    private IOverviewModelService modelService;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private IEquipmentLocationService locationService;

    @Override
    public Boolean editDetailName(String relationKey, Integer type, String name) {
        OverviewDetail overviewDetail = this.getEntity(relationKey, type);
        overviewDetail.setName(name);
        return saveOrUpdate(overviewDetail);
    }

    @Override
    public Boolean editDetailCard(OverviewDetailCardDto dto) {
        OverviewDetail overviewDetail = this.getEntity(dto.getRelationKey(), dto.getType());
        overviewDetail.setCardName(dto.getCardName());
        overviewDetail.setCardNameEnable(dto.getCardNameEnable());
        overviewDetail.setCardType(dto.getCardType());
        overviewDetail.setCardUrl(dto.getCardUrl());
        overviewDetail.setStudioType(dto.getStudioType());
        if(dto.getCardType() == 1) {
            //组态名称、缩略图
            overviewDetail.setCardStudioName(dto.getCardStudioName());
            overviewDetail.setCardStudioPic(dto.getCardStudioPic());
        }else{
            overviewDetail.setCardStudioName("");
            overviewDetail.setCardStudioPic("");
        }

        return saveOrUpdate(overviewDetail);
    }

    @Override
    public Boolean clearDetailCard(String relationKey, Integer type){
        LambdaUpdateWrapper<OverviewDetail> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(OverviewDetail::getCardType, 0);
        wrapper.set(OverviewDetail::getStudioType, null);
        wrapper.set(OverviewDetail::getCardUrl, null);
        wrapper.set(OverviewDetail::getCardStudioName, null);
        wrapper.set(OverviewDetail::getCardStudioPic, null);
        wrapper.eq(OverviewDetail::getRelationKey, relationKey);
        wrapper.eq(OverviewDetail::getType, type);
        update(wrapper);
        return true;
    }

    private OverviewDetail getEntity(String relationKey, Integer type){
        LambdaQueryWrapper<OverviewDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(OverviewDetail::getRelationKey, relationKey);
        wrapper.eq(OverviewDetail::getType, type);
        OverviewDetail overviewDetail = detailMapper.selectOne(wrapper);
        if(null == overviewDetail){
            overviewDetail = new OverviewDetail();
            overviewDetail.setRelationKey(relationKey);
            overviewDetail.setType(type);
            overviewDetail.setCardNameEnable(false);
            overviewDetail.setCardType(0);
        }
        return overviewDetail;
    }

    @Override
    public OverviewDetailCardDto getDetailCard(String relationKey, Integer type) {
        OverviewDetailCardDto cardDto = CopyDataUtil.copyObject(this.getEntity(relationKey, type), OverviewDetailCardDto.class);
        if(StringUtils.isBlank(cardDto.getCardName())){
            cardDto.setCardName(type == 1 ? "位置概览图" : "设备概览图");
        }
        if(null != cardDto.getCardType() && cardDto.getCardType() == StaticValue.ZERO && type == 2){
            //获取设备图片
            cardDto.setCardUrl(equipmentInfoService.equipmentPicUrl(relationKey));
        }
        return cardDto;
    }

    @Override
    public OverviewDetailDto getDetailDto(String relationKey, Integer type) {
        OverviewDetailDto overviewDetailDto = new OverviewDetailDto();
        if(null != type) {
            String modelRelationKey = null;
            if (type == 1) {
                //位置类型
                EquipmentLocationDto locationDto = locationService.getDtoById(relationKey);
                modelRelationKey = null != locationDto ? locationDto.getType().toString() : null;
            } else {
                //类型id
                modelRelationKey = equipmentInfoService.getCategoryId(relationKey);
            }
            if (StringUtils.isNotBlank(modelRelationKey)) {
                OverviewModelDto overviewModelDto = modelService.getModelDto(modelRelationKey, type);

                overviewDetailDto = CopyDataUtil.copyObject(this.getEntity(relationKey, type), OverviewDetailDto.class);
                if (StringUtils.isBlank(overviewDetailDto.getName())) {
                    overviewDetailDto.setName(overviewModelDto.getName());
                }
                overviewDetailDto.setEnable(overviewModelDto.getEnable());
                overviewDetailDto.setModelType(overviewModelDto.getModelType());
                overviewDetailDto.setCardList(overviewModelDto.getCardList());
            } else {
                overviewDetailDto.setEnable(false);
            }
        }
        return overviewDetailDto;
    }
}
