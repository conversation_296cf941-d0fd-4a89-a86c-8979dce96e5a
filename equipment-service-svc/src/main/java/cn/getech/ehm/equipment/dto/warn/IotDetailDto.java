package cn.getech.ehm.equipment.dto.warn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备告警 新增参数
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "IotDetailDto", description = "iot回调详情dto")
public class IotDetailDto{
    @ApiModelProperty(value = "iot告警id")
    private String alarmId;

    @ApiModelProperty(value = "iot关联设备id")
    private String deviceId;

    @ApiModelProperty(value = "告警级别(1预警2报警3离线4危险)")
    private Integer alarmType;

    @ApiModelProperty(value = "告警信息")
    private String alarmInfo;

    @ApiModelProperty(value = "告警状态(1告警中2持续报警3结束)")
    private Integer alarmStatus;

    @ApiModelProperty(value = "告警时间")
    private Long startTs;

    @ApiModelProperty(value = "修复时间")
    private Long endTs;

    @ApiModelProperty(value = "设备状态(0运行/在线1故障2未激活/离线)")
    private Integer deviceStatus;

    @ApiModelProperty(value = "网络状态（网络状态0-在线 1-离线 2-未接入）")
    private Integer deviceNetStatus;

    //[{\"param\":\"Slot14_FAULT_A\",\"part\":\"整体\"}]
    @ApiModelProperty(value = "告警规则")
    private String paramLists;
}