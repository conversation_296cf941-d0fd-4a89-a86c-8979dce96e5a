package cn.getech.ehm.equipment.utils;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.entity.EquipmentCategory;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentLocation;
import cn.getech.poros.device.dto.infoclient.InfoDataSyncDto;
import cn.getech.poros.device.dto.locationclient.LocationDataSyncDto;
import cn.getech.poros.device.dto.typeclient.TypeDataSyncDto;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022-03-04 09:48:50
 **/
public class PorosDeviceUtils {

    public static String SYSTEM_LABEL = "ehm";

    public static EquipmentLocation locationDataConvertor(LocationDataSyncDto locationDataSyncDto, String rootId){
        EquipmentLocation equipmentLocation = new EquipmentLocation();
        equipmentLocation.setId(locationDataSyncDto.getId());
        equipmentLocation.setCode(locationDataSyncDto.getCode());
        equipmentLocation.setLayerCode(rootId + StringPool.SLASH + locationDataSyncDto.getIdPath());
        equipmentLocation.setName(locationDataSyncDto.getName());
        equipmentLocation.setType(4);
        equipmentLocation.setParentId(StringUtils.isNotBlank(locationDataSyncDto.getParentId()) ? locationDataSyncDto.getParentId() : rootId);
        equipmentLocation.setCreateBy(locationDataSyncDto.getCreateBy());
        equipmentLocation.setCreateTime(locationDataSyncDto.getCreateTime());
        equipmentLocation.setUpdateBy(locationDataSyncDto.getUpdateBy());
        equipmentLocation.setUpdateTime(locationDataSyncDto.getUpdateTime());
        equipmentLocation.setTenantId(locationDataSyncDto.getTenantId());
        equipmentLocation.setDeleted(locationDataSyncDto.getDeleted() ? 1 : 0);
        return equipmentLocation;
    }

    public static EquipmentCategory categoryDataConvertor(TypeDataSyncDto typeDataSyncDto){
        EquipmentCategory equipmentCategory = new EquipmentCategory();
        equipmentCategory.setId(typeDataSyncDto.getId());
        equipmentCategory.setCode(typeDataSyncDto.getCode());
        equipmentCategory.setName(typeDataSyncDto.getName());
        equipmentCategory.setLayerCode(typeDataSyncDto.getIdPath());
        equipmentCategory.setParentId(StringUtils.isNotBlank(typeDataSyncDto.getParentId()) ? typeDataSyncDto.getParentId() : "0");
        equipmentCategory.setCreateBy(typeDataSyncDto.getCreateBy());
        equipmentCategory.setCreateTime(typeDataSyncDto.getCreateTime());
        equipmentCategory.setUpdateBy(typeDataSyncDto.getUpdateBy());
        equipmentCategory.setUpdateTime(typeDataSyncDto.getUpdateTime());
        equipmentCategory.setTenantId(typeDataSyncDto.getTenantId());
        equipmentCategory.setDeleted(typeDataSyncDto.getDeleted() ? 1 : 0);
        return equipmentCategory;
    }

    public static EquipmentInfo infoDataConvertor(InfoDataSyncDto infoDataSyncDto){
        EquipmentInfo equipmentInfo = new EquipmentInfo();
        equipmentInfo.setId(infoDataSyncDto.getId());
        equipmentInfo.setCode(infoDataSyncDto.getDeviceCode());
        equipmentInfo.setName(infoDataSyncDto.getDeviceName());
        equipmentInfo.setTenantId(infoDataSyncDto.getTenantId());
        equipmentInfo.setLocationId(infoDataSyncDto.getLocationId());
        equipmentInfo.setCategoryId(infoDataSyncDto.getTypeId());
        equipmentInfo.setParentId(infoDataSyncDto.getLocationId());
        equipmentInfo.setLayerCode(infoDataSyncDto.getId());
        equipmentInfo.setType(StaticValue.ONE);
        equipmentInfo.setCreateBy(infoDataSyncDto.getCreateBy());
        equipmentInfo.setCreateTime(infoDataSyncDto.getCreateTime());
        equipmentInfo.setUpdateBy(infoDataSyncDto.getUpdateBy());
        equipmentInfo.setUpdateTime(infoDataSyncDto.getUpdateTime());
        equipmentInfo.setCategoryId(infoDataSyncDto.getTypeId());
        equipmentInfo.setDeleted(infoDataSyncDto.getDeleted() ? 1 : 0);
        return equipmentInfo;
    }

}
