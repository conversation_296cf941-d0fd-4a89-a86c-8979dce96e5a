package cn.getech.ehm.equipment.dto.iot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 组态修改数据模型
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudioEditParam", description = "组态修改数据模型")
public class StudioEditParam {

    @ApiModelProperty(value = "组态id", required = true)
    @NotBlank(message = "组态id不能为空")
    private String studioId;

    @ApiModelProperty(value = "设备id", required = true)
    private String equipmentId;

    @ApiModelProperty(value = "1-手机端应用2-PC端应用", required = true)
    private String appType;

}