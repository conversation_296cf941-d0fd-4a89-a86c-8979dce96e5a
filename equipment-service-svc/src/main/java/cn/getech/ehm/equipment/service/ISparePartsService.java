package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.spareparts.SparePartsAddParam;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsDto;
import cn.getech.ehm.equipment.dto.spareparts.SparePartsEditParam;
import cn.getech.ehm.equipment.entity.EquipmentSpareParts;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 设备零件
 *
 * <AUTHOR>
 * @since 2020-12-04
 */
public interface ISparePartsService extends IBaseService<EquipmentSpareParts> {

        /**
         * 保存
         * @param addParam
         * @return
         */
        String saveByParam(SparePartsAddParam addParam);

        /**
         * 批量新增
         * @param addParams
         * @return
         */
        boolean saveBatchParams(List<SparePartsAddParam> addParams);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        SparePartsDto getDtoById(String id);

        /**
         * 更新
         * @param editParam
         */
        boolean updateByParam(SparePartsEditParam editParam);

        /**
         * 根据id删除
         * @param id
         * @return
         */
        Boolean deleteById(String id);

        /**
         * 根据设备ids获取零件map
         * @param equipmentIds
         * @return
         */
        Map<String, List<SparePartsDto>> getMapByInfoIds(List<String> equipmentIds);

        /**
         * 根据设备id获取零件
         * @param equipmentId
         * @return
         */
        List<SparePartsDto> getListByInfoId(String equipmentId);

        /**
         * 过滤拥有零件的设备ids
         * @param equipmentIds
         * @return
         */
        List<String> havePartsInfoIds(List<String> equipmentIds);
}