package cn.getech.ehm.equipment.dto.overview;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备卡片运行历程详情
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentProgressDetailDto", description = "设备卡片运行历程详情")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentProgressDetailDto {

    @ApiModelProperty(value = "状态")
    private Integer status = StaticValue.ZERO;

    @ApiModelProperty(value = "日期")
    private String name;

    @ApiModelProperty(value = "状态名称")
    private String description;

    @ApiModelProperty(value = "颜色")
    private String color ;

    @ApiModelProperty(value = "时长(H)")
    private Double hourTime ;

    @ApiModelProperty(value = "比例")
    private Double rate ;

    @ApiModelProperty(value = "开始日期")
    private Long beginTime;

    @ApiModelProperty(value = "开始日期")
    private Long endTime;
}