package cn.getech.ehm.equipment.dto.category;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备类型基础数据
 */
@Data
@ApiModel(value = "CategorySummaryDto", description = "设备类型基础数据")
public class CategorySummaryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "OEE计算")
    private Boolean oeeOpen = false;

    @ApiModelProperty(value = "设备状态开关")
    private Boolean statusParamOpen = false;
}