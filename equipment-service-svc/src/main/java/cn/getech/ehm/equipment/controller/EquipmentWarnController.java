package cn.getech.ehm.equipment.controller;

import cn.getech.ehm.equipment.dto.warn.*;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.equipment.service.IEquipmentWarnService;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备告警控制器
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/equipmentWarn")
@Api(tags = "设备告警服务接口")
public class EquipmentWarnController {

    @Autowired
    private IEquipmentWarnService equipmentWarnService;

    /**
     * 分页获取IOT设备告警列表
     */
    @ApiOperation("分页获取IOT设备告警列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<EquipmentWarnDto>> pageList(@RequestBody @Valid EquipmentWarnQueryParam equipmentWarnQueryParam){
        return RestResponse.ok(equipmentWarnService.pageDto(equipmentWarnQueryParam));
    }

    /**
     * 新增设备告警
     */
    @ApiOperation("新增设备告警")
    @AuditLog(title = "设备告警",desc = "新增设备告警",businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody @Valid EquipmentWarnAddParam equipmentWarnAddParam) {
        return RestResponse.ok(equipmentWarnService.saveByParam(equipmentWarnAddParam));
    }

    /**
     * 修改设备告警
     */
    @ApiOperation(value="修改设备告警")
    @AuditLog(title = "设备告警",desc = "修改设备告警",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public RestResponse<Boolean> update(@RequestBody @Valid EquipmentWarnEditParam equipmentWarnEditParam) {
        return RestResponse.ok(equipmentWarnService.updateByParam(equipmentWarnEditParam));
    }

    /**
     * 导出设备告警列表
     */
    @ApiOperation(value = "导出设备告警列表")
    @AuditLog(title = "设备告警",desc = "导出设备告警列表",businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void excelExport(@RequestBody @Valid EquipmentWarnQueryParam equipmentWarnQueryParam, HttpServletResponse response){
//        List<EquipmentWarnExcel> excels = equipmentWarnService.exportExcel(equipmentWarnQueryParam);
        equipmentWarnQueryParam.setLimit(10000);
        PageResult<EquipmentWarnDto> pageResult =  equipmentWarnService.pageDto(equipmentWarnQueryParam);
        ExcelUtils<EquipmentWarnDto> util = new ExcelUtils<>(EquipmentWarnDto.class);

        util.exportExcel(pageResult.getRecords(), "设备告警列表",response);
    }
}
