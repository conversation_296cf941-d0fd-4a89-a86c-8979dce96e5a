package cn.getech.ehm.equipment.dto.location;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备位置导入
 *  父节点/子节点/子节点
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "LocationCWExcel", description = "CW设备位置导入")
public class LocationCWExcel {

    @ApiModelProperty(value = "设备位置名称")
    @Excel(name="位置",cellType = Excel.ColumnType.STRING )
    private String name;

}