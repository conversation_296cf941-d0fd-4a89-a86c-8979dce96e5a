package cn.getech.ehm.equipment.dto.info;

import cn.getech.ehm.equipment.dto.special.EquipmentSpecialDto;
import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 设备表 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentSortEditParam", description = "EquipmentSortEditParam")
public class EquipmentSortEditParam extends ApiParam {
    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "排序")
    private Integer sort;


}
