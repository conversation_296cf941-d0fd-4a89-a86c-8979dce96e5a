package cn.getech.ehm.equipment.dto.warn;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 设备告警 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "EquipmentWarnDto", description = "设备告警返回数据模型")
public class EquipmentWarnDto{

    @ApiModelProperty(value = "告警级别(0未知,1预警2报警)")
    @Excel(name="告警级别",cellType = Excel.ColumnType.STRING, readConverterExp = "0=未知,1=预警,2=报警" )
    private Integer level;

    @ApiModelProperty(value = "设备编码")
    @Excel(name="设备编号",cellType = Excel.ColumnType.STRING )
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    @Excel(name="设备名称",cellType = Excel.ColumnType.STRING )
    private String equipmentName;

    @ApiModelProperty(value = "卡槽名")
    @Excel(name="卡槽名",cellType = Excel.ColumnType.STRING )
    private String fixture;

    @ApiModelProperty(value = "卡件名")
    @Excel(name="卡件名",cellType = Excel.ColumnType.STRING )
    private String frame;

    @ApiModelProperty(value = "客户名称")
    @Excel(name="客户",cellType = Excel.ColumnType.STRING )
    private String customName;

    @ApiModelProperty(value = "设备类型名称")
    @Excel(name="设备类型",cellType = Excel.ColumnType.STRING )
    private String categoryName;

    @ApiModelProperty(value = "告警信息")
    @Excel(name="告警信息",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "告警状态(1告警中,3结束)")
    @Excel(name="告警状态",cellType = Excel.ColumnType.STRING, readConverterExp = "1=告警中,3=结束 ")
    private Integer status;

    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name="告警时间",dateFormat = "yyyy-MM-dd HH:mm:ss",cellType = Excel.ColumnType.STRING )
    private Date warnTime;

    @ApiModelProperty(value = "修复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name="修复时间",dateFormat = "yyyy-MM-dd HH:mm:ss",cellType = Excel.ColumnType.STRING )
    private Date repairTime;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "iot关联设备id")
    private String iotEquipmentId;

    @ApiModelProperty(value = "告警时间")
    private String warnTimeString;

    @ApiModelProperty(value = "修复时间")
    private String repairTimeString;
}