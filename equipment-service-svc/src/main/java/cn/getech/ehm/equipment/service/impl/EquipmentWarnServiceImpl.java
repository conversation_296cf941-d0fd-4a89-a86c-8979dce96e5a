package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.dto.CheckEquipmentIdDto;
import cn.getech.ehm.equipment.dto.WarnAppQueryParam;
import cn.getech.ehm.equipment.dto.WarnFirstPageDto;
import cn.getech.ehm.equipment.dto.warn.*;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentWarn;
import cn.getech.ehm.equipment.mapper.EquipmentWarnMapper;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentWarnService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.getech.poros.permission.dto.SecStaffUidParam;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.client.SystemClient;
import cn.getech.ehm.system.dto.CustomerDto;
import cn.getech.ehm.system.dto.EquipmentCustomerDto;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.system.dto.notify.EmailNotify;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import cn.getech.ehm.system.dto.notify.PushNotify;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备告警 服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Slf4j
@Service
public class EquipmentWarnServiceImpl extends BaseServiceImpl<EquipmentWarnMapper, EquipmentWarn> implements IEquipmentWarnService {

    @Autowired
    private EquipmentWarnMapper equipmentWarnMapper;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private NotifyClient notifyClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private PorosSecGrantClient porosSecGrantClient;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Value("${kjs.manager:poros-manage_manager}")
    private String kjsManager;

    @Override
    public PageResult<EquipmentWarnDto> pageDto(EquipmentWarnQueryParam equipmentWarnQueryParam) {
        List<String> equipmentIds = equipmentWarnQueryParam.getEquipmentIds();
        //客户端自动过滤当前用户设备
        if(null != equipmentWarnQueryParam.getIsClient() && equipmentWarnQueryParam.getIsClient().intValue() == StaticValue.ONE) {
            RestResponse<List<String>> restResponse = systemClient.getCurrentInfoIds();
            if (restResponse.isOk()) {
                List<String> clientEquipmentIds = restResponse.getData();
                if (CollectionUtils.isEmpty(clientEquipmentIds)) {
                    return new PageResult<>();
                }
                if (CollectionUtils.isNotEmpty(equipmentIds)) {
                    List<String> intersection = equipmentIds.stream().filter(item -> clientEquipmentIds.contains(item)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(intersection)) {
                        equipmentIds = intersection;
                    } else {
                        return new PageResult<>();
                    }
                } else {
                    equipmentIds = clientEquipmentIds;
                }
            } else {
                log.error("获取客户绑定设备信息失败," + restResponse.getMsg());
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
        }
        //根据客户name过滤设备
        if (StringUtils.isNotBlank(equipmentWarnQueryParam.getCustomerName())) {
            if (StringUtils.isNotBlank(equipmentWarnQueryParam.getInnerCustomerName())) {
                RestResponse<List<String>> response = systemClient.getEquipmentIdsByTwoCustomerNames(equipmentWarnQueryParam.getCustomerName(),equipmentWarnQueryParam.getInnerCustomerName());
                if (response.isOk()) {
                    List<String> customerNameInfoIds = response.getData();
                    if (CollectionUtils.isNotEmpty(customerNameInfoIds)) {
                        //取交集
                        if (CollectionUtils.isNotEmpty(equipmentIds)) {
                            List<String> intersection = equipmentIds.stream().filter(item -> customerNameInfoIds.contains(item)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(intersection)) {
                                equipmentIds = intersection;
                            }
                        } else {
                            equipmentIds = customerNameInfoIds;
                        }
                    }else{
                        return new PageResult<>();
                    }
                } else {
                    log.error("查询客户信息失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
                }
            }else{
                RestResponse<List<String>> response = systemClient.getEquipmentIdsByCustomerNames(equipmentWarnQueryParam.getCustomerName());
                if (response.isOk()) {
                    List<String> customerNameInfoIds = response.getData();
                    if (CollectionUtils.isNotEmpty(customerNameInfoIds)) {
                        //取交集
                        if (CollectionUtils.isNotEmpty(equipmentIds)) {
                            List<String> intersection = equipmentIds.stream().filter(item -> customerNameInfoIds.contains(item)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(intersection)) {
                                equipmentIds = intersection;
                            }
                        } else {
                            equipmentIds = customerNameInfoIds;
                        }
                    }else{
                        return new PageResult<>();
                    }
                } else {
                    log.error("查询客户信息失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
                }
            }
        }else{
            if (StringUtils.isNotBlank(equipmentWarnQueryParam.getInnerCustomerName())) {
                RestResponse<List<String>> response = systemClient.getEquipmentIdsByCustomerNames(equipmentWarnQueryParam.getInnerCustomerName());
                if (response.isOk()) {
                    List<String> customerNameInfoIds = response.getData();
                    if (CollectionUtils.isNotEmpty(customerNameInfoIds)) {
                        //取交集
                        if (CollectionUtils.isNotEmpty(equipmentIds)) {
                            List<String> intersection = equipmentIds.stream().filter(item -> customerNameInfoIds.contains(item)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(intersection)) {
                                equipmentIds = intersection;
                            }
                        } else {
                            equipmentIds = customerNameInfoIds;
                        }
                    }else{
                        return new PageResult<>();
                    }
                } else {
                    log.error("查询客户信息失败");
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
                }
            }
        }
        equipmentWarnQueryParam.setEquipmentIds(equipmentIds);

        Page<EquipmentWarnDto> page = new Page<>(equipmentWarnQueryParam.getPageNo(), equipmentWarnQueryParam.getLimit());
        equipmentWarnQueryParam.setSortValue(buildSortSql(equipmentWarnQueryParam.getSortPros()));
        Page<EquipmentWarnDto> equipmentWarnDtoPage = equipmentWarnMapper.pageList(page, buildCategorySql(equipmentWarnQueryParam));
        List<EquipmentWarnDto> equipmentWarnDtos = equipmentWarnDtoPage.getRecords();
        if(CollectionUtils.isNotEmpty(equipmentWarnDtos)){
            String[] equipmentIdStrs = equipmentWarnDtos.stream().map(EquipmentWarnDto::getEquipmentId).toArray(String[]::new);
            if(null != equipmentIdStrs && equipmentIdStrs.length > StaticValue.ZERO){
                Map<String, EquipmentCustomerDto> customerDtoMap = getCustomerMap(equipmentIdStrs);
                for(EquipmentWarnDto equipmentWarnDto : equipmentWarnDtos){
                    EquipmentCustomerDto customerDto = customerDtoMap.get(equipmentWarnDto.getEquipmentId());
                    if(null != customerDto){
                        equipmentWarnDto.setCustomerId(customerDto.getId());
                        equipmentWarnDto.setCustomName(customerDto.getName());
                    }
                }
            }
        }

        return Optional.ofNullable(PageResult.<EquipmentWarnDto>builder()
                .records(equipmentWarnDtos)
                .total(equipmentWarnDtoPage.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    /**
     * 构建排序sql
     *
     * @param sortPros
     */
    private String buildSortSql(Map<String, String> sortPros) {
        if (null == sortPros || sortPros.isEmpty()) {
            return " ORDER BY warn.warn_time DESC";
        }
        //是否已有排序字段
        Boolean flag = false;
        StringBuffer sb = new StringBuffer(" ORDER BY ");
        for (Map.Entry<String, String> cols : sortPros.entrySet()) {
            if (flag) {
                sb.append(", ");
            }
            if (cols.getKey().equals("level")) {
                flag = true;
                sb.append("warn.level ").append(cols.getValue());
            } else if (cols.getKey().equals("equipmentCode")) {
                flag = true;
                sb.append("info.code ").append(cols.getValue());
            } else if (cols.getKey().equals("equipmentName")) {
                flag = true;
                sb.append("info.name ").append(cols.getValue());
            } else if (cols.getKey().equals("customName")) {
                flag = true;
                sb.append("info.customer_name ").append(cols.getValue());
            } else if (cols.getKey().equals("fixture")) {
                flag = true;
                sb.append("warn.fixture ").append(cols.getValue());
            } else if (cols.getKey().equals("frame")) {
                flag = true;
                sb.append("warn.frame ").append(cols.getValue());
            } else if (cols.getKey().equals("categoryName")) {
                flag = true;
                sb.append("category.name ").append(cols.getValue());
            } else if (cols.getKey().equals("remark")) {
                flag = true;
                sb.append("warn.remark ").append(cols.getValue());
            } else if (cols.getKey().equals("status")) {
                flag = true;
                sb.append("warn.status ").append(cols.getValue());
            } else if (cols.getKey().equals("warnTime")) {
                flag = true;
                sb.append("warn.warn_time ").append(cols.getValue());
            } else if (cols.getKey().equals("repairTime")) {
                flag = true;
                sb.append("warn.repair_time ").append(cols.getValue());
            }
        }

        if (flag) {
            return sb.toString();
        } else {
            return " ORDER BY warn.warn_time DESC";
        }
    }

    private EquipmentWarnQueryParam buildCategorySql(EquipmentWarnQueryParam queryParam) {
        if (StringUtils.isNotBlank(queryParam.getCategoryId())) {
            String[] categoryIds = queryParam.getCategoryId().split(",");
            StringBuffer categorys = new StringBuffer(" and (");
            for (int i = 0; i < categoryIds.length; i++) {
                categorys.append("category.layer_code LIKE concat('%','");
                categorys.append(categoryIds[i]);
                categorys.append("','%')");
                if (i < categoryIds.length - 1) {
                    categorys.append(" or ");
                } else {
                    categorys.append(")");
                }
            }
            queryParam.setCategoryId(categorys.toString());
        }

        return queryParam;
    }

    @Override
    public List<EquipmentWarnDto> getList(EquipmentWarnQueryParam equipmentWarnQueryParam){
        List<EquipmentWarnDto> equipmentWarnDtos = equipmentWarnMapper.getList(equipmentWarnQueryParam);
        return equipmentWarnDtos;
    }

    private Map<String, EquipmentCustomerDto> getCustomerMap(String[] equipmentIds) {
        Map<String, EquipmentCustomerDto> customerDtoMap = new HashMap<>();
        if (null != equipmentIds && equipmentIds.length > StaticValue.ZERO) {
            RestResponse<Map<String, EquipmentCustomerDto>> restResponse = systemClient.getMapByEquipmentIds(equipmentIds);
            if (restResponse.isOk()) {
                customerDtoMap = restResponse.getData();
            } else {
                log.info("获取客户信息失败");
            }
        }
        return customerDtoMap;
    }

    @Override
    public PageResult<EquipmentWarnAppDto> appPageDto(WarnAppQueryParam warnAppQueryParam){
        //客户端数据过滤
        CheckEquipmentIdDto checkEquipmentIdDto = equipmentInfoService.getClientEquipmentIds();
        if(checkEquipmentIdDto.getType() == StaticValue.ONE){
            //审核用户不提供任何数据返回
            return new PageResult<>();
        }else if(checkEquipmentIdDto.getType() == StaticValue.TWO){
            List<String> equipmentIds = checkEquipmentIdDto.getEquipmentIds();
            if(CollectionUtils.isNotEmpty(equipmentIds)) {
                warnAppQueryParam.setEquipmentIds(equipmentIds);
            }else{
                return new PageResult<>();
            }
        }

        Page<EquipmentWarnAppDto> page = new Page<>(warnAppQueryParam.getPageNo(), warnAppQueryParam.getLimit());
        Page<EquipmentWarnAppDto> equipmentWarnDtoPage = equipmentWarnMapper.appPageList(page, warnAppQueryParam);
        List<EquipmentWarnAppDto> equipmentWarnDtos = equipmentWarnDtoPage.getRecords();
        buildPics(equipmentWarnDtos);
        return Optional.ofNullable(PageResult.<EquipmentWarnAppDto>builder()
                .records(equipmentWarnDtos)
                .total(equipmentWarnDtoPage.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    private void buildPics(List<EquipmentWarnAppDto> equipmentWarnDtos){
        if(CollectionUtils.isNotEmpty(equipmentWarnDtos)){
            String[] equipmentIds = equipmentWarnDtos.stream().map(EquipmentWarnAppDto::getEquipmentId).toArray(String[]::new);
            Map<String, EquipmentCustomerDto> customerDtoMap = getCustomerMap(equipmentIds);
            List<String> picIdList = equipmentWarnDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPicIds()))
                    .map(EquipmentWarnAppDto::getPicIds).collect(Collectors.toList());
            Map<String, AttachmentClientDto> attachmentMap = equipmentInfoService.getAttachmentMap(picIdList);

            for(EquipmentWarnAppDto equipmentWarnAppDto : equipmentWarnDtos){
                EquipmentCustomerDto customerDto = customerDtoMap.get(equipmentWarnAppDto.getEquipmentId());
                if(null != customerDto){
                    equipmentWarnAppDto.setCustomerId(customerDto.getId());
                    equipmentWarnAppDto.setCustomName(customerDto.getName());
                }
                String picIds = equipmentWarnAppDto.getPicIds();
                if(StringUtils.isNotBlank(picIds)) {
                    String[] picStrs = picIds.split(StringPool.COMMA);
                    AttachmentClientDto attachmentClientDto = attachmentMap.get(picStrs[0]);
                    if(null != attachmentClientDto){
                        equipmentWarnAppDto.setPicUrl(attachmentClientDto.getUrl());
                    }
                }
            }
        }
    }

    @Override
    public PageResult<WarnFirstPageDto> firstPage(WarnAppQueryParam warnAppQueryParam){
        warnAppQueryParam.setStatus(StaticValue.ONE);
        PageResult<EquipmentWarnAppDto> page =  this.appPageDto(warnAppQueryParam);
        List<EquipmentWarnAppDto> equipmentWarnDtos = page.getRecords();
        if(CollectionUtils.isNotEmpty(equipmentWarnDtos)){
            List<WarnFirstPageDto> warnFirstPageDtos = CopyDataUtil.copyList(equipmentWarnDtos, WarnFirstPageDto.class);
            return Optional.ofNullable(PageResult.<WarnFirstPageDto>builder()
                    .records(warnFirstPageDtos)
                    .total(page.getTotal())
                    .build())
                    .orElse(new PageResult<>());
        }
        return new PageResult<>();
    }

    @Override
    public PageResult<WarnFirstPageDto> warnList(WarnAppQueryParam warnAppQueryParam){
        if(null != warnAppQueryParam.getIsClient() && warnAppQueryParam.getIsClient().intValue() == StaticValue.ONE) {
            RestResponse<List<String>> restResponse = systemClient.getCurrentInfoIds();
            if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
                warnAppQueryParam.setEquipmentIds(restResponse.getData());
            } else {
                return new PageResult<>();
            }
        }
        Page<EquipmentWarnAppDto> page = new Page<>(warnAppQueryParam.getPageNo(), warnAppQueryParam.getLimit());
        warnAppQueryParam.setStatus(StaticValue.ONE);
        Page<EquipmentWarnAppDto> equipmentWarnDtoPage = equipmentWarnMapper.appPageList(page, warnAppQueryParam);
        List<EquipmentWarnAppDto> equipmentWarnDtos = equipmentWarnDtoPage.getRecords();
        buildPics(equipmentWarnDtos);
        if(CollectionUtils.isNotEmpty(equipmentWarnDtos)){
            List<WarnFirstPageDto> warnFirstPageDtos = CopyDataUtil.copyList(equipmentWarnDtos, WarnFirstPageDto.class);
            return Optional.ofNullable(PageResult.<WarnFirstPageDto>builder()
                    .records(warnFirstPageDtos)
                    .total(equipmentWarnDtoPage.getTotal())
                    .build())
                    .orElse(new PageResult<>());
        }
        return new PageResult<>();
    }


    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(EquipmentWarnAddParam equipmentWarnAddParam) {
        EquipmentWarn equipmentWarn = new EquipmentWarn();
        equipmentWarn.setId(equipmentWarnAddParam.getId());
        equipmentWarn.setIotEquipmentId(equipmentWarnAddParam.getDeviceId());
        equipmentWarn.setStatus(equipmentWarnAddParam.getAlarmStatus());
        equipmentWarn.setLevel(equipmentWarnAddParam.getAlarmType());
        equipmentWarn.setWarnTime(new Date(equipmentWarnAddParam.getStartTs()));
        equipmentWarn.setRemark(equipmentWarnAddParam.getAlarmInfo());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,equipmentWarn);
        return save(equipmentWarn);
    }

    @Override
    public boolean saveIotBatch(List<IotDetailDto> iotDetailDtos, String tenantId) {
        List<EquipmentWarn> equipmentWarns = new ArrayList<>(iotDetailDtos.size());
        for(IotDetailDto iotDetailDto : iotDetailDtos){
            /*EquipmentInfo equipmentInfo = equipmentInfoService.getEquipmentByDeviceId(iotDetailDto.getDeviceId());

            EquipmentWarn equipmentWarn = new EquipmentWarn();
            equipmentWarn.setIotEquipmentId(iotDetailDto.getDeviceId());
            equipmentWarn.setId(iotDetailDto.getAlarmId());
            equipmentWarn.setStatus(iotDetailDto.getAlarmStatus());
            equipmentWarn.setLevel(iotDetailDto.getAlarmType());
            equipmentWarn.setWarnTime(new Date(iotDetailDto.getStartTs()));
            equipmentWarn.setRemark(iotDetailDto.getAlarmInfo());
            equipmentWarn.setTenantId(tenantId);
            equipmentWarn.setCreateBy("iot_system");
            equipmentWarn.setCreateTime(new Date());
            equipmentWarn.setUpdateBy("iot_system");
            equipmentWarn.setUpdateTime(new Date());

            String paramLists = iotDetailDto.getParamLists();
            if(StringUtils.isNotBlank(paramLists)){
                List<WarnRuleDto> warnRuleDtos = JSONArray.parseArray(paramLists, WarnRuleDto.class);
                if(CollectionUtils.isNotEmpty(warnRuleDtos)) {
                    WarnRuleDto warnRuleDto = warnRuleDtos.get(0);
                    //*******cha1_Slot14_FAULT_A*******
                    String param = warnRuleDto.getParam();
                    if (StringUtils.isNotBlank(param) && param.contains("cha")) {
                        Integer frameIndex = param.indexOf("cha");
                        Integer frameEnd = param.indexOf(StringPool.UNDERSCORE);
                        //cha后面的为机架号
                        Integer frameNum = Integer.valueOf(param.substring(frameIndex + StaticValue.THREE, frameEnd));
                        String frame = "机架#" + frameNum;
                        equipmentWarn.setFrame(frame);
                        //机架后下划线之后为卡槽
                        param = param.substring(frameEnd + StaticValue.ONE);
                        Integer fixtureIndex = param.indexOf(StringPool.UNDERSCORE);
                        if (null != fixtureIndex && fixtureIndex > 0) {
                            String fixture = param.substring(0, fixtureIndex);
                            Integer platform = equipmentInfo.getPlatform();
                            if (null != platform && platform == StaticValue.ZERO) {
                                //机架1是MPA,MPB,MPC,S4-S16;后面机架S1-S16
                                if (fixture.toUpperCase().contains("S")) {
                                    //卡槽可能有MPA,2L, S1等格式，只有s1需要替换成Slot
                                    fixture = fixture.toUpperCase().replace("S", "Slot");
                                } else if (fixture.toUpperCase().contains("MP")) {
                                    fixture = fixture.toUpperCase();
                                }
                            } else if (null != platform && platform == StaticValue.ONE) {
                                //plus为为*******cha1_Slot0_FAULT_A    cha2_Slot16_FAULT_A*******
                                //cha1机架1卡槽slot0-slot15   cha2机架2 slot16-slot31
                                if (fixture.toUpperCase().contains("SLOT")) {
                                    //卡槽为slotx，实际16卡槽，标识必须加1
                                    Integer num = Integer.valueOf(fixture.substring(StaticValue.FOUR)) + 1;
                                    num = num - (frameNum - 1) * 16;
                                    fixture = "Slot" + num;
                                }
                            }
                            equipmentWarn.setFixture(fixture);
                        }
                    }
                }
            }
            String frame = StringUtils.isNotBlank(equipmentWarn.getFrame()) ? equipmentWarn.getFrame() : "";
            String fixture = StringUtils.isNotBlank(equipmentWarn.getFixture()) ? equipmentWarn.getFixture() : "";

            asyncSend(equipmentInfo, frame, fixture, iotDetailDto);

            equipmentWarns.add(equipmentWarn);*/
        }
        if(CollectionUtils.isNotEmpty(equipmentWarns)){
            return equipmentWarnMapper.saveIotBatch(equipmentWarns);
        }
        return false;
    }

    /**
     * 异步推送消息
     */
    private void asyncSend(EquipmentInfo equipmentInfo, String frame, String fixture, IotDetailDto iotDetailDto){
        new Thread(()-> {
            try{
                RestResponse<List<String>> managerRestResponse = porosSecGrantClient.getUidsByRoleCode(kjsManager);
                log.info("managerRestResponse:" + managerRestResponse);
                List<String> managerUids = new ArrayList<>();
                if(managerRestResponse.isOk()){
                    managerUids = managerRestResponse.getData();
                }else{
                    log.error("根据管理员角色获取用户列表失败", managerRestResponse.getMsg());
                }
                if(CollectionUtils.isNotEmpty(managerUids)){
                    //推送管理员告警消息，需要添加公司（客户）名
                    log.info("推送管理员告警消息");
                    send(equipmentInfo, managerUids, frame + fixture + iotDetailDto.getAlarmInfo(), true);
                }
                RestResponse<List<String>> userRestResponse = systemClient.getUidsByEquipmentId(equipmentInfo.getId());
                if(userRestResponse.isOk()){
                    List<String> uids = userRestResponse.getData();
                    if(CollectionUtils.isNotEmpty(uids)){
                        //推送设备关联用户告警消息
                        log.info("推送设备关联用户告警消息");
                        send(equipmentInfo, uids, frame + fixture + iotDetailDto.getAlarmInfo(), false);
                    }

                }else{
                    log.error("根据设备ID获取用户列表失败", userRestResponse.getMsg());
                }
            }catch (Exception e){
                log.error("发送告警通知异常", e.getMessage());
            }
        });
    }

    /**
     * 推送消息
     * manager 是否管理员
     */
    private void send(EquipmentInfo equipmentInfo, List<String> uids, String alarmInfo, Boolean manager){
        new Thread(() -> {
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(uids);
            RestResponse<List<PorosSecStaffDto>> staffRestResponse = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if(staffRestResponse.isOk()){
                List<PorosSecStaffDto> porosSecStaffDtos = staffRestResponse.getData();
                List<String> emails = new ArrayList<>();
                List<String> mobiles = new ArrayList<>();
                porosSecStaffDtos.stream().forEach(porosSecStaffDto -> {
                    emails.add(porosSecStaffDto.getEmail());
                    mobiles.add(porosSecStaffDto.getMobile());
                });
                List<String> _uids = uids.stream().distinct().collect(Collectors.toList());
                List<String> _emails = emails.stream().distinct().collect(Collectors.toList());
                String customerName = "";
                if(manager){
                    RestResponse<CustomerDto> customerRes = systemClient.getCustomerIdByInfoId(equipmentInfo.getId());
                    if (customerRes.isOk()) {
                        CustomerDto customerDto = customerRes.getData();
                        customerName = null != customerDto ? customerDto.getName() : "";
                    }else{
                        log.info("此设备未绑定客户");
                    }
                }
                String title = "设备告警通知";
                String content = customerName + "设备" + equipmentInfo.getName() + "[ " + equipmentInfo.getCode() + " ]发生告警,告警内容:" + alarmInfo + ",请及时查看并处理";
                EmailNotify emailNotify = EmailNotify.builder().title(title).content(content).emails(_emails.toArray(new String[_emails.size()])).build();
                PushNotify pushNotify = PushNotify.builder().title(title).content(content).uids(_uids.toArray(new String[_uids.size()])).build();
                notifyClient.sendNotify(NotifyParam.builder().notifyTypes(EnumSet.of(NotifyType.EMAIL, NotifyType.APP)).emailNotify(emailNotify).pushNotify(pushNotify).build());
            }else{
                log.error("根据UIDS获取用户列表失败", staffRestResponse.getMsg());
            }
        });

    }

    @Override
    public boolean updateIotBatch(List<IotDetailDto> iotDetailDtos) {
        List<EquipmentWarn> equipmentWarns = new ArrayList<>(iotDetailDtos.size());
        for(IotDetailDto iotDetailDto : iotDetailDtos) {
            EquipmentWarn equipmentWarn = new EquipmentWarn();
            equipmentWarn.setId(iotDetailDto.getAlarmId());
            //状态置为已完成
            equipmentWarn.setStatus(StaticValue.THREE);
            equipmentWarn.setRepairTime(new Date(iotDetailDto.getEndTs()));
            equipmentWarn.setUpdateTime(new Date());
            equipmentWarns.add(equipmentWarn);
        }
        if(CollectionUtils.isNotEmpty(equipmentWarns)){
            return equipmentWarnMapper.updateIotBatch(equipmentWarns);
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(EquipmentWarnEditParam equipmentWarnEditParam) {
        EquipmentWarn equipmentWarn = CopyDataUtil.copyObject(equipmentWarnEditParam, EquipmentWarn.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,equipmentWarn);
        equipmentWarn.setRepairTime(new Date(equipmentWarnEditParam.getEndTs()));
        equipmentWarn.setUpdateTime(new Date());
        return equipmentWarnMapper.updateById(equipmentWarn) > 0;
    }

    @Override
    public EquipmentWarnAppDto getWarnById(@RequestParam String id){
        EquipmentWarnAppDto equipmentWarnAppDto = equipmentWarnMapper.getWarnById(id);
        if(null != equipmentWarnAppDto){
            String equipmentId = equipmentWarnAppDto.getEquipmentId();
            if(StringUtils.isNotBlank(equipmentId)){
                Map<String, EquipmentCustomerDto> customerDtoMap = getCustomerMap(new String[]{equipmentId});
                EquipmentCustomerDto customerDto = customerDtoMap.get(equipmentWarnAppDto.getEquipmentId());
                if(null != customerDto){
                    equipmentWarnAppDto.setCustomerId(customerDto.getId());
                    equipmentWarnAppDto.setCustomName(customerDto.getName());
                }
            }
            String picIds = equipmentWarnAppDto.getPicIds();
            if(StringUtils.isNotBlank(picIds)){
                String[] picStrs = picIds.split(StringPool.COMMA);
                Map<String, AttachmentClientDto> map = equipmentInfoService.getAttachmentMap(Arrays.asList(picStrs));
                AttachmentClientDto attachmentClientDto = map.get(picStrs[0]);
                if(null != attachmentClientDto) {
                    equipmentWarnAppDto.setPicUrl(attachmentClientDto.getUrl());
                }
            }
        }
        return equipmentWarnAppDto;
    }
}
