package cn.getech.ehm.equipment.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 诊断报告V2详情
 */
@Data
@ApiModel(value = "ReportV2DetailHealthDto", description = "诊断报告V2详情")
public class ReportV2DetailHealthDto {

    @ApiModelProperty(value = "序号")
    private Integer num;

    @ApiModelProperty(value = "值")
    private Integer value;

    @ApiModelProperty(value = "状态名称")
    private String name;

    @ApiModelProperty(value = "设备数量")
    private Long equipmentNum;

    @ApiModelProperty(value = "占比")
    private Double rate;

    @ApiModelProperty(value = "描述")
    private String content;

}