package cn.getech.ehm.equipment.utils;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.dto.iot.*;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.EquipmentDetailDto;
import cn.getech.ehm.system.dto.EquipmentEditDto;
import cn.getech.ehm.system.dto.IotResDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户控制器
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Component
@Slf4j
public class IotUtils {

    @Autowired
    private static ResourceBundleMessageSource messageSource;

    private static String appId;
    private static String grantType;
    private static String secret;
    private static String tenantId;
    private static String preUrl;

    @Value("${iot.kjs.appId:KANG}")
    public void setAppId(String appId){
        IotUtils.appId = appId;
    }
    @Value("${iot.kjs.grantType:client_credential}")
    public void setGrantType(String grantType){
        IotUtils.grantType = grantType;
    }
    @Value("${iot.kjs.secret:4MSrrBOg4ZhWJEIE}")
    public void setSecret(String secret){
        IotUtils.secret = secret;
    }
    @Value("${iot.kjs.tenantId:geek}")
    public void setTenantId(String tenantId){
        IotUtils.tenantId = tenantId;
    }
    //@Value("${iot.kjs.preUrl:http://kong.iot-tenant.************.nip.io/}")
    @Value("${iot.kjs.preUrl:http://iot-kong.cf8220bb140bc4a40b3480c111604d968.cn-beijing.alicontainer.com/}")
    public void setPreUrl(String preUrl){
        IotUtils.preUrl = preUrl;
    }

    private static String  tokenUrl = "/api/iot-out/auth/getAccessToken";
    private static String  addDeviceUrl = "/api/iot-out/device/add";
    private static String  updateDeviceUrl = "/api/iot-out/device/update";
    private static String  queryStudioUrl = "/api/iot-out/cds/queryAllConfigurationDesignStudio";
    private static String  getStudioUrl = "/api/iot-out/cds/queryConfigurationDesignStudio";
    private static String  queryLogUrl = "/api/iot-out/device/paramsData";
    private static String  addDeviceBatchUrl = "/api/iot-out/device/addBatch";

    /**
     * 获取iot系统token
     * @return
     */
    public static String getAccessToken(){
        log.info("获取iot平台token");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appId", appId);
        paramMap.put("grant_type", grantType);
        paramMap.put("secret", secret);
        paramMap.put("tenantId", tenantId);
        String url = preUrl + tokenUrl;
        String token = getJson(url, JSONObject.toJSONString(paramMap), null).toString();
        return token;
    }

    /**
     * 新增设备
     * @param equipmentDetailDto
     * @return
     */
    public static String addDevice(EquipmentDetailDto equipmentDetailDto){
        String token = getAccessToken();
        equipmentDetailDto.setAccessToken(token);
        log.info("新增iot平台设备->" + JSONObject.toJSONString(equipmentDetailDto));
        String url = preUrl + addDeviceUrl;
        Object object = getJson(url, JSONObject.toJSONString(equipmentDetailDto), token);
        IotResDto iotResDto = JSONArray.parseObject(object.toString(), IotResDto.class);
        return iotResDto.getDeviceId();
    }

    /**
     * 批量新增设备
     * @param equipmentEditDto
     * @return
     */
    public static List<IotResDto> addDeviceBatch(EquipmentEditDto equipmentEditDto){
        String token = getAccessToken();
        equipmentEditDto.setAccessToken(token);
        log.info("批量新增iot平台设备->"+ JSONObject.toJSONString(equipmentEditDto));
        String url = preUrl + addDeviceBatchUrl;
        Object object = getJson(url, JSONObject.toJSONString(equipmentEditDto), token);
        List<IotResDto> iotResDtos = JSONArray.parseArray(object.toString(), IotResDto.class);
        return iotResDtos;
    }

    /**
     * 修改设备名称
     * @param equipmentDetailDto
     * @return
     */
    public static Boolean updateDeviceName(EquipmentDetailDto equipmentDetailDto){
        String token = getAccessToken();
        equipmentDetailDto.setAccessToken(token);
        log.info("修改设备名称->"+JSONObject.toJSONString(equipmentDetailDto));
        String url = preUrl + updateDeviceUrl;
        Object object = getJson(url, JSONObject.toJSONString(equipmentDetailDto), token);
        Boolean flag = JSONArray.parseObject(object.toString(), Boolean.class);
        return flag;
    }

    /**
     * 获取组态数据
     * @param studioQueryParam
     * @return
     */
    public static StudioResDto queryStudio(StudioQueryParam studioQueryParam){
        String token = getAccessToken();
        studioQueryParam.setAccessToken(token);
        log.info("获取组态数据->"+ JSONObject.toJSONString(studioQueryParam));
        String url = preUrl + queryStudioUrl;
        Object object = getJson(url, JSONObject.toJSONString(studioQueryParam), token);
        StudioResDto studioResDtos = JSONArray.parseObject(object.toString(), StudioResDto.class);
        return studioResDtos;
    }

    /**
     * 根据id获取组态
     * @param studioQueryParam
     * @return
     */
    public static StudioResDetailDto getStudioById(StudioQueryParam studioQueryParam){
        String token = getAccessToken();
        studioQueryParam.setAccessToken(token);
        log.info("根据id获取组态->"+ JSONObject.toJSONString(studioQueryParam));
        String url = preUrl + getStudioUrl;
        try {
            HttpRequest httpRequest = HttpRequest.get(url);
            httpRequest.header("Authorization", "Bearer " + token);
            Map<String, Object> paramMap = new HashMap<>();
            //paramMap.put("Authorization", "Bearer " + token);
            paramMap.put("configToolId", studioQueryParam.getConfigToolId());
            String json = httpRequest.form(paramMap).execute().body();
            log.info("iot返回日志" + json);
            RestResponse<Object> restResponse = JSONArray.parseObject(json, RestResponse.class);
            if(restResponse.isOk() && null != restResponse.getData()){
                StudioResDetailDto studioResDetailDto = JSONArray.parseObject(restResponse.getData().toString(), StudioResDetailDto.class);
                return studioResDetailDto;
            }else{
                log.info("同步获取iot平台数据失败 ：" + json);
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
            }
        }catch (Exception e){
            log.info("同步获取iot平台数据失败,e -> " + JSONObject.toJSON(e));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
    }

    /**
     * 获取日志
     * @param iotLogQueryParam
     * @return
     */
    public static List<IotLogResDto> queryInfoLog(IotLogQueryParam iotLogQueryParam){
        String token = getAccessToken();
        iotLogQueryParam.setAccessToken(token);
        log.info("获取日志数据->"+ JSONObject.toJSONString(iotLogQueryParam));
        String url = preUrl + queryLogUrl;
        Object object = getJson(url, JSONObject.toJSONString(iotLogQueryParam), token);
        List<IotLogResDto> iotLogResDtos = JSONArray.parseArray(object.toString(), IotLogResDto.class);
        return iotLogResDtos;
    }

    private static Object getJson(String url, String param, String token){
        try {
            HttpRequest httpRequest = HttpRequest.post(url);
            if(StringUtils.isNotBlank(token)){
                //httpRequest.header("accessToken", token);
                httpRequest.header("Authorization", "Bearer " + token);
            }
            String json = httpRequest.body(param).execute().body();
            log.info("iot返回日志" + json);
            RestResponse<Object> restResponse = JSONArray.parseObject(json, RestResponse.class);
            if(restResponse.isOk() && null != restResponse.getData()){
                return restResponse.getData();
            }else{
                log.info("同步获取iot平台数据失败 ：" + json);
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
            }
        }catch (Exception e){
            log.info("同步获取iot平台数据失败,e -> " + JSONObject.toJSON(e));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
    }
}
