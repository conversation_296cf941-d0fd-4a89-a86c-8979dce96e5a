package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelDto;
import cn.getech.ehm.equipment.dto.bearing.FactoryModelQueryParam;
import cn.getech.ehm.equipment.entity.bearing.BearingModel;
import cn.getech.ehm.equipment.mapper.BearingModelMapper;
import cn.getech.ehm.equipment.service.IBearingModelService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 轴承型号 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class BearingModelServiceImpl extends BaseServiceImpl<BearingModelMapper, BearingModel> implements IBearingModelService {

    @Autowired
    private BearingModelMapper bearingModelMapper;
    @Autowired
    private MessageSource messageSource;

    @Override
    public PageResult<FactoryModelDto> pageDto(FactoryModelQueryParam queryParam) {
        LambdaQueryWrapper<BearingModel> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(queryParam.getName())){
            wrapper.like(BearingModel::getName, queryParam.getName());
        }
        wrapper.eq(BearingModel::getDeleted, DeletedType.NO.getValue());
        wrapper.orderByDesc(BearingModel::getCreateTime);
        PageResult<BearingModel> result = page(queryParam, wrapper);

        return Optional.ofNullable(PageResult.<FactoryModelDto>builder()
                .records(CopyDataUtil.copyList(result.getRecords(), FactoryModelDto.class))
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public String saveByName(String name) {
        LambdaQueryWrapper<BearingModel> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BearingModel::getName, name);
        BearingModel bearingModel = bearingModelMapper.selectOne(wrapper);
        if(null == bearingModel) {
            bearingModel = new BearingModel();
            bearingModel.setName(name);
            this.save(bearingModel);
        }
        return bearingModel.getId();
    }

    @Override
    public boolean saveByParam(FactoryModelDto dto) {
        if(check(null, dto.getName())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        BearingModel bearingModel = new BearingModel();
        bearingModel.setName(dto.getName());
        return save(bearingModel);
    }

    /**
     * 校验名称
     * @param id
     * @param name
     * @return
     */
    private boolean check(String id, String name){
        return bearingModelMapper.checkNameExits(id, name) > 0;
    }

    @Override
    public boolean updateByParam(FactoryModelDto dto) {
        if(check(dto.getId(), dto.getName())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        BearingModel bearingModel = new BearingModel();
        bearingModel.setId(dto.getId());
        bearingModel.setName(dto.getName());
        return updateById(bearingModel);
    }

    @Override
    public Boolean deleteById(String id) {
        LambdaUpdateWrapper<BearingModel> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(BearingModel::getId, id);
        wrapper.set(BearingModel::getDeleted, DeletedType.YES.getValue());
        return this.update(wrapper);
    }

    @Override
    public List<FactoryModelDto> getList(String keyword) {
        LambdaQueryWrapper<BearingModel> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(keyword)){
            wrapper.like(BearingModel::getName, keyword);
        }
        wrapper.eq(BearingModel::getDeleted, DeletedType.NO.getValue());
        wrapper.select(BearingModel::getName, BearingModel::getId);
        wrapper.last(" limit 20");
        return CopyDataUtil.copyList(bearingModelMapper.selectList(wrapper), FactoryModelDto.class);
    }

    @Override
    public Map<String, String> getModelMap(){
        Map<String, String> map = new HashMap<>();
        LambdaQueryWrapper<BearingModel> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BearingModel::getDeleted, DeletedType.NO.getValue());
        wrapper.select(BearingModel::getName, BearingModel::getId);
        List<BearingModel> bearingModels = bearingModelMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(bearingModels)){
            for(BearingModel bearingModel : bearingModels){
                map.put(Strings.toLowerCase(bearingModel.getName()), bearingModel.getId());
            }
        }
        return map;
    }

    @Override
    public List<BearingModel> getDefaultModel(){
        return bearingModelMapper.getDefaultModel();
    }
}
