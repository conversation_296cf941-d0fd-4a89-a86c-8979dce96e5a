package cn.getech.ehm.equipment.mapper;

import cn.getech.ehm.equipment.entity.bearing.BasicLibrary;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 基础库Mapper
 */
@Repository
public interface BasicLibraryMapper extends BaseMapper<BasicLibrary> {

    /**
     * 获取最大排序
     * @return 最大排序
     */
    Integer getMaxSort();

    /**
     * 获取默认geek下基础细
     * @return
     */
    @SqlParser(filter = true)
    List<BasicLibrary> getDefaultLibrary();
}
