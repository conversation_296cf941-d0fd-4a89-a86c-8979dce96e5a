package cn.getech.ehm.equipment.entity.bearing;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础库详情
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_library_detail")
public class BasicLibraryDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 基础库id
     */
    @TableField("basic_library_id")
    private String basicLibraryId;

    /**
     * 品牌id
     */
    @TableField("factory_id")
    private String factoryId;

    /**
     * 型号id
     */
    @TableField("model_id")
    private String modelId;

    /**
     * 详情字段
     */
    @TableField("detail")
    private String detail;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
