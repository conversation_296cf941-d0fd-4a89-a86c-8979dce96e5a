package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 特种设备安全管理员
 * <AUTHOR>
 * @since 2020-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_special_manager")
public class SpecialManager extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 证书编号
     */
    @TableField("certificate_no")
    private String certificateNo;

    /**
     * 作业项目
     */
    @TableField("job_item")
    private String jobItem;

    /**
     * 发证日期
     */
    @TableField("issue_date")
    private Date issueDate;

    /**
     * 复审日期
     */
    @TableField("review_date")
    private Date reviewDate;

    /**
     * 证件有效期
     */
    @TableField("certificate_validity")
    private Date certificateValidity;

    /**
     * 发证机关
     */
    @TableField("issuing_authority")
    private String issuingAuthority;

    /**
     * 聘用单位
     */
    @TableField("employer")
    private String employer;

    /**
     * 所属车间
     */
    @TableField("workshop")
    private String workshop;


    /**
     * 附件ids
     */
    @TableField("doc_ids")
    private String docIds;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 提前预警天数
     */
    @TableField("deadline_days")
    private Integer deadlineDays;

    /**
     * 证书名称
     */
    @TableField("certificate_name")
    private String certificateName;

    /**
     * 证书类型
     */
    @TableField("certificate_type")
    private String certificateType;

    /**
     * 身份证号
     */
    @TableField("iD_number")
    private String idNumber;

}
