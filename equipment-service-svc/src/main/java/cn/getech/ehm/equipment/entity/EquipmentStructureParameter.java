package cn.getech.ehm.equipment.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 设备部件运行参数
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_structure_parameter")
public class EquipmentStructureParameter extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 设备零部件id
     */
    @TableField("structure_id")
    private String structureId;

    /**
     * 运行参数id
     */
    @TableField("running_parameter_id")
    private String runningParameterId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 对应值
     */
    @TableField("value")
    private BigDecimal value;

    /**
     * 参数分类
     */
    @TableField("param_type")
    private Integer paramType;

    /**
     * 数据来源(1录入2计算)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 是否生成参数表(0否1是)
     */
    @TableField("create_param")
    private Integer createParam;

    /**
     * 计算脚本
     */
    @TableField("expression")
    private String expression;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
}
