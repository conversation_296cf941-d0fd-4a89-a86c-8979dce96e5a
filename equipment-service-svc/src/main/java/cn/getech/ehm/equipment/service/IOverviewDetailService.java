package cn.getech.ehm.equipment.service;

import cn.getech.ehm.equipment.dto.overview.OverviewDetailCardDto;
import cn.getech.ehm.equipment.dto.overview.OverviewDetailDto;
import cn.getech.ehm.equipment.entity.OverviewDetail;
import cn.getech.poros.framework.common.service.IBaseService;

/**
 * 概览详情 服务类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IOverviewDetailService extends IBaseService<OverviewDetail> {

        /**
         * 更新概览详情顶部名称
         * @return
         */
        Boolean editDetailName(String relationKey, Integer type, String name);

        /**
         * 更新概览详情中央图片
         * @return
         */
        Boolean editDetailCard(OverviewDetailCardDto dto);

        /**
         * 清理概览详情中央图片
         * @return
         */
        Boolean clearDetailCard(String relationKey, Integer type);

        /**
         * 查询概览详情中央图片
         * @return
         */
        OverviewDetailCardDto getDetailCard(String relationKey, Integer type);

        /**
         * 查询概览详情
         * @return
         */
        OverviewDetailDto getDetailDto(String relationKey, Integer type);
}