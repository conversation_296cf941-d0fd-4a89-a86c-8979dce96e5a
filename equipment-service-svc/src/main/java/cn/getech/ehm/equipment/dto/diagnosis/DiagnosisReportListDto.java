package cn.getech.ehm.equipment.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 诊断报告返回列表
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@ApiModel(value = "DiagnosisReportListDto", description = "诊断报告返回列表")
public class DiagnosisReportListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "创建人uid")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}