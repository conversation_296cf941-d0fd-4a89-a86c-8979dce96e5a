package cn.getech.ehm.equipment.dto.category;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型部件 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InfoCategoryStructureEditParam", description = "设备类型部件编辑参数")
public class InfoCategoryStructureEditParam extends ApiParam {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "零部件类型id")
    private String sparePartsId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

}
