package cn.getech.ehm.equipment.service.impl;

import cn.getech.ehm.equipment.dto.EquipmentIotStatusDto;
import cn.getech.ehm.equipment.entity.EquipmentInfo;
import cn.getech.ehm.equipment.entity.EquipmentStatus;
import cn.getech.ehm.equipment.mapper.EquipmentStatusMapper;
import cn.getech.ehm.equipment.service.IEquipmentInfoService;
import cn.getech.ehm.equipment.service.IEquipmentStatusService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 设备状态 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Slf4j
@Service
public class EquipmentStatusServiceImpl extends BaseServiceImpl<EquipmentStatusMapper, EquipmentStatus> implements IEquipmentStatusService {

    @Autowired
    private EquipmentStatusMapper statusMapper;
    @Autowired
    private IEquipmentInfoService equipmentInfoService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public Boolean updateStatus(EquipmentIotStatusDto dto){
        this.updateEquipmentStatus(dto.getStatus(), dto.getEquipmentId(), dto.getMarkTime());
        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentInfo::getId, dto.getEquipmentId());
        //报警状态下不更新设备运行状态
        wrapper.set(EquipmentInfo::getRunningStatus, dto.getStatus());
        return equipmentInfoService.update(wrapper);
    }

    @Override
    public Boolean updateIotStatus(EquipmentIotStatusDto dto){
        LambdaUpdateWrapper<EquipmentInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(EquipmentInfo::getId, dto.getEquipmentId());
        //报警状态下不更新设备运行状态
        wrapper.set(EquipmentInfo::getIotStatus, dto.getStatus());
        return equipmentInfoService.update(wrapper);
    }

    @Override
    public Boolean updateEquipmentStatus(Integer runningStatus, String equipmentId, Date markTime){
        String key = "iot_edit_info_status:" + equipmentId;
        if(!redisTemplate.opsForValue().setIfAbsent(key, true, 60, TimeUnit.SECONDS)){
            log.debug("-------------当前设备其他参数在更新设备状态值:" + equipmentId);
            return true;
        }
        if(null != runningStatus) {
            //格式化
            markTime = DateUtil.beginOfSecond(markTime);
            LambdaQueryWrapper<EquipmentStatus> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(EquipmentStatus::getEquipmentId, equipmentId);
            wrapper.orderByDesc(EquipmentStatus::getMarkTime);
            wrapper.last(" limit 1");
            EquipmentStatus equipmentStatus = statusMapper.selectOne(wrapper);
            if (null != equipmentStatus) {
                //判断最新状态与当前是否相同
                if (equipmentStatus.getStatus() != runningStatus && equipmentStatus.getMarkTime().compareTo(markTime) < 0) {
                    //重复推送，或者时间错乱，MQ中靠后的时间先被处理,所以不再处理当前数据

                    //本次标记时间为上次的结束时间
                    equipmentStatus.setEndTime(markTime);
                    updateById(equipmentStatus);
                    EquipmentStatus newStatus = new EquipmentStatus();
                    newStatus.setEquipmentId(equipmentId);
                    newStatus.setStatus(runningStatus);
                    newStatus.setMarkTime(markTime);
                    save(newStatus);
                }

            } else {
                equipmentStatus = new EquipmentStatus();
                equipmentStatus.setEquipmentId(equipmentId);
                equipmentStatus.setStatus(runningStatus);
                equipmentStatus.setMarkTime(markTime);
                save(equipmentStatus);
            }
        }
        redisTemplate.delete(key);
        return true;
    }

    @Override
    public List<EquipmentStatus> getList(String equipmentId, Date beginTime, Date endTime, Integer[] statusList){
        LambdaQueryWrapper<EquipmentStatus> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentStatus::getEquipmentId, equipmentId);
        if(null != beginTime) {
            wrapper.and(w -> w.ge(EquipmentStatus::getEndTime, beginTime).or()
                    .isNull(EquipmentStatus::getEndTime));
        }
        wrapper.le(null != endTime, EquipmentStatus::getMarkTime, endTime);
        wrapper.in(null != statusList, EquipmentStatus::getStatus, statusList);
        wrapper.orderByAsc(EquipmentStatus::getMarkTime);
        return statusMapper.selectList(wrapper);
    }
}
