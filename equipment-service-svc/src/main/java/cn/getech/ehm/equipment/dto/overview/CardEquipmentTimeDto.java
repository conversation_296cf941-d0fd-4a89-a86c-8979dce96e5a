package cn.getech.ehm.equipment.dto.overview;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备时间
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "CardEquipmentTimeDto", description = "设备时间")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardEquipmentTimeDto {

    @ApiModelProperty(value = "设备状态")
    private Integer status = StaticValue.ZERO;

    @ApiModelProperty(value = "设备状态名称")
    private String statusName;

    @ApiModelProperty(value = "设备位置")
    private String locationName;

    @ApiModelProperty(value = "累积运行时间")
    private String runningTime = "0d";

    @ApiModelProperty(value = "无故障时间")
    private String noFaultTime = "0d";
}