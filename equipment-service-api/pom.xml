<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>equipment-service</artifactId>
        <groupId>cn.getech.ehm.equipment</groupId>
        <version>2.2-tbea-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>equipment-service-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.getech.ehm</groupId>
            <artifactId>ehm-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.getech.poros</groupId>
            <artifactId>poros-common</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
</project>
