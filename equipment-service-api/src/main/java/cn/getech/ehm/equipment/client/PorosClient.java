package cn.getech.ehm.equipment.client;

import cn.getech.ehm.equipment.dto.PorosOrgDetailDto;
import cn.getech.ehm.equipment.dto.PorosStaffSearchDto;
import cn.getech.ehm.equipment.dto.SecStaffQueryParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.permission.dto.OrgTreeDto;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "poros-permission", path = "/api/poros-permission")
public interface PorosClient {

    /**
     * 获取中台组织树
     * @param tenantId
     * @return
     */
    @GetMapping({"/secOrg/findAllBuildTree"})
    RestResponse<List<OrgTreeDto>> getOrgTree(@RequestParam("tenantId") String tenantId);

    /**
     * 获取中台租户下所有用户
     * @return
     */
    @PostMapping({"/secStaff/user/all/tenant"})
    RestResponse<List<PorosSecStaffDto>> getUserList(@RequestBody PorosStaffSearchDto searchDto);

    /**
     * 懒加载获取中台组织
     * @param tenantId
     * @return
     */
    @GetMapping({"/secOrg/list"})
    RestResponse<List<PorosOrgDetailDto>> getOrgList(@RequestParam(value = "code",required = false) String code, @RequestParam("dataSources") String dataSources,@RequestParam("tenantId") String tenantId,@RequestParam("systemCode") String systemCode);

    /**
     * 获取组织对应人员数量
     * @return
     */
    @GetMapping({"/secOrg/getUserCountMapByOrgCodes"})
    RestResponse<Map<String, Integer>> getUserCountMapByOrgCodes(@RequestParam(value = "orgCodes") String[] orgCodes);

    /**
     * 获取当前登录人员所属部门及其子部门code
     * @return
     */
    @GetMapping({"/secOrg/getCurrentUserOrgList"})
    RestResponse<List<String>> getCurrentUserOrgList();

    /**
     * 查询用户列表
     */
    @PostMapping("/secStaff/list")
    RestResponse<PageResult<PorosSecStaffDto>> getStaffList(@RequestBody SecStaffQueryParam queryParam);

    /**
     * 查询用户列表不分页
     */
    @PostMapping("/secStaff/findList")
    RestResponse<List<PorosSecStaffDto>> findStaffList(@RequestBody SecStaffQueryParam queryParam);


}
