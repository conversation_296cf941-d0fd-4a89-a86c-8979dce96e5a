package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 设备类型统计
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "CategoryStatisticsDto", description = "设备类型统计")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CategoryStatisticsDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "是否叶节点(0否1是)")
    private Integer isLeaf = StaticValue.ONE;
}