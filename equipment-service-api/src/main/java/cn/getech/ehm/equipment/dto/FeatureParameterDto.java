package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 时频特征计算参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "FeatureParameterDto", description = "时频特征计算参数")
public class FeatureParameterDto {

    @ApiModelProperty(value = "部件/零件id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "参数")
    private String params;

    @ApiModelProperty(value = "类型(1:部件2:零件)")
    private Integer type;

}