package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备同步类型部件
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "QuoteCategoryStructureDto", description = "设备同步类型部件")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuoteCategoryStructureDto {

    @ApiModelProperty("设备id")
    private String equipmentId;

    @ApiModelProperty("类型id")
    private String categoryId;

    @ApiModelProperty("类型测点下参数ids")
    private List<String> categoryStructureIds;

}
