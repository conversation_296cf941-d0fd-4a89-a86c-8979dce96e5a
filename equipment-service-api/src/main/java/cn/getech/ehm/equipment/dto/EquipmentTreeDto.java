package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备结构树
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentTreeDto", description = "设备结构树")
public class EquipmentTreeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类型(0位置1设备2部件)")
    private Integer type;

    private Integer locationType;

    @ApiModelProperty(value = "类别装饰器")
    private Map<String, String> scopedSlots = new HashMap<String, String>(){{put("title", "title");}};

    @ApiModelProperty(value = "是否展开/勾选", hidden = true)
    private Boolean spread = false;

    @ApiModelProperty(value = "是否开启概览图")
    private Boolean enableOverview = false;


    @ApiModelProperty("排序比重")
    private Integer sort;

    @ApiModelProperty(value = "父节点id")
    private String parentId;
}