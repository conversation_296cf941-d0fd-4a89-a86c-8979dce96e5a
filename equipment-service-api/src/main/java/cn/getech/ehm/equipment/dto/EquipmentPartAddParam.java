package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <pre>
 * 设备备件 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Equipment备件新增", description = "设备表新增参数")
public class EquipmentPartAddParam extends ApiParam {
    @ApiModelProperty(value = "设备ID", required = true)
    @NotBlank(message = "设备ID不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "备件ID列表", required = true)
    @NotEmpty(message = "备件ID列表不能为空")
    private String[] partIds;

}
