package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 设备表 编辑参数对象
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Equipment编辑", description = "设备表编辑参数")
public class EquipmentInfoEditParam extends ApiParam {
    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "型号规格")
    private String specification;

    @ApiModelProperty(value = "投产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date completionTime;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "是否需要校验")
    private Boolean checked;

    @ApiModelProperty(value = "特种设备详情")
    private EquipmentSpecialDto specialDto;

    @ApiModelProperty(value = "校验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkDate;

    @ApiModelProperty(value = "校验人员")
    private String checkUser;

    @ApiModelProperty(value = "校验状态")
    private String checkStatus;

    @ApiModelProperty(value = "是否联网")
    private Boolean networked;

    @ApiModelProperty(value = "是否监控")
    private Boolean monitored;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "健康状态")
    private Integer healthStatus;

    @ApiModelProperty(value = "制造商")
    private String manufacturerId;

    @ApiModelProperty(value = "供应商")
    private String supplierId;

    @ApiModelProperty(value = "设计单位")
    private String designCompanyId;

    @ApiModelProperty(value = "安装单位")
    private String installCompanyId;

    @ApiModelProperty(value = "出厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "出厂日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionTime;

    @ApiModelProperty(value = "设计年限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date designPeriod;

    @ApiModelProperty(value = "保修年限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date warrantyPeriod;

    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "资产状态")
    private String assetStatus;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "资产原值")
    private Double originalAsset;

    @ApiModelProperty(value = "资产净值")
    private Double netAsset;

    @ApiModelProperty(value = "管理部门")
    private String manageDepart;

    @ApiModelProperty(value = "管理责任人")
    private String managePrincipal;

    @ApiModelProperty(value = "使用部门")
    private String useDepart;

    @ApiModelProperty(value = "使用责任人")
    private String usePrincipal;

    @ApiModelProperty(value = "维保责任人")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维保班组")
    private String[] teamIds;

    @ApiModelProperty(value = "图片")
    private String picId;

    @ApiModelProperty(value = "附件")
    private String docIds;

    @ApiModelProperty(value = "扩展信息")
    private EquipmentPropMainDto propDtos;

    @ApiModelProperty(value = "扩展字段X")
    private String alternateFieldX;

    @ApiModelProperty(value = "扩展字段Y")
    private String alternateFieldY;

    //标签tagId
    @ApiModelProperty("标签tagId")
    private String positionTagId;
}
