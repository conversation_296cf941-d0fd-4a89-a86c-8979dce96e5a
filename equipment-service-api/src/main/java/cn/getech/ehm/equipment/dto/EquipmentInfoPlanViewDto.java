package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;


/**
 * <pre>
 * 设备表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentPlanViewDto", description = "设备表返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentInfoPlanViewDto {
    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "设备位置id")
    private String locationId;

    @ApiModelProperty(value = "设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "型号规格")
    private String specification;

    @ApiModelProperty(value = "出厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "重要程度(1 A:关键设备 2 B:重要设备 3 C:一般设备 4 D:次要设备)")
    private Integer importance;

    @ApiModelProperty(value = "厂商Id")
    private String supplierId;

    @ApiModelProperty(value = "厂商名称")
    private String supplierName;

    @ApiModelProperty(value = "资产状态(0闲置1正常2故障3失效4限用5禁用6借出7报废8变卖)")
    private Integer status;

    @ApiModelProperty(value = "状态说明")
    private String statusRemark;

    @ApiModelProperty(value = "维护班组id列表")
    private List<String> maintTeamIds;

    @ApiModelProperty(value = "维护人员id列表")
    private List<String> maintainerIds;

    @ApiModelProperty(value = "维护班组名称列表")
    private String maintTeamNames;

    @ApiModelProperty(value = "维护人员姓名列表")
    private String maintainerNames;

    @ApiModelProperty(value = "设备责任人")
    private String principal;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "出厂日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date productionTime;

    @ApiModelProperty(value = "启用日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date installDate;
}