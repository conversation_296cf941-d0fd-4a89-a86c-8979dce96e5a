package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <pre>
 * 设备表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentPlanView查询", description = "设备表查询参数")
public class EquipmentInfoPlanViewQueryParam extends PageParam {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "设备位置Id")
    private String locationId;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "资产状态(0闲置1正常2故障3失效4限用5禁用6借出7报废8变卖)")
    private Integer status;

    @ApiModelProperty(value = "0其他1校准单")
    private Integer type;

    @ApiModelProperty(value = "设备ids")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "是否客户平台(0否1是)")
    private Integer isClient = StaticValue.ZERO;

}
