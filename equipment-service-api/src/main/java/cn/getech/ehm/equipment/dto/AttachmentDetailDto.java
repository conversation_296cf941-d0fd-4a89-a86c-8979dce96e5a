package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 * 附件表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-13
 */
@Data
@ApiModel(value = "AttachmentDetailDto", description = "附件表返回数据模型")
public class AttachmentDetailDto {
    @ApiModelProperty(value = "附件id")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "附件名称")
    @Excel(name="附件名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "附件存储路径")
    @Excel(name="附件存储路径",cellType = Excel.ColumnType.STRING )
    private String url;

    @ApiModelProperty(value = "文件类型（后缀）")
    private String fileType;

    @ApiModelProperty(value = "文件大小（KB）")
    private Integer fileSize;

    @ApiModelProperty(value = "上传人")
    @Excel(name="上传人",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "上传人名称")
    private String createUserName;

    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name="上传时间",cellType = Excel.ColumnType.STRING )
    private Date createTime;

}