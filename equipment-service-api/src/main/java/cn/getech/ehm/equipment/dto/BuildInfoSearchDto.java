package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * 查询过滤设备id
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@Builder
@ApiModel(value = "BuildInfoSearchDto", description = "查询过滤设备id")
public class BuildInfoSearchDto {
    @ApiModelProperty(value = "设备ID")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "是否过滤")
    private Boolean flag = false;

    /**
     * 包含父位置id、子位置所有id
     */
    @ApiModelProperty(value = "位置ID")
    private List<String> locationIds;

}