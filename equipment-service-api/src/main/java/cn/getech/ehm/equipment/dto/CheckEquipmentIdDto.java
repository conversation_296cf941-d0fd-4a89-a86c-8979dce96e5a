package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 校验客户端
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "CheckEquipmentIdDto", description = "校验客户端")
public class CheckEquipmentIdDto {
    @ApiModelProperty(value = "0其他1审核用户2客户/工程师")
    private Integer type = StaticValue.ZERO;

    @ApiModelProperty(value = "设备ids")
    private List<String> equipmentIds;
}