package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <pre>
 * 设备表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "InfoSearchDto", description = "设备表不分页查询参数")
public class InfoSearchDto extends PageParam {

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备IDs")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "设备位置Id")
    private String locationId;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "是否客户分配0否1是")
    private Integer assign;

    @ApiModelProperty(value = "查询类型(0全设备1故障报修查询2工单查询3计划单查询)")
    private Integer searchType = StaticValue.ZERO;

    @ApiModelProperty(value = "不允许分配的设备id集合", hidden = true)
    private List<String> noEquipmentIds;

    @ApiModelProperty(value = "主设备IDs")
    private List<String> rootEquipmentIds;

}
