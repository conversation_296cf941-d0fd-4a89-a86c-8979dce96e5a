package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * <pre>
 * 设备告警 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "WarnAppQueryParam", description = "设备告警查询参数")
public class WarnAppQueryParam{
    @ApiModelProperty(value = "编码/名称")
    private String keyword;

    @ApiModelProperty(value = "设备位置Ids(选取全部，传值null)")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备类型Ids(选取全部，传值null)")
    private List<String> categoryIds;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "设备id集合")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "告警状态(1告警中3结束)")
    private Integer status;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNo;

    @ApiModelProperty(value = "页大小", example = "10")
    private Integer limit;

    @ApiModelProperty(value = "是否客户平台(0否1是)")
    private Integer isClient = StaticValue.ZERO;
}
