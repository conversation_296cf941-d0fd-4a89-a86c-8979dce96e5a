package cn.getech.ehm.equipment.client;

import cn.getech.ehm.equipment.dto.*;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/3
 */
@FeignClient(name = "equipment-service", path = "/api/equipment-service")
public interface EquipmentClient {

    /**
     * 获取摘要信息
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/equipmentInfo/getEquipmentInfoById")
    RestResponse<EquipmentInfoDto> getEquipmentInfo(@RequestParam("id") String id);

    /**
     * 校验相关id是否被设备使用
     *
     * @param relatedId
     * @return
     */
    @GetMapping("/equipmentInfo/checkSupplierUsed")
    RestResponse<Boolean> checkSupplierUsed(@RequestParam("relatedId") String relatedId);

    /**
     * 根据设备编码和设备名称，判设备是否存在
     *
     * @param code 设备编码
     * @param name 设备名称
     * @return
     */
    @GetMapping("/equipmentInfo/checkExists")
    RestResponse<Boolean> checkExists(@RequestParam("code") String code, @RequestParam(value = "name", required = false) String name);

    /**
     * 根据字段查询数据库中存在的条数
     *
     * @param tableName  表名
     * @param columnName 字段名
     * @param value      值
     * @return
     */
    @GetMapping("/equipmentType/getCount")
    RestResponse<Integer> getCount(@RequestParam("tableName") String tableName,
                                   @RequestParam("columnName") String columnName,
                                   @RequestParam("value") String value);

    /**
     * 校验备件是否被设备相关使用CalibrationTaskOrderController
     *
     * @param partIds
     * @return
     */
    @PostMapping("/equipmentInfo/checkPartUsed")
    RestResponse<List<String>> checkPartUsed(@RequestBody String[] partIds);

    /**
     * 根据id集合获取设备列表
     *
     * @param equipmentIds
     * @return
     */
    @PostMapping("/equipmentInfo/getListByIds")
    RestResponse<Map<String, EquipmentListDto>> getListByIds(@RequestBody String[] equipmentIds);

    /**
     * 获取设备概要信息列表
     *
     * @param searchDto
     * @return
     */
    @PostMapping("/equipmentInfo/getSummaryMap")
    RestResponse<Map<String, EquipmentSummaryDto>> getSummaryMap(@RequestBody EquipmentInfoSearchDto searchDto);

    /**
     * 根据设备id集合获取设备维护人员
     *
     * @param equipmentIds
     * @return
     */
    @PostMapping("/equipmentInfo/getMaintainerListByIds")
    RestResponse<List<EquipmentMaintainerDto>> getMaintainerListByIds(@RequestBody String[] equipmentIds);

    /**
     * 根据查询条件获取设备ids
     */
    @PostMapping("/equipmentInfo/getEquipmentIdsByParam")
    RestResponse<List<String>> getEquipmentIdsByParam(@RequestBody EquipmentInfoSearchDto param);

    /**
     * 根据查询条件获取设备ids
     */
    @PostMapping("/equipmentInfo/getEquipmentIdsByParamNonAuth")
    RestResponse<List<String>> getEquipmentIdsByParamNonAuth(@RequestBody EquipmentInfoSearchDto param);


    /**
     * 获取当前用户权限
     *
     * @return
     */
    @GetMapping(value = "/equipmentAuthorization/getCurrentUserAuz")
    RestResponse<List<String>> getCurrentUserAuz();

    /**
     * 获取类别子类ID集合
     *
     * @param categoryId
     * @return 类别ID集合
     */
    @GetMapping(value = "/equipmentType/getChildIds/{categoryId}")
    RestResponse<List<String>> getChildIds(@PathVariable String categoryId);

    /**
     * 根据id列表获取所有的子类ID集合
     *
     * @param idList
     * @return
     */
    @PostMapping(value = "/equipmentType/fetchCategoryIdsByCategoryParentIds")
    RestResponse<List<String>> fetchCategoryIdsByCategoryParentIds(@RequestBody List<String> idList);

    /**
     * 根据设备名称模糊查询设备id
     *
     * @param equipmentName
     * @return
     */
    @GetMapping("/equipmentInfo/getInfosIdByName")
    RestResponse<List<String>> getInfoIdsByName(@RequestParam(value = "equipmentName",required = false) String equipmentName);

    /**
     * 根据类型ids、位置ids获取设备信息集合
     */
    @PostMapping("/equipmentInfo/getEquipmentStatistics")
    RestResponse<Map<String, EquipmentStatisticsDto>> getEquipmentStatistics(@RequestBody InfoSearchDto infoSearchDto);


    /**
     * 更新设备
     */
    @PutMapping("/equipmentInfo")
    RestResponse<Boolean> update(@RequestBody EquipmentInfoEditParam equipmentInfoEditParam);

    /**
     * 获取设备类型map
     */
    @GetMapping("/equipmentType/getCategoryStatistics")
    RestResponse<Map<String, CategoryStatisticsDto>> getCategoryStatistics();

    /**
     * 获取类型名称模糊查询code
     */
    @GetMapping("/equipmentType/getCodesByName")
    RestResponse<List<String>> getCodesByName(String name);

    /**
     * 获取设备位置map
     */
    @PostMapping("/equipmentLocation/getLocationStatistics")
    RestResponse<Map<String, LocationStatisticsDto>> getLocationStatistics(@RequestBody String[] locationIds);

    /**
     * 根据流程引擎ID获取资源异动
     *
     * @param processInstanceId
     * @return
     */
    @GetMapping("/equipmentChange/getProcessInstanceId")
    RestResponse<EquipChangeDto> getEquipChangeByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 初始化公用设备类型数据
     */
    @GetMapping(value = "/equipmentType/initialization")
    RestResponse<Boolean> initialization(@RequestParam("tenantId") String tenantId);

    /**
     * 获取首页设备管理(告警)
     */
    @PostMapping("/equipmentInfoApp/warn/firstPage")
    RestResponse<PageResult<WarnFirstPageDto>> firstPage(@RequestBody WarnAppQueryParam warnAppQueryParam);

    /**
     * app首页搜索
     *
     * @param pageParam
     * @return
     */
    @PostMapping("/equipmentInfoApp/search")
    RestResponse<List<EquipmentListDto>> search(@RequestBody PageParam pageParam);

    /**
     * 判断是否客户/工程师/审核用户
     */
    @GetMapping("/equipmentInfo/getClientEquipmentIds")
    RestResponse<CheckEquipmentIdDto> getClientEquipmentIds();

    /**
     * 分页获取设备列表
     */
    @PostMapping("/equipmentInfo/listPlanView")
    RestResponse<PageResult<EquipmentInfoPlanViewDto>> pageListPlanView(@RequestBody EquipmentInfoPlanViewQueryParam equipmentInfoPlanViewQueryParam);

    /**
     * 参数监测设备结构树
     */
    @PostMapping("/equipmentInfo/paramEquipmentTree")
    RestResponse<List<EquipmentTreeDto>> paramEquipmentTree(@RequestBody RelSearchDto relSearchDto);

    /**
     * 参数树查询(只查询设备)
     *
     * @param relSearchDto
     * @return
     */
    @PostMapping("/equipmentInfo/searchTree")
    RestResponse<List<EquipmentTreeDto>> searchTree(@RequestBody RelSearchDto relSearchDto);

    /**
     * 获取类别层级编码
     *
     * @param categoryIdList
     * @return 获取类别层级编码
     */
    @PostMapping(value = "/equipmentType/layerCode")
    RestResponse<Map<String, String>> getLayerCode(@RequestBody List<String> categoryIdList);

    /**
     * 获取设备最顶级主设备id
     */
    @GetMapping(value = "/equipmentInfo/getRootInfoId")
    RestResponse<String> getRootInfoId(@RequestParam String equipmentId);

    /**
     * 获取组态设备树(子设备、部件)
     */
    @PostMapping("/equipmentInfo/getStudioEquipmentBomList")
    RestResponse<List<StudioEquipmentBomDto>> getStudioEquipmentBomList(@RequestBody RelSearchDto relSearchDto);

    /**
     * 根据id集合获取设备类型名称集合
     */
    @PostMapping("/equipmentType/getMapByIds")
    RestResponse<Map<String, String>> getCategoryMapByIds(@RequestBody String[] categoryIds);

    /**
     * 根据id集合获取设备位置名称集合
     */
    @PostMapping("/equipmentLocation/getMapByIds")
    RestResponse<Map<String, String>> getLocationMapByIds(@RequestBody String[] locationIds);

    /**
     * 根据id集合获取设备部件名称集合
     */
    @PostMapping("/equipmentInfo/getStructureMapByIds")
    RestResponse<Map<String, String>> getStructureMapByIds(@RequestBody String[] structureIds);

    /**
     * 获取监测中所有特种参数
     */
    @GetMapping(value = "/equipmentInfo/getFeatureParameter")
    RestResponse<List<FeatureParameterDto>> getFeatureParameter(@RequestParam String equipmentId);

    /**
     * 获取设备转速范围
     */
    @PostMapping("/equipmentInfo/getEquipmentRateMap")
    RestResponse<Map<String, EquipmentRateDto>> getEquipmentRateMap(@RequestBody String[] equipmentIds);

    /**
     * 更新设备运行状态记录
     */
    @PostMapping("/infoStatus/updateStatus")
    RestResponse<Boolean> updateStatus(@RequestBody EquipmentIotStatusDto dto);

    /**
     * 更新设备报警状态记录
     */
    @PostMapping("/infoStatus/updateIotStatus")
    RestResponse<Boolean> updateIotStatus(@RequestBody EquipmentIotStatusDto dto);

    @ApiOperation("获取特种设备预警数量-下次检验日期")
    @GetMapping("/equipmentInfo/getSpecialWarningCount/next")
    public RestResponse<String> getSpecialWarningCountOfNext(@RequestParam("param") String param);

    @GetMapping("/equipmentInfo/getSpecialWarningCount/over/remind")
    public RestResponse<String> getSpecialWarningCountOfOverRemind(@RequestParam("param") String param);

    @GetMapping("/equipmentInfo/getSpecialWarningCount/over/next")
    public RestResponse<String> getSpecialWarningCountOfOverNext(@RequestParam("param") String param);

    @GetMapping("/specialManager/getSpecialWarningCount/next")
    public RestResponse<String> getSpecialWarningPersonCountOfNext(@RequestParam("param") String param);

    @GetMapping("/specialManager/getSpecialWarningCount/over/remind")
    public RestResponse<String> getSpecialWarningPersonCountOfOverRemind(@RequestParam("param") String param);

    @GetMapping("/specialManager/getSpecialWarningCount/over/next")
    public RestResponse<String> getSpecialWarningPersonCountOfOverNext(@RequestParam("param") String param);

    /**
     * 获取当前设置权限的用户可查看设备ids
     */
    @GetMapping("/equipmentInfo/currentUserInfoIds")
    RestResponse<BuildInfoSearchDto> getCurrentUserInfoIds();

    /**
     * 获取全部设备类型
     */
    @GetMapping("/equipmentType/getAllCategory")
    RestResponse<List<CategoryInfoDto>> getAllCategory();

    /**
     * 获取零部件对应名称
     */
    @PostMapping("/equipmentInfo/getStructureNameMap")
    RestResponse<Map<String, String>> getStructureNameMap(@RequestBody String[] structureIds);

    /**
     * 根据ids获取dtos(包含子节点)
     * @param categoryIds
     * @return
     */
    @PostMapping("/equipmentType/getDtosIncludeChild")
    RestResponse<List<CategoryInfoDto>> getDtosIncludeChild(@RequestBody(required = false) String[] categoryIds);

    /**
     * 获取设备类型id/全路径名称MAP
     * @return
     */
    @GetMapping("/equipmentType/getCategoryAllNameMap")
    RestResponse<Map<String, String>> getCategoryAllNameMap();

    @ApiOperation("新增诊断案例")
    @PostMapping("/diagnosisCase")
    public RestResponse<Boolean> add(@RequestBody DiagnosisCaseAddParam diagnosisCaseAddParam);

    /**
     * oee分页获取设备列表
     */
    @PostMapping("/equipmentInfo/oeeEquipmentPageList")
    RestResponse<PageResult<OeeDetailListDto>> oeeEquipmentPageList(@RequestBody OeeQueryParam queryParam);

    /**
     * oee获取设备列表
     */
    @PostMapping("/equipmentInfo/oeeEquipmentList")
    RestResponse<List<OeeDetailListDto>> oeeEquipmentList(@RequestBody OeeQueryParam queryParam);

    /**
     * 获取开启oee的设备ids
     * @return
     */
    @PostMapping("/equipmentInfo/getOeeOpenInfoIds")
    RestResponse<List<String>> getOeeOpenInfoIds();

    /**
     * 更新设备停机标识
     */
    @GetMapping("/equipmentInfo/updateEquipmentStopEnable")
    RestResponse<Boolean> updateEquipmentStopEnable(@RequestParam String equipmentId, @RequestParam Boolean enabled);

    @GetMapping("/equipmentInfo/quoteCategoryStructure")
    public RestResponse<Boolean> quoteCategoryStructure(@RequestParam String equipmentId,@RequestParam String categoryId);

    @GetMapping("/equipmentInfo/getStructureByEquipmentId")
    public RestResponse<List<EquipmentStructureDto>> getStructureByEquipmentId(@RequestParam String equipmentId);

    @PostMapping("/equipmentInfo/getListByStructureId")
    public RestResponse<List<EquipmentStructureDto>> getListByStructureId(@RequestBody List<String> equipmentId);

    /**
     * 获取设备是否停机
     * @return
     */
    @GetMapping("/equipmentInfo/getInfoParamStop")
    RestResponse<EquipmentSummaryDto> getInfoParamStop(@RequestParam String equipmentId);

    /**
     * 设备同步选中类型部件
     * @param dto
     * @return
     */
    @PostMapping("/equipmentInfo/quoteSelectCategoryStructure")
    RestResponse<Map<String, String>> quoteSelectCategoryStructure(@RequestBody QuoteCategoryStructureDto dto);

    /**
     * 获取位置信息
     * @param locationIds
     * @return
     */
    @PostMapping("/equipmentLocation/getMapByLocationIds")
    RestResponse<Map<String, LocationSummaryDto>> getMapByLocationIds(@RequestBody String[] locationIds);

    /**
     * 获取设备寿命预警数量
     */
    @GetMapping("/equipmentInfo/getStatisticsOfRemainingLifeCount")
    RestResponse<String> getStatisticsOfRemainingLifeCount(@RequestParam("param") String param);

    /**
     * 清空健康指数
     * @param equipmentId
     * @return
     */
    @GetMapping("/equipmentInfo/clearRemainingLife")
    RestResponse<Boolean> clearRemainingLife(@RequestParam String equipmentId);

    /**
     * 算法修改设备残余寿命/健康指数/健康状态
     */
    @PostMapping("/equipmentInfo/algorithmUpdateEquipment")
    RestResponse<Boolean> algorithmUpdateEquipment(@RequestBody InfoDefaultParamDto dto);

    /**
     * 根据设备编码获取设备信息
     */
    @GetMapping("/equipmentInfo/getEquipmentInfoByCode")
    RestResponse<EquipmentSummaryDto> getEquipmentInfoByCode(@RequestParam String equipmentCode);

    /**
     * 根据设备编码获取设备ID
     */
    @PostMapping("/equipmentInfo/getIdByCode")
    RestResponse<Map<String, String>> getEquipmentIdByCode(@RequestBody String[] codes);

    @GetMapping("/equipmentInfo/getFirstIdByCode")
    RestResponse<String> getFirstIdByCode(@RequestParam("code") String code);
}