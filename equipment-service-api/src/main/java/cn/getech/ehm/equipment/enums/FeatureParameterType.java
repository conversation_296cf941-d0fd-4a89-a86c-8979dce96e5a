package cn.getech.ehm.equipment.enums;

import cn.getech.ehm.common.dto.EnumListDto;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 特征参数枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum FeatureParameterType {
    STAT_BAR_PAS_CHAR(1,"定子条通过频率", "StatBarPasChar"),
    SLIP_CHAR(2,"滑差频率", "SlipChar"),
    POLE_PAS_CHAR(3,"极通过频率", "PolePasChar"),
    VIB_CHAR(4,"转动频率", "RotaChar"),
    ROTOR_BAR_PAS_CHAR(5,"转子条通过频率", "RotorBarPasChar"),
    BPI(6,"内圈故障频率", "BPI"),
    BPO(7,"外圈故障频率", "BPO"),
    BS(8,"滚动体故障频率", "BS"),
    FT(9,"保持架故障频率", "FT"),
    MESH_CHAR(10,"啮合频率", "MeshChar"),
    BLA_PAS_CHAR(11,"叶片通过频率", "BlaPasChar"),
    BELT_PAS_CHAR(12,"皮带通过频率", "BeltPasChar");
    //ROTA_RATIO(13,"速比", "RotaRatio");


    FeatureParameterType(int value, String name, String code) {
        this.value = value;
        this.name = name;
        this.code = code;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(FeatureParameterType featureParameterType : FeatureParameterType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(featureParameterType.value);
            enumListDto.setName(featureParameterType.name);
            enumListDto.setCode(featureParameterType.code);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByCode(String code){
        if(StringUtils.isNotBlank(code)) {
            for (FeatureParameterType paramType : FeatureParameterType.values()) {
                if (paramType.getCode().equals(code)) {
                    return paramType.getName();
                }
            }
        }
        return null;
    }

    private int value;

    private String name;

    private String code;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() { return code; }

    public void setCode(String code) { this.code = code; }
}
