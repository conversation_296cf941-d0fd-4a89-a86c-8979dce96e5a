package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * iot同步对象
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "IotEquipmentDto", description = "iot同步对象")
public class IotEquipmentDto {

    @ApiModelProperty(value = "iot设备名称")
    private String deviceName;

    @ApiModelProperty(value = "iot设备id")
    private String deviceId;

    @ApiModelProperty(value = "ehm设备id")
    private String outDeviceId;

}