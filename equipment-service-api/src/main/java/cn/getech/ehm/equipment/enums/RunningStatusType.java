package cn.getech.ehm.equipment.enums;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备运行状态
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum RunningStatusType {
    /*NORMAL(0, "运行", "#6dcd7a"),
    OFF_LINE(1,"离线", "#43a2ff"),
    FAULT(2, "非计划性停机", "#ff9900"),
    STOP(3,"计划性停机", "#fc5a3d");*/

    OFF_LINE(1,"离线", "#43a2ff"),
    STOP(2, "关机", "#ff9900"),
    NORMAL(3, "作业", "#6dcd7a"),
    STAY(4,"待机", "#fc5a3d");


    RunningStatusType(int value, String name, String color) {
        this.value = value;
        this.name = name;
        this.color = color;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(runningStatusType.value);
            enumListDto.setName(runningStatusType.name);
            enumListDto.setColor(runningStatusType.color);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByValue(int value){
        switch (value){
            /*case 0 : return RunningStatusType.NORMAL.name;
            case 1 : return RunningStatusType.OFF_LINE.name;
            case 2 : return RunningStatusType.FAULT.name;
            case 3 : return RunningStatusType.STOP.name;*/
            case 1 : return RunningStatusType.OFF_LINE.name;
            case 2 : return RunningStatusType.STOP.name;
            case 3 : return RunningStatusType.NORMAL.name;
            case 4 : return RunningStatusType.STAY.name;
        }
        return null;
    }

    public static Integer getValueByName(String name){
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            if(runningStatusType.getName().equals(name)){
                return runningStatusType.value;
            }
        }
        return null;
    }

    public static RunningStatusType getEnumByValue(int value){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(RunningStatusType runningStatusType : RunningStatusType.values()){
            if(runningStatusType.getValue() == value){
                return runningStatusType;
            }
        }
        return null;
    }

    private int value;

    private String name;

    private String color;

    public String getColor() { return color; }

    public void setColor(String color) { this.color = color; }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
