package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * oee列表dto
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "OeeDetailListDto", description = "oee列表dto")
public class OeeDetailListDto {

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "上级节点名称")
    private String parentAllName;

    @ApiModelProperty(value = "类型id")
    private String categoryId;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "类型")
    private String categoryAllName;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "设备运行状态名称")
    private String runningStatusName;

    @ApiModelProperty(value = "OEE(%)")
    private Double oee = 0d;

    @ApiModelProperty(value = "AF(%)")
    private Double af = 0d;

    @ApiModelProperty(value = "QF(%)")
    private Double qf = 0d;

    @ApiModelProperty(value = "PF(%)")
    private Double pf = 0d;

    @ApiModelProperty(value = "班次开动时长")
    private Long opDuration = 0l;

    @ApiModelProperty(value = "班次负荷时长")
    private Long ldDuration = 0l;

    @ApiModelProperty(value = "班次合格品数量")
    private Integer qualifiedQty = 0;

    @ApiModelProperty(value = "班次加工数量")
    private Integer processedQty = 0;
}