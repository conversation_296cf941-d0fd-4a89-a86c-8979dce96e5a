package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 所有用户查询dto
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "PorosStaffSearchDto", description = "所有用户查询dto")
public class PorosStaffSearchDto {
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "uid")
    private String uid;
}