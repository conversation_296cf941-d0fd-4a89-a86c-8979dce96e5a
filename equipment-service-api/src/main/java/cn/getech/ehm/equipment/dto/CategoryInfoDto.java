package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "CategoryInfoDto", description = "设备类型信息")
public class CategoryInfoDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型编码")
    private String code;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "层级编码")
    private String layerCode;

    @ApiModelProperty(value = "父节点id")
    private String parentId;
}