package cn.getech.ehm.equipment.enums;

import lombok.Getter;

/**
 * 设备位置
 * <AUTHOR> @date 2022/10/27
 */
@Getter
public enum ControlCabinWorkshopEnum {
    M1_WORKSHOP_1ST("357a977c3aee4974aa837da0d782e15f"),//M1车间1层
    M1_3WORKSHOP("46563328deca4a0db96d9207bf52c72d"),//M1(3)车间
    M3_1WORKSHOP("107ef2f781de482abba1c7f36b13db42"),//M3(1)车间
    M3_2WORKSHOP("97970b4fee26413098d68893c4b36db5"),//M3(2)车间
    M6_WORKSHOP("10caf0d442eb46c9801827191146d81d"),//M6车间
    M7_WORKSHOP("01fc16004b1548b9b4fe74a86823934d"),//M7车间
    M8_WORKSHOP("d7371cf6c4e64e92a128f27cbd79cc1d");//M8车间


    ControlCabinWorkshopEnum(String name) {
        this.name = name;
    }

    private String name;


}
