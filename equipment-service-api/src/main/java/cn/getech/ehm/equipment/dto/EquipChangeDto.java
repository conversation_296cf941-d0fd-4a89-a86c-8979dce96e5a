package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <pre>
 * 设备异动表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-08-25
 */
@Data
@ApiModel(value = "EquipmentChangeDto", description = "设备异动表返回数据模型")
public class EquipChangeDto {

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "异动类型(0领用,1移转 2借用)")
    private Integer type;

    @ApiModelProperty(value = "异动地点")
    private String locationName;

    @ApiModelProperty(value = "责任人姓名")
    private String dutyName;

    @ApiModelProperty(value = "责任人部门")
    private String dutyDept;

    @ApiModelProperty(value = "状态 0 审批中 1 审批通过 2 驳回")
    private Integer status;

    @ApiModelProperty(value = "异动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

}