package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备部件 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-20
 */
@Data
@ApiModel(value = "EquipmentStructureDto", description = "设备部件编辑dto")
public class EquipmentStructureDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "零部件类型id")
    private String sparePartsCategoryId;

    @ApiModelProperty(value = "零部件类型名称")
    private String sparePartsCategoryName;

    @ApiModelProperty(value = "基础库类型")
    private String basicLibraryId;

    @ApiModelProperty(value = "基础库")
    private String basicLibraryName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "类型(1部件2零件)")
    private Integer type;

    @ApiModelProperty(value = "添加来源(1手动2设备类型)")
    private Integer sourceType;

    @ApiModelProperty(value = "是否监测")
    private Boolean monitored;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "子节点")
    private List<EquipmentStructureDto> children;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "是否拥有参数")
    private Boolean hasParam = false;

    @ApiModelProperty(value = "关联id(轴承id、齿轮id)")
    private String relationId;

    //来源id(设备类型结构引用,则为设备类型结构id
    @ApiModelProperty(value = "来源id(设备类型结构引用,则为设备类型结构id")
    private String sourceId;

    @ApiModelProperty(value = "级别(1,2)")
    private Integer level = 2;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty("备件id")
    private String partId;

    @ApiModelProperty("备件名")
    private String partName;

    @ApiModelProperty("备件code")
    private String partCode;

    @ApiModelProperty("备件剩余库存")
    private BigDecimal partCurrentStock;
}