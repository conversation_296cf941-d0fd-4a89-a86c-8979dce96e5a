package cn.getech.ehm.equipment.client;

import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-04 13:33:45
 **/
@FeignClient(name = "scada-service", path = "/api/scada-service")
public interface ScadaClient {

    /**
     * 判断设备是否被使用
     *
     * @param equipmentIds
     * @return
     */
    @PostMapping("/definition/checkInfoIdsUsed")
    RestResponse<List<String>> checkInfoIdsUsed(@RequestBody String[] equipmentIds);

}
