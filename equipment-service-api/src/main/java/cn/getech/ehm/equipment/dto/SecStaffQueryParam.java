package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-04-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SecStaff控制台查询", description = "查询参数")
public class SecStaffQueryParam extends PageParam {

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("岗位名称")
    private String posName;

    @ApiModelProperty("租户标识")
    private String tenantId;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty(value = "职位code")
    private String positionCode;

    @ApiModelProperty("用户名/姓名")
    private String keyword;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("是否包含子组织")
    private Boolean isDeepSearch = false;

    @ApiModelProperty("是否查询无组织用户")
    private Boolean isSearchNonOrg = false;

    @ApiModelProperty("用户类型")
    private String dataSources;

    @ApiModelProperty("条数限制")
    @Range(max = 500)
    private Integer limit;

    @ApiModelProperty("uid")
    private String uid;

    @ApiModelProperty("名称")
    private String name;

}
