package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备摘要信息DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "EquipmentSummaryDto", description = "设备信息返回数据模型")
public class EquipmentSummaryDto {

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "编码")
    private String equipmentCode;

    @ApiModelProperty(value = "位号")
    private String equipmentItemNo;

    @ApiModelProperty(value = "名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型全名称(向上追溯三级)")
    private String categoryAllName;

    @ApiModelProperty(value = "设备类型编码")
    private String categoryCode;

    @ApiModelProperty(value = "设备类型层级编码")
    private String categoryLayerCode;

    @ApiModelProperty(value = "设备位置id")
    private String locationId;

    @ApiModelProperty(value = "设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "设备位置层级编码")
    private String locationLayerCode;

    @ApiModelProperty(value = "设备状态")
    private String assetStatus;

    @ApiModelProperty(value = "上级节点名称")
    private String parentAllName;

    @ApiModelProperty(value = "设备运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "设备健康状态")
    private Integer healthStatus;

    @ApiModelProperty(value = "参数报警对应设备状态")
    private Integer iotStatus;

    @ApiModelProperty(value = "参数停机标识")
    private Boolean paramStopEnable;

    @ApiModelProperty(value = "设备全路径名称(子设备)")
    private String equipmentAllName;

    @ApiModelProperty(value = "最大转速")
    private Integer maxRate;

    @ApiModelProperty(value = "最小转速")
    private Integer minRate;

    @ApiModelProperty(value = "健康指数")
    private Double healthIndex;

    @ApiModelProperty(value = "剩余寿命")
    private Integer remainingLife;
}