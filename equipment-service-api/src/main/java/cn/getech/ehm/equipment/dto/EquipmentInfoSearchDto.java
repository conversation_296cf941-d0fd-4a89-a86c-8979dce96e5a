package cn.getech.ehm.equipment.dto;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "EquipmentInfoSearchDto", description = "设备表不分页查询参数")
public class EquipmentInfoSearchDto {

    @ApiModelProperty(value = "设备名称/编码/位号", hidden = true)
    private String keyword;

    @ApiModelProperty(value = "设备id")
    String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备IDs")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "设备位置Id")
    private String locationId;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "是否包含设备子类型")
    private Boolean containCategoryChild = true;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "是否客户分配0否1是")
    private Integer assign;

    @ApiModelProperty(value = "查询类型(0全设备1故障报修2工单)")
    private Integer searchType = StaticValue.ZERO;

    @ApiModelProperty(value = "不允许分配的设备id集合", hidden = true)
    private List<String> noEquipmentIds;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    private String sortValue;

    private String tenantId;

    @ApiModelProperty(value = "设备运行状态")
    private Integer runningStatus;

}
