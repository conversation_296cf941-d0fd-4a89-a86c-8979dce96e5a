package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <pre>
 * 设备表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentStatisticsDto", description = "设备表统计返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentStatisticsDto {
    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "编码")
    private String equipmentCode;

    @ApiModelProperty(value = "名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "设备类型编码")
    private String categoryCode;

    @ApiModelProperty(value = "设备类型层级编码")
    private String categoryLayerCode;

    @ApiModelProperty(value = "设备位置id")
    private String locationId;

    @ApiModelProperty(value = "设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "设备位置层级编码")
    private String locationLayerCode;

    @ApiModelProperty(value = "设备状态")
    private Integer status;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "制造商id")
    private String manufacturerId;

    @ApiModelProperty(value = "制造商名称")
    private String manufacturerName;

}