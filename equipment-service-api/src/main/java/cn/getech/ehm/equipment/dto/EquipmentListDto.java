package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * <pre>
 * 设备表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-08-03
 */
@Data
@ApiModel(value = "EquipmentListDto", description = "设备表返回数据模型")
public class EquipmentListDto {
    @ApiModelProperty(value = "设备类别(1主设备2子设备)")
    private Integer equipmentType;

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "编码")
    private String equipmentCode;

    @ApiModelProperty(value = "名称")
    private String equipmentName;

    @ApiModelProperty(value = "位号")
    private String equipmentItemNo;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "管理责任人")
    private String managePrincipal;

    @ApiModelProperty(value = "管理责任人uid")
    private String managePrincipalUid;

    @ApiModelProperty(value = "使用责任人")
    private String usePrincipal;

    @ApiModelProperty(value = "结构位置id")
    private String parentId;

    @ApiModelProperty(value = "结构位置名称")
    private String parentName;

    @ApiModelProperty(value = "结构位置名称")
    private String parentAllName;

    @ApiModelProperty(value = "类型Id")
    private String categoryId;

    @ApiModelProperty(value = "类型")
    private String categoryName;

    @ApiModelProperty(value = "类型")
    private String categoryAllName;

    @ApiModelProperty(value = "位置Id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "位置层级编码")
    private String locationLayerCode;

    @ApiModelProperty(value = "客户Id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "型号规格")
    private String specification;

    @ApiModelProperty(value = "出厂日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date productionTime;

    @ApiModelProperty(value = "维护人员id列表")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护班组id列表")
    private String[] teamIds;

    @ApiModelProperty(value = "图片")
    private String picId;

    @ApiModelProperty(value = "图片URL")
    private String picUrl;

    @ApiModelProperty(value = "重要程度")
    private String importance;
}