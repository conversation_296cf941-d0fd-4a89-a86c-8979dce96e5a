package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentInfoDto", description = "设备表返回数据模型")
public class EquipmentInfoDto {
    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称")
    private String parentName;

    @ApiModelProperty(value = "设备位置id")
    private String locationId;

    @ApiModelProperty(value = "设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "设备位号")
    private String itemNo;

    @ApiModelProperty(value = "型号规格")
    private String specification;

    @ApiModelProperty(value = "投产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date completionTime;

    @ApiModelProperty(value = "重要程度")
    private String importance;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "是否特种设备")
    private Boolean specialInfo;

    @ApiModelProperty(value = "是否需要校验")
    private Boolean checked;

    @ApiModelProperty(value = "特种设备详情")
    private EquipmentSpecialDto specialDto;

    @ApiModelProperty(value = "校验日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkDate;

    @ApiModelProperty(value = "校验人员")
    private String checkUser;

    @ApiModelProperty(value = "校验状态")
    private String checkStatus;

    @ApiModelProperty(value = "是否联网")
    private Boolean networked;

    @ApiModelProperty(value = "是否监控")
    private Boolean monitored;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "健康状态")
    private Integer healthStatus;

    @ApiModelProperty(value = "制造商id")
    private String manufacturerId;

    @ApiModelProperty(value = "制造商名称")
    private String manufacturerName;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "设计单位id")
    private String designCompanyId;

    @ApiModelProperty(value = "设计单位名称")
    private String designCompanyName;

    @ApiModelProperty(value = "安装单位id")
    private String installCompanyId;

    @ApiModelProperty(value = "安装单位名称")
    private String installCompanyName;

    @ApiModelProperty(value = "出厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "出厂日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionTime;

    @ApiModelProperty(value = "设计年限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date designPeriod;

    @ApiModelProperty(value = "保修年限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date warrantyPeriod;

    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "资产状态")
    private String assetStatus;

    @ApiModelProperty(value = "成本中心code")
    private String costCenter;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "资产原值")
    private Double originalAsset;

    @ApiModelProperty(value = "资产净值")
    private Double netAsset;

    @ApiModelProperty(value = "管理部门code")
    private String manageDepart;

    @ApiModelProperty(value = "管理部门名称")
    private String manageDepartName;

    @ApiModelProperty(value = "管理责任人uid")
    private String managePrincipal;

    @ApiModelProperty(value = "管理责任人名称")
    private String managePrincipalName;

    @ApiModelProperty(value = "使用部门code")
    private String useDepart;

    @ApiModelProperty(value = "使用部门名称")
    private String useDepartName;

    @ApiModelProperty(value = "使用责任人uid")
    private String usePrincipal;

    @ApiModelProperty(value = "使用责任人名称")
    private String usePrincipalName;

    @ApiModelProperty(value = "维保责任人ids")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维保责任人名称")
    private String maintainerNames;

    @ApiModelProperty(value = "维保班组ids")
    private String[] teamIds;

    @ApiModelProperty(value = "维保班组名称")
    private String teamNames;

    @ApiModelProperty(value = "图片")
    private String picId;

    @ApiModelProperty(value = "扩展信息")
    private EquipmentPropMainDto propDtos;

    @ApiModelProperty(value = "扩展字段X")
    private String alternateFieldX;

    @ApiModelProperty(value = "扩展字段Y")
    private String alternateFieldY;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "设备二维码base64字符串,供前端显示和打印")
    private String base64;

    @ApiModelProperty(value = "文档附件id列表")
    private String docIds;

    @ApiModelProperty(value = "面包屑列表")
    private List<BomParentDto> bomParent  = new ArrayList<>();
}