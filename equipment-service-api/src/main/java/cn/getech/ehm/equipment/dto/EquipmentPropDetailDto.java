package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备扩展属性详情
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "EquipmentPropDetailDto", description = "设备扩展属性详情")
public class EquipmentPropDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型属性id")
    private String categoryPropId;

    @ApiModelProperty(value = "设备id", hidden = true)
    private String equipmentId;

    @ApiModelProperty(value = "组名称")
    private String groupName;

    @ApiModelProperty(value = "属性名称")
    private String name;

    @ApiModelProperty(value = "属性类型")
    private Integer propType;

    @ApiModelProperty(value = "属性定义")
    private String define;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "是否固定0否1是")
    private Integer fixed;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}