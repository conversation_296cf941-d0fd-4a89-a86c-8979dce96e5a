package cn.getech.ehm.equipment.enums;

/**
 * iot参数状态
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum IotParamStatusType {
    //其余按照报警等级值
    //OFF_LINE(-2,"离线", "#43a2ff"),
    //STOP(-1,"停机", "#2D9CF0"),
    NORMAL(0, "正常", "#36DF1A");


    IotParamStatusType(int value, String name, String color) {
        this.value = value;
        this.name = name;
        this.color = color;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static IotParamStatusType getEnumByValue(int value){
        switch (value){
            //case -2: return IotParamStatusType.OFF_LINE;
            //case -1: return IotParamStatusType.STOP;
            case 0:  return IotParamStatusType.NORMAL;
        }
        return null;
    }

    private int value;

    private String name;

    private String color;

    public String getColor() { return color; }

    public void setColor(String color) { this.color = color; }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
