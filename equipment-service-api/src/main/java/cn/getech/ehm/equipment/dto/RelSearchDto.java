package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 查询相关dto
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "RelSearchDto", description = "查询相关dto")
public class RelSearchDto {
    @ApiModelProperty(value = "设备名称/编码")
    private String keyword;

    @ApiModelProperty(value = "拥有参数的设备IDs", hidden = true)
    private List<String> equipmentIds;

    @ApiModelProperty(value = "拥有参数的部件IDs", hidden = true)
    private List<String> structureIds;

    @ApiModelProperty(value = "拥有参数的测点IDs", hidden = true)
    private List<String> measuringIds;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "父节点类型(0位置1设备2部件3测点)")
    private Integer parentType;

    @ApiModelProperty(value = "选中的测点参数关联id")
    private String checkRelId;

    @ApiModelProperty(value = "选中的设备id", hidden = true)
    private String checkEquipmentId;

    @ApiModelProperty(value = "选中的部件id", hidden = true)
    private String checkStructureId;

    @ApiModelProperty(value = "选中的测点id", hidden = true)
    private String checkMeasuringId;

    @ApiModelProperty(value = "数据源类型")
    private String dataSourceType;
}