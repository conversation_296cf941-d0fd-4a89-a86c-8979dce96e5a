package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 设备维护人员 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentMaintainerDto", description = "设备维护人员返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentMaintainerDto {
    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "维护人员id列表")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护班组id列表")
    private String[] teamIds;

}