package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 设备文档
 *
 * <AUTHOR>
 * @date 2020-08-03
 */
@Data
@ApiModel(value = "EquipmentDocDto", description = "设备文档")
public class EquipmentDocDto {

    @ApiModelProperty(value = "设备id")
    private String id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "类型(1主设备2子设备)")
    private Integer type;

    @ApiModelProperty(value = "上级节点(设备位置/上级设备)")
    private String parentId;

    @ApiModelProperty(value = "上级节点名称")
    private String parentName;

    @ApiModelProperty(value = "设备类型文档ids")
    private String commonDocIds;

    @ApiModelProperty(value = "设备类型文档dtos")
    List<AttachmentDetailDto> commonDocUrls;

    @ApiModelProperty(value = "设备文档ids")
    private String infoDocIds;

    @ApiModelProperty(value = "设备文档dtos")
    List<AttachmentDetailDto> infoDocUrls;

}