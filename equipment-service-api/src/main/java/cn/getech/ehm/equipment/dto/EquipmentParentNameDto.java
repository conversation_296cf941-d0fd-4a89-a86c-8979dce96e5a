package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备摘要信息DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "EquipmentParentNameDto", description = "设备父节点名称")
public class EquipmentParentNameDto {
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "设备全路径名称(子设备)")
    private String equipmentAllName;

    @ApiModelProperty(value = "设备位置全路径名称")
    private String locationAllName;

    @ApiModelProperty(value = "设备位置+设备路径")
    private String parentAllName;
}