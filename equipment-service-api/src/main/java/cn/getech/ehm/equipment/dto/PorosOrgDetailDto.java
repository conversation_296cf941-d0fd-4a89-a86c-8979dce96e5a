package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * poros部门
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "PorosOrgDetailDto", description = "poros部门")
public class PorosOrgDetailDto {

    @ApiModelProperty("组织ID")
    private String id;
    @ApiModelProperty("组织标识")
    private String code;
    @ApiModelProperty("组织名称")
    private String name;
    @ApiModelProperty("父级节点编码")
    private String parentCode;
    @ApiModelProperty("节点编码")
    private String codePath;
    @ApiModelProperty("节点路径")
    private String namePath;
    @ApiModelProperty("是否根节点")
    private String isRoot;
    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("数据来源")
    private String dataSources;
    @ApiModelProperty("是否叶节点")
    private Boolean leaf;
    @ApiModelProperty(value = "系统")
    private String systemCode;
}