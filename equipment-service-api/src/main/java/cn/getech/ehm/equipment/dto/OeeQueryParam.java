package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * oee查询
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OeeQueryParam", description = "oee查询")
public class OeeQueryParam extends PageParam {

    @ApiModelProperty(value = "位置ids")
    private String[] locationIds;

    @ApiModelProperty(value = "类型id")
    private String[] categoryIds;

    @ApiModelProperty(value = "设备id")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    @ApiModelProperty(value = "班次ID")
    private String rotationId;
}
