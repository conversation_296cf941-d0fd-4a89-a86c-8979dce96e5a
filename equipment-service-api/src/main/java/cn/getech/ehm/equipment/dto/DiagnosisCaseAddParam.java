package cn.getech.ehm.equipment.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 诊断案例 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisCase新增", description = "诊断案例新增参数")
public class DiagnosisCaseAddParam extends ApiParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "位置id")
    private String locationId;

    @ApiModelProperty(value = "位置名称")
    private String locationName;

    @ApiModelProperty(value = "分析图片")
    private String[] analyzePics;

    @ApiModelProperty(value = "分析结论")
    private String analyzeConclusion;

    @ApiModelProperty(value = "分析原因")
    private String analyzeReason;

    @ApiModelProperty(value = "处理建议")
    private String handlingSuggestions;

    @ApiModelProperty(value = "附件")
    private String[] docIds;

    @ApiModelProperty(value = "备注")
    private String remark;
}