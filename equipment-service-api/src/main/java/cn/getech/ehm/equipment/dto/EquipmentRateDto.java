package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "EquipmentRateDto", description = "设备转速")
public class EquipmentRateDto {
    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "最大转速")
    private Integer maxRate;

    @ApiModelProperty(value = "最小转速")
    private Integer minRate;
}