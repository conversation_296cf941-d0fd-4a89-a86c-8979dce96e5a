package cn.getech.ehm.equipment.enums;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备健康状态
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum HealthStatusType {
    NORMAL(0, "健康", "#7CFFB2", 70d, "设备近期趋势平稳"),
    MINOR_FAULTS(1,"亚健康","#58D9F9", 45d, "设备故障可接受，能够持续运行，且状态持续劣化"),
    MODERATE_FAULT(2, "异常","#FDDD60", 30d, "故障明显，只适合短期运行，需加强监测"),
    SEVERE_FAULT(3,"故障", "#FF6E76", 0d, "故障严重，需尽快检修");


    HealthStatusType(int value, String name, String color, Double rate, String content) {
        this.value = value;
        this.name = name;
        this.color = color;
        this.rate = rate;
        this.content = content;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(HealthStatusType runningStatusType : HealthStatusType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(runningStatusType.value);
            enumListDto.setName(runningStatusType.name);
            enumListDto.setColor(runningStatusType.color);
            enumListDto.setRate(runningStatusType.rate);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByValue(int value){
        for(HealthStatusType runningStatusType : HealthStatusType.values()){
            if(runningStatusType.getValue() == value){
                return runningStatusType.name;
            }
        }
        return null;
    }

    public static Integer getValueByName(String name){
        for(HealthStatusType runningStatusType : HealthStatusType.values()){
            if(runningStatusType.getName().equals(name)){
                return runningStatusType.value;
            }
        }
        return null;
    }

    public static HealthStatusType getEnumByValue(int value){
        for(HealthStatusType runningStatusType : HealthStatusType.values()){
            if(runningStatusType.getValue() == value){
                return runningStatusType;
            }
        }
        return null;
    }

    public static HealthStatusType getEnumByRate(Double rate){
        if(rate.compareTo(HealthStatusType.MODERATE_FAULT.getRate()) < 0){
            return HealthStatusType.SEVERE_FAULT;
        }else if(rate.compareTo(HealthStatusType.MODERATE_FAULT.getRate()) >= 0 && rate.compareTo(HealthStatusType.MINOR_FAULTS.getRate()) < 0){
            return HealthStatusType.MODERATE_FAULT;
        }else if(rate.compareTo(HealthStatusType.MINOR_FAULTS.getRate()) >= 0 && rate.compareTo(HealthStatusType.NORMAL.getRate()) < 0){
            return HealthStatusType.MINOR_FAULTS;
        }else{
            return HealthStatusType.NORMAL;
        }

    }

    private int value;

    private String name;

    private String color;

    private Double rate;

    private String content;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public String getContent() { return content; }

    public void setContent(String content) { this.content = content; }
}
