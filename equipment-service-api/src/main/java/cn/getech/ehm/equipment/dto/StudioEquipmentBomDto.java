package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 组态设备bom结构树
 * <AUTHOR>
 * @date 2020-09-14 18:50:04
 **/
@Data
@ApiModel(value = "StudioEquipmentBomDto", description = "组态设备bom结构树返回数据模型")
public class StudioEquipmentBomDto {

    @ApiModelProperty(value = "设备id/部件id/测点id")
    private String id;

    @ApiModelProperty("设备名称/部件名称/测点名称")
    private String name;

    /**
     * 设备父节点为设备
     * 部件父节点为设备，部件
     * 测点父节点为设备，部件
     */
    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty("类型(0设备1测点2部件)")
    private Integer type;

    @ApiModelProperty(value = "排序", hidden = true)
    private Integer sort;

    @ApiModelProperty("子集")
    private List<StudioEquipmentBomDto> children;

    @ApiModelProperty("是否是叶子节点")
    @JsonProperty(value = "isLeaf")
    private Boolean leaf = false;

    @ApiModelProperty("是否禁用")
    private Boolean disabled = true;

}
