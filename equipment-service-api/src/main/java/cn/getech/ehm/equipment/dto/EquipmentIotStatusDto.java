package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 设备状态表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-03
 */
@Data
@ApiModel(value = "EquipmentIotStatusDto", description = "设备状态返回数据模型")
public class EquipmentIotStatusDto {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "-1停机0正常其余按照报警配置状态")
    private Integer status;

    @ApiModelProperty(value = "标记时间")
    private Date markTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}