package cn.getech.ehm.equipment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备位置信息
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@ApiModel(value = "LocationSummaryDto", description = "设备位置信息")
public class LocationSummaryDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备位置名称")
    private String name;

    @ApiModelProperty(value = "设备位置全路径")
    private String allName;
}