package cn.getech.ehm.equipment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 默认设备状态参数
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "InfoDefaultParamDto", description = "默认设备状态参数")
public class InfoDefaultParamDto {

    @ApiModelProperty(value = "设备参数ID")
    private String equipmentId;

    @ApiModelProperty(value = "运行状态")
    private Integer runningStatus;

    @ApiModelProperty(value = "健康状态")
    private Integer healthStatus;

    @ApiModelProperty(value = "报警状态值")
    private Integer iotStatus;

    @ApiModelProperty(value = "健康指数")
    private Double healthIndex;

    @ApiModelProperty(value = "残余寿命")
    private Integer remainingLife;

    @ApiModelProperty(value = "处理日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date date;
}