server:
  port: 8030
  servlet:
    context-path: /api/knowledge-service

management:
  endpoint:
    health:
      show-details: ALWAYS
  server:
    port: 8031
  endpoints:
    web:
      exposure:
        include: '*'
  health:
    sentinel:
      enabled: false

spring:
  application:
    name: knowledge-service
  cloud:
    nacos:
      config:
        enabled: false
        server-addr: nacos.poros-platform:8848
        group: POROS_PLATFORM
        file-extension: yaml
        shared-configs[0]:
          dataId: poros-common.yaml
          group: POROS_PLATFORM
          refresh: true
      discovery:
        enabled: false
        server-addr: nacos.poros-platform:8848
        group: DEFAULT_GROUP
        metadata: {"service.desc": "knowledge-service"}
    sentinel:
      #取消Sentinel控制台懒加载
      #eager: true
      enabled: true
      transport:
        dashboard: sentinel.poros-platform:8080
        port: 8032
      datasource:
        ds1:
          nacos:
            server-addr: nacos.poros-platform:8848
            dataId: knowledge-service-rule.json
            groupId: POROS_PLATFORM
            ruleType: flow
            dataType: json