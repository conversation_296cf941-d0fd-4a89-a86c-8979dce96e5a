package cn.getech.ehm.knowledge.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 知识分类 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "KnowledgeCategoryDto", description = "知识分类对象返回数据模型")
public class KnowledgeCategoryDto {

    @ApiModelProperty(value = "类型。1-新闻，2-课程，3-文档分类")
    private Integer type;

    @ApiModelProperty(value = "跳转链接。")
    private String href;

    @ApiModelProperty(value = "icon图标。")
    private String icon;

    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "子分类数。")
    private Integer children;

    @ApiModelProperty(value = "文章总数。")
    private Integer total;

    @ApiModelProperty(value = "文档分类id。")
    private String categoryId;

}