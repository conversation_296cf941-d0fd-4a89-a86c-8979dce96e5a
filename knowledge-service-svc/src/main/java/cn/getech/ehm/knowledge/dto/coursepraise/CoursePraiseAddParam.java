package cn.getech.ehm.knowledge.dto.coursepraise;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 课程用户点赞关系表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CoursePraise新增", description = "课程用户点赞关系表新增参数")
public class CoursePraiseAddParam extends ApiParam {

}