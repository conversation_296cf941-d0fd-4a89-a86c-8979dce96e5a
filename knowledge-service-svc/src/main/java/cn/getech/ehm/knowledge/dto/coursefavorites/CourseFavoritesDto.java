package cn.getech.ehm.knowledge.dto.coursefavorites;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 课程用户收藏关系表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-16
 */
@Data
@ApiModel(value = "CourseFavoritesDto", description = "课程用户收藏关系表返回数据模型")
public class CourseFavoritesDto{

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "描述")
    @Excel(name="描述",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "课程ID")
    private String courseId;

}