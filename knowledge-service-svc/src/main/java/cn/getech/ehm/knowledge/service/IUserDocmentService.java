package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentAddParam;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentDto;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentQueryParam;
import cn.getech.ehm.knowledge.entity.UserDocment;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 用户文档表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
public interface IUserDocmentService extends IBaseService<UserDocment> {

    /**
     * 分页查询，返回Dto
     *
     * @param userDocmentQueryParam
     * @return
     */
    PageResult<UserDocmentDto> pageDto(UserDocmentQueryParam userDocmentQueryParam);

    PageResult<UserDocmentDto> appPageDto(UserDocmentQueryParam userDocmentQueryParam);

    /**
     * 保存
     * @param userDocmentAddParam
     * @return
     */
    boolean saveByParam(UserDocmentAddParam userDocmentAddParam);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    UserDocmentDto getDtoById(String id);

    /**
     * 批量保存
     * @param rows
     */
    boolean saveDtoBatch(List<UserDocmentDto> rows);

    boolean removeByIds(String[] ids);

    /**
     * 获取Uid 的附件拼接字符串
     * @param uid
     * @return
     */
    String getAttachsByUid(String uid);
}