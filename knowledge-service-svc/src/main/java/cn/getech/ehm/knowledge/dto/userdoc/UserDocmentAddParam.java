package cn.getech.ehm.knowledge.dto.userdoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 用户文档表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserDocment新增", description = "用户文档表新增参数")
public class UserDocmentAddParam extends ApiParam {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "附件ID(用,分隔的字符串)")
    private String attachIds;

    @ApiModelProperty(value = "名字(用,分隔的字符串)")
    private String names;

}