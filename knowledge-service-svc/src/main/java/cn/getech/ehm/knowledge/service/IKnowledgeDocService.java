package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.knowledgedoc.*;
import cn.getech.ehm.knowledge.entity.KnowledgeDoc;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;

import java.util.List;

/**
 * <pre>
 * 知识学习库 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface IKnowledgeDocService extends IBaseService<KnowledgeDoc> {

        /**
         * 分页查询，返回Dto
         *
         * @param knowledgeDocQueryParam
         * @return
         */
        PageResult<KnowledgeDocDto> pageDto(KnowledgeDocQueryParam knowledgeDocQueryParam);

        /**
         * app分页查询，返回Dto
         *
         * @param knowledgeDocQueryParam
         * @return
         */
        PageResult<KnowledgeDocDto> appPageList(KnowledgeDocQueryParam knowledgeDocQueryParam);

        /**
         * 分页查询客户端列表
         * @param knowledgeDocQueryParam
         * @return
         */
        PageResult<KnowledgeDocDto> pageCustomDto(KnowledgeDocQueryParam knowledgeDocQueryParam);

        /**
         * 分页查找 该用户下的知识文档
         * @param knowledgeDocQueryParam
         * @return
         */
        PageResult<KnowledgeDocDto> appAllFavoritesPageList(KnowledgeDocQueryParam knowledgeDocQueryParam);

        /**
         * 保存
         * @param knowledgeDocQueryParam
         * @return
         */
        boolean saveByParam(KnowledgeDocAddParam knowledgeDocQueryParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        KnowledgeDocDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<KnowledgeDocDto> rows);

        /**
         * 更新
         * @param knowledgeDocQueryParam
         */
        boolean updateByParam(KnowledgeDocEditParam knowledgeDocQueryParam);

        /**
         * 根据id数组删除
         * @param ids
         * @return
         */
        boolean removeByIds(String[] ids);

        /**
         * 查找知识库 里面 知识文档的列表
         * @return
         */
         List<KnowledgeCategoryDto> listKnowledgeCategoryDto();

        /**
         * 收藏
         * @param knowledgeDocId
         * @return
         */
        boolean favorites(String knowledgeDocId);

        /**
         * 批量授权
         * @param param
         * @return
         */
        Boolean batchEmpower(DocEmpowerParam param);
}