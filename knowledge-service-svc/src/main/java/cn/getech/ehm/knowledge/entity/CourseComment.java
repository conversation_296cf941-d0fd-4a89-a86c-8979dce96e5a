package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 课程评论信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_course_comment")
public class CourseComment extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 用户ID。
     */
    @TableField("uid")
    private String uid;

    /**
     * 评论内容。
     */
    @TableField("content")
    private String content;

    /**
     * 动态ID。
     */
    @TableField("course_id")
    private String courseId;

    /**
     * 序号。
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户ID。
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 记录状态。0-正常，1-删除
     */
    @TableField("deleted")
    private Integer deleted;

}
