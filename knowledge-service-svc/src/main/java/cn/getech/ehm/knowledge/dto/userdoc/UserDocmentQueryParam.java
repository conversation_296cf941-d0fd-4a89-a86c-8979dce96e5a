package cn.getech.ehm.knowledge.dto.userdoc;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 用户文档表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserDocment查询", description = "用户文档表查询参数")
public class UserDocmentQueryParam extends PageParam {
}
