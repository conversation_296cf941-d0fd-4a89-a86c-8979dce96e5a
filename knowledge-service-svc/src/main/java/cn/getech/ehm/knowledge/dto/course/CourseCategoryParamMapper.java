package cn.getech.ehm.knowledge.dto.course;

import cn.getech.ehm.knowledge.entity.CourseCategory;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程分类信息表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CourseCategoryParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param courseCategoryAddParam
     * @return
     */
    CourseCategory addParam2Entity(CourseCategoryAddParam courseCategoryAddParam);

    /**
     * 编辑参数转换为实体
     * @param courseCategoryEditParam
     * @return
     */
    CourseCategory editParam2Entity(CourseCategoryEditParam courseCategoryEditParam);

    /**
     * 实体转换为Dto
     * @param courseCategory
     * @return
     */
    CourseCategoryDto entity2Dto(CourseCategory courseCategory);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CourseCategoryDto> pageEntity2Dto(PageResult<CourseCategory> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CourseCategory> dtoList2Entity(List<CourseCategoryDto> rows);

}
