package cn.getech.ehm.knowledge.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 分类对象 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "ChildrenCategoryDto", description = "分类菜单对象返回数据模型")
public class ChildrenCategoryDto extends CategoryMenuDto {

    @ApiModelProperty(value = "禁用")
    private Boolean disabled;

}