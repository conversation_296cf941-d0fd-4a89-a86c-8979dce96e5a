package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 知识学习库 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudyKnowledgeDoc查询", description = "知识学习库查询参数")
public class KnowledgeDocQueryParam extends PageParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = " 分类ID ")
    private String categoryId;

    @ApiModelProperty(value = "是否客户平台(0否1是)", hidden = true)
    private Integer isClient = StaticValue.ZERO;

}
