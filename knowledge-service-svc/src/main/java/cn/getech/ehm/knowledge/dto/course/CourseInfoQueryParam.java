package cn.getech.ehm.knowledge.dto.course;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 课程信息表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseInfo查询", description = "课程信息表查询参数")
public class CourseInfoQueryParam extends PageParam {

    @ApiModelProperty(value = "分类id。")
    private String categoryId;

    @ApiModelProperty(value = "是否客户平台(0否1是)", hidden = true)
    private Integer isClient = StaticValue.ZERO;


}
