package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <pre>
 * 知识学习库 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudyKnowledgeDoc新增", description = "知识学习库新增参数")
public class KnowledgeDocAddParam extends ApiParam {

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank
    private String name;

    @ApiModelProperty(value = "知识文档分类ID" ,required = true)
    @NotBlank
    private String categoryId;


    @ApiModelProperty(value = "附件ID", required = true)
    @NotBlank
    private String attachId;

    @ApiModelProperty(value = "培训师")
    private String trainer;

    @ApiModelProperty(value = "备注信息")
    private String description;
}