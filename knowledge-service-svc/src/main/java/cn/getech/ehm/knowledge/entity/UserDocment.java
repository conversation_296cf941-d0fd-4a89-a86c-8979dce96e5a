package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 用户文档表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_user_docment")
public class UserDocment extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 用户ID
     */
    @TableField("uid")
    private String uid;

    /**
     * 名字
     */
    @TableField("name")
    private String name;

    /**
     * 附件ID
     */
    @TableField("attach_id")
    private String attachId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 0：未删除  1: 已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;


}
