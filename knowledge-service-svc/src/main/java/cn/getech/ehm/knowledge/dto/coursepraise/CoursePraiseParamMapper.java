package cn.getech.ehm.knowledge.dto.coursepraise;

import cn.getech.ehm.knowledge.entity.CoursePraise;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程用户点赞关系表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-13
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CoursePraiseParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param coursePraiseAddParam
     * @return
     */
    CoursePraise addParam2Entity(CoursePraiseAddParam coursePraiseAddParam);

    /**
     * 编辑参数转换为实体
     * @param coursePraiseEditParam
     * @return
     */
    CoursePraise editParam2Entity(CoursePraiseEditParam coursePraiseEditParam);

    /**
     * 实体转换为Dto
     * @param coursePraise
     * @return
     */
    CoursePraiseDto entity2Dto(CoursePraise coursePraise);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CoursePraiseDto> pageEntity2Dto(PageResult<CoursePraise> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CoursePraise> dtoList2Entity(List<CoursePraiseDto> rows);

}
