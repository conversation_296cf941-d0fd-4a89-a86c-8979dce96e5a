package cn.getech.ehm.knowledge.dto.common;

import cn.getech.ehm.knowledge.dto.course.CourseInfoDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocDto;
import lombok.Data;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;

import java.util.List;

@Data
public class KnowledgeDto {

    private List<KnowledgeDocDto> knowledgeDocDtos;

    private List<CourseInfoDto> courseInfoDtos;

    private List<NewsInfoDto> newsInfoDtos;
}
