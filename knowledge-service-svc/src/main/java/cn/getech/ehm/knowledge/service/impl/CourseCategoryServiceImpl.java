package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.knowledge.entity.CourseCategory;
import cn.getech.ehm.knowledge.entity.CourseInfo;
import cn.getech.ehm.knowledge.dto.course.*;
import cn.getech.ehm.knowledge.mapper.CourseCategoryMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.service.ICourseCategoryService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.service.ICourseInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;


/**
 * <pre>
 * 课程分类信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CourseCategoryServiceImpl extends BaseServiceImpl<CourseCategoryMapper, CourseCategory> implements ICourseCategoryService {

    @Autowired
    private CourseCategoryParamMapper courseCategoryParamMapper;
    @Autowired
    private CourseCategoryMapper courseCategoryMapper;
    @Autowired
    private ICourseInfoService courseInfoService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Override
    public PageResult<CourseCategoryDto> pageDto(CourseCategoryQueryParam courseCategoryQueryParam) {
        Wrapper<CourseCategory> wrapper = getPageSearchWrapper(courseCategoryQueryParam);
        PageResult<CourseCategoryDto> result = courseCategoryParamMapper.pageEntity2Dto(page(courseCategoryQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CourseCategoryAddParam addParam) {
        CourseCategory category = getByNameAndParentId(addParam.getName(), addParam.getParentId());
        if (category != null){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }

        CourseCategory courseCategory = courseCategoryParamMapper.addParam2Entity(addParam);
        if (StringUtils.isNotEmpty(addParam.getParentId())){
            CourseCategory parentCategory = new CourseCategory();
            parentCategory.setId(addParam.getParentId());
            parentCategory.setIsLeaf(false);
            updateById(parentCategory);
        }
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseCategory);
        return save(courseCategory);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CourseCategoryEditParam editParam) {
        CourseCategory category = getByNameAndParentId(editParam.getName(), editParam.getParentId());
        if (category != null && !category.getId().equals(editParam.getId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        CourseCategory courseCategory = courseCategoryParamMapper.editParam2Entity(editParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseCategory);
        return updateById(courseCategory);
    }

    @Override
    @Transactional
    public boolean batchUpdateCategoryMenu(List<CategoryMenuParam> menuParamList) {
        List<CourseCategory> categoryList = new ArrayList<>();
        initCourseCategoryList(menuParamList, categoryList, "");
        int count = 0;
        for (CourseCategory courseCategory : categoryList){
            count += this.baseMapper.updateById(courseCategory);
        }
        return count > 0;
    }

    @Override
    public CourseCategoryDto getDtoById(String id) {
        return courseCategoryParamMapper.entity2Dto((CourseCategory) this.getById(id));
    }

    @Override
    @Transactional
    public boolean removeById(String id) {
        List<String> list = fetchItemIdListByParentId(id);
        list.add(id);
        LambdaQueryWrapper<CourseInfo> query = Wrappers.lambdaQuery();
        query.in(CourseInfo::getCategoryId, list);
        int count = courseInfoService.getBaseMapper().selectCount(query);
        if (count > 0){
            log.error("该分类有对应的课程信息，不能删除！");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("referenced", null, LocaleContextHolder.getLocale())));
        }
        CourseCategoryDto categoryDto = getDtoById(id);
        if (super.removeById(id)) {
            if (StringUtils.isBlank(categoryDto.getParentId())) {
                return true;
            }

            LambdaQueryWrapper<CourseCategory> wrapperQuery = Wrappers.lambdaQuery();
            // 不为0则不修改父节点的is_leaf
            if (count(wrapperQuery.eq(CourseCategory::getParentId, categoryDto.getParentId())) != 0) {
                return true;
            }

            LambdaUpdateWrapper<CourseCategory> wrapperUpdate = Wrappers.lambdaUpdate();
            return update(wrapperUpdate.eq(CourseCategory::getId, categoryDto.getParentId()).set(CourseCategory::getIsLeaf, true));
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CourseCategoryDto> rows) {
        return saveBatch(courseCategoryParamMapper.dtoList2Entity(rows));
    }

    @Override
    public List<CategoryMenuDto> fetchCategoryMenuList(){
        List<CategoryMenuDto> menuList = courseCategoryMapper.fetchCategoryMenuList();
        menuList.forEach(dto -> generateScopedSlots(dto));
        return Optional.ofNullable(menuList).orElse(new ArrayList<>());
    }

    @Override
    public List<String> fetchItemIdListByParentId(String parentId) {
        if (StringUtils.isBlank(parentId)){
            return new ArrayList<>();
        }
        List<CategoryMenuDto> menuDtoList = courseCategoryMapper.fetchItemMenuByParentId(parentId);
        List<String> idList = new ArrayList<>();
        initIdList(menuDtoList, idList);
        return idList;
    }

    @Override
    public List<ChildrenCategoryDto> fetchChildrenCategoryList() {
        List<CategoryMenuDto> categoryMenuDtoList = courseCategoryMapper.fetchCategoryMenuList();
        return initChildrenMenuNameList(categoryMenuDtoList);
    }

    private Wrapper<CourseCategory> getPageSearchWrapper(CourseCategoryQueryParam queryParam) {
        LambdaQueryWrapper<CourseCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), CourseCategory::getName, queryParam.getName());
        if(BaseEntity.class.isAssignableFrom(CourseCategory.class)){
            wrapper.orderByDesc(CourseCategory::getUpdateTime,CourseCategory::getCreateTime);
        }
        return wrapper;
    }

    private void initCourseCategoryList(List<CategoryMenuParam> menuParamList, List<CourseCategory> categoryList, String parentId){
        int sortNumber = 1;
        for (CategoryMenuParam menuParam : menuParamList){
            CourseCategory courseCategory = new CourseCategory();
            BeanUtils.copyProperties(menuParam, courseCategory);
            courseCategory.setParentId(parentId);
            courseCategory.setSortNumber(sortNumber);
            categoryList.add(courseCategory);
            if (CollectionUtils.isNotEmpty(menuParam.getChildren())){
                initCourseCategoryList(menuParam.getChildren(), categoryList, menuParam.getId());
            }
            sortNumber++;
        }
    }

    private void initIdList(List<CategoryMenuDto> menuParamList, List<String> categoryList){
        for (CategoryMenuDto menuDto : menuParamList){
            categoryList.add(menuDto.getId());
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                initIdList(menuDto.getChildren(), categoryList);
            }
        }
    }

    /**
     * 获取同一目录下相同名称的对象
     * @param name
     * @param parentId
     * @return
     */
    private CourseCategory getByNameAndParentId(String name, String parentId){
        parentId = StringUtils.isNotBlank(parentId) ? parentId : "";
        LambdaQueryWrapper<CourseCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CourseCategory::getParentId, parentId);
        wrapper.eq(CourseCategory::getName, name);
        return (CourseCategory) this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据菜单列表处理子类菜单disable属性
     * 叶子节点的disable为false，父子节点为true
     * @return
     */
    private List<ChildrenCategoryDto> initChildrenMenuNameList(List<CategoryMenuDto> categoryMenuDtoList){
        List<ChildrenCategoryDto> result = new ArrayList<>();
        for (CategoryMenuDto menuDto : categoryMenuDtoList){
            ChildrenCategoryDto childrenMenuDto = new ChildrenCategoryDto();
            BeanUtils.copyProperties(menuDto, childrenMenuDto);
            childrenMenuDto.setDisabled(CollectionUtils.isNotEmpty(menuDto.getChildren()));
            result.add(childrenMenuDto);
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                childrenMenuDto.setChildren(new ArrayList<>());
                List<ChildrenCategoryDto> categoryDtoList = initChildrenMenuNameList(menuDto.getChildren());
                childrenMenuDto.getChildren().addAll(categoryDtoList);
            }
        }
        result = result.stream().sorted(Comparator.comparing(ChildrenCategoryDto::getName)).collect(Collectors.toList());
        return result;
    }

    private void generateScopedSlots(CategoryMenuDto categoryMenuDto){
        if (categoryMenuDto.getIsLeaf()){
            categoryMenuDto.getScopedSlots().setSwitcherIcon("");
        } else {
            categoryMenuDto.getChildren().forEach( dto-> generateScopedSlots(dto) );
        }
    }

}
