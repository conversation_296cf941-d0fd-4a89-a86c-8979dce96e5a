package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.ehm.knowledge.entity.CategoryKnowledgeDoc;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 * 知识文档分类信息表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface CategoryKnowledgeDocParamMapper {

    /**
     * 新增参数转换为实体
     *
     * @param studyCategoryKnowledgeDocAddParam
     * @return
     */
    CategoryKnowledgeDoc addParam2Entity(CategoryKnowledgeDocAddParam studyCategoryKnowledgeDocAddParam);

    /**
     * 编辑参数转换为实体
     * @param studyCategoryKnowledgeDocEditParam
     * @return
     */
    CategoryKnowledgeDoc editParam2Entity(CategoryKnowledgeDocEditParam studyCategoryKnowledgeDocEditParam);

    /**
     * 实体转换为Dto
     * @param studyCategoryKnowledgeDoc
     * @return
     */
    CategoryKnowledgeDocDto entity2Dto(CategoryKnowledgeDoc studyCategoryKnowledgeDoc);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CategoryKnowledgeDocDto> pageEntity2Dto(PageResult<CategoryKnowledgeDoc> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CategoryKnowledgeDoc> dtoList2Entity(List<CategoryKnowledgeDocDto> rows);


    /**
     * entity集合转dto集合
     * @param rows
     * @return
     */
    List<CategoryKnowledgeDocDto> entityList2Dto(List<CategoryKnowledgeDoc> rows);

}
