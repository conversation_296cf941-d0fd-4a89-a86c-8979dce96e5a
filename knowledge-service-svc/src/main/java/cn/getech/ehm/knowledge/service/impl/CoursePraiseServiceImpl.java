package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.knowledge.entity.CoursePraise;
import cn.getech.ehm.knowledge.mapper.CoursePraiseMapper;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.service.ICoursePraiseService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseQueryParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseAddParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseEditParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseParamMapper;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <pre>
 * 课程用户点赞关系表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Slf4j
@Service
public class CoursePraiseServiceImpl extends BaseServiceImpl<CoursePraiseMapper, CoursePraise> implements ICoursePraiseService {

    @Autowired
    private CoursePraiseParamMapper coursePraiseParamMapper;

    @Autowired
    private CoursePraiseMapper coursePraiseMapper;

    @Override
    public PageResult<CoursePraiseDto> pageDto(CoursePraiseQueryParam coursePraiseQueryParam) {
        Wrapper<CoursePraise> wrapper = getPageSearchWrapper(coursePraiseQueryParam);
        PageResult<CoursePraiseDto> result = coursePraiseParamMapper.pageEntity2Dto(page(coursePraiseQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CoursePraiseAddParam coursePraiseAddParam) {
        CoursePraise coursePraise = coursePraiseParamMapper.addParam2Entity(coursePraiseAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,coursePraise);
        return save(coursePraise);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CoursePraiseEditParam coursePraiseEditParam) {
        CoursePraise coursePraise = coursePraiseParamMapper.editParam2Entity(coursePraiseEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,coursePraise);
        return updateById(coursePraise);
    }

    @Override
    public List<String> getCurrUserPraiseCourse(List<String> courseIds) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<CoursePraise> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CoursePraise::getUid, currentUser.getUid());
        queryWrapper.in(CoursePraise::getCourseId, courseIds);
        List<CoursePraise> coursePraises = list(queryWrapper);
        return coursePraises.stream().map(coursePraise -> coursePraise.getCourseId()).collect(Collectors.toList());
    }

    @Override
    public boolean isPraiseCourse(String courseId) {
        if (StringUtils.isBlank(courseId)){
            return false;
        }
        LambdaQueryWrapper<CoursePraise> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CoursePraise::getCourseId, courseId);
        wrapper.eq(CoursePraise::getUid, PorosContextHolder.getCurrentUser().getUid());
        Integer selectCount = coursePraiseMapper.selectCount(wrapper);
        return selectCount > 0;
    }

    @Override
    public CoursePraiseDto getDtoById(Long id) {
        return coursePraiseParamMapper.entity2Dto((CoursePraise) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CoursePraiseDto> rows) {
        return saveBatch(coursePraiseParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<CoursePraise> getPageSearchWrapper(CoursePraiseQueryParam coursePraiseQueryParam) {
        LambdaQueryWrapper<CoursePraise> wrapper = Wrappers.lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(CoursePraise.class)){
            wrapper.orderByDesc(CoursePraise::getUpdateTime,CoursePraise::getCreateTime);
        }
        return wrapper;
    }
}
