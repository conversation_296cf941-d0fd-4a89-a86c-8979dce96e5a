package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <pre>
 * 新闻分类信息表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsCategory新增", description = "新闻分类信息表新增参数")
public class NewsCategoryAddParam extends ApiParam {

    @ApiModelProperty(value = "标签名。")
    @NotBlank(message = "标签名不能为空")
    private String name;

    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "备注信息。")
    private String remark;
}