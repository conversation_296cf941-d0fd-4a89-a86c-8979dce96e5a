package cn.getech.ehm.knowledge.controller;


import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentDto;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentQueryParam;
import cn.getech.ehm.knowledge.service.IUserDocmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 用户文档表控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@RestController
@RequestMapping("/appUserDocment")
@Api(tags = "app：用户文档服务接口")
public class AppUserDocmentController {

    @Autowired
    private IUserDocmentService userDocmentService;

    /**
     * 分页获取用户文档表列表
     */
    @ApiOperation("分页获取用户文档表列表")
    @GetMapping("/list")
    //@Permission("user:docment:list")
    public RestResponse<PageResult<UserDocmentDto>> pageList(@Valid UserDocmentQueryParam userDocmentQueryParam){
        return RestResponse.ok(userDocmentService.appPageDto(userDocmentQueryParam));
    }



}
