package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.dto.course.CourseInfoQueryParam;
import cn.getech.ehm.knowledge.dto.knowledge.KnowledgeMenuChildrenQueryParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocQueryParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoQueryParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeQueryParam;
import cn.getech.ehm.knowledge.service.*;
import cn.getech.ehm.system.client.SystemConfigClient;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * app知识库 服务接口
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Slf4j
@Service
public class AppKnowledgeServiceImpl implements IAppKnowledgeService {

    private static final String NEWS_ICON_NAME = "knowledge_news_icon";         // 新闻icon配置名称
    private static final String COURSE_ICON_NAME = "knowledge_course_icon";     // 课程icon配置名称
    private static final String DOC_ICON_NAME = "knowledge_doc_icon";           // 文档icon配置名称，会有多个

    private static final String NEWS_LIST_HREF_NAME = "knowledge_news_list_url";       // 新闻列表跳转链接名称
    private static final String COURSE_LIST_HREF_NAME = "knowledge_course_list_url";   // 课程列表跳转链接名称
    private static final String DOC_LIST_HREF_NAME = "knowledge_doc_list_url";         // 文档列表跳转链接名称


    @Autowired
    private ICourseInfoService courseInfoService;

    @Autowired
    private ICourseCategoryService courseCategoryService;

    @Autowired
    private INewsInfoService newsInfoService;

    @Autowired
    private INewsCategoryService newsCategoryService;

    @Autowired
    private IKnowledgeDocService knowledgeDocService;

    @Autowired
    private ICategoryKnowledgeDocService categoryKnowledgeDocService;

    @Autowired
    private SystemConfigClient systemConfigClient;

    @Override
    public List<KnowledgeCategoryDto> knowledgeList() {
        RestResponse<Map<String, SystemConfigDto>> client = systemConfigClient.map(getConfigNames());
        Map<String, SystemConfigDto> configMap = client.getData();

        List<KnowledgeCategoryDto> knowledgeCategoryDtoList = new ArrayList();

        //sdsh,金银河只有文档列表
        KnowledgeCategoryDto newsDto = newsInfoService.getKnowledgeCategoryDto();
       /* KnowledgeCategoryDto courseDto = courseInfoService.getKnowledgeCategoryDto();
        if (CollectionUtils.isNotEmpty(configMap)){
            newsDto.setIcon(null != configMap.get(NEWS_ICON_NAME) ? configMap.get(NEWS_ICON_NAME).getCfgValue() : null);
            newsDto.setHref(null != configMap.get(NEWS_LIST_HREF_NAME) ? configMap.get(NEWS_LIST_HREF_NAME).getCfgValue() : null);

            courseDto.setIcon(null != configMap.get(COURSE_ICON_NAME) ? configMap.get(COURSE_ICON_NAME).getCfgValue() : null);
            courseDto.setHref(null != configMap.get(COURSE_LIST_HREF_NAME) ? configMap.get(COURSE_LIST_HREF_NAME).getCfgValue() : null);
        }

        knowledgeCategoryDtoList.add(newsDto);
        knowledgeCategoryDtoList.add(courseDto);*/

        List<KnowledgeCategoryDto> categoryDtoList = knowledgeDocService.listKnowledgeCategoryDto();
        RestResponse<PageResult<SystemConfigDto>> list = systemConfigClient.list(DOC_ICON_NAME);

        int total = list.getData().getRecords().size();
        for (int i = 0; i < categoryDtoList.size(); i++){
            KnowledgeCategoryDto categoryDto = categoryDtoList.get(i);
            if (CollectionUtils.isNotEmpty(configMap)){
                categoryDto.setHref(configMap.get(DOC_LIST_HREF_NAME).getCfgValue() + categoryDto.getCategoryId());
                categoryDto.setIcon(list.getData().getRecords().get(i % total).getCfgValue());
            }
            knowledgeCategoryDtoList.add(categoryDto);
        }
        return knowledgeCategoryDtoList;
    }

    @Override
    public List<CategoryMenuDto> getMenuChildrenList(KnowledgeMenuChildrenQueryParam queryParam){
        if (queryParam.getType() == KnowledgeType.NEWS.getCode()){
            return newsCategoryService.fetchCategoryMenuList();
        }else if (queryParam.getType() == KnowledgeType.COURSE.getCode()){
            return courseCategoryService.fetchCategoryMenuList();
        }else if (queryParam.getType() == KnowledgeType.DOC.getCode()){
            if (StringUtils.isBlank(queryParam.getCategoryId())){
                queryParam.setCategoryId("");
            }
            return categoryKnowledgeDocService.findCategoryTree(queryParam.getCategoryId());
        }
        return new ArrayList();
    }

    @Override
    public KnowledgeDto listKnowledge(KnowledgeQueryParam queryParam) {
        KnowledgeDto knowledgeDto = new KnowledgeDto();
        if (queryParam.getType() == KnowledgeType.NEWS.getCode()){
            NewsInfoQueryParam newsInfoQueryParam = new NewsInfoQueryParam();
            newsInfoQueryParam.setKeyword(queryParam.getKeyword());
            newsInfoQueryParam.setLimit(queryParam.getLimit());
            newsInfoQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setNewsInfoDtos(newsInfoService.pageDto(newsInfoQueryParam).getRecords());
        } else if (queryParam.getType() == KnowledgeType.COURSE.getCode()){
            CourseInfoQueryParam courseInfoQueryParam = new CourseInfoQueryParam();
            courseInfoQueryParam.setKeyword(queryParam.getKeyword());
            courseInfoQueryParam.setLimit(queryParam.getLimit());
            courseInfoQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setCourseInfoDtos(courseInfoService.appPageList(courseInfoQueryParam).getRecords());
        } else if (queryParam.getType() == KnowledgeType.DOC.getCode()){
            KnowledgeDocQueryParam knowledgeDocQueryParam = new KnowledgeDocQueryParam();
            knowledgeDocQueryParam.setKeyword(queryParam.getKeyword());
            knowledgeDocQueryParam.setLimit(queryParam.getLimit());
            knowledgeDocQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setKnowledgeDocDtos(knowledgeDocService.pageDto(knowledgeDocQueryParam).getRecords());
        } else {
            NewsInfoQueryParam newsInfoQueryParam = new NewsInfoQueryParam();
            newsInfoQueryParam.setKeyword(queryParam.getKeyword());
            newsInfoQueryParam.setLimit(queryParam.getLimit());
            newsInfoQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setNewsInfoDtos(newsInfoService.pageDto(newsInfoQueryParam).getRecords());

            CourseInfoQueryParam courseInfoQueryParam = new CourseInfoQueryParam();
            courseInfoQueryParam.setKeyword(queryParam.getKeyword());
            courseInfoQueryParam.setLimit(queryParam.getLimit());
            courseInfoQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setCourseInfoDtos(courseInfoService.appPageList(courseInfoQueryParam).getRecords());

            KnowledgeDocQueryParam knowledgeDocQueryParam = new KnowledgeDocQueryParam();
            knowledgeDocQueryParam.setKeyword(queryParam.getKeyword());
            knowledgeDocQueryParam.setLimit(queryParam.getLimit());
            knowledgeDocQueryParam.setPageNo(queryParam.getPageNo());

            knowledgeDto.setKnowledgeDocDtos(knowledgeDocService.pageDto(knowledgeDocQueryParam).getRecords());
        }
        return knowledgeDto;
    }


    private String getConfigNames(){
        StringBuilder names = new StringBuilder();
        names.append(NEWS_ICON_NAME).append(",");
        names.append(COURSE_ICON_NAME).append(",");
        names.append(COURSE_LIST_HREF_NAME).append(",");
        names.append(NEWS_LIST_HREF_NAME).append(",");
        names.append(DOC_LIST_HREF_NAME);
        return names.toString();
    }

}
