package cn.getech.ehm.knowledge.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * <pre>
 * 分类菜单对象 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "CourseCategoryMenuDto", description = "分类菜单对象返回数据模型")
public class CategoryMenuDto {

    @ApiModelProperty(value = "全局ID。")
    private String id;

    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "父分类ID。")
    private String parentId;

    @ApiModelProperty(value = "是否是叶子节点。0-否，1-是")
    private Boolean isLeaf;

    @ApiModelProperty(value = "排序编号。")
    private Integer sortNumber;

    private Integer deleted;

    @ApiModelProperty(value = "子级分类列表。")
    protected List<CategoryMenuDto> children = new ArrayList();

    @ApiModelProperty(value = "前端要求附加信息")
    private ScopedSlots scopedSlots = new ScopedSlots();
}