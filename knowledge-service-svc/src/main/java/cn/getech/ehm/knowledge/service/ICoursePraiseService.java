package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseAddParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseDto;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseEditParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseQueryParam;
import cn.getech.ehm.knowledge.entity.CoursePraise;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程用户点赞关系表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
public interface ICoursePraiseService extends IBaseService<CoursePraise> {

        /**
         * 分页查询，返回Dto
         *
         * @param coursePraiseQueryParam
         * @return
         */
        PageResult<CoursePraiseDto> pageDto(CoursePraiseQueryParam coursePraiseQueryParam);

        /**
         * 保存
         * @param coursePraiseAddParam
         * @return
         */
        boolean saveByParam(CoursePraiseAddParam coursePraiseAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CoursePraiseDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<CoursePraiseDto> rows);

        /**
         * 更新
         * @param coursePraiseEditParam
         */
        boolean updateByParam(CoursePraiseEditParam coursePraiseEditParam);

        /**
         * 筛选 出 当前用户点赞过的课程ID
         * @param courseIds
         * @return
         */
        List<String> getCurrUserPraiseCourse(List<String> courseIds);

        /**
         * 是否点赞课程
         * @param courseId
         * @return true-是，false-否
         */
        boolean isPraiseCourse(String courseId);
}