package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import cn.getech.ehm.knowledge.dto.news.NewsInfoAddParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoEditParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoQueryParam;
import cn.getech.ehm.knowledge.service.INewsInfoService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;

import javax.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 新闻资讯信息模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@RestController
@RequestMapping("/newsInfo")
@Api(tags = "web: 新闻资讯信息模块服务接口")
public class WebNewsInfoController {

    @Autowired
    private INewsInfoService newsInfoService;

    /**
     * 分页获取新闻资讯信息模块列表
     */
    @ApiOperation("分页获取新闻资讯信息模块列表")
    @GetMapping("/list")
    //@Permission("news:info:list")
    public RestResponse<PageResult<NewsInfoDto>> pageList(@Valid NewsInfoQueryParam newsInfoQueryParam){
        return RestResponse.ok(newsInfoService.pageDto(newsInfoQueryParam));
    }

    /**
     * 分页获取新闻资讯信息模块列表
     */
    @ApiOperation("获取app轮播图配置新闻列表")
    @GetMapping("/selectList")
    //@Permission("news:info:list")
    public RestResponse<PageResult<NewsInfoDto>> selectList(@Valid PageParam pageParam){
        return RestResponse.ok(newsInfoService.selectList(pageParam));
    }

    /**
     * 分页获取新闻资讯信息模块列表
     */
    @ApiOperation("分页获取新闻资讯信息模块客户列表")
    @GetMapping("/customList")
    //@Permission("news:info:list")
    public RestResponse<PageResult<NewsInfoDto>> pageCustomList(@Valid NewsInfoQueryParam newsInfoQueryParam){
        return RestResponse.ok(newsInfoService.pageCustomDto(newsInfoQueryParam));
    }

    /**
     * 新增新闻资讯信息模块
     */
    @ApiOperation("新增新闻资讯信息")
    @AuditLog(title = "新闻资讯信息",desc = "新增新闻资讯信息",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("news:info:update")
    public RestResponse<Boolean> add(@RequestBody @Valid NewsInfoAddParam newsInfoAddParam) {
        return RestResponse.ok(newsInfoService.saveByParam(newsInfoAddParam));
    }

    /**
     * 修改新闻资讯信息模块
     */
    @ApiOperation(value="修改新闻资讯信息")
    @AuditLog(title = "新闻资讯信息",desc = "修改新闻资讯信息",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("news:info:update")
    public RestResponse<Boolean> update(@RequestBody @Valid NewsInfoEditParam newsInfoEditParam) {
        return RestResponse.ok(newsInfoService.updateByParam(newsInfoEditParam));
    }

    /**
     * 根据id删除新闻资讯信息模块
     */
    @ApiOperation(value="根据id删除新闻资讯信息")
    @AuditLog(title = "新闻资讯信息",desc = "新闻资讯信息",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("news:info:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(newsInfoService.removeByIds(ids));
    }

    /**
     * 根据id获取新闻资讯信息模块
     */
    @ApiOperation(value = "根据id获取新闻资讯信息")
    @GetMapping(value = "/{id}")
    //@Permission("news:info:list")
    public RestResponse<NewsInfoDto> get(@PathVariable  String id) {
        return RestResponse.ok(newsInfoService.getDtoById(id));
    }

    /**
     * 分页获取课程信息列表
     */
    @ApiOperation("web首页课程信息列表")
    @GetMapping("/webFirstpage/{size}")
    //@Permission("course:info:list")
    public RestResponse<List<NewsInfoDto>> webFirstpage(@PathVariable("size") Integer size){
        NewsInfoQueryParam newsInfoQueryParam = new NewsInfoQueryParam();
        newsInfoQueryParam.setLimit(size);

        return RestResponse.ok(newsInfoService.webFirstpage(newsInfoQueryParam));
    }

}
