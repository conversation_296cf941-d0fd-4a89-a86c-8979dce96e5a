package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocEditParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocQueryParam;
import cn.getech.ehm.knowledge.entity.CategoryKnowledgeDoc;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;

import java.util.List;

/**
 * <pre>
 * 知识文档分类信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICategoryKnowledgeDocService extends IBaseService<CategoryKnowledgeDoc> {

        /**
         * 分页查询，返回Dto
         *
         * @param studyCategoryKnowledgeDocQueryParam
         * @return
         */
        PageResult<CategoryKnowledgeDocDto> pageDto(CategoryKnowledgeDocQueryParam studyCategoryKnowledgeDocQueryParam);

        /**
         * 保存
         * @param studyCategoryKnowledgeDocAddParam
         * @return
         */
        boolean saveByParam(CategoryKnowledgeDocAddParam studyCategoryKnowledgeDocAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CategoryKnowledgeDocDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<CategoryKnowledgeDocDto> rows);

        /**
         * 更新
         * @param studyCategoryKnowledgeDocEditParam
         */
        boolean updateByParam(CategoryKnowledgeDocEditParam studyCategoryKnowledgeDocEditParam);

        /**
         * 查找所有类别，以树状结构返回所有
             * @return
         */
        List<CategoryMenuDto> findCategoryTree();

        List<CategoryMenuDto> findCategoryTree(String categoryId);

        /**
         * 根据id数组删除,并且如果父节点没有子节点，则修改父节点为叶子节点
         * @param id
         * @return
         */
        boolean removeById(String id);

        /**
         * 获取子类菜单列表
         * @return
         */
        List<ChildrenCategoryDto> fetchChildrenCategoryList();
}