package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.knowledge.entity.CourseAccessible;
import cn.getech.ehm.knowledge.mapper.CourseAccessibleMapper;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecRoleRefDTO;
import cn.getech.poros.permission.dto.SecStaffDto;
import cn.getech.ehm.knowledge.service.ICourseAccessibleService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleQueryParam;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleAddParam;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleEditParam;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleParamMapper;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <pre>
 * 非公开课程可访问表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
@Slf4j
@Service
public class CourseAccessibleServiceImpl extends BaseServiceImpl<CourseAccessibleMapper, CourseAccessible> implements ICourseAccessibleService {

    @Autowired
    private CourseAccessibleParamMapper courseAccessibleParamMapper;
    @Autowired
    private CourseAccessibleMapper courseAccessibleMapper;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;

    @Override
    public PageResult<CourseAccessibleDto> pageDto(CourseAccessibleQueryParam courseAccessibleQueryParam) {
        Wrapper<CourseAccessible> wrapper = getPageSearchWrapper(courseAccessibleQueryParam);
        PageResult<CourseAccessibleDto> result = courseAccessibleParamMapper.pageEntity2Dto(page(courseAccessibleQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CourseAccessibleAddParam courseAccessibleAddParam) {
        CourseAccessible courseAccessible = courseAccessibleParamMapper.addParam2Entity(courseAccessibleAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseAccessible);
        return save(courseAccessible);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CourseAccessibleEditParam courseAccessibleEditParam) {
        CourseAccessible courseAccessible = courseAccessibleParamMapper.editParam2Entity(courseAccessibleEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseAccessible);
        return updateById(courseAccessible);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<String> courseIds, List<String> roleIds) {
        List<CourseAccessible> courseAccessibles = new ArrayList<>();
        for(String courseId : courseIds){
            for(String roleId : roleIds){
                CourseAccessible entity = new CourseAccessible();
                entity.setCourseId(courseId);
                entity.setRoleId(roleId);
                courseAccessibles.add(entity);
            }
        }
        return saveBatch(courseAccessibles);
    }


    @Override
    public boolean removeByIds(String[] ids){
        return removeByIds(Stream.of(ids).collect(Collectors.toList()));
    }

    @Override
    public List<String> listCurrentCourseIds(){
        RestResponse<SecStaffDto> restResponse = porosSecStaffClient.getCurUser();
        if(restResponse.isOk()) {
            SecStaffDto secStaffDto = restResponse.getData();
            List<PorosSecRoleRefDTO> roleDtoList = secStaffDto.getRoleRefDtoList();
            if (CollectionUtils.isNotEmpty(roleDtoList)) {
                List<String> roleIds = roleDtoList.stream().map(PorosSecRoleRefDTO::getId).distinct().collect(Collectors.toList());
                LambdaQueryWrapper<CourseAccessible> wrapper = Wrappers.<CourseAccessible>lambdaQuery();
                wrapper.in(CourseAccessible::getRoleId,roleIds);
                wrapper.select(CourseAccessible::getCourseId);
                return courseAccessibleMapper.selectList(wrapper).stream()
                        .map(CourseAccessible::getCourseId).distinct().collect(Collectors.toList());
            }
        }else{
            log.error("获取用户信息失败");
        }

        return null;
    }

    private Wrapper<CourseAccessible> getPageSearchWrapper(CourseAccessibleQueryParam courseAccessibleQueryParam) {
        LambdaQueryWrapper<CourseAccessible> wrapper = Wrappers.<CourseAccessible>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(CourseAccessible.class)){
            wrapper.orderByDesc(CourseAccessible::getUpdateTime,CourseAccessible::getCreateTime);
        }
        return wrapper;
    }

    @Override
    public Boolean deleteByCourseIds(List<String> courseIds){
        LambdaQueryWrapper<CourseAccessible> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CourseAccessible::getCourseId, courseIds);
        return this.remove(wrapper);
    }

    @Override
    public Map<String, List<String>> getCourseAccessMap(List<String> courseInfoIds){
        LambdaQueryWrapper<CourseAccessible> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CourseAccessible::getCourseId, courseInfoIds);
        wrapper.select(CourseAccessible::getCourseId, CourseAccessible::getRoleId);
        return courseAccessibleMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(
                CourseAccessible::getCourseId, Collectors.mapping(CourseAccessible::getRoleId, Collectors.toList())));
    }
}
