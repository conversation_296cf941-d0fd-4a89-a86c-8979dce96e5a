package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryAddParam;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryDto;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryEditParam;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryQueryParam;
import cn.getech.ehm.knowledge.entity.CourseCategory;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程分类信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICourseCategoryService extends IBaseService<CourseCategory> {

        /**
         * 分页查询，返回Dto
         *
         * @param courseCategoryQueryParam
         * @return
         */
        PageResult<CourseCategoryDto> pageDto(CourseCategoryQueryParam courseCategoryQueryParam);

        /**
         * 保存
         * @param courseCategoryAddParam
         * @return
         */
        boolean saveByParam(CourseCategoryAddParam courseCategoryAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CourseCategoryDto getDtoById(String id);

        /**
         * 根据id删除记录
         * @param id
         * @return
         */
        boolean removeById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<CourseCategoryDto> rows);

        /**
         * 更新
         * @param courseCategoryEditParam
         */
        boolean updateByParam(CourseCategoryEditParam courseCategoryEditParam);

        /**
         * 批量更新分类菜单信息
         * @param menuParamList
         * @return
         */
        boolean batchUpdateCategoryMenu(List<CategoryMenuParam> menuParamList);

        /**
         * 获取分类菜单接口
         * @return
         */
        List<CategoryMenuDto> fetchCategoryMenuList();

        /**
         * 根据父级分类id查询菜单列表
         * @param parentId
         * @return
         */
        List<String> fetchItemIdListByParentId(String parentId);

        /**
         * 获取子类菜单列表
         * @return
         */
        List<ChildrenCategoryDto> fetchChildrenCategoryList();

}