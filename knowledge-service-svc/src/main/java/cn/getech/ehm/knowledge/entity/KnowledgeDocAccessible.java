package cn.getech.ehm.knowledge.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识文档权限
 * <AUTHOR>
 * @since 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_doc_accessible")
public class KnowledgeDocAccessible extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 角色code
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 知识文档ID
     */
    @TableField("knowledge_doc_id")
    private String knowledgeDocId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;


}
