package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleAddParam;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleDto;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleEditParam;
import cn.getech.ehm.knowledge.dto.courseaccessible.CourseAccessibleQueryParam;
import cn.getech.ehm.knowledge.entity.CourseAccessible;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 非公开课程可访问表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
public interface ICourseAccessibleService extends IBaseService<CourseAccessible> {

        /**
         * 分页查询，返回Dto
         *
         * @param courseAccessibleQueryParam
         * @return
         */
        PageResult<CourseAccessibleDto> pageDto(CourseAccessibleQueryParam courseAccessibleQueryParam);

        /**
         * 保存
         * @param courseAccessibleAddParam
         * @return
         */
        boolean saveByParam(CourseAccessibleAddParam courseAccessibleAddParam);

        /**
         * 批量保存
         */
        boolean saveDtoBatch(List<String> courseIds, List<String> roleIds);

        /**
         * 更新
         * @param courseAccessibleEditParam
         */
        boolean updateByParam(CourseAccessibleEditParam courseAccessibleEditParam);

        /**
         * 批量删除
         * @param ids
         * @return
         */
        boolean removeByIds(String[] ids);

        /**
         * 获取当前登录人对应课程ids
         * @return
         */
        List<String> listCurrentCourseIds();

        /**
         * 根据课程id集合删除
         * @param courseIds
         * @return
         */
        Boolean deleteByCourseIds(List<String> courseIds);

        /**
         * 获取课程对应角色权限集合
         * @param courseInfoIds
         * @return
         */
        Map<String, List<String>> getCourseAccessMap(List<String> courseInfoIds);
}