package cn.getech.ehm.knowledge.dto.newsfavorites;

import cn.getech.ehm.knowledge.entity.NewsFavorites;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻资讯收藏 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-26
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  NewsFavoritesParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param newsFavoritesAddParam
     * @return
     */
    NewsFavorites addParam2Entity(NewsFavoritesAddParam newsFavoritesAddParam);

    /**
     * 编辑参数转换为实体
     * @param newsFavoritesEditParam
     * @return
     */
    NewsFavorites editParam2Entity(NewsFavoritesEditParam newsFavoritesEditParam);

    /**
     * 实体转换为Dto
     * @param newsFavorites
     * @return
     */
    NewsFavoritesDto entity2Dto(NewsFavorites newsFavorites);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<NewsFavoritesDto> pageEntity2Dto(PageResult<NewsFavorites> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<NewsFavorites> dtoList2Entity(List<NewsFavoritesDto> rows);

}
