package cn.getech.ehm.knowledge.dto.course;

import cn.getech.ehm.knowledge.entity.CourseInfo;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程信息表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CourseInfoParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param courseInfoAddParam
     * @return
     */
    CourseInfo addParam2Entity(CourseInfoAddParam courseInfoAddParam);

    /**
     * 编辑参数转换为实体
     * @param courseInfoEditParam
     * @return
     */
    CourseInfo editParam2Entity(CourseInfoEditParam courseInfoEditParam);

    /**
     * 实体转换为Dto
     * @param courseInfo
     * @return
     */
    CourseInfoDto entity2Dto(CourseInfo courseInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CourseInfoDto> pageEntity2Dto(PageResult<CourseInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CourseInfo> dtoList2Entity(List<CourseInfoDto> rows);

}
