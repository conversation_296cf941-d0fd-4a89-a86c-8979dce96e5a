package cn.getech.ehm.knowledge.dto.coursecomment;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <pre>
 * 课程评论信息 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseComment查询", description = "课程评论信息查询参数")
public class CourseCommentQueryParam extends PageParam {

    @ApiModelProperty(value = "评论内容。")
    private String content;

    @ApiModelProperty(value = "课程id。")
    private String courseId;

    @ApiModelProperty(value = "序号。")
    private String sort;

}
