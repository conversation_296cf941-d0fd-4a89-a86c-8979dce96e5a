package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.knowledge.entity.CategoryKnowledgeDoc;
import cn.getech.ehm.knowledge.entity.KnowledgeDoc;
import cn.getech.ehm.knowledge.dto.knowledgedoc.*;
import cn.getech.ehm.knowledge.mapper.CategoryKnowledgeDocMapper;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.service.ICategoryKnowledgeDocService;
import cn.getech.ehm.knowledge.service.IKnowledgeDocService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <pre>
 * 知识文档分类信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CategoryKnowledgeDocServiceImpl extends BaseServiceImpl<CategoryKnowledgeDocMapper, CategoryKnowledgeDoc> implements ICategoryKnowledgeDocService {

    @Autowired
    private CategoryKnowledgeDocParamMapper categoryKnowledgeDocParamMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private CategoryKnowledgeDocMapper categoryKnowledgeDocMapper;
    @Autowired
    private IKnowledgeDocService knowledgeDocService;

    @Override
    public PageResult<CategoryKnowledgeDocDto> pageDto(CategoryKnowledgeDocQueryParam studyCategoryKnowledgeDocQueryParam) {
        Wrapper<CategoryKnowledgeDoc> wrapper = getPageSearchWrapper(studyCategoryKnowledgeDocQueryParam);
        PageResult<CategoryKnowledgeDocDto> result = categoryKnowledgeDocParamMapper.pageEntity2Dto(page(studyCategoryKnowledgeDocQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByParam(CategoryKnowledgeDocAddParam addParam) {
        CategoryKnowledgeDoc category = getByNameAndParentId(addParam.getName(), addParam.getParentId());
        if (category != null){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        CategoryKnowledgeDoc studyCategoryKnowledgeDoc = categoryKnowledgeDocParamMapper.addParam2Entity(addParam);
        // 校验失败 报异常 ServiceException
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,studyCategoryKnowledgeDoc);

        if (save(studyCategoryKnowledgeDoc) && StringUtils.isNoneBlank(addParam.getParentId())) {

            // 增加成功 修改 Parent 的 is_leaf
            LambdaUpdateWrapper<CategoryKnowledgeDoc> wrapperUpdate = Wrappers.lambdaUpdate();
            return update(wrapperUpdate.eq(CategoryKnowledgeDoc::getId, addParam.getParentId()).set(CategoryKnowledgeDoc::getIsLeaf, false));
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CategoryKnowledgeDocEditParam editParam) {
        CategoryKnowledgeDoc category = getByNameAndParentId(editParam.getName(), editParam.getParentId());
        if (category != null && !category.getId().equals(editParam.getId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        CategoryKnowledgeDoc categoryKnowledgeDoc = categoryKnowledgeDocParamMapper.editParam2Entity(editParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,categoryKnowledgeDoc);
        return updateById(categoryKnowledgeDoc);
    }

    @Override
    public List<CategoryMenuDto> findCategoryTree() {
        List<CategoryMenuDto> allCategoryKnowledgeDoc = categoryKnowledgeDocMapper.findAllCategoryKnowledgeDoc(0);
        allCategoryKnowledgeDoc.forEach(dto -> generateScopedSlots(dto));
        return allCategoryKnowledgeDoc;
    }

    @Override
    public List<CategoryMenuDto> findCategoryTree(String categoryId) {
        return categoryKnowledgeDocMapper.getCategoryChildren(0,categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(String id) {
        List<String> idList = fetchItemIdListByParentId(id);
        idList.add(id);
        LambdaQueryWrapper<KnowledgeDoc> query = Wrappers.lambdaQuery();
        query.in(KnowledgeDoc::getCategoryId, idList);
        int count = knowledgeDocService.getBaseMapper().selectCount(query);
        if (count > 0){
            log.error("该分类有对应的文档信息，不能删除！");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("referenced", null, LocaleContextHolder.getLocale())));
        }
        CategoryKnowledgeDocDto docDto = getDtoById(id);
        if (super.removeById(id)) {
            if (StringUtils.isBlank(docDto.getParentId())) {
                return true;
            }
            // 不为0则不修改父节点的is_leaf
            LambdaQueryWrapper<CategoryKnowledgeDoc> wrapperQuery = Wrappers.lambdaQuery();
            if (count(wrapperQuery.eq(CategoryKnowledgeDoc::getParentId, docDto.getParentId())) != 0) {
                return true;
            }

            LambdaUpdateWrapper<CategoryKnowledgeDoc> wrapperUpdate = Wrappers.lambdaUpdate();
            return update(wrapperUpdate.eq(CategoryKnowledgeDoc::getId, docDto.getParentId()).set(CategoryKnowledgeDoc::getIsLeaf, true));
        }
        return false;
    }

    @Override
    public List<ChildrenCategoryDto> fetchChildrenCategoryList() {
        List<CategoryMenuDto> categoryMenuDtoList = categoryKnowledgeDocMapper.findAllCategoryKnowledgeDoc(0);
        return initChildrenMenuNameList(categoryMenuDtoList);
    }


    @Override
    public CategoryKnowledgeDocDto getDtoById(String id) {
        return categoryKnowledgeDocParamMapper.entity2Dto((CategoryKnowledgeDoc) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CategoryKnowledgeDocDto> rows) {
        return saveBatch(categoryKnowledgeDocParamMapper.dtoList2Entity(rows));
    }

    private List<String> fetchItemIdListByParentId(String parentId) {
        if (StringUtils.isBlank(parentId)){
            return new ArrayList();
        }
        List<CategoryMenuDto> menuDtoList = findCategoryTree(parentId);
        List<String> idList = new ArrayList();
        initIdList(menuDtoList, idList);
        return idList;
    }

    private Wrapper<CategoryKnowledgeDoc> getPageSearchWrapper(CategoryKnowledgeDocQueryParam queryParam) {
        LambdaQueryWrapper<CategoryKnowledgeDoc> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), CategoryKnowledgeDoc::getName, queryParam.getName());
        if(BaseEntity.class.isAssignableFrom(CategoryKnowledgeDoc.class)){
            wrapper.orderByDesc(CategoryKnowledgeDoc::getUpdateTime, CategoryKnowledgeDoc::getCreateTime);
        }
        return wrapper;
    }

    /**
     * 获取同一目录下相同名称的对象
     * @param name
     * @param parentId
     * @return
     */
    private CategoryKnowledgeDoc getByNameAndParentId(String name, String parentId){
        parentId = StringUtils.isNotBlank(parentId) ? parentId : "";
        LambdaQueryWrapper<CategoryKnowledgeDoc> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CategoryKnowledgeDoc::getParentId, parentId);
        wrapper.eq(CategoryKnowledgeDoc::getName, name);
        return (CategoryKnowledgeDoc) this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据菜单列表处理子类菜单disable属性
     * 叶子节点的disable为false，父子节点为true
     * @return
     */
    private List<ChildrenCategoryDto> initChildrenMenuNameList(List<CategoryMenuDto> categoryMenuDtoList){
        List<ChildrenCategoryDto> result = new ArrayList();
        for (CategoryMenuDto menuDto : categoryMenuDtoList){
            ChildrenCategoryDto childrenMenuDto = new ChildrenCategoryDto();
            BeanUtils.copyProperties(menuDto, childrenMenuDto);
            childrenMenuDto.setDisabled(false);
            result.add(childrenMenuDto);
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                childrenMenuDto.setChildren(new ArrayList());
                List<ChildrenCategoryDto> categoryDtoList = initChildrenMenuNameList(menuDto.getChildren());
                childrenMenuDto.getChildren().addAll(categoryDtoList);
            }
        }
        result = result.stream().sorted(Comparator.comparing(ChildrenCategoryDto::getName)).collect(Collectors.toList());
        return result;
    }

    private void generateScopedSlots(CategoryMenuDto categoryMenuDto){
        if (categoryMenuDto.getIsLeaf()){
            categoryMenuDto.getScopedSlots().setSwitcherIcon("");
        } else {
            categoryMenuDto.getChildren().forEach( dto-> generateScopedSlots(dto) );
        }
    }

    private void initIdList(List<CategoryMenuDto> menuParamList, List<String> categoryList){
        for (CategoryMenuDto menuDto : menuParamList){
            categoryList.add(menuDto.getId());
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                initIdList(menuDto.getChildren(), categoryList);
            }
        }
    }
}
