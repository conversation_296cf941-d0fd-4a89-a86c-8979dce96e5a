package cn.getech.ehm.knowledge.dto.newsfavorites;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 新闻资讯收藏 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-26
 */
@Data
@ApiModel(value = "NewsFavoritesDto", description = "新闻资讯收藏返回数据模型")
public class NewsFavoritesDto{

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "描述")
    @Excel(name="描述",cellType = Excel.ColumnType.STRING )
    private String remark;

}