package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentDto;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentQueryParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;

import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.ICourseCommentService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 课程评论信息模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@RestController
@RequestMapping("/courseComment")
@Api(tags = "web: 课程评论信息模块服务接口")
public class WebCourseCommentController {

    @Autowired
    private ICourseCommentService courseCommentService;

    /**
     * 分页获取课程评论信息模块列表
     */
    @ApiOperation("分页获取课程评论信息模块列表")
    @GetMapping("/list")
    //@Permission("course:comment:list")
    public RestResponse<PageResult<CourseCommentDto>> pageList(@Valid CourseCommentQueryParam courseCommentQueryParam){
        return RestResponse.ok(courseCommentService.pageDto(courseCommentQueryParam));
    }

    /**
     * 根据id删除课程评论信息模块
     */
    @ApiOperation(value="根据id删除课程评论信息模块")
    @AuditLog(title = "课程评论信息模块",desc = "课程评论信息模块",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("course:comment:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(courseCommentService.removeById(id));
    }

    /**
     * 根据id获取课程评论信息模块
     */
    @ApiOperation(value = "根据id获取课程评论信息模块")
    @GetMapping(value = "/{id}")
    //@Permission("course:comment:list")
    public RestResponse<CourseCommentDto> get(@PathVariable  String id) {
        return RestResponse.ok(courseCommentService.getDtoById(id));
    }

}
