package cn.getech.ehm.knowledge.dto.coursefavorites;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 课程用户收藏关系表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseFavorites新增", description = "课程用户收藏关系表新增参数")
public class CourseFavoritesAddParam extends ApiParam {

    @ApiModelProperty(value = "描述")
    private String remark;
}