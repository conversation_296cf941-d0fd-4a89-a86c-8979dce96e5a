package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.entity.NewsInfo;
import cn.getech.ehm.knowledge.dto.news.NewsInfoAddParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoEditParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoQueryParam;
import cn.getech.poros.framework.common.param.PageParam;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻资讯信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
public interface INewsInfoService extends IBaseService<NewsInfo> {

        /**
         * 分页查询，返回Dto
         *
         * @param newsInfoQueryParam
         * @return
         */
        PageResult<NewsInfoDto> pageDto(NewsInfoQueryParam newsInfoQueryParam);

        /**
         * 分页查询app轮播图配置下拉列表，返回Dto
         *
         * @param pageParam
         * @return
         */
        PageResult<NewsInfoDto> selectList(PageParam pageParam);

        List<NewsInfoDto> webFirstpage(NewsInfoQueryParam newsInfoQueryParam);

        PageResult<NewsInfoDto> pageCustomDto(NewsInfoQueryParam newsInfoQueryParam);

        /**
         * 分页查询，用户收藏列表
         * @param pageParam
         * @return
         */
        PageResult<NewsInfoDto> userFavoritesList(PageParam pageParam);

        /**
         * 根据id查询新闻信息，包括是否收藏
         * @param id
         * @return
         */
        NewsInfoDto getUserNewsDtoById(String id);

        /**
         * app分页查询，返回Dto
         *
         * @param newsInfoQueryParam
         * @return
         */
        PageResult<NewsInfoDto> appPageList(NewsInfoQueryParam newsInfoQueryParam);

        /**
         * 关键词搜索，分成查询
         * @param pageParam
         * @return
         */
        PageResult<NewsInfoDto> keywordSearch(PageParam pageParam);

        /**
         * 保存
         * @param newsInfoAddParam
         * @return
         */
        boolean saveByParam(NewsInfoAddParam newsInfoAddParam);

        /**
         * 收藏接口
         * @param newsId
         * @return
         */
        boolean favorites(String newsId);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        NewsInfoDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<NewsInfoDto> rows);

        /**
         * 更新
         * @param newsInfoEditParam
         */
        boolean updateByParam(NewsInfoEditParam newsInfoEditParam);

        /**
         * 批量删除
         * @param ids
         * @return
         */
        boolean removeByIds(String[] ids);

        /**
         * 获取新闻子类数和记录数
         * @return
         */
        KnowledgeCategoryDto getKnowledgeCategoryDto();

}