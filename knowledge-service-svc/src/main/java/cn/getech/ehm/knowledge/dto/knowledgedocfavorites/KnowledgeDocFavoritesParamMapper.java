package cn.getech.ehm.knowledge.dto.knowledgedocfavorites;

import cn.getech.ehm.knowledge.entity.KnowledgeDocFavorites;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 知识文档用户收藏关系表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-16
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  KnowledgeDocFavoritesParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param knowledgeDocFavoritesAddParam
     * @return
     */
    KnowledgeDocFavorites addParam2Entity(KnowledgeDocFavoritesAddParam knowledgeDocFavoritesAddParam);

    /**
     * 编辑参数转换为实体
     * @param knowledgeDocFavoritesEditParam
     * @return
     */
    KnowledgeDocFavorites editParam2Entity(KnowledgeDocFavoritesEditParam knowledgeDocFavoritesEditParam);

    /**
     * 实体转换为Dto
     * @param knowledgeDocFavorites
     * @return
     */
    KnowledgeDocFavoritesDto entity2Dto(KnowledgeDocFavorites knowledgeDocFavorites);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<KnowledgeDocFavoritesDto> pageEntity2Dto(PageResult<KnowledgeDocFavorites> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<KnowledgeDocFavorites> dtoList2Entity(List<KnowledgeDocFavoritesDto> rows);

}
