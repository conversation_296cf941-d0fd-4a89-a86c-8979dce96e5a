package cn.getech.ehm.knowledge.mapper;

import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.entity.NewsCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 新闻分类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Repository
public interface NewsCategoryMapper extends BaseMapper<NewsCategory> {

    List<CategoryMenuDto> fetchCategoryMenuList();

    List<CategoryMenuDto> fetchItemMenuByParentId(String parentId);

}
