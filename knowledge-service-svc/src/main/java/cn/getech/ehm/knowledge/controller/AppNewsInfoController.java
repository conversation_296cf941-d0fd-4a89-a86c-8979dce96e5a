package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.news.NewsInfoQueryParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.service.INewsCategoryService;
import cn.getech.ehm.knowledge.service.INewsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 新闻资讯信息模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@RestController
@RequestMapping("/appNewsInfo")
@Api(tags = "app: 新闻资讯信息模块服务接口")
public class AppNewsInfoController {

    @Autowired
    private INewsInfoService newsInfoService;

    @Autowired
    private INewsCategoryService newsCategoryService;

    /**
     * 分页获取新闻资讯信息模块列表
     */
    @ApiOperation("分页获取新闻资讯信息模块列表")
    @GetMapping("/list")
    //@Permission("news:info:list")
    public RestResponse<PageResult<NewsInfoDto>> pageList(@Valid NewsInfoQueryParam newsInfoQueryParam){
        return RestResponse.ok(newsInfoService.appPageList(newsInfoQueryParam));
    }

    /**
     * 获取新闻分类菜单测试服务接口列表
     */
    @ApiOperation("获取新闻分类菜单接口")
    @GetMapping("/categoryMenuList")
    //@Permission("course:category:list")
    public RestResponse<List<CategoryMenuDto>> categoryMenuList(){
        return RestResponse.ok(newsCategoryService.fetchCategoryMenuList());
    }

    /**
     * 获取新闻用户手册列表
     */
    @ApiOperation("用户收藏列表")
    @GetMapping("/userFavoritesList")
    //@Permission("course:category:list")
    public RestResponse<PageResult<NewsInfoDto>> userFavoritesList(PageParam pageParam){
        return RestResponse.ok(newsInfoService.userFavoritesList(pageParam));
    }

    /**
     * 根据id获取新闻资讯信息模块
     */
    @ApiOperation(value = "根据id获取新闻资讯信息")
    @GetMapping(value = "/{id}")
    //@Permission("news:info:list")
    public RestResponse<NewsInfoDto> get(@PathVariable  String id) {
        return RestResponse.ok(newsInfoService.getUserNewsDtoById(id));
    }

    @GetMapping(value = "/favorites/{newsId}")
    @ApiOperation(value = "收藏", notes = "返回true 为 收藏成功，返回false 表示 未收藏或者已经取消收藏")
    @AuditLog(title = "收藏", desc = "收藏", businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> favorites(@PathVariable("newsId") @NotEmpty String newsId){
        return RestResponse.ok(newsInfoService.favorites(newsId));
    }

}
