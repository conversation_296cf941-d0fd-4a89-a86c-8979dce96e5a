package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.course.*;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentAddParam;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentDto;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentQueryParam;
import cn.getech.ehm.system.dto.KeTianVideoQueryParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.ehm.knowledge.service.ICourseCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;

import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.ICourseInfoService;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 课程信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@RestController
@RequestMapping("/courseInfo")
@Api(tags = "web: 课程信息服务接口")
public class WebCourseInfoController {

    @Autowired
    private ICourseInfoService courseInfoService;

    @Autowired
    private ICourseCommentService courseCommentService;

    /**
     * 分页获取课程信息列表
     */
    @ApiOperation("分页获取课程信息列表")
    @GetMapping("/list")
    //@Permission("course:info:list")
    public RestResponse<PageResult<CourseInfoDto>> pageList(@Valid CourseInfoQueryParam courseInfoQueryParam){
        return RestResponse.ok(courseInfoService.pageDto(courseInfoQueryParam));
    }

    /**
     * 分页获取课程信息客户列表
     */
    @ApiOperation("分页获取课程信息客户列表")
    @GetMapping("/customList")
    //@Permission("course:info:list")
    public RestResponse<PageResult<CourseInfoDto>> pageCustomList(@Valid CourseInfoQueryParam courseInfoQueryParam){
        return RestResponse.ok(courseInfoService.pageCustomDto(courseInfoQueryParam));
    }


    /**
     * 分页获取课程信息列表
     */
    @ApiOperation("web首页课程信息列表")
    @GetMapping("/webFirstpage/{size}")
    //@Permission("course:info:list")
    public RestResponse<List<CourseInfoDto>> webFirstpage(@PathVariable("size") Integer size,
                                                          @RequestParam(value = "isClient", required = false) Integer isClient){
        CourseInfoQueryParam courseInfoQueryParam = new CourseInfoQueryParam();
        courseInfoQueryParam.setLimit(size);
        if(null != isClient){
            courseInfoQueryParam.setIsClient(isClient);
        }

        return RestResponse.ok(courseInfoService.webFirstpage(courseInfoQueryParam));
    }

    /**
     * 新增课程信息
     */
    @ApiOperation("新增课程信息")
    @AuditLog(title = "课程信息",desc = "新增课程信息",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("course:info:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CourseInfoAddParam courseInfoAddParam) {
        return RestResponse.ok(courseInfoService.saveByParam(courseInfoAddParam));
    }

    /**
     * 修改课程信息
     */
    @ApiOperation(value="修改课程信息")
    @AuditLog(title = "课程信息",desc = "修改课程信息",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("course:info:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CourseInfoEditParam courseInfoEditParam) {
        return RestResponse.ok(courseInfoService.updateByParam(courseInfoEditParam));
    }

    /**
     * 批量授权
     */
    @ApiOperation(value="批量授权")
    @AuditLog(title = "课程信息",desc = "批量授权",businessType = BusinessType.UPDATE)
    @PostMapping("batchEmpower")
    //@Permission("course:info:update")
    public RestResponse<Boolean> batchEmpower(@RequestBody @Valid CourseEmpowerParam param) {
        return RestResponse.ok(courseInfoService.batchEmpower(param));
    }

    /**
     * 根据id删除课程信息
     */
    @ApiOperation(value="根据id删除课程信息")
    @AuditLog(title = "课程信息",desc = "课程信息",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("course:info:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(courseInfoService.removeById(id));
    }

    /**
     * 根据id获取课程信息
     */
    @ApiOperation(value = "根据id获取课程信息")
    @GetMapping(value = "/{id}")
    //@Permission("course:info:list")
    public RestResponse<CourseInfoDto> get(@PathVariable  String id) {
        return RestResponse.ok(courseInfoService.getDtoById(id));
    }

    /**
     * 分页获取课程评论信息模块列表
     */
    @ApiOperation("分页获取课程评论信息模块列表")
    @GetMapping("comment/list")
    //@Permission("course:comment:list")
    public RestResponse<PageResult<CourseCommentDto>> pageList(@Valid CourseCommentQueryParam courseCommentQueryParam){
        return RestResponse.ok(courseCommentService.pageDto(courseCommentQueryParam));
    }

    /**
     * 新增课程评论信息模块
     */
    @ApiOperation("新增课程评论信息模块")
    @AuditLog(title = "课程评论信息模块",desc = "新增课程评论信息模块",businessType = BusinessType.INSERT)
    @PostMapping("comment")
    //@Permission("course:comment:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CourseCommentAddParam courseCommentAddParam) {
        return RestResponse.ok(courseCommentService.saveByParam(courseCommentAddParam));
    }

    /**
     * 根据id删除评论信息
     */
    @ApiOperation(value="根据id删除课程信息")
    @AuditLog(title = "删除评论信息",desc = "删除评论信息",businessType = BusinessType.DELETE)
    @DeleteMapping("comment/{id}")
    //@Permission("course:info:delete")
    public RestResponse<Boolean> deleteComment(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(courseCommentService.removeById(id));
    }

    @PutMapping(value = "/likeCount/{courseId}")
    @ApiOperation(value = "点赞", notes = "返回true 为 点赞成功，返回false 表示 未点赞或者已经取消点赞")
    @AuditLog(title = "点赞",desc = "点赞",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> addLikeCount(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.likeCount(courseId));
    }

    @PutMapping(value = "/favorites/{courseId}")
    @ApiOperation(value = "收藏", notes = "返回true 为 收藏成功，返回false 表示 未收藏或者已经取消收藏")
    @AuditLog(title = "收藏",desc = "收藏",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> favorites(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.favorites(courseId));
    }

    @PutMapping(value = "/learningCount/{courseId}")
    @ApiOperation(value="增加观看数")
    @AuditLog(title = "增加观看数",desc = "增加观看数",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> addLearningCount(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.addLearningCount(courseId));
    }

    @ApiOperation("获取科天云Sign")
    @GetMapping("/getKeTianYunSign")
    public RestResponse<String> getKeTianYunSign(@Valid KeTianVideoQueryParam keTianVideoQueryParam){
        return courseInfoService.getKeTianYunSign(keTianVideoQueryParam);
    }

    @ApiOperation("获取课程数量")
    @GetMapping("/getCourseCount")
    public RestResponse<Integer> getCourseCount(){
        return RestResponse.ok(courseInfoService.getCourseCount());
    }

    @ApiOperation("获取课程播放数量")
    @GetMapping("/getPlayCount")
    public RestResponse<Integer> getPlayCount(){
        return RestResponse.ok(courseInfoService.getPlayCount());
    }

}
