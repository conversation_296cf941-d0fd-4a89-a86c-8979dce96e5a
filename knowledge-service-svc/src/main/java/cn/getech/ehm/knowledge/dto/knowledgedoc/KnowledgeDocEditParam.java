package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <pre>
 * 知识学习库 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudyKnowledgeDoc编辑", description = "知识学习库编辑参数")
public class KnowledgeDocEditParam extends ApiParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "全局ID", required = true)
    @NotBlank()
    private String id;

    @ApiModelProperty(value ="知识文档分类ID")
    private String categoryId;

    @ApiModelProperty(value = "附件ID")
    private String attachId;

    @ApiModelProperty(value = "培训师")
    private String trainer;

    @ApiModelProperty(value = "备注信息")
    private String description;

}
