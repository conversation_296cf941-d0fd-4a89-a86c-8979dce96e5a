package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentAddParam;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentDto;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentQueryParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.course.CourseInfoDto;
import cn.getech.ehm.knowledge.dto.course.CourseInfoQueryParam;
import cn.getech.ehm.knowledge.service.ICourseCategoryService;
import cn.getech.ehm.knowledge.service.ICourseCommentService;
import cn.getech.ehm.knowledge.service.ICourseFavoritesService;
import cn.getech.ehm.knowledge.service.ICourseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 课程信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@RestController
@RequestMapping("/appCourseInfo")
@Api(tags = "app: 课程信息服务接口")
public class AppCourseInfoController {

    @Autowired
    private ICourseInfoService courseInfoService;

    @Autowired
    private ICourseCommentService courseCommentService;

    @Autowired
    private ICourseCategoryService courseCategoryService;

    /**
     * 分页获取课程信息列表
     */
    @ApiOperation("分页获取课程信息列表")
    @GetMapping("/list")
    //@Permission("course:info:list")
    public RestResponse<PageResult<CourseInfoDto>> pageList(@Valid CourseInfoQueryParam queryParam){
        return RestResponse.ok(courseInfoService.appPageList(queryParam));
    }

    /**
     * 获取课程分类菜单测试服务接口列表
     */
    @ApiOperation("获取课程分类菜单接口")
    @GetMapping("/categoryMenuList")
    //@Permission("course:category:list")
    public RestResponse<List<CategoryMenuDto>> categoryMenuList(){
        return RestResponse.ok(courseCategoryService.fetchCategoryMenuList());
    }

    /**
     * 根据id获取课程信息
     */
    @ApiOperation(value = "根据id获取课程信息")
    @GetMapping(value = "/{id}")
    //@Permission("course:info:list")
    public RestResponse<CourseInfoDto> get(@PathVariable  String id) {
        return RestResponse.ok(courseInfoService.getUserCourseDtoById(id));
    }

    /**
     * 分页获取课程评论信息模块列表
     */
    @ApiOperation(value = "分页获取课程评论信息模块列表", notes = "页数固定为1，sort第一次默认0，下拉翻页操作取最后一条的sort值")
    @GetMapping("comment/list")
    //@Permission("course:comment:list")
    public RestResponse<PageResult<CourseCommentDto>> pageList(@Valid CourseCommentQueryParam courseCommentQueryParam){
        return RestResponse.ok(courseCommentService.pageDto(courseCommentQueryParam));
    }

    /**
     * 新增课程评论信息模块
     */
    @ApiOperation("新增课程评论信息模块")
    @AuditLog(title = "课程评论信息模块",desc = "新增课程评论信息模块",businessType = BusinessType.INSERT)
    @PostMapping("comment")
    //@Permission("course:comment:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CourseCommentAddParam courseCommentAddParam) {
        return RestResponse.ok(courseCommentService.saveByParam(courseCommentAddParam));
    }

    @PutMapping(value = "/likeCount/{courseId}")
    @ApiOperation(value = "点赞", notes = "返回true 为 点赞成功，返回false 表示 未点赞或者已经取消点赞")
    @AuditLog(title = "点赞",desc = "点赞",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> addLikeCount(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.likeCount(courseId));
    }

    @PutMapping(value = "/learningCount/{courseId}")
    @ApiOperation(value="增加观看数")
    @AuditLog(title = "增加观看数",desc = "增加观看数",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> addLearningCount(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.addLearningCount(courseId));
    }

    @PutMapping(value = "/favorites/{courseId}")
    @ApiOperation(value = "收藏", notes = "返回true 为 收藏成功，返回false 表示 未收藏或者已经取消收藏")
    @AuditLog(title = "收藏",desc = "收藏",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> favorites(@PathVariable("courseId") @NotEmpty String courseId){
        return RestResponse.ok(courseInfoService.favorites(courseId));
    }

    /**
     * 分页获取知识文档收藏模块列表
     */
    @ApiOperation("分页获取课程收藏列表")
    @GetMapping("/favoritesList")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<PageResult<CourseInfoDto>> pageFavoritesList(@Valid CourseInfoQueryParam courseInfoQueryParam){
        return RestResponse.ok(courseInfoService.appAllFavoritesPageList(courseInfoQueryParam));
    }

}
