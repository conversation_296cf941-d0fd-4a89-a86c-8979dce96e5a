package cn.getech.ehm.knowledge.dto.coursepraise;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 课程用户点赞关系表 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CoursePraise编辑", description = "课程用户点赞关系表编辑参数")
public class CoursePraiseEditParam extends ApiParam {

    @ApiModelProperty(value = "全局ID")
    private String id;

}
