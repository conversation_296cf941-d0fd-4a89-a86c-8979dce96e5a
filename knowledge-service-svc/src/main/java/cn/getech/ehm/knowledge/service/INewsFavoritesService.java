package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesDto;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesQueryParam;
import cn.getech.ehm.knowledge.entity.NewsFavorites;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻资讯收藏 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-26
 */
public interface INewsFavoritesService extends IBaseService<NewsFavorites> {

        /**
         * 分页查询，返回Dto
         *
         * @param newsFavoritesQueryParam
         * @return
         */
        PageResult<NewsFavoritesDto> pageDto(NewsFavoritesQueryParam newsFavoritesQueryParam);

        /**
         * 保存
         * @param newsFavoritesAddParam
         * @return
         */
        boolean saveByParam(NewsFavoritesAddParam newsFavoritesAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        NewsFavoritesDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<NewsFavoritesDto> rows);

        /**
         * 更新
         * @param newsFavoritesEditParam
         */
        boolean updateByParam(NewsFavoritesEditParam newsFavoritesEditParam);

        /**
         * 更新用户收藏数
         */
        void updateFavorites();

        /**
         * 收藏接口
         * @param newsId
         * @return
         */
        boolean favorites(String newsId);

        /**
         * 是否收藏
         * @param newsId
         * @return
         */
        boolean isFavorite(String newsId);
}