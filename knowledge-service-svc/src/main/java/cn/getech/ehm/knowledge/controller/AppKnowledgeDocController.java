package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocQueryParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.service.IKnowledgeDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 知识文档模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/appKnowledgeDoc")
@Api(tags = "app:知识文档模块服务接口")
public class AppKnowledgeDocController {

    @Autowired
    private IKnowledgeDocService knowledgeDocService;

    /**
     * 分页获取知识文档模块列表
     */
    @ApiOperation("分页获取知识文档模块列表")
    @GetMapping("/list")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<PageResult<KnowledgeDocDto>> pageList(@Valid KnowledgeDocQueryParam knowledgeDocQueryParam){
        return RestResponse.ok(knowledgeDocService.appPageList(knowledgeDocQueryParam));
    }


    /**
     * 根据id获取知识文档模块
     */
    @ApiOperation(value = "根据id获取知识文档")
    @GetMapping(value = "/{id}")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<KnowledgeDocDto> get(@PathVariable String id) {
        return RestResponse.ok(knowledgeDocService.getDtoById(id));
    }

    @PutMapping(value = "/favorites/{knowledgeDocId}")
    @ApiOperation(value = "收藏", notes = "返回true 为 收藏成功，返回false 表示 未收藏或者已经取消收藏")
    @AuditLog(title = "收藏",desc = "收藏",businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> favorites(@PathVariable("knowledgeDocId") @NotEmpty String knowledgeDocId){
        return RestResponse.ok(knowledgeDocService.favorites(knowledgeDocId));
    }

    /**
     * 分页获取知识文档收藏模块列表
     */
    @ApiOperation("分页获取知识文档收藏列表")
    @GetMapping("/favoritesList")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<PageResult<KnowledgeDocDto>> pageFavoritesList(@Valid KnowledgeDocQueryParam queryParam){
        return RestResponse.ok(knowledgeDocService.appAllFavoritesPageList(queryParam));
    }


}
