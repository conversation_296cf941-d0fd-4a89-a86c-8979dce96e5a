package cn.getech.ehm.knowledge.controller;


import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseQueryParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseAddParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseEditParam;
import cn.getech.ehm.knowledge.dto.coursepraise.CoursePraiseDto;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;

import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.ICoursePraiseService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 课程点赞模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@RestController
@RequestMapping("/coursePraise")
@Api(tags = "web:课程点赞模块服务接口")
public class WebCoursePraiseController {

    @Autowired
    private ICoursePraiseService coursePraiseService;

    /**
     * 分页获取课程点赞模块列表
     */
    @ApiOperation("分页获取课程点赞模块列表")
    @GetMapping("/list")
    //@Permission("course:praise:list")
    public RestResponse<PageResult<CoursePraiseDto>> pageList(@Valid CoursePraiseQueryParam coursePraiseQueryParam){
        return RestResponse.ok(coursePraiseService.pageDto(coursePraiseQueryParam));
    }

    /**
     * 新增课程点赞模块
     */
    @ApiOperation("新增课程点赞模块")
    @AuditLog(title = "课程点赞模块",desc = "新增课程点赞模块",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("course:praise:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CoursePraiseAddParam coursePraiseAddParam) {
        return RestResponse.ok(coursePraiseService.saveByParam(coursePraiseAddParam));
    }

    /**
     * 修改课程点赞模块
     */
    @ApiOperation(value="修改课程点赞模块")
    @AuditLog(title = "课程点赞模块",desc = "修改课程点赞模块",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("course:praise:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CoursePraiseEditParam coursePraiseEditParam) {
        return RestResponse.ok(coursePraiseService.updateByParam(coursePraiseEditParam));
    }

    /**
     * 根据id删除课程点赞模块
     */
    @ApiOperation(value="根据id删除课程点赞模块")
    @AuditLog(title = "课程点赞模块",desc = "课程点赞模块",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("course:praise:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
        return RestResponse.ok(coursePraiseService.removeByIds(ids));
    }

    /**
     * 根据id获取课程点赞模块
     */
    @ApiOperation(value = "根据id获取课程点赞模块")
    @GetMapping(value = "/{id}")
    //@Permission("course:praise:list")
    public RestResponse<CoursePraiseDto> get(@PathVariable  Long id) {
        return RestResponse.ok(coursePraiseService.getDtoById(id));
    }

}
