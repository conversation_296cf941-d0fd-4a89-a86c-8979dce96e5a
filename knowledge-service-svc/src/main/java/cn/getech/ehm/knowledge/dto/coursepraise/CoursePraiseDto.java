package cn.getech.ehm.knowledge.dto.coursepraise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 课程用户点赞关系表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-13
 */
@Data
@ApiModel(value = "CoursePraiseDto", description = "课程用户点赞关系表返回数据模型")
public class CoursePraiseDto{

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

}