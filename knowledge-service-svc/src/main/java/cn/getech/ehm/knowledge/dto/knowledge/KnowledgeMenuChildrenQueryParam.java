package cn.getech.ehm.knowledge.dto.knowledge;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CoursePraise查询", description = "课程用户点赞关系表查询参数")
public class KnowledgeMenuChildrenQueryParam extends ApiParam {

    @Range(max = 3L, min = 1L)
    @ApiModelProperty(value = "类型。1-新闻，2-课程，3-文档分类", required = true)
    Integer type;

    @ApiModelProperty(value = "文档分类id。")
    String categoryId;
}
