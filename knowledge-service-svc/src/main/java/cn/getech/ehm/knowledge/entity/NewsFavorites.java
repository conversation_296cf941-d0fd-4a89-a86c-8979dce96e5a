package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 新闻资讯收藏
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_news_favorites")
public class NewsFavorites extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 用户ID
     */
    @TableField("uid")
    private String uid;

    /**
     * 新闻ID
     */
    @TableField("news_id")
    private String newsId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 0：未删除  1: 已删除
     */
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;


}
