package cn.getech.ehm.knowledge.dto.newsfavorites;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 新闻资讯收藏 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsFavorites编辑", description = "新闻资讯收藏编辑参数")
public class NewsFavoritesEditParam extends ApiParam {

    @ApiModelProperty(value = "全局ID")
    private String id;

    @ApiModelProperty(value = "描述")
    private String remark;

}
