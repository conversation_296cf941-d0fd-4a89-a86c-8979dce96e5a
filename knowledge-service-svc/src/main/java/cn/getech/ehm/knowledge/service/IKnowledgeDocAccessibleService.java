package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.entity.KnowledgeDocAccessible;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 知识文档权限
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
public interface IKnowledgeDocAccessibleService extends IBaseService<KnowledgeDocAccessible> {


    /**
     * 批量保存
     */
    boolean saveDtoBatch(List<String> knowledgeDocIds, List<String> roleIds);

    /**
     * 获取当前登录人对应知识文档ids
     * @return
     */
    List<String> listCurrentDocIds();

    /**
     * 根据知识文档id集合删除
     * @param knowledgeDocIds
     * @return
     */
    Boolean deleteByDocIds(List<String> knowledgeDocIds);

    /**
     * 获取知识文档对应角色权限集合
     * @param knowledgeDocIds
     * @return
     */
    Map<String, List<String>> getDocAccessMap(List<String> knowledgeDocIds);

}
