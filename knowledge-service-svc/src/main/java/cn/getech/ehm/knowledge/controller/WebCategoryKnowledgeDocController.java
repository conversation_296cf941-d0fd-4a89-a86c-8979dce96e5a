package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocEditParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocQueryParam;
import cn.getech.ehm.knowledge.service.ICategoryKnowledgeDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 知识文档模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/categoryKnowledgeDoc")
@Api(tags = "web:知识文档分类模块服务接口")
public class WebCategoryKnowledgeDocController {

    @Autowired
    private ICategoryKnowledgeDocService categoryKnowledgeDocService;

    /**
     * 分页获取知识文档模块列表
     */
    @ApiOperation("分页获取知识文档分类列表")
    @GetMapping("/list")
    //@Permission("study:category:knowledge:doc:list")
    public RestResponse<PageResult<CategoryKnowledgeDocDto>> pageList(@Valid CategoryKnowledgeDocQueryParam queryParam){
        return RestResponse.ok(categoryKnowledgeDocService.pageDto(queryParam));
    }

    /**
     * 新增知识文档模块
     */
    @ApiOperation("新增知识文档分类")
    @AuditLog(title = "知识文档分类",desc = "新增知识文档分类",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("study:category:knowledge:doc:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CategoryKnowledgeDocAddParam categoryKnowledgeDocAddParam) {
        return RestResponse.ok(categoryKnowledgeDocService.saveByParam(categoryKnowledgeDocAddParam));
    }

    /**
     * 修改知识文档模块
     */
    @ApiOperation(value="修改分类知识文档模块")
    @AuditLog(title = "知识文档分类",desc = "修改知识文档分类",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("study:category:knowledge:doc:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CategoryKnowledgeDocEditParam categoryKnowledgeDocEditParam) {
        return RestResponse.ok(categoryKnowledgeDocService.updateByParam(categoryKnowledgeDocEditParam));
    }

    /**
     * 根据id删除知识文档模块
     */
    @ApiOperation(value="根据id删除知识文档分类")
    @AuditLog(title = "知识文档分类",desc = "知识文档分类",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("study:category:knowledge:doc:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(categoryKnowledgeDocService.removeById(id));
    }

    /**
     * 根据id获取分类知识文档模块
     */
    @ApiOperation(value = "根据id获取知识文档模分类")
    @GetMapping(value = "/{id}")
    //@Permission("study:category:knowledge:doc:list")
    public RestResponse<CategoryKnowledgeDocDto> get(@PathVariable  String id) {
        return RestResponse.ok(categoryKnowledgeDocService.getDtoById(id));
    }

    @ApiOperation(value = "查询知识文档分类菜单接口")
    @GetMapping("/findCategoryTree")
    public RestResponse<List<CategoryMenuDto>> findCategoryTree(){
        return RestResponse.ok(categoryKnowledgeDocService.findCategoryTree());
    }


    @ApiOperation("获取子分类菜下拉列表接口")
    @GetMapping("/childrenCategoryList")
    //@Permission("course:category:list")
    public RestResponse<List<ChildrenCategoryDto>> childrenCategoryList(){
        return RestResponse.ok(categoryKnowledgeDocService.fetchChildrenCategoryList());
    }

    @ApiOperation("获取客户子分类菜下拉列表接口")
    @GetMapping("/childrenCategoryCustomList")
    //@Permission("course:category:list")
    public RestResponse<List<ChildrenCategoryDto>> childrenCategoryCustomList(){
        return RestResponse.ok(categoryKnowledgeDocService.fetchChildrenCategoryList());
    }

}
