package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 新闻分类信息表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsCategory查询", description = "新闻分类信息表查询参数")
public class NewsCategoryQueryParam extends PageParam {

    @ApiModelProperty(value = "标签名。")
    private String name;

}
