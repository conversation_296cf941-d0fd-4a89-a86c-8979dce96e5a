package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.knowledge.entity.NewsCategory;
import cn.getech.ehm.knowledge.entity.NewsInfo;
import cn.getech.ehm.knowledge.dto.news.*;
import cn.getech.ehm.knowledge.mapper.NewsCategoryMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.service.INewsCategoryService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.service.INewsInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.ArrayList;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;


/**
 * <pre>
 * 新闻分类信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class NewsCategoryServiceImpl extends BaseServiceImpl<NewsCategoryMapper, NewsCategory> implements INewsCategoryService {

    @Autowired
    private NewsCategoryParamMapper newsCategoryParamMapper;
    @Autowired
    private NewsCategoryMapper newsCategoryMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private INewsInfoService newsInfoService;

    @Override
    public PageResult<NewsCategoryDto> pageDto(NewsCategoryQueryParam newsCategoryQueryParam) {
        Wrapper<NewsCategory> wrapper = getPageSearchWrapper(newsCategoryQueryParam);
        PageResult<NewsCategoryDto> result = newsCategoryParamMapper.pageEntity2Dto(page(newsCategoryQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(NewsCategoryAddParam addParam) {
        NewsCategory category = getByNameAndParentId(addParam.getName(), addParam.getParentId());
        if (category != null){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        NewsCategory newsCategory = newsCategoryParamMapper.addParam2Entity(addParam);
        if (StringUtils.isNotEmpty(addParam.getParentId())){
            NewsCategory parentCategory = new NewsCategory();
            parentCategory.setId(addParam.getParentId());
            parentCategory.setIsLeaf(false);
            updateById(parentCategory);
        }
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsCategory);
        return save(newsCategory);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(NewsCategoryEditParam editParam) {
        NewsCategory category = getByNameAndParentId(editParam.getName(), editParam.getParentId());
        if (category != null && !category.getId().equals(editParam.getId())){
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("node_name_exists", null, LocaleContextHolder.getLocale())));
        }
        NewsCategory newsCategory = newsCategoryParamMapper.editParam2Entity(editParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsCategory);
        return updateById(newsCategory);
    }

    @Override
    @Transactional
    public boolean batchUpdateCategoryMenu(List<CategoryMenuParam> menuParamList) {
        List<NewsCategory> categoryList = new ArrayList();
        initNewsCategoryList(menuParamList, categoryList, "");
        int count = 0;
        for (NewsCategory category : categoryList){
            count += this.baseMapper.updateById(category);
        }
        return count > 0;
    }

    @Override
    public NewsCategoryDto getDtoById(String id) {
        return newsCategoryParamMapper.entity2Dto((NewsCategory) this.getById(id));
    }

    @Override
    @Transactional
    public boolean removeById(String id) {
        List<String> idList = fetchItemIdListByParentId(id);
        idList.add(id);
        LambdaQueryWrapper<NewsInfo> query = Wrappers.lambdaQuery();
        query.in(NewsInfo::getCategoryId, idList);
        int count = newsInfoService.getBaseMapper().selectCount(query);
        if (count > 0){
            log.error("该分类有对应的新闻信息，不能删除！");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("referenced", null, LocaleContextHolder.getLocale())));
        }
        NewsCategoryDto categoryDto = getDtoById(id);
        if (super.removeById(id)) {
            if (StringUtils.isBlank(categoryDto.getParentId())) {
                return true;
            }

            LambdaQueryWrapper<NewsCategory> wrapperQuery = Wrappers.lambdaQuery();
            // 不为0则不修改父节点的is_leaf
            if (count(wrapperQuery.eq(NewsCategory::getParentId, categoryDto.getParentId())) != 0) {
                return true;
            }

            LambdaUpdateWrapper<NewsCategory> wrapperUpdate = Wrappers.lambdaUpdate();
            return update(wrapperUpdate.eq(NewsCategory::getId, categoryDto.getParentId()).set(NewsCategory::getIsLeaf, true));
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<NewsCategoryDto> rows) {
        return saveBatch(newsCategoryParamMapper.dtoList2Entity(rows));
    }

    @Override
    public List<CategoryMenuDto> fetchCategoryMenuList(){
        List<CategoryMenuDto> menuList = newsCategoryMapper.fetchCategoryMenuList();
        menuList.forEach(dto -> generateScopedSlots(dto));
        return Optional.ofNullable(menuList).orElse(new ArrayList());
    }

    @Override
    public List<String> fetchItemIdListByParentId(String parentId) {
        if (StringUtils.isBlank(parentId)){
            return new ArrayList();
        }
        List<CategoryMenuDto> menuDtoList = newsCategoryMapper.fetchItemMenuByParentId(parentId);
        List<String> idList = new ArrayList();
        initIdList(menuDtoList, idList);
        return idList;
    }

    @Override
    public List<ChildrenCategoryDto> fetchChildrenCategoryList() {
        List<CategoryMenuDto> categoryMenuDtoList = newsCategoryMapper.fetchCategoryMenuList();
        return initChildrenMenuNameList(categoryMenuDtoList);
    }

    private Wrapper<NewsCategory> getPageSearchWrapper(NewsCategoryQueryParam queryParam) {
        LambdaQueryWrapper<NewsCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), NewsCategory::getName, queryParam.getName());
        if(BaseEntity.class.isAssignableFrom(NewsCategory.class)){
            wrapper.orderByDesc(NewsCategory::getUpdateTime,NewsCategory::getCreateTime);
        }
        return wrapper;
    }

    private void initNewsCategoryList(List<CategoryMenuParam> menuParamList, List<NewsCategory> categoryList, String parentId){
        for (CategoryMenuParam menuParam : menuParamList){
            NewsCategory newsCategory = new NewsCategory();
            BeanUtils.copyProperties(menuParam, newsCategory);
            newsCategory.setParentId(parentId);
            categoryList.add(newsCategory);
            if (CollectionUtils.isNotEmpty(menuParam.getChildren())){
                initNewsCategoryList(menuParam.getChildren(), categoryList, menuParam.getId());
            }
        }
    }

    private void initIdList(List<CategoryMenuDto> menuParamList, List<String> categoryList){
        for (CategoryMenuDto menuDto : menuParamList){
            categoryList.add(menuDto.getId());
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                initIdList(menuDto.getChildren(), categoryList);
            }
        }
    }

    /**
     * 获取同一目录下相同名称的对象
     * @param name
     * @param parentId
     * @return
     */
    private NewsCategory getByNameAndParentId(String name, String parentId){
        parentId = StringUtils.isNotBlank(parentId) ? parentId : "";
        LambdaQueryWrapper<NewsCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(NewsCategory::getParentId, parentId);
        wrapper.eq(NewsCategory::getName, name);
        return (NewsCategory) this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据菜单列表处理子类菜单disable属性
     * 叶子节点的disable为false，父子节点为true
     * @return
     */
    private List<ChildrenCategoryDto> initChildrenMenuNameList(List<CategoryMenuDto> categoryMenuDtoList){
        List<ChildrenCategoryDto> result = new ArrayList();
        for (CategoryMenuDto menuDto : categoryMenuDtoList){
            ChildrenCategoryDto childrenMenuDto = new ChildrenCategoryDto();
            BeanUtils.copyProperties(menuDto, childrenMenuDto);
            childrenMenuDto.setDisabled(CollectionUtils.isNotEmpty(menuDto.getChildren()));
            childrenMenuDto.setIsLeaf(CollectionUtils.isEmpty(menuDto.getChildren()));
            result.add(childrenMenuDto);
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                childrenMenuDto.setChildren(new ArrayList());
                List<ChildrenCategoryDto> categoryDtoList = initChildrenMenuNameList(menuDto.getChildren());
                childrenMenuDto.getChildren().addAll(categoryDtoList);
            }
        }
        return result;
    }

    private void generateScopedSlots(CategoryMenuDto categoryMenuDto){
        if (categoryMenuDto.getIsLeaf()){
            categoryMenuDto.getScopedSlots().setSwitcherIcon("");
        } else {
            categoryMenuDto.getChildren().forEach( dto-> generateScopedSlots(dto) );
        }
    }

}
