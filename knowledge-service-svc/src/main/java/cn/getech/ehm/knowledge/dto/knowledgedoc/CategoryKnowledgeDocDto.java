package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 知识文档分类信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "StudyCategoryKnowledgeDocDto", description = "知识文档分类信息表返回数据模型")
public class CategoryKnowledgeDocDto {

    @ApiModelProperty(value = "标签名。")
    @Excel(name="标签名。",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "全局ID。")
    @Excel(name="全局ID。",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "排序")
    private Integer sortNumber;

    @ApiModelProperty(value = "是否为叶子节点")
    private Boolean isLeaf;

}