package cn.getech.ehm.knowledge.dto.course;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 课程分类信息表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseCategory查询", description = "课程分类信息表查询参数")
public class CourseCategoryQueryParam extends PageParam {

    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "父分类ID。")
    private String parentId;

    @ApiModelProperty(value = "是否是叶子节点。0-否，1-是")
    private Boolean isLeaf;

}
