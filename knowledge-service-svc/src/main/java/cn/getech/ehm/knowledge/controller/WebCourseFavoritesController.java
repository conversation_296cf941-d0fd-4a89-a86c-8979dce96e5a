package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesDto;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesQueryParam;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.knowledge.service.ICourseFavoritesService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 课程收藏模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@RestController
@RequestMapping("/courseFavorites")
@Api(tags = "web:课程收藏模块服务接口")
public class WebCourseFavoritesController {

    @Autowired
    private ICourseFavoritesService courseFavoritesService;

    /**
     * 分页获取课程收藏模块列表
     */
    @ApiOperation("分页获取课程收藏模块列表")
    @GetMapping("/list")
    //@Permission("course:favorites:list")
    public RestResponse<PageResult<CourseFavoritesDto>> pageList(@Valid CourseFavoritesQueryParam courseFavoritesQueryParam){
        return RestResponse.ok(courseFavoritesService.pageDto(courseFavoritesQueryParam));
    }

    /**
     * 根据id获取课程收藏模块
     */
    @ApiOperation(value = "根据id获取课程收藏模块")
    @GetMapping(value = "/{id}")
    //@Permission("course:favorites:list")
    public RestResponse<CourseFavoritesDto> get(@PathVariable  String id) {
        return RestResponse.ok(courseFavoritesService.getDtoById(id));
    }


}
