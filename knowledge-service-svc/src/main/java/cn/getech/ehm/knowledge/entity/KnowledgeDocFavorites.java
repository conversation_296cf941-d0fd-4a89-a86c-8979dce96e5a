package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 知识文档用户收藏关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_knowledge_doc_favorites")
public class KnowledgeDocFavorites extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 用户ID
     */
    @TableField("uid")
    private String uid;

    /**
     * 知识文档ID
     */
    @TableField("knowledge_doc_id")
    private String knowledgeDocId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 0：未删除  1: 已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;


}
