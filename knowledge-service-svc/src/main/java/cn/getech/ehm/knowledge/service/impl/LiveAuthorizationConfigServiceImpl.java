package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.LiveType;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigDto;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigEditParam;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigParamMapper;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigQueryParam;
import cn.getech.ehm.knowledge.entity.LiveAuthorizationConfig;
import cn.getech.ehm.knowledge.mapper.LiveAuthorizationConfigMapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import cn.getech.ehm.knowledge.service.ILiveAuthorizationConfigService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.Arrays;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
@Slf4j
@Service
public class LiveAuthorizationConfigServiceImpl extends BaseServiceImpl<LiveAuthorizationConfigMapper, LiveAuthorizationConfig> implements ILiveAuthorizationConfigService {

    @Autowired
    private LiveAuthorizationConfigParamMapper liveAuthorizationConfigParamMapper;

    @Value("${ketianyun.live.channel-id}")
    private long channelId;

    @Override
    public LiveAuthorizationConfigDto getChannelConfig() {
        LambdaQueryWrapper<LiveAuthorizationConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LiveAuthorizationConfig::getChannelId, channelId).last("LIMIT 1");
        LiveAuthorizationConfig liveAuthorizationConfig = (LiveAuthorizationConfig) getOne(lambdaQueryWrapper);
        if(liveAuthorizationConfig == null){
            liveAuthorizationConfig = new LiveAuthorizationConfig();
            liveAuthorizationConfig.setId(new DefaultIdentifierGenerator().nextUUID(liveAuthorizationConfig));
            liveAuthorizationConfig.setChannelId(channelId);
            liveAuthorizationConfig.setType(LiveType.PUBLIC);
            this.save(liveAuthorizationConfig);
        }
        return liveAuthorizationConfigParamMapper.entity2Dto(liveAuthorizationConfig);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(LiveAuthorizationConfigEditParam liveAuthorizationConfigEditParam) {
        LiveAuthorizationConfig liveAuthorizationConfig = liveAuthorizationConfigParamMapper.editParam2Entity(liveAuthorizationConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,liveAuthorizationConfig);
        return updateById(liveAuthorizationConfig);
    }

    @Override
    public boolean checkAuthorization(String uid) {
        LambdaQueryWrapper<LiveAuthorizationConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LiveAuthorizationConfig::getChannelId, channelId).last("LIMIT 1");
        LiveAuthorizationConfig liveAuthorizationConfig = (LiveAuthorizationConfig) getOne(lambdaQueryWrapper);
        if(liveAuthorizationConfig == null){
            return true;
        }else{
            if(liveAuthorizationConfig.getType().equals(LiveType.PUBLIC)){
                return true;
            }
            String[] uids = liveAuthorizationConfig.getUids();
            return Arrays.asList(uids).contains(uid);
        }
    }

    private Wrapper<LiveAuthorizationConfig> getPageSearchWrapper(LiveAuthorizationConfigQueryParam liveAuthorizationConfigQueryParam) {
        LambdaQueryWrapper<LiveAuthorizationConfig> wrapper = Wrappers.<LiveAuthorizationConfig>lambdaQuery();
        wrapper.like(null != liveAuthorizationConfigQueryParam.getChannelId(),
                    LiveAuthorizationConfig::getChannelId, liveAuthorizationConfigQueryParam.getChannelId());
        if(BaseEntity.class.isAssignableFrom(LiveAuthorizationConfig.class)){
            wrapper.orderByDesc(LiveAuthorizationConfig::getUpdateTime,LiveAuthorizationConfig::getCreateTime);
        }
        return wrapper;
    }
}
