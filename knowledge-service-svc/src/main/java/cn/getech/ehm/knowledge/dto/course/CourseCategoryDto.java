package cn.getech.ehm.knowledge.dto.course;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;


/**
 * <pre>
 * 课程分类信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "CourseCategoryDto", description = "课程分类信息表返回数据模型")
public class CourseCategoryDto{

    @ApiModelProperty(value = "全局ID。")
    @Excel(name="全局ID。",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "标签名。")
    @Excel(name="标签名",cellType = Excel.ColumnType.STRING)
    private String name;

    @ApiModelProperty(value = "父分类ID。")
    @Excel(name="父分类ID",cellType = Excel.ColumnType.STRING)
    private String parentId;

    @ApiModelProperty(value = "是否是叶子节点。0-否，1-是")
    @Excel(name="是否是叶子节点",cellType = Excel.ColumnType.STRING)
    private Boolean isLeaf;

    @ApiModelProperty(value = "备注信息。")
    @Excel(name="备注信息。",cellType = Excel.ColumnType.STRING )
    private String remark;

}