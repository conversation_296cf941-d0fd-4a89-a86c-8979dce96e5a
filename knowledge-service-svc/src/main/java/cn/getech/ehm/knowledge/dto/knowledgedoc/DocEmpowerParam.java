package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识文档授权参数
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DocEmpowerParam", description = "知识文档授权参数")
public class DocEmpowerParam extends ApiParam {

    @ApiModelProperty(value = "知识文档id集合")
    private List<String> knowledgeDocIds;

    @ApiModelProperty(value = "是否公开 0-隐私，1-公开", required = true)
    @NotNull(message = "权限必须设置")
    private Integer isPublic;

    @ApiModelProperty(value = "角色id集合")
    private List<String> roleIds;

}
