package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.knowledge.KnowledgeMenuChildrenQueryParam;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeQueryParam;

import java.util.List;

/**
 * <pre>
 * app知识库 服务接口
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
public interface IAppKnowledgeService {

    /**
     * 获取知识库模块信息列表
     * @return
     */
    List<KnowledgeCategoryDto> knowledgeList();

    /**
     * 下拉列表-树形结构
     * @param knowledgeMenuChildrenQueryParam
     * @return
     */
    List<CategoryMenuDto> getMenuChildrenList(KnowledgeMenuChildrenQueryParam knowledgeMenuChildrenQueryParam);

    /**
     * 查找 知识文档，新闻资讯，课程信息
     * @param queryParam
     * @return
     */
    KnowledgeDto listKnowledge(KnowledgeQueryParam queryParam);
}
