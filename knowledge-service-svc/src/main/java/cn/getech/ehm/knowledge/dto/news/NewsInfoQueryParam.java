package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 新闻资讯信息表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsInfo查询", description = "新闻资讯信息表查询参数")
public class NewsInfoQueryParam extends PageParam {

    @ApiModelProperty(value = " 分类ID ")
    private String categoryId;

}
