package cn.getech.ehm.knowledge.dto.coursecomment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import cn.getech.ehm.knowledge.dto.common.SecStaffDto;

import java.util.Date;


/**
 * <pre>
 * 课程评论信息 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-10
 */
@Data
@ApiModel(value = "CourseCommentDto", description = "课程评论信息返回数据模型")
public class CourseCommentDto{

    @ApiModelProperty(value = "全局ID。[globalId]")
    @Excel(name="全局ID。[globalId]",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "评论内容。")
    @Excel(name="评论内容",cellType = Excel.ColumnType.STRING)
    private String content;

    @ApiModelProperty(value = "课程id。")
    @Excel(name="课程id",cellType = Excel.ColumnType.STRING)
    private String courseId;

    @ApiModelProperty(value = "创建时间。")
    @Excel(name="创建时间",cellType = Excel.ColumnType.STRING)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "用户id。")
    @Excel(name="用户id",cellType = Excel.ColumnType.STRING)
    private String uid;

    @ApiModelProperty(value = "用户信息。")
    @Excel(name="用户信息",cellType = Excel.ColumnType.STRING)
    private SecStaffDto userInfo;

    @ApiModelProperty(value = "备注信息。")
    @Excel(name="备注信息。",cellType = Excel.ColumnType.STRING )
    private String remark;

}