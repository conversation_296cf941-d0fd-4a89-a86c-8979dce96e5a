package cn.getech.ehm.knowledge.dto.courseaccessible;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 非公开课程可访问表 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseAccessible编辑", description = "非公开课程可访问表编辑参数")
public class CourseAccessibleEditParam extends ApiParam {

    @ApiModelProperty(value = "全局ID")
    private String id;

    @ApiModelProperty(value = "描述")
    private String remark;

}
