package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocEditParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.DocEmpowerParam;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocDto;
import cn.getech.ehm.knowledge.dto.knowledgedoc.KnowledgeDocQueryParam;
import cn.getech.ehm.knowledge.service.IKnowledgeDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 知识文档模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/knowledgeDoc")
@Api(tags = "web:知识文档模块服务接口")
public class WebKnowledgeDocController {

    @Autowired
    private IKnowledgeDocService knowledgeDocService;

    /**
     * 分页获取知识文档模块列表
     */
    @ApiOperation("分页获取知识文档模块列表")
    @GetMapping("/list")
    //@Permission("study:knowledge:doc:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<PageResult<KnowledgeDocDto>> pageList(@Valid KnowledgeDocQueryParam knowledgeDocQueryParam){
        return RestResponse.ok(knowledgeDocService.pageDto(knowledgeDocQueryParam));
    }

    /**
     * 分页获取知识文档模块客户列表
     */
    @ApiOperation("分页获取知识文档模块客户列表")
    @GetMapping("/customList")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<PageResult<KnowledgeDocDto>> pageCustomList(@Valid KnowledgeDocQueryParam knowledgeDocQueryParam){
        return RestResponse.ok(knowledgeDocService.pageCustomDto(knowledgeDocQueryParam));
    }

    /**
     * 新增知识文档模块
     */
    @ApiOperation("新增知识文档")
    @AuditLog(title = "知识文档",desc = "新增知识文档",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("study:knowledge:doc:update")
    public RestResponse<Boolean> add(@RequestBody @Valid KnowledgeDocAddParam knowledgeDocAddParam) {
        return RestResponse.ok(knowledgeDocService.saveByParam(knowledgeDocAddParam));
    }

    /**
     * 修改知识文档模块
     */
    @ApiOperation(value="修改知识文档")
    @AuditLog(title = "知识文档",desc = "修改知识文档",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("study:knowledge:doc:update")
    public RestResponse<Boolean> update(@RequestBody @Valid KnowledgeDocEditParam knowledgeDocEditParam) {
        return RestResponse.ok(knowledgeDocService.updateByParam(knowledgeDocEditParam));
    }

    /**
     * 根据id删除知识文档模块
     */
    @ApiOperation(value="根据id删除知识文档")
    @AuditLog(title = "知识文档",desc = "知识文档",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("study:knowledge:doc:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(knowledgeDocService.removeByIds(ids));
    }

    /**
     * 根据id获取知识文档模块
     */
    @ApiOperation(value = "根据id获取知识文档")
    @GetMapping(value = "/{id}")
    //@Permission("study:knowledge:doc:list")
    public RestResponse<KnowledgeDocDto> get(@PathVariable String id) {
        return RestResponse.ok(knowledgeDocService.getDtoById(id));
    }

    /**
     * 批量授权
     */
    @ApiOperation(value="批量授权")
    @AuditLog(title = "知识文档信息",desc = "批量授权",businessType = BusinessType.UPDATE)
    @PostMapping("batchEmpower")
    //@Permission("course:info:update")
    public RestResponse<Boolean> batchEmpower(@RequestBody @Valid DocEmpowerParam param) {
        return RestResponse.ok(knowledgeDocService.batchEmpower(param));
    }

}
