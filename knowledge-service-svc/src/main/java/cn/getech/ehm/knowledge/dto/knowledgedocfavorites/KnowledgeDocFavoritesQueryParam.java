package cn.getech.ehm.knowledge.dto.knowledgedocfavorites;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 知识文档用户收藏关系表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "KnowledgeDocFavorites查询", description = "知识文档用户收藏关系表查询参数")
public class KnowledgeDocFavoritesQueryParam extends PageParam {

}
