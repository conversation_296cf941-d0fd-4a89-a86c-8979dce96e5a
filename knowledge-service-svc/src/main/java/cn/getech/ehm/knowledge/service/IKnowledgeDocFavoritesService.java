package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesDto;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesQueryParam;
import cn.getech.ehm.knowledge.entity.KnowledgeDocFavorites;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 知识文档用户收藏关系表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
public interface IKnowledgeDocFavoritesService extends IBaseService<KnowledgeDocFavorites> {

        /**
         * 分页查询，返回Dto
         *
         * @param knowledgeDocFavoritesQueryParam
         * @return
         */
        PageResult<KnowledgeDocFavoritesDto> pageDto(KnowledgeDocFavoritesQueryParam knowledgeDocFavoritesQueryParam);


        /**
         * 筛选 出 当前用户收藏过的文档ID
         * @param docIds
         * @return
         */
        List<String> getCurrUserFavoritesKnowledge(List<String> docIds);

        List<String> getCurrUserFavoritesKnowledge();

        /**
         * 保存
         * @param knowledgeDocFavoritesAddParam
         * @return
         */
        boolean saveByParam(KnowledgeDocFavoritesAddParam knowledgeDocFavoritesAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        KnowledgeDocFavoritesDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<KnowledgeDocFavoritesDto> rows);

        /**
         * 更新
         * @param knowledgeDocFavoritesEditParam
         */
        boolean updateByParam(KnowledgeDocFavoritesEditParam knowledgeDocFavoritesEditParam);

        /**
         * 更新用户收藏数
         */
        void updateFavorites();

        /**
         * 批量删除
         * @param ids
         * @return
         */
        boolean removeByIds(String[] ids);
}