package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.annotation.Excel;
import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <pre>
 * 新闻资讯信息表 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsInfo编辑", description = "新闻资讯信息表编辑参数")
public class NewsInfoEditParam extends ApiParam {

    @ApiModelProperty(value = " 全局id ")
    @NotBlank
    private String id;

    @ApiModelProperty(value = " 图文标题 ")
    private String title;

    @ApiModelProperty(value = " 分类ID ")
    private String categoryId;

    @ApiModelProperty(value = " 图文内容 ")
    private String content;

}
