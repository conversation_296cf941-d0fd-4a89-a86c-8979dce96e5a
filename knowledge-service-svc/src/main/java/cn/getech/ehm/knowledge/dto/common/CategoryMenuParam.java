package cn.getech.ehm.knowledge.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * <pre>
 * 课程分类信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "CourseCategoryMenuParam", description = "课程分类菜单批量修改参数数据模型")
public class CategoryMenuParam {

    @ApiModelProperty(value = "全局ID。")
    private String id;

    @ApiModelProperty(value = "子级分类列表。")
    private List<CategoryMenuParam> children = new ArrayList();

}