package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.knowledge.entity.NewsCategory;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻分类信息表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  NewsCategoryParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param newsCategoryAddParam
     * @return
     */
    NewsCategory addParam2Entity(NewsCategoryAddParam newsCategoryAddParam);

    /**
     * 编辑参数转换为实体
     * @param newsCategoryEditParam
     * @return
     */
    NewsCategory editParam2Entity(NewsCategoryEditParam newsCategoryEditParam);

    /**
     * 实体转换为Dto
     * @param newsCategory
     * @return
     */
    NewsCategoryDto entity2Dto(NewsCategory newsCategory);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<NewsCategoryDto> pageEntity2Dto(PageResult<NewsCategory> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<NewsCategory> dtoList2Entity(List<NewsCategoryDto> rows);

}
