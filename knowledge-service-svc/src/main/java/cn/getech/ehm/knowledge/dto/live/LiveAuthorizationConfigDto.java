package cn.getech.ehm.knowledge.dto.live;

import cn.getech.ehm.common.enums.LiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-30
 */
@Data
@ApiModel(value = "LiveAuthorizationConfigDto", description = "返回数据模型")
public class LiveAuthorizationConfigDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "频道ID")
    @Excel(name="频道ID",cellType = Excel.ColumnType.STRING )
    private Long channelId;

    @ApiModelProperty(value = "直播类型")
    private LiveType type;

    @ApiModelProperty(value = "授权用户uids")
    @Excel(name="授权用户uids",cellType = Excel.ColumnType.STRING )
    private String[] uids;

}