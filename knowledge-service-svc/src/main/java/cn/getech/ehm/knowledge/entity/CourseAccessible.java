package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 非公开课程可访问表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_course_accessible")
public class CourseAccessible extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 课程ID
     */
    @TableField("course_id")
    private String courseId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

}
