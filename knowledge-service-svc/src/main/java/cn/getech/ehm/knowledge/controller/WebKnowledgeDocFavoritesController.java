package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesDto;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesQueryParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;

import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.IKnowledgeDocFavoritesService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 知识文档收藏模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@RestController
@RequestMapping("/knowledgeDocFavorites")
@Api(tags = "web: 知识文档收藏模块服务接口")
public class WebKnowledgeDocFavoritesController {

    @Autowired
    private IKnowledgeDocFavoritesService knowledgeDocFavoritesService;

    /**
     * 分页获取知识文档收藏模块列表
     */
    @ApiOperation("分页获取知识文档收藏模块列表")
    @GetMapping("/list")
    //@Permission("knowledge:doc:favorites:list")
    public RestResponse<PageResult<KnowledgeDocFavoritesDto>> pageList(@Valid KnowledgeDocFavoritesQueryParam knowledgeDocFavoritesQueryParam){
        return RestResponse.ok(knowledgeDocFavoritesService.pageDto(knowledgeDocFavoritesQueryParam));
    }

    /**
     * 新增知识文档收藏模块
     */
    @ApiOperation("新增知识文档收藏模块")
    @AuditLog(title = "知识文档收藏模块",desc = "新增知识文档收藏模块",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("knowledge:doc:favorites:update")
    public RestResponse<Boolean> add(@RequestBody @Valid KnowledgeDocFavoritesAddParam knowledgeDocFavoritesAddParam) {
        return RestResponse.ok(knowledgeDocFavoritesService.saveByParam(knowledgeDocFavoritesAddParam));
    }

    /**
     * 修改知识文档收藏模块
     */
    @ApiOperation(value="修改知识文档收藏模块")
    @AuditLog(title = "知识文档收藏模块",desc = "修改知识文档收藏模块",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("knowledge:doc:favorites:update")
    public RestResponse<Boolean> update(@RequestBody @Valid KnowledgeDocFavoritesEditParam knowledgeDocFavoritesEditParam) {
        return RestResponse.ok(knowledgeDocFavoritesService.updateByParam(knowledgeDocFavoritesEditParam));
    }

    /**
     * 根据id删除知识文档收藏模块
     */
    @ApiOperation(value="根据id删除知识文档收藏模块")
    @AuditLog(title = "知识文档收藏模块",desc = "知识文档收藏模块",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("knowledge:doc:favorites:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(knowledgeDocFavoritesService.removeByIds(ids));
    }

    /**
     * 根据id获取知识文档收藏模块
     */
    @ApiOperation(value = "根据id获取知识文档收藏模块")
    @GetMapping(value = "/{id}")
    //@Permission("knowledge:doc:favorites:list")
    public RestResponse<KnowledgeDocFavoritesDto> get(@PathVariable String id) {
        return RestResponse.ok(knowledgeDocFavoritesService.getDtoById(id));
    }
}
