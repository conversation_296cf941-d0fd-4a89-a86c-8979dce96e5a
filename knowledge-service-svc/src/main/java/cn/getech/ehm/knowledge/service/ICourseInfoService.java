package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.dto.course.*;
import cn.getech.ehm.knowledge.entity.CourseInfo;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.ehm.system.dto.KeTianVideoQueryParam;

import java.util.List;

/**
 * <pre>
 * 课程信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
public interface ICourseInfoService extends IBaseService<CourseInfo> {

     /**
      * 分页查询，返回Dto
      *
      * @param courseInfoQueryParam
      * @return
      */
     PageResult<CourseInfoDto> pageDto(CourseInfoQueryParam courseInfoQueryParam);

    List<CourseInfoDto> webFirstpage(CourseInfoQueryParam courseInfoQueryParam);

    /**
     * 分页查询客户端列表
     * @param courseInfoQueryParam
     * @return
     */
    PageResult<CourseInfoDto> pageCustomDto(CourseInfoQueryParam courseInfoQueryParam);

    /**
      * app课程分类列表接口，包含用户点赞信息
      * @param courseInfoQueryParam
      * @return
      */
     PageResult<CourseInfoDto> appPageList(CourseInfoQueryParam courseInfoQueryParam);

    /**
     * 分页查找所有收藏的课程
     * @param queryParam
     * @return
     */
    PageResult<CourseInfoDto> appAllFavoritesPageList(CourseInfoQueryParam queryParam);

    /**
     * 保存
     * @param courseInfoAddParam
     * @return
     */
    boolean saveByParam(CourseInfoAddParam courseInfoAddParam);

    /**
     * 收藏接口
     * @param courseId
     * @return
     */
    boolean favorites(String courseId);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    CourseInfoDto getDtoById(String id);

    /**
     * 根据id查询课程信息，包括用户点赞和收藏
     * @param id
     * @return
     */
    CourseInfoDto getUserCourseDtoById(String id);

    /**
     * 根据id删除记录
     * @param id
     * @return
     */
    boolean removeById(String id);

    /**
     * 批量保存
     * @param rows
     */
    boolean saveDtoBatch(List<CourseInfoDto> rows);

    /**
     * 更新
     * @param courseInfoEditParam
     */
    boolean updateByParam(CourseInfoEditParam courseInfoEditParam);

    /**
     * 点赞
     * @param courseId
     * @return
     */
    boolean likeCount(String courseId);

    /**
     * 增加学习数
     * @param courseId
     * @return
     */
    boolean addLearningCount(String courseId);



    /**
     * 增加评论数
     * @param id
     * @param count
     * @return
     */
    boolean addCommentCount(String id, Integer count);

    /**
     * 获取课程子类数和记录数
     * @return
     */
    KnowledgeCategoryDto getKnowledgeCategoryDto();

    /**
     * 根据参数获取Sign
     * @param keTianVideoQueryParam
     * @return
     */
    RestResponse<String> getKeTianYunSign(KeTianVideoQueryParam keTianVideoQueryParam);

    /**
     * 获取课程数量
     * @return
     */
    Integer getCourseCount();

    /**
     * 获取播放数
     * @return
     */
    Integer getPlayCount();

    /**
     * 批量授权
     * @param param
     * @return
     */
    Boolean batchEmpower(CourseEmpowerParam param);
}