package cn.getech.ehm.knowledge.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import cn.getech.ehm.knowledge.entity.NewsInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <p>
 * 新闻资讯信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Repository
public interface NewsInfoMapper extends BaseMapper<NewsInfo> {

    Page<NewsInfoDto> fetchUserFavoritesList(Page page, @Param("uid") String uid);

}
