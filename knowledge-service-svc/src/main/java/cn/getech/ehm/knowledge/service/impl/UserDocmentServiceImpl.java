package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.entity.UserDocment;
import cn.getech.ehm.knowledge.mapper.UserDocmentMapper;
import cn.getech.ehm.knowledge.service.IUserDocmentService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentQueryParam;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentAddParam;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentParamMapper;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <pre>
 * 用户文档表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@Slf4j
@Service
public class UserDocmentServiceImpl extends BaseServiceImpl<UserDocmentMapper, UserDocment> implements IUserDocmentService {

    @Autowired
    private UserDocmentParamMapper userDocmentParamMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private BaseServiceClient baseServiceClient;

    @Override
    public PageResult<UserDocmentDto> pageDto(UserDocmentQueryParam userDocmentQueryParam) {
        Wrapper<UserDocment> wrapper = getPageSearchWrapper(userDocmentQueryParam);
        PageResult<UserDocmentDto> result = userDocmentParamMapper.pageEntity2Dto(page(userDocmentQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public PageResult<UserDocmentDto> appPageDto(UserDocmentQueryParam userDocmentQueryParam) {
        Wrapper<UserDocment> wrapper = getAppPageSearchWrapper(userDocmentQueryParam);
        PageResult<UserDocmentDto> result = userDocmentParamMapper.pageEntity2Dto(page(userDocmentQueryParam, wrapper));


        String[] attachIds = result.getRecords().stream().map(UserDocmentDto::getAttachId).toArray(String[]::new);
        if (attachIds != null && attachIds.length!=0 ){
            // 前端无法获取 后端获取
            RestResponse<Map<String, AttachmentClientDto>> attachmentMapResult = baseServiceClient.getAttachmentMap(attachIds);

            if (!attachmentMapResult.isOk()){
                log.error("附件URL查询失败");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }

            Map<String, AttachmentClientDto> attachmentMapResultData = attachmentMapResult.getData();

            result.getRecords().forEach(dto->{
                AttachmentClientDto attachmentClientDto = attachmentMapResultData.get(dto.getAttachId());
                dto.setAttachUrl(attachmentClientDto.getUrl());
            });
        }

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByParam(UserDocmentAddParam userDocmentAddParam) {
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,userDocmentAddParam);

        LambdaQueryWrapper<UserDocment> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(userDocmentAddParam.getUid()),UserDocment::getUid,userDocmentAddParam.getUid());

        remove(queryWrapper);
        //  当 为空的时候全部删除
        if (StringUtils.isBlank(userDocmentAddParam.getAttachIds())){
            return true;
        }
        String[] names = userDocmentAddParam.getNames().split(",");
        String[] attachIds = userDocmentAddParam.getAttachIds().split(",");
        List<UserDocment> userDocments = new ArrayList<>();
        int i = 0;
        for (String attachId : attachIds){
            UserDocment userDocment = new UserDocment();
            userDocment.setUid(userDocmentAddParam.getUid());
            userDocment.setAttachId(attachId);
            userDocment.setName(names[i]);
            userDocments.add(userDocment);
            i++;
        }
        return saveBatch(userDocments);
    }

    @Override
    public UserDocmentDto getDtoById(String id) {
        return userDocmentParamMapper.entity2Dto((UserDocment) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UserDocmentDto> rows) {
        return saveBatch(userDocmentParamMapper.dtoList2Entity(rows));
    }

    @Override
    public boolean removeByIds(String[] ids){
        return removeByIds(Stream.of(ids).collect(Collectors.toList()));
    }

    @Override
    public String getAttachsByUid(String uid) {
        LambdaQueryWrapper<UserDocment> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(uid),UserDocment::getUid,uid);
        List<UserDocment> userDocments = list(queryWrapper);

        if (userDocments == null || userDocments.size() == 0){
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer();
        userDocments.forEach(userDocment -> {
            stringBuffer.append(userDocment.getAttachId()+",");
        });
        int length = stringBuffer.toString().length();
        return stringBuffer.toString().substring(0,length-1);
    }

    private Wrapper<UserDocment> getPageSearchWrapper(UserDocmentQueryParam userDocmentQueryParam) {
        LambdaQueryWrapper<UserDocment> wrapper = Wrappers.<UserDocment>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(UserDocment.class)){
            wrapper.orderByDesc(UserDocment::getUpdateTime,UserDocment::getCreateTime);
        }
        return wrapper;
    }

    private Wrapper<UserDocment> getAppPageSearchWrapper(UserDocmentQueryParam userDocmentQueryParam) {
        LambdaQueryWrapper<UserDocment> wrapper = Wrappers.<UserDocment>lambdaQuery();

        wrapper.eq(UserDocment::getUid, PorosContextHolder.getCurrentUser().getUid());
        if(BaseEntity.class.isAssignableFrom(UserDocment.class)){
            wrapper.orderByDesc(UserDocment::getUpdateTime,UserDocment::getCreateTime);
        }
        return wrapper;
    }
}
