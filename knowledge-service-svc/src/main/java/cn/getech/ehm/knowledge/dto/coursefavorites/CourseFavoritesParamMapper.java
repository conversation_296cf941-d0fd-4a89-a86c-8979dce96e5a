package cn.getech.ehm.knowledge.dto.coursefavorites;

import cn.getech.ehm.knowledge.entity.CourseFavorites;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程用户收藏关系表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-16
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CourseFavoritesParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param courseFavoritesAddParam
     * @return
     */
    CourseFavorites addParam2Entity(CourseFavoritesAddParam courseFavoritesAddParam);

    /**
     * 编辑参数转换为实体
     * @param courseFavoritesEditParam
     * @return
     */
    CourseFavorites editParam2Entity(CourseFavoritesEditParam courseFavoritesEditParam);

    /**
     * 实体转换为Dto
     * @param courseFavorites
     * @return
     */
    CourseFavoritesDto entity2Dto(CourseFavorites courseFavorites);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CourseFavoritesDto> pageEntity2Dto(PageResult<CourseFavorites> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CourseFavorites> dtoList2Entity(List<CourseFavoritesDto> rows);

}
