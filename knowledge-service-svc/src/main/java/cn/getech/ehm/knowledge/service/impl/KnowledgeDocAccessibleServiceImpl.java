package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.knowledge.entity.KnowledgeDocAccessible;
import cn.getech.ehm.knowledge.mapper.KnowledgeDocAccessibleMapper;
import cn.getech.ehm.knowledge.service.IKnowledgeDocAccessibleService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecRoleRefDTO;
import cn.getech.poros.permission.dto.SecStaffDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 知识文档权限
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
@Slf4j
@Service
public class KnowledgeDocAccessibleServiceImpl extends BaseServiceImpl<KnowledgeDocAccessibleMapper, KnowledgeDocAccessible> implements IKnowledgeDocAccessibleService {

    @Autowired
    private KnowledgeDocAccessibleMapper docAccessibleMapper;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;


    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<String> knowledgeDocIds, List<String> roleIds) {
        List<KnowledgeDocAccessible> docAccessibles = new ArrayList<>();
        for(String knowledgeDocId : knowledgeDocIds){
            for(String roleId : roleIds){
                KnowledgeDocAccessible entity = new KnowledgeDocAccessible();
                entity.setKnowledgeDocId(knowledgeDocId);
                entity.setRoleId(roleId);
                docAccessibles.add(entity);
            }
        }
        return saveBatch(docAccessibles);
    }

    @Override
    public List<String> listCurrentDocIds(){
        RestResponse<SecStaffDto> restResponse = porosSecStaffClient.getCurUser();
        if(restResponse.isOk()) {
            SecStaffDto secStaffDto = restResponse.getData();
            List<PorosSecRoleRefDTO> roleDtoList = secStaffDto.getRoleRefDtoList();
            if (CollectionUtils.isNotEmpty(roleDtoList)) {
                List<String> roleIds = roleDtoList.stream().map(PorosSecRoleRefDTO::getId).distinct().collect(Collectors.toList());
                LambdaQueryWrapper<KnowledgeDocAccessible> wrapper = Wrappers.<KnowledgeDocAccessible>lambdaQuery();
                wrapper.in(KnowledgeDocAccessible::getRoleId,roleIds);
                wrapper.select(KnowledgeDocAccessible::getKnowledgeDocId);
                return docAccessibleMapper.selectList(wrapper).stream()
                        .map(KnowledgeDocAccessible::getKnowledgeDocId).distinct().collect(Collectors.toList());
            }
        }else{
            log.error("获取用户信息失败");
        }
        return null;
    }

    @Override
    public Boolean deleteByDocIds(List<String> knowledgeDocIds){
        LambdaQueryWrapper<KnowledgeDocAccessible> wrapper = Wrappers.lambdaQuery();
        wrapper.in(KnowledgeDocAccessible::getKnowledgeDocId, knowledgeDocIds);
        return this.remove(wrapper);
    }

    @Override
    public Map<String, List<String>> getDocAccessMap(List<String> knowledgeDocIds){
        LambdaQueryWrapper<KnowledgeDocAccessible> wrapper = Wrappers.lambdaQuery();
        wrapper.in(KnowledgeDocAccessible::getKnowledgeDocId, knowledgeDocIds);
        wrapper.select(KnowledgeDocAccessible::getKnowledgeDocId, KnowledgeDocAccessible::getRoleId);
        return docAccessibleMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(
                KnowledgeDocAccessible::getKnowledgeDocId, Collectors.mapping(KnowledgeDocAccessible::getRoleId, Collectors.toList())));
    }
}
