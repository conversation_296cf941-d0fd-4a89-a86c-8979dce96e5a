package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.entity.KnowledgeDocFavorites;
import cn.getech.ehm.knowledge.mapper.KnowledgeDocFavoritesMapper;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.service.IKnowledgeDocFavoritesService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesQueryParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesParamMapper;
import cn.getech.ehm.knowledge.dto.knowledgedocfavorites.KnowledgeDocFavoritesDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.UserAnalysisClient;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <pre>
 * 知识文档用户收藏关系表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@Slf4j
@Service
public class KnowledgeDocFavoritesServiceImpl extends BaseServiceImpl<KnowledgeDocFavoritesMapper, KnowledgeDocFavorites> implements IKnowledgeDocFavoritesService {

    @Autowired
    private KnowledgeDocFavoritesParamMapper knowledgeDocFavoritesParamMapper;

    @Autowired
    private UserAnalysisClient userAnalysisClient;

    @Override
    public PageResult<KnowledgeDocFavoritesDto> pageDto(KnowledgeDocFavoritesQueryParam knowledgeDocFavoritesQueryParam) {
        Wrapper<KnowledgeDocFavorites> wrapper = getPageSearchWrapper(knowledgeDocFavoritesQueryParam);
        PageResult<KnowledgeDocFavoritesDto> result = knowledgeDocFavoritesParamMapper.pageEntity2Dto(page(knowledgeDocFavoritesQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public List<String> getCurrUserFavoritesKnowledge(List<String> docIds) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<KnowledgeDocFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeDocFavorites::getUid, currentUser.getUid());
        queryWrapper.in(KnowledgeDocFavorites::getKnowledgeDocId, docIds);
        List<KnowledgeDocFavorites> docFavorites = list(queryWrapper);
        return docFavorites.stream().map(coursePraise -> coursePraise.getKnowledgeDocId()).collect(Collectors.toList());
    }

    @Override
    public List<String> getCurrUserFavoritesKnowledge() {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<KnowledgeDocFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeDocFavorites::getUid, currentUser.getUid());
        List<KnowledgeDocFavorites> docFavorites = list(queryWrapper);
        return docFavorites.stream().map(coursePraise -> coursePraise.getKnowledgeDocId()).collect(Collectors.toList());
    }


    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(KnowledgeDocFavoritesAddParam knowledgeDocFavoritesAddParam) {
        KnowledgeDocFavorites knowledgeDocFavorites = knowledgeDocFavoritesParamMapper.addParam2Entity(knowledgeDocFavoritesAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,knowledgeDocFavorites);
        return save(knowledgeDocFavorites);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(KnowledgeDocFavoritesEditParam knowledgeDocFavoritesEditParam) {
        KnowledgeDocFavorites knowledgeDocFavorites = knowledgeDocFavoritesParamMapper.editParam2Entity(knowledgeDocFavoritesEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,knowledgeDocFavorites);
        return updateById(knowledgeDocFavorites);
    }

    @Override
    public void updateFavorites(){
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<KnowledgeDocFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeDocFavorites::getUid, currentUser.getUid());
        Integer favorites = this.baseMapper.selectCount(queryWrapper);
        UpdateFavoritesParam updateFavoritesParam = new UpdateFavoritesParam();
        updateFavoritesParam.setFavorites(favorites);
        updateFavoritesParam.setKnowledgeType(KnowledgeType.DOC.getCode());
        userAnalysisClient.updateFavorites(updateFavoritesParam);
    }

    @Override
    public boolean removeByIds(String[] ids) {
        return removeByIds(Stream.of(ids).collect(Collectors.toList()));
    }


    @Override
    public KnowledgeDocFavoritesDto getDtoById(String id) {
        return knowledgeDocFavoritesParamMapper.entity2Dto((KnowledgeDocFavorites) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<KnowledgeDocFavoritesDto> rows) {
        return saveBatch(knowledgeDocFavoritesParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<KnowledgeDocFavorites> getPageSearchWrapper(KnowledgeDocFavoritesQueryParam queryParam) {
        LambdaQueryWrapper<KnowledgeDocFavorites> wrapper = Wrappers.lambdaQuery();

        // 查询当前用户
        String uid = PorosContextHolder.getCurrentUser().getUid();
        wrapper.eq(StringUtils.isNotBlank(uid), KnowledgeDocFavorites::getUid, uid);
        if(BaseEntity.class.isAssignableFrom(KnowledgeDocFavorites.class)){
            wrapper.orderByDesc(KnowledgeDocFavorites::getUpdateTime,KnowledgeDocFavorites::getCreateTime);
        }
        return wrapper;
    }
}
