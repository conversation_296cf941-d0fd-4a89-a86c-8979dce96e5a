package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

import java.util.Date;

/**
 * <p>
 * 新闻资讯信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_news_info")
public class NewsInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *  图文标题。
     */
    @TableField("title")
    private String title;

    /**
     *  分类ID。
     */
    @TableField("category_id")
    private String categoryId;

    /**
     *  摘要概述。
     */
    @TableField("summary")
    private String summary;

    /**
     *  图文内容。
     */
    @TableField("content")
    private String content;

    /**
     *  发布时间。
     */
    @TableField("publish_time")
    private Date publishTime;

    /**
     *  排序序号。
     */
    @TableField("sort_number")
    private Integer sortNumber;

    /**
     *  浏览量。
     */
    @TableField("scan_count")
    private Integer scanCount;

    /**
     *  租户ID。
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     *  记录状态。0-正常，1-删除 
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

}
