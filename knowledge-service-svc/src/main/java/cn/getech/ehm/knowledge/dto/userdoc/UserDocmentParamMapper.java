package cn.getech.ehm.knowledge.dto.userdoc;

import cn.getech.ehm.knowledge.entity.UserDocment;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 用户文档表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-12-03
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UserDocmentParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param userDocmentAddParam
     * @return
     */
    UserDocment addParam2Entity(UserDocmentAddParam userDocmentAddParam);

    /**
     * 实体转换为Dto
     * @param userDocment
     * @return
     */
    UserDocmentDto entity2Dto(UserDocment userDocment);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UserDocmentDto> pageEntity2Dto(PageResult<UserDocment> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UserDocment> dtoList2Entity(List<UserDocmentDto> rows);

}
