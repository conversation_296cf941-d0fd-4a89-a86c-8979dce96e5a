package cn.getech.ehm.knowledge.dto.course;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 课程授权参数
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseEmpowerParam", description = "课程授权参数")
public class CourseEmpowerParam extends ApiParam {

    @ApiModelProperty(value = "课程id集合")
    private List<String> courseIds;

    @ApiModelProperty(value = "是否公开 0-隐私，1-公开", required = true)
    @NotNull(message = "权限必须设置")
    private Integer isPublic;

    @ApiModelProperty(value = "角色roleId集合")
    private List<String> roleIds;

}
