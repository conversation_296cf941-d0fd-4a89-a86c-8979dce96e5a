package cn.getech.ehm.knowledge.controller;

import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentAddParam;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentDto;
import cn.getech.ehm.knowledge.dto.userdoc.UserDocmentQueryParam;
import cn.getech.ehm.knowledge.service.IUserDocmentService;
import cn.getech.poros.framework.common.api.PageResult;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户文档表控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@RestController
@RequestMapping("/userDocment")
@Api(tags = "web：用户文档服务接口")
public class WebUserDocmentController {

    @Autowired
    private IUserDocmentService userDocmentService;

    /**
     * 设置该客户的文件AttachIds
     */
    @ApiOperation("设置该客户的文件AttachIds")
    @AuditLog(title = "用户文档表",desc = "设置该客户的文件AttachIds",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("user:docment:update")
    public RestResponse<Boolean> add(@RequestBody @Valid UserDocmentAddParam userDocmentAddParam) {
        return RestResponse.ok(userDocmentService.saveByParam(userDocmentAddParam));
    }


    /**
     * 获取客户的附件Ids
     */
    @ApiOperation("获取客户的附件IDs")
    @AuditLog(title = "用户文档表",desc = "获取客户的附件Ids",businessType = BusinessType.INSERT)
    @GetMapping("/customer/{uid}")
    //@Permission("user:docment:update")
    public RestResponse<String> get(@PathVariable("uid") String uid) {
        return RestResponse.ok(userDocmentService.getAttachsByUid(uid));
    }

    /**
     * 分页获取用户文档表列表
     */
    @ApiOperation("客户中心分页获取用户文档表列表")
    @GetMapping("/list")
    //@Permission("user:docment:list")
    public RestResponse<PageResult<UserDocmentDto>> pageList(@Valid UserDocmentQueryParam userDocmentQueryParam){
        return RestResponse.ok(userDocmentService.appPageDto(userDocmentQueryParam));
    }

}
