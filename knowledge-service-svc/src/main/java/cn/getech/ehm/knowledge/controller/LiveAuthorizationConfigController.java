package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigDto;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigEditParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.knowledge.service.ILiveAuthorizationConfigService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 直播权限配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
@Slf4j
@RestController
@RequestMapping("/liveAuthorizationConfig")
@Api(tags = "直播权限配置服务接口")
public class LiveAuthorizationConfigController {

    @Autowired
    private ILiveAuthorizationConfigService liveAuthorizationConfigService;

    /**
     * 获取频道授权信息
     */
    @ApiOperation("获取频道授权信息")
    @GetMapping("/getChannelConfig")
    //@Permission("live:authorization:config:list")
    public RestResponse<LiveAuthorizationConfigDto> getChannelConfig(){
        return RestResponse.ok(liveAuthorizationConfigService.getChannelConfig());
    }

    /**
     * 修改直播权限配置
     */
    @ApiOperation(value="修改直播权限配置")
    @AuditLog(title = "直播权限配置",desc = "修改直播权限配置",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("live:authorization:config:update")
    public RestResponse<Boolean> update(@RequestBody @Valid LiveAuthorizationConfigEditParam liveAuthorizationConfigEditParam) {
        return RestResponse.ok(liveAuthorizationConfigService.updateByParam(liveAuthorizationConfigEditParam));
    }

    /**
     * 校验用户观看直播权限
     */
    @ApiOperation("校验用户观看直播权限")
    @GetMapping("/checkAuthorization")
    //@Permission("live:authorization:config:list")
    public RestResponse<Boolean> checkAuthorization(@RequestParam String uid, HttpServletRequest request){
        log.info("poros-current-user:" + request.getHeader("poros-current-user"));
        return RestResponse.ok(liveAuthorizationConfigService.checkAuthorization(uid));
    }

}
