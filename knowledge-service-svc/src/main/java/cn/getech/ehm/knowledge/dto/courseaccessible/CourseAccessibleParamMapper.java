package cn.getech.ehm.knowledge.dto.courseaccessible;

import cn.getech.ehm.knowledge.entity.CourseAccessible;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 非公开课程可访问表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-24
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CourseAccessibleParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param courseAccessibleAddParam
     * @return
     */
    CourseAccessible addParam2Entity(CourseAccessibleAddParam courseAccessibleAddParam);

    /**
     * 编辑参数转换为实体
     * @param courseAccessibleEditParam
     * @return
     */
    CourseAccessible editParam2Entity(CourseAccessibleEditParam courseAccessibleEditParam);

    /**
     * 实体转换为Dto
     * @param courseAccessible
     * @return
     */
    CourseAccessibleDto entity2Dto(CourseAccessible courseAccessible);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CourseAccessibleDto> pageEntity2Dto(PageResult<CourseAccessible> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CourseAccessible> dtoList2Entity(List<CourseAccessibleDto> rows);

    /**
     * entity集合转dto集合
     * @param rows
     * @return
     */
    List<CourseAccessibleDto> entityList2dto(List<CourseAccessible> rows);

}
