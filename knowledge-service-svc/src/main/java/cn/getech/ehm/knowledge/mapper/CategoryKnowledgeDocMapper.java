package cn.getech.ehm.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.entity.CategoryKnowledgeDoc;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 知识文档分类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Repository
public interface CategoryKnowledgeDocMapper extends BaseMapper<CategoryKnowledgeDoc> {

    List<CategoryMenuDto> findAllCategoryKnowledgeDoc(Integer deleted);

    List<CategoryMenuDto> getCategoryChildren(Integer deleted,String parentId);

}
