package cn.getech.ehm.knowledge.dto.course;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotEmpty;

/**
 * <pre>
 * 课程信息表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseInfo新增", description = "课程信息表新增参数")
public class CourseInfoAddParam extends ApiParam {

    @NotEmpty(message = "标题不能为空")
    @ApiModelProperty(value = "标题。")
    private String title;

    @NotEmpty(message = "分类id不能为空")
    @ApiModelProperty(value = "分类id。")
    private String categoryId;

    @ApiModelProperty(value = "封面图片id。")
    private String imgId;

    @ApiModelProperty(value = "封面图片url。")
    private String imgUrl;

    @NotEmpty(message = "视频id不能为空")
    @ApiModelProperty(value = "视频id。")
    private String videoId;

    @ApiModelProperty("播放地址Mp4")
    private String playMp4Url;

    @ApiModelProperty("播放地址M3u8")
    private String playM3u8Url;

    @ApiModelProperty(value = "下载地址。")
    private String downloadUrl;

    @ApiModelProperty(value = "视频时长。单位：秒")
    private Integer duration;

    @ApiModelProperty(value = "备注信息。")
    private String remark;

    @ApiModelProperty(value = "视频文件名")
    private String videoFileName;

}