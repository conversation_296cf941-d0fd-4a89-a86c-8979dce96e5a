package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 课程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_course_info")
public class CourseInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *  租户ID。
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 课程标题。
     */
    @TableField("title")
    private String title;

    /**
     * 分类ID。
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 摘要概述。
     */
    @TableField("summary")
    private String summary;

    /**
     * 封面图片id。
     */
    @TableField("img_id")
    private String imgId;

    /**
     * 封面图片url。
     */
    @TableField("img_url")
    private String imgUrl;

    /**
     * 视频附件id。
     */
    @TableField("video_id")
    private String videoId;

    /**
     * 视频文件名。
     */
    @TableField("video_file_name")
    private String videoFileName;

    /**
     * 播放地址。
     */
    @TableField("play_mp4_url")
    private String playMp4Url;

    /**
     * 播放地址。
     */
    @TableField("play_m3u8_url")
    private String playM3u8Url;

    /**
     * 下载地址。
     */
    @TableField("download_url")
    private String downloadUrl;

    /**
     * 课时总时长。单位：秒
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 参加学习人数。
     */
    @TableField("learning_count")
    private Integer learningCount;

    /**
     * 点赞数。
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 评论数。
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 排序序号。
     */
    @TableField("sort_number")
    private Integer sortNumber;

    /**
     * 记录状态。0-正常，1-删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    @TableField("is_public")
    private Boolean isPublic;


}
