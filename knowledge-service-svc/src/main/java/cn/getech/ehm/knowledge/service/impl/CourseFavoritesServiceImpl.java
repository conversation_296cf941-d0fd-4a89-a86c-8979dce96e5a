package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.entity.CourseFavorites;
import cn.getech.ehm.knowledge.mapper.CourseFavoritesMapper;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.service.ICourseFavoritesService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesQueryParam;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesParamMapper;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.UserAnalysisClient;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <pre>
 * 课程用户收藏关系表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@Slf4j
@Service
public class CourseFavoritesServiceImpl extends BaseServiceImpl<CourseFavoritesMapper, CourseFavorites> implements ICourseFavoritesService {

    @Autowired
    private CourseFavoritesParamMapper courseFavoritesParamMapper;

    @Autowired
    private CourseFavoritesMapper courseFavoritesMapper;
    @Autowired
    private UserAnalysisClient userAnalysisClient;

    @Override
    public PageResult<CourseFavoritesDto> pageDto(CourseFavoritesQueryParam courseFavoritesQueryParam) {
        Wrapper<CourseFavorites> wrapper = getPageSearchWrapper(courseFavoritesQueryParam);
        PageResult<CourseFavoritesDto> result = courseFavoritesParamMapper.pageEntity2Dto(page(courseFavoritesQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CourseFavoritesAddParam courseFavoritesAddParam) {
        CourseFavorites courseFavorites = courseFavoritesParamMapper.addParam2Entity(courseFavoritesAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseFavorites);
        return save(courseFavorites);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CourseFavoritesEditParam courseFavoritesEditParam) {
        CourseFavorites courseFavorites = courseFavoritesParamMapper.editParam2Entity(courseFavoritesEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseFavorites);
        return updateById(courseFavorites);
    }

    @Override
    public void updateFavorites(){
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<CourseFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CourseFavorites::getUid, currentUser.getUid());
        Integer favorites = this.baseMapper.selectCount(queryWrapper);
        UpdateFavoritesParam updateFavoritesParam = new UpdateFavoritesParam();
        updateFavoritesParam.setFavorites(favorites);
        updateFavoritesParam.setKnowledgeType(KnowledgeType.COURSE.getCode());
        userAnalysisClient.updateFavorites(updateFavoritesParam);
    }

    @Override
    public CourseFavoritesDto getDtoById(String id) {
        return courseFavoritesParamMapper.entity2Dto((CourseFavorites) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CourseFavoritesDto> rows) {
        return saveBatch(courseFavoritesParamMapper.dtoList2Entity(rows));
    }

    @Override
    public boolean isFavoriteCourse(String courseId) {
        if (StringUtils.isBlank(courseId)){
            return false;
        }
        LambdaQueryWrapper<CourseFavorites> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CourseFavorites::getCourseId, courseId);
        wrapper.eq(CourseFavorites::getUid, PorosContextHolder.getCurrentUser().getUid());
        Integer selectCount = courseFavoritesMapper.selectCount(wrapper);
        return selectCount > 0;
    }

    @Override
    public List<String> getCurrUserFavoritesCourse() {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<CourseFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CourseFavorites::getUid, currentUser.getUid());
        List<CourseFavorites> courseFavorites = list(queryWrapper);
        return courseFavorites.stream().map( favorites -> favorites.getCourseId()).collect(Collectors.toList());
    }

    private Wrapper<CourseFavorites> getPageSearchWrapper(CourseFavoritesQueryParam courseFavoritesQueryParam) {
        LambdaQueryWrapper<CourseFavorites> wrapper = Wrappers.lambdaQuery();

        // 查询当前用户
        String uid = PorosContextHolder.getCurrentUser().getUid();
        wrapper.eq(StringUtils.isNotBlank(uid), CourseFavorites::getUid, uid);
        if(BaseEntity.class.isAssignableFrom(CourseFavorites.class)){
            wrapper.orderByDesc(CourseFavorites::getUpdateTime,CourseFavorites::getCreateTime);
        }
        return wrapper;
    }
}
