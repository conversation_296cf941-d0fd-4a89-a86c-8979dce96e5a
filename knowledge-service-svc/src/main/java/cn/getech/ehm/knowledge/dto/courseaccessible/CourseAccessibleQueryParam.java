package cn.getech.ehm.knowledge.dto.courseaccessible;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 非公开课程可访问表 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseAccessible查询", description = "非公开课程可访问表查询参数")
public class CourseAccessibleQueryParam extends PageParam {

}
