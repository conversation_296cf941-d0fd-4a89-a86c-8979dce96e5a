package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.ehm.knowledge.entity.KnowledgeDoc;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 * 知识学习库 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface KnowledgeDocParamMapper {

    /**
     * 新增参数转换为实体
     *
     * @param studyKnowledgeDocAddParam
     * @return
     */
    KnowledgeDoc addParam2Entity(KnowledgeDocAddParam studyKnowledgeDocAddParam);

    /**
     * 编辑参数转换为实体
     * @param studyKnowledgeDocEditParam
     * @return
     */
    KnowledgeDoc editParam2Entity(KnowledgeDocEditParam studyKnowledgeDocEditParam);

    /**
     * 实体转换为Dto
     * @param studyKnowledgeDoc
     * @return
     */
    KnowledgeDocDto entity2Dto(KnowledgeDoc studyKnowledgeDoc);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<KnowledgeDocDto> pageEntity2Dto(PageResult<KnowledgeDoc> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<KnowledgeDoc> dtoList2Entity(List<KnowledgeDocDto> rows);

}
