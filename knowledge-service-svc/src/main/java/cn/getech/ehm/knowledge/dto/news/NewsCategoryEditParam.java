package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <pre>
 * 新闻分类信息表 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsCategory编辑", description = "新闻分类信息表编辑参数")
public class NewsCategoryEditParam extends ApiParam {

    @NotEmpty(message = "id不能为空")
    @ApiModelProperty(value = "全局ID。")
    private String id;

    @NotEmpty(message = "标签名不能为空")
    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "父分类ID。")
    private String parentId;

    @ApiModelProperty(value = "排序编号。")
    private Integer sortNumber;

    @ApiModelProperty(value = "备注信息。")
    private String remark;

}
