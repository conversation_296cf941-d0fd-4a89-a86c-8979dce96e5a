package cn.getech.ehm.knowledge.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 用户基本信息表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@ApiModel(value = "SecStaffDto", description = "用户基本信息返回数据模型")
public class SecStaffDto {

    @ApiModelProperty("用户标识")
    private String uid;

    @ApiModelProperty("用户名称")
    private String name;

    @ApiModelProperty("员工号")
    private String empNumber;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("头像")
    private String avatar;

}