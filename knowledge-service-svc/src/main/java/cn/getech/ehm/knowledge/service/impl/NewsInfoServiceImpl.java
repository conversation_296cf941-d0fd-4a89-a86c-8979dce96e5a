package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.entity.NewsInfo;
import cn.getech.ehm.knowledge.dto.news.NewsInfoAddParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoEditParam;
import cn.getech.ehm.knowledge.dto.news.NewsInfoParamMapper;
import cn.getech.ehm.knowledge.dto.news.NewsInfoQueryParam;
import cn.getech.ehm.knowledge.mapper.NewsCategoryMapper;
import cn.getech.ehm.knowledge.mapper.NewsInfoMapper;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.param.PageParam;
import cn.getech.poros.framework.common.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.service.INewsCategoryService;
import cn.getech.ehm.knowledge.service.INewsFavoritesService;
import cn.getech.ehm.knowledge.service.INewsInfoService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;


/**
 * <pre>
 * 新闻资讯信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Slf4j
@Service
public class NewsInfoServiceImpl extends BaseServiceImpl<NewsInfoMapper, NewsInfo> implements INewsInfoService {

    @Autowired
    private NewsInfoParamMapper newsInfoParamMapper;

    @Autowired
    private NewsInfoMapper newsInfoMapper;

    @Autowired
    private NewsCategoryMapper newsCategoryMapper;

    @Autowired
    private INewsCategoryService newsCategoryService;

    @Autowired
    private INewsFavoritesService newsFavoritesService;



    @Override
    public PageResult<NewsInfoDto> pageDto(NewsInfoQueryParam newsInfoQueryParam) {
        Wrapper<NewsInfo> wrapper = getPageSearchWrapper(newsInfoQueryParam);
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(newsInfoQueryParam, wrapper));

        List<CategoryMenuDto> categoryMenuDtos = newsCategoryMapper.fetchCategoryMenuList();

        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){
            Map<String, String> map = new HashMap();
            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(map, dto, "");
            }
            result.getRecords().forEach(dto -> dto.setCategoryName(map.get(dto.getCategoryId())));
        }

        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<NewsInfoDto> selectList(PageParam pageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("id, title");
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(pageParam, queryWrapper));
        return Optional.ofNullable(result).orElse(new PageResult(Collections.emptyList(), 0));
    }

    @Override
    public List<NewsInfoDto> webFirstpage(NewsInfoQueryParam newsInfoQueryParam) {
        Wrapper<NewsInfo> wrapper = getPageSearchWrapper(newsInfoQueryParam);
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(newsInfoQueryParam, wrapper));

        List<CategoryMenuDto> categoryMenuDtos = newsCategoryMapper.fetchCategoryMenuList();

        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){
            Map<String, String> map = new HashMap();
            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(map, dto, "");
            }
            result.getRecords().forEach(dto -> dto.setCategoryName(map.get(dto.getCategoryId())));
        }

        return result.getRecords();
    }

    @Override
    public PageResult<NewsInfoDto> pageCustomDto(NewsInfoQueryParam newsInfoQueryParam) {
        Wrapper<NewsInfo> wrapper = getPageSearchWrapper(newsInfoQueryParam);
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(newsInfoQueryParam, wrapper));

        List<CategoryMenuDto> categoryMenuDtos = newsCategoryMapper.fetchCategoryMenuList();

        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){
            Map<String, String> map = new HashMap();
            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(map, dto, "");
            }
            result.getRecords().forEach(dto -> dto.setCategoryName(map.get(dto.getCategoryId())));
        }

        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<NewsInfoDto> userFavoritesList(PageParam pageParam) {
        Page<NewsInfoDto> pages = new Page(pageParam.getPageNo(), pageParam.getLimit());
        String uid = PorosContextHolder.getCurrentUser().getUid();
        Page<NewsInfoDto> result = newsInfoMapper.fetchUserFavoritesList(pages, uid);
        List list = new ArrayList();
        if (CollectionUtils.isNotEmpty(result.getRecords())){
            for (NewsInfoDto newsInfoDto : result.getRecords()){
                newsInfoDto.setIsFavorite(true);
                list.add(newsInfoDto);
            }
        }
        return null != result ? PageResult.builder().records(list).total(list.size()).build() : new PageResult();
    }

    @Override
    public PageResult<NewsInfoDto> appPageList(NewsInfoQueryParam newsInfoQueryParam) {
        Wrapper<NewsInfo> wrapper = getPageSearchWrapper(newsInfoQueryParam);
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(newsInfoQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<NewsInfoDto> keywordSearch(PageParam pageParam){
        if (StringUtils.isBlank(pageParam.getKeyword())){
            return new PageResult();
        }
        LambdaQueryWrapper<NewsInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.like(NewsInfo::getTitle, pageParam.getKeyword()).or()
                .like(NewsInfo::getContent, pageParam.getKeyword());
        PageResult<NewsInfoDto> result = newsInfoParamMapper.pageEntity2Dto(page(pageParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(NewsInfoAddParam newsInfoAddParam) {
        NewsInfo newsInfo = newsInfoParamMapper.addParam2Entity(newsInfoAddParam);
        newsInfo.setPublishTime(DateUtils.getNowDate());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsInfo);
        return save(newsInfo);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(NewsInfoEditParam newsInfoEditParam) {
        NewsInfo newsInfo = newsInfoParamMapper.editParam2Entity(newsInfoEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsInfo);
        return updateById(newsInfo);
    }

    @Override
    public boolean favorites(String newsId) {
        return newsFavoritesService.favorites(newsId);
    }

    @Override
    public boolean removeByIds(String[] ids) {
        return removeByIds(Arrays.stream(ids).collect(Collectors.toList()));
    }


    @Override
    public NewsInfoDto getDtoById(String id) {
        return newsInfoParamMapper.entity2Dto((NewsInfo) this.getById(id));
    }

    @Override
    public NewsInfoDto getUserNewsDtoById(String id) {
        NewsInfoDto newsInfoDto = getDtoById(id);
        if (null == newsInfoDto){
            return newsInfoDto;
        }
        newsInfoDto.setIsFavorite(newsFavoritesService.isFavorite(id));
        return newsInfoDto;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<NewsInfoDto> rows) {
        return saveBatch(newsInfoParamMapper.dtoList2Entity(rows));
    }

    @Override
    public KnowledgeCategoryDto getKnowledgeCategoryDto() {
        KnowledgeCategoryDto knowledgeCategoryDto = new KnowledgeCategoryDto();
        List<ChildrenCategoryDto> categoryList = newsCategoryService.fetchChildrenCategoryList();
        Integer total = newsInfoMapper.selectCount(Wrappers.lambdaQuery());

        knowledgeCategoryDto.setType(KnowledgeType.NEWS.getCode());
        knowledgeCategoryDto.setName("新闻资讯");
        knowledgeCategoryDto.setChildren(categoryList.size());
        knowledgeCategoryDto.setTotal(total);
        return knowledgeCategoryDto;
    }


    private Wrapper<NewsInfo> getPageSearchWrapper(NewsInfoQueryParam newsInfoQueryParam) {
        LambdaQueryWrapper<NewsInfo> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(newsInfoQueryParam.getCategoryId())){
            List<String> idList = newsCategoryService.fetchItemIdListByParentId(newsInfoQueryParam.getCategoryId());
            idList.add(newsInfoQueryParam.getCategoryId());
            wrapper.in(CollectionUtils.isNotEmpty(idList), NewsInfo::getCategoryId, idList);
        }
        wrapper.like(StringUtils.isNotBlank(newsInfoQueryParam.getKeyword()), NewsInfo::getTitle, newsInfoQueryParam.getKeyword());
        if(BaseEntity.class.isAssignableFrom(NewsInfo.class)){
            wrapper.orderByDesc(NewsInfo::getPublishTime);
        }
        return wrapper;
    }


    /**
     * 生成 Map 的分类列表
     * 例如： A - B - C
     * 只生成为叶子节点的列
     * @return
     */
    private void generateCategoryStr(Map<String, String> map, CategoryMenuDto treeBean, String categoryStr){
        StringBuffer categoryStrBuffer = new StringBuffer().append(categoryStr);
        categoryStrBuffer.append("-"+ treeBean.getName());
        map.put(treeBean.getId(), categoryStrBuffer.toString().substring(1, categoryStrBuffer.length()));
        if (CollectionUtils.isNotEmpty(treeBean.getChildren())) {
            for (CategoryMenuDto doc : treeBean.getChildren()) {
                generateCategoryStr(map, doc, categoryStrBuffer.toString());
            }
        }
    }

}
