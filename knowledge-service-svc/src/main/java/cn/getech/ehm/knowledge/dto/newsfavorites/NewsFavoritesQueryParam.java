package cn.getech.ehm.knowledge.dto.newsfavorites;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 新闻资讯收藏 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsFavorites查询", description = "新闻资讯收藏查询参数")
public class NewsFavoritesQueryParam extends PageParam {

}
