package cn.getech.ehm.knowledge.dto.course;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <pre>
 * 课程分类信息表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseCategory新增", description = "课程分类信息表新增参数")
public class CourseCategoryAddParam extends ApiParam {

    @NotEmpty(message = "标签名不能为空")
    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "备注信息。")
    private String remark;

}