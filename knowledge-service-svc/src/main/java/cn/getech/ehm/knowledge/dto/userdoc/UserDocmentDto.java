package cn.getech.ehm.knowledge.dto.userdoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <pre>
 * 用户文档表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-12-03
 */
@Data
@ApiModel(value = "UserDocmentDto", description = "用户文档表返回数据模型")
public class UserDocmentDto{

    @ApiModelProperty(value = "全局ID")
    private String id;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "附件ID")
    private String attachId;

    @ApiModelProperty(value = "附件URL")
    private String attachUrl;

}