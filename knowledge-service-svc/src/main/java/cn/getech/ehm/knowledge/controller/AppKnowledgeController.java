package cn.getech.ehm.knowledge.controller;


import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeQueryParam;
import cn.getech.ehm.knowledge.dto.knowledge.KnowledgeMenuChildrenQueryParam;
import cn.getech.ehm.knowledge.dto.knowledgedoc.CategoryKnowledgeDocDto;
import cn.getech.ehm.knowledge.service.IAppKnowledgeService;
import cn.getech.ehm.knowledge.service.ICategoryKnowledgeDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 知识库接口控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@RestController
@RequestMapping("/appKnowledge")
@Api(tags = "app: 知识库接口")
public class AppKnowledgeController {

    @Autowired
    private IAppKnowledgeService appKnowledgeService;
    @Autowired
    private ICategoryKnowledgeDocService categoryKnowledgeDocService;


    /**
     * 获取知识库模块信息列表
     */
    @ApiOperation("获取知识库模块信息列表")
    @GetMapping("/list")
    //@Permission("course:info:list")
    public RestResponse<List<KnowledgeCategoryDto>> knowledgeList(){
        return RestResponse.ok(appKnowledgeService.knowledgeList());
    }

    /**
     * 下拉列表-树形结构
     */
    @ApiOperation(value = "下拉列表-树形结构",notes = "type 1-2 不需要CategoryId  type 3 的时候 需要CateGoryId")
    @GetMapping("/getMenuChildrenList")
    public RestResponse<List<CategoryMenuDto>> getMenuChildrenList(@Valid KnowledgeMenuChildrenQueryParam knowledgeMenuChildrenQueryParam){
        return RestResponse.ok(appKnowledgeService.getMenuChildrenList(knowledgeMenuChildrenQueryParam));
    }

    /**
     * 查找 知识文档，新闻资讯，课程信息
     * @param queryParam
     * @return
     */
    @ApiOperation(value = "查找 知识文档，新闻资讯，课程信息")
    @PostMapping("/listKnowledge")
    public RestResponse<KnowledgeDto> listKnowledge(@RequestBody KnowledgeQueryParam queryParam){
        return RestResponse.ok(appKnowledgeService.listKnowledge(queryParam));
    }

    /**
     * 根据id获取分类知识文档模块
     */
    @ApiOperation(value = "根据id获取知识文档模分类")
    @GetMapping(value = "/docCategory/{id}")
    public RestResponse<CategoryKnowledgeDocDto> get(@PathVariable  String id) {
        return RestResponse.ok(categoryKnowledgeDocService.getDtoById(id));
    }

}
