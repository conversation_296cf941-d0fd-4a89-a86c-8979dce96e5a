package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesDto;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.coursefavorites.CourseFavoritesQueryParam;
import cn.getech.ehm.knowledge.entity.CourseFavorites;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程用户收藏关系表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
public interface ICourseFavoritesService extends IBaseService<CourseFavorites> {

        /**
         * 分页查询，返回Dto
         *
         * @param courseFavoritesQueryParam
         * @return
         */
        PageResult<CourseFavoritesDto> pageDto(CourseFavoritesQueryParam courseFavoritesQueryParam);

        /**
         * 保存
         * @param courseFavoritesAddParam
         * @return
         */
        boolean saveByParam(CourseFavoritesAddParam courseFavoritesAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CourseFavoritesDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<CourseFavoritesDto> rows);

        /**
         * 更新
         * @param courseFavoritesEditParam
         */
        boolean updateByParam(CourseFavoritesEditParam courseFavoritesEditParam);

        /**
         * 更新用户收藏数
         */
        void updateFavorites();

        /**
         * 是否收藏课程
         * @param courseId
         * @return
         */
        boolean isFavoriteCourse(String courseId);

        List<String> getCurrUserFavoritesCourse();
}