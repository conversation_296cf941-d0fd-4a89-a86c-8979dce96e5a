package cn.getech.ehm.knowledge.controller;


import cn.getech.ehm.knowledge.dto.news.NewsCategoryAddParam;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryDto;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryEditParam;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryQueryParam;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.INewsCategoryService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 新闻分类信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/newsCategory")
@Api(tags = "web: 新闻分类信息服务接口")
public class WebNewsCategoryController {

    @Autowired
    private INewsCategoryService newsCategoryService;

    /**
     * 分页获取新闻分类信息列表
     */
    @ApiOperation("分页获取新闻分类信息列表")
    @GetMapping("/list")
    //@Permission("news:category:list")
    public RestResponse<PageResult<NewsCategoryDto>> pageList(@Valid NewsCategoryQueryParam newsCategoryQueryParam){
        return RestResponse.ok(newsCategoryService.pageDto(newsCategoryQueryParam));
    }

    /**
     * 获取新闻分类菜单测试服务接口列表
     */
    @ApiOperation("获取新闻分类菜单接口")
    @GetMapping("/categoryMenuList")
    //@Permission("course:category:list")
    public RestResponse<List<CategoryMenuDto>> categoryMenuList(){
        return RestResponse.ok(newsCategoryService.fetchCategoryMenuList());
    }

    /**
     * 获取子分类菜下拉列表
     */
    @ApiOperation("获取子分类菜下拉列表接口")
    @GetMapping("/childrenCategoryList")
    //@Permission("course:category:list")
    public RestResponse<List<ChildrenCategoryDto>> childrenCategoryList(){
        return RestResponse.ok(newsCategoryService.fetchChildrenCategoryList());
    }

    /**
     * 新增新闻分类信息
     */
    @ApiOperation("新增新闻分类信息")
    @AuditLog(title = "新闻分类信息",desc = "新增新闻分类信息",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("news:category:update")
    public RestResponse<Boolean> add(@RequestBody @Valid NewsCategoryAddParam newsCategoryAddParam) {
        return RestResponse.ok(newsCategoryService.saveByParam(newsCategoryAddParam));
    }

    /**
     * 修改新闻分类信息
     */
    @ApiOperation(value="修改新闻分类信息")
    @AuditLog(title = "新闻分类信息",desc = "修改新闻分类信息",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("news:category:update")
    public RestResponse<Boolean> update(@RequestBody @Valid NewsCategoryEditParam newsCategoryEditParam) {
        return RestResponse.ok(newsCategoryService.updateByParam(newsCategoryEditParam));
    }

    /**
     * 修改课程分类测试服务接口
     */
    @ApiOperation(value="批量修改课程分类菜单测试服务接口")
    @AuditLog(title = "课程分类测试服务接口",desc = "修改课程分类测试服务接口",businessType = BusinessType.UPDATE)
    @PutMapping("batchUpdateCategoryMenu")
    //@Permission("course:category:update")
    public RestResponse<Boolean> batchUpdateCategoryMenu(@RequestBody List<CategoryMenuParam> menuList) {
        return RestResponse.ok(newsCategoryService.batchUpdateCategoryMenu(menuList));
    }

    /**
     * 根据id删除新闻分类信息
     */
    @ApiOperation(value="根据id删除新闻分类信息")
    @AuditLog(title = "新闻分类信息",desc = "新闻分类信息",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("news:category:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(newsCategoryService.removeById(id));
    }

    /**
     * 根据id获取新闻分类信息
     */
    @ApiOperation(value = "根据id获取新闻分类信息")
    @GetMapping(value = "/{id}")
    //@Permission("news:category:list")
    public RestResponse<NewsCategoryDto> get(@PathVariable String id) {
        return RestResponse.ok(newsCategoryService.getDtoById(id));
    }

}
