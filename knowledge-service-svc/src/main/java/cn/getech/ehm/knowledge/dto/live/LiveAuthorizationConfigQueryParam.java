package cn.getech.ehm.knowledge.dto.live;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LiveAuthorizationConfig查询", description = "查询参数")
public class LiveAuthorizationConfigQueryParam extends PageParam {

    @ApiModelProperty(value = "频道ID")
    private Long channelId;

}
