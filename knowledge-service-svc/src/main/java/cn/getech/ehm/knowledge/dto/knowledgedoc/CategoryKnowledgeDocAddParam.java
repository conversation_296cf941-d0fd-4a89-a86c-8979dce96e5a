package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <pre>
 * 知识文档分类信息表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudyCategoryKnowledgeDoc新增", description = "知识文档分类信息表新增参数")
public class CategoryKnowledgeDocAddParam extends ApiParam {

    @ApiModelProperty(value = "标签名。")
    @NotBlank(message = "标签名不能为空")
    private String name;

    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "备注信息。")
    private String remark;

}