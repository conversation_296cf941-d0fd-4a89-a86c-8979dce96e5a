package cn.getech.ehm.knowledge.dto.live;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.knowledge.entity.LiveAuthorizationConfig;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-30
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  LiveAuthorizationConfigParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param liveAuthorizationConfigAddParam
     * @return
     */
    LiveAuthorizationConfig addParam2Entity(LiveAuthorizationConfigAddParam liveAuthorizationConfigAddParam);

    /**
     * 编辑参数转换为实体
     * @param liveAuthorizationConfigEditParam
     * @return
     */
    LiveAuthorizationConfig editParam2Entity(LiveAuthorizationConfigEditParam liveAuthorizationConfigEditParam);

    /**
     * 实体转换为Dto
     * @param liveAuthorizationConfig
     * @return
     */
    LiveAuthorizationConfigDto entity2Dto(LiveAuthorizationConfig liveAuthorizationConfig);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<LiveAuthorizationConfigDto> pageEntity2Dto(PageResult<LiveAuthorizationConfig> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<LiveAuthorizationConfig> dtoList2Entity(List<LiveAuthorizationConfigDto> rows);

}
