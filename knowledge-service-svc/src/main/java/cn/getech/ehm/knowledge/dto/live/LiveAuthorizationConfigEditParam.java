package cn.getech.ehm.knowledge.dto.live;

import cn.getech.ehm.common.enums.LiveType;
import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LiveAuthorizationConfig编辑", description = "编辑参数")
public class LiveAuthorizationConfigEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "频道ID")
    private Long channelId;

    @ApiModelProperty(value = "直播类型")
    private LiveType type;

    @ApiModelProperty(value = "授权用户uids")
    private String[] uids;

}
