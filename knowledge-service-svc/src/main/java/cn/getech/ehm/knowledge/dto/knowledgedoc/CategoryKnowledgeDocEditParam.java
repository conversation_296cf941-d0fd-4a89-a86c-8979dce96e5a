package cn.getech.ehm.knowledge.dto.knowledgedoc;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <pre>
 * 知识文档分类信息表 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudyCategoryKnowledgeDoc编辑", description = "知识文档分类信息表编辑参数")
public class CategoryKnowledgeDocEditParam extends ApiParam {

    @NotEmpty(message = "id不能为空")
    @ApiModelProperty(value = "全局ID。")
    private String id;

    @NotEmpty(message = "标签名不能为空")
    @ApiModelProperty(value = "标签名。")
    private String name;

    @ApiModelProperty(value = "父分类ID。")
    private String parentId;

    @ApiModelProperty(value = "备注信息。")
    private String remark;

}
