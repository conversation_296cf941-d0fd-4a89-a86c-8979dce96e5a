package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.dto.course.*;
import cn.getech.ehm.knowledge.entity.CourseFavorites;
import cn.getech.ehm.knowledge.entity.CourseInfo;
import cn.getech.ehm.knowledge.entity.CoursePraise;
import cn.getech.ehm.knowledge.mapper.CourseCategoryMapper;
import cn.getech.ehm.knowledge.mapper.CourseInfoMapper;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.service.*;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.KeTianLiveClient;
import cn.getech.ehm.system.dto.KeTianVideoQueryParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程信息表 服务实现类
 * <AUTHOR>
 * @since 2020-11-04
 */
@Slf4j
@Service
public class CourseInfoServiceImpl extends BaseServiceImpl<CourseInfoMapper, CourseInfo> implements ICourseInfoService {

    @Autowired
    private CourseInfoParamMapper courseInfoParamMapper;

    @Autowired
    private CourseCategoryMapper courseCategoryMapper;

    @Autowired
    private ICourseCategoryService courseCategoryService;

    @Autowired
    private CourseInfoMapper courseInfoMapper;

    @Autowired
    private ICoursePraiseService coursePraiseService;

    @Autowired
    private ICourseFavoritesService courseFavoritesService;

    @Autowired
    private KeTianLiveClient keTianLiveClient;

    @Autowired
    private ICourseAccessibleService courseAccessibleService;


    @Override
    public PageResult<CourseInfoDto> pageDto(CourseInfoQueryParam courseInfoQueryParam) {
        Wrapper<CourseInfo> wrapper = getPageSearchWrapper(courseInfoQueryParam);
        PageResult<CourseInfoDto> result = courseInfoParamMapper.pageEntity2Dto(page(courseInfoQueryParam, wrapper));
        List<CategoryMenuDto> categoryMenuDtos = courseCategoryMapper.fetchCategoryMenuList();

        Map<String, String> categoryMap = new HashMap<>();
        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){

            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(categoryMap, dto, "");
            }
        }
        if(CollectionUtils.isNotEmpty(result.getRecords())){
            List<String> courseIds = result.getRecords().stream().map(CourseInfoDto::getId).distinct().collect(Collectors.toList());
            Map<String, List<String>>roleIdMap = courseAccessibleService.getCourseAccessMap(courseIds);
            result.getRecords().forEach(dto -> {
                dto.setCategoryName(categoryMap.get(dto.getCategoryId()));
                dto.setRoleIds(roleIdMap.get(dto.getId()));
            });
        }
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public List<CourseInfoDto> webFirstpage(CourseInfoQueryParam courseInfoQueryParam) {
        Wrapper<CourseInfo> wrapper = getPageSearchWrapper(courseInfoQueryParam);
        PageResult<CourseInfoDto> result = courseInfoParamMapper.pageEntity2Dto(page(courseInfoQueryParam, wrapper));
        List<CategoryMenuDto> categoryMenuDtos = courseCategoryMapper.fetchCategoryMenuList();

        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){
            Map<String, String> map = new HashMap<>();
            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(map, dto, "");
            }
            result.getRecords().forEach(dto -> dto.setCategoryName(map.get(dto.getCategoryId())));
        }
        return result.getRecords();
    }


    @Override
    public PageResult<CourseInfoDto> pageCustomDto(CourseInfoQueryParam courseInfoQueryParam) {
        Wrapper<CourseInfo> wrapper = getAppPageSearchWrapper(courseInfoQueryParam);
        PageResult<CourseInfoDto> result = courseInfoParamMapper.pageEntity2Dto(page(courseInfoQueryParam, wrapper));


        List<CategoryMenuDto> categoryMenuDtos = courseCategoryMapper.fetchCategoryMenuList();

        Map<String, String> categoryMap = new HashMap<>();
        if (result.getRecords().size() != 0 && categoryMenuDtos.size() != 0 ){

            for (CategoryMenuDto dto : categoryMenuDtos){
                generateCategoryStr(categoryMap, dto, "");
            }
        }
        if(CollectionUtils.isNotEmpty(result.getRecords())){
            List<String> courseIds = result.getRecords().stream().map(CourseInfoDto::getId).distinct().collect(Collectors.toList());
            Map<String, List<String>> roleIdMap = courseAccessibleService.getCourseAccessMap(courseIds);
            result.getRecords().forEach(dto -> {
                dto.setCategoryName(categoryMap.get(dto.getCategoryId()));
                dto.setRoleIds(roleIdMap.get(dto.getId()));
            });
        }
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<CourseInfoDto> appPageList(CourseInfoQueryParam courseInfoQueryParam) {
        Wrapper<CourseInfo> wrapper = getAppPageSearchWrapper(courseInfoQueryParam);
        PageResult<CourseInfoDto> result = courseInfoParamMapper.pageEntity2Dto(page(courseInfoQueryParam, wrapper));

        // 设置已收藏的课程属性
        Map<String, CourseInfoDto> map = new HashMap();
        List<String> courseIdList = new ArrayList();
        for (CourseInfoDto courseInfoDto : result.getRecords()){
            courseInfoDto.setIsLike(false);
            map.put(courseInfoDto.getId(), courseInfoDto);
            courseIdList.add(courseInfoDto.getId());
        }
        if (CollectionUtils.isNotEmpty(courseIdList)){
            List<String> likeCourseIdList = coursePraiseService.getCurrUserPraiseCourse(courseIdList);
            if (CollectionUtils.isNotEmpty(likeCourseIdList)){
                for (String likeCourseId : likeCourseIdList){
                    map.get(likeCourseId).setIsLike(true);
                }
            }
        }

        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<CourseInfoDto> appAllFavoritesPageList(CourseInfoQueryParam queryParam) {
        List<String> currUserFavoritesCourse = courseFavoritesService.getCurrUserFavoritesCourse();
        PageResult<CourseInfoDto> result = new PageResult();
        if (CollectionUtils.isNotEmpty(currUserFavoritesCourse)){
            LambdaQueryWrapper<CourseInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(CourseInfo::getId,currUserFavoritesCourse);
            result = courseInfoParamMapper.pageEntity2Dto(page(queryParam, wrapper));
            result.getRecords().forEach( dto -> dto.setIsFavorite(true));
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByParam(CourseInfoAddParam courseInfoAddParam) {
        CourseInfo courseInfo = courseInfoParamMapper.addParam2Entity(courseInfoAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseInfo);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        courseInfo.setId(uuid);
        return save(courseInfo);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByParam(CourseInfoEditParam courseInfoEditParam) {
        CourseInfo courseInfo = courseInfoParamMapper.editParam2Entity(courseInfoEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseInfo);

        return updateById(courseInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likeCount(String courseId) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<CoursePraise> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CoursePraise::getCourseId,courseId);
        queryWrapper.eq(CoursePraise::getUid,currentUser.getUid());
        CoursePraise coursePraise = (CoursePraise) coursePraiseService.getOne(queryWrapper);
        if (coursePraise != null){
            if (courseInfoMapper.deleteLikeCount(courseId)){
                queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(CoursePraise::getCourseId,courseId);
                queryWrapper.eq(CoursePraise::getUid,currentUser.getUid());
                return !coursePraiseService.remove(queryWrapper);
            }
        }else{
            if (courseInfoMapper.addLikeCount(courseId)){
                coursePraise = new CoursePraise();
                coursePraise.setCourseId(courseId);
                coursePraise.setUid(currentUser.getUid());
                return coursePraiseService.save(coursePraise);
            }
        }
        return false;
    }

    @Override
    public boolean addLearningCount(String id) {
        return courseInfoMapper.addLearningCount(id);
    }

    @Override
    public boolean addCommentCount(String id, Integer count) {
        return courseInfoMapper.addCommentCount(id, count);
    }


    @Override
    public boolean favorites(String courseId){
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<CourseFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CourseFavorites::getCourseId,courseId);
        queryWrapper.eq(CourseFavorites::getUid,currentUser.getUid());
        CourseFavorites courseFavorites = (CourseFavorites) courseFavoritesService.getOne(queryWrapper);
        if (courseFavorites != null){
            boolean remove = courseFavoritesService.remove(queryWrapper);
            courseFavoritesService.updateFavorites();
            return !remove;
        }
        courseFavorites = new CourseFavorites();
        courseFavorites.setCourseId(courseId);
        courseFavorites.setUid(currentUser.getUid());
        boolean saveSuccess = courseFavoritesService.save(courseFavorites);
        courseFavoritesService.updateFavorites();
        return saveSuccess;
    }

    @Override
    public CourseInfoDto getDtoById(String id) {
        return courseInfoParamMapper.entity2Dto((CourseInfo) this.getById(id));
    }

    @Override
    public CourseInfoDto getUserCourseDtoById(String id) {
        CourseInfoDto courseInfoDto = getDtoById(id);
        if (null == courseInfoDto){
            return courseInfoDto;
        }
        courseInfoDto.setIsLike(coursePraiseService.isPraiseCourse(id));
        courseInfoDto.setIsFavorite(courseFavoritesService.isFavoriteCourse(id));
        return courseInfoDto;
    }

    @Override
    public boolean removeById(String id) {
        return SqlHelper.retBool(this.baseMapper.deleteById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CourseInfoDto> rows) {
        return saveBatch(courseInfoParamMapper.dtoList2Entity(rows));
    }

    @Override
    public KnowledgeCategoryDto getKnowledgeCategoryDto() {
        KnowledgeCategoryDto knowledgeCategoryDto = new KnowledgeCategoryDto();
        List<ChildrenCategoryDto> categoryList = courseCategoryService.fetchChildrenCategoryList();
        Integer total = courseInfoMapper.selectCount(Wrappers.lambdaQuery());
        knowledgeCategoryDto.setType(KnowledgeType.COURSE.getCode());
        knowledgeCategoryDto.setName("培训课程");
        knowledgeCategoryDto.setChildren(categoryList.size());
        knowledgeCategoryDto.setTotal(total);
        return knowledgeCategoryDto;
    }

    @Override
    public RestResponse<String> getKeTianYunSign(KeTianVideoQueryParam keTianVideoQueryParam){
        return keTianLiveClient.getKeTianYunSign(keTianVideoQueryParam);
    }

    @Override
    public Integer getCourseCount() {
        LambdaQueryWrapper<CourseInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(CourseInfo::getId);
        queryWrapper.eq(CourseInfo::getDeleted, StaticValue.ZERO);
        courseInfoMapper.selectCount(queryWrapper);
        return courseInfoMapper.selectCount(queryWrapper);
    }

    @Override
    public Integer getPlayCount() {
        return courseInfoMapper.sumCoursePlayCount();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean batchEmpower(CourseEmpowerParam param){
        LambdaUpdateWrapper<CourseInfo> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(CourseInfo::getId, param.getCourseIds());
        wrapper.set(CourseInfo::getIsPublic, param.getIsPublic());
        Boolean flag = this.update(wrapper);

        courseAccessibleService.deleteByCourseIds(param.getCourseIds());
        if(CollectionUtils.isNotEmpty(param.getRoleIds())){
            courseAccessibleService.saveDtoBatch(param.getCourseIds(), param.getRoleIds());
        }
        return flag;
    }

    private Wrapper<CourseInfo> getPageSearchWrapper(CourseInfoQueryParam courseInfoQueryParam) {
        LambdaQueryWrapper<CourseInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(courseInfoQueryParam.getKeyword()), CourseInfo::getTitle, courseInfoQueryParam.getKeyword());

        // 查询该分类下所有的分类ID
        if (StringUtils.isNotEmpty(courseInfoQueryParam.getCategoryId())){
            List<String> idList = courseCategoryService.fetchItemIdListByParentId(courseInfoQueryParam.getCategoryId());
            idList.add(courseInfoQueryParam.getCategoryId());
            wrapper.in(CollectionUtils.isNotEmpty(idList), CourseInfo::getCategoryId, idList);
        }
        if( courseInfoQueryParam.getIsClient() == StaticValue.ONE){
            // 筛选公开 和 非公开的指定的课程
            List<String> courseList = courseAccessibleService.listCurrentCourseIds();
            if(CollectionUtils.isNotEmpty(courseList)){
                wrapper.and( w -> w.eq(CourseInfo::getIsPublic,1)
                        .or()
                        .in(CourseInfo::getId, courseList));
            }else{
                wrapper.eq(CourseInfo::getIsPublic,1);
            }
        }

        if(BaseEntity.class.isAssignableFrom(CourseInfo.class)){
            wrapper.orderByDesc(CourseInfo::getUpdateTime,CourseInfo::getCreateTime);
        }
        return wrapper;
    }


    private Wrapper<CourseInfo> getAppPageSearchWrapper(CourseInfoQueryParam courseInfoQueryParam) {
        LambdaQueryWrapper<CourseInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(courseInfoQueryParam.getKeyword()), CourseInfo::getTitle, courseInfoQueryParam.getKeyword());

        // 查询该分类下所有的分类ID
        if (StringUtils.isNotEmpty(courseInfoQueryParam.getCategoryId())){
            List<String> idList = courseCategoryService.fetchItemIdListByParentId(courseInfoQueryParam.getCategoryId());
            idList.add(courseInfoQueryParam.getCategoryId());
            wrapper.in(CollectionUtils.isNotEmpty(idList), CourseInfo::getCategoryId, idList);
        }
        // 筛选公开 和 非公开的指定的课程
        List<String> courseList = courseAccessibleService.listCurrentCourseIds();
        if(CollectionUtils.isNotEmpty(courseList)){
            wrapper.and( w -> w.eq(CourseInfo::getIsPublic,1)
                    .or()
                    .in(CourseInfo::getId, courseList));
        }else{
            wrapper.eq(CourseInfo::getIsPublic,1);
        }

        if(BaseEntity.class.isAssignableFrom(CourseInfo.class)){
            wrapper.orderByDesc(CourseInfo::getUpdateTime,CourseInfo::getCreateTime);
        }
        return wrapper;
    }

    /**
     * 生成 Map 的分类列表
     * 例如： A - B - C
     * 只生成为叶子节点的列
     * @return
     */
    private void generateCategoryStr(Map<String, String> map, CategoryMenuDto treeBean, String categoryStr){
        StringBuffer categoryStrBuffer = new StringBuffer().append(categoryStr);
        categoryStrBuffer.append("-"+ treeBean.getName());
        map.put(treeBean.getId(), categoryStrBuffer.toString().substring(1, categoryStrBuffer.length()));
        if (CollectionUtils.isNotEmpty(treeBean.getChildren())) {
            for (CategoryMenuDto doc : treeBean.getChildren()) {
                generateCategoryStr(map, doc, categoryStrBuffer.toString());
            }
        }
    }
}
