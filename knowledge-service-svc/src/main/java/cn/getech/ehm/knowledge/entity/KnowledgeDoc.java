package cn.getech.ehm.knowledge.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 知识学习库
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_knowledge_doc")
public class KnowledgeDoc extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *  租户ID。
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 附件ID
     */
    @TableField("attach_id")
    private String attachId;

    /**
     * 类别id
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 培训师
     */
    @TableField("trainer")
    private String trainer;

    /**
     * 备注信息描述
     */
    @TableField("description")
    private String description;

    /**
     * 0：未删除  1: 已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    @TableField("is_public")
    private Boolean isPublic;

}
