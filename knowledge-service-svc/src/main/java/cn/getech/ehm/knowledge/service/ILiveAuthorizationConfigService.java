package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigDto;
import cn.getech.ehm.knowledge.dto.live.LiveAuthorizationConfigEditParam;
import cn.getech.ehm.knowledge.entity.LiveAuthorizationConfig;
import cn.getech.poros.framework.common.service.IBaseService;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
public interface ILiveAuthorizationConfigService extends IBaseService<LiveAuthorizationConfig> {

        /**
         * 获取频道信息
         * @return
         */
        LiveAuthorizationConfigDto getChannelConfig();

        /**
         * 更新直播配置
         * @param liveAuthorizationConfigEditParam
         * @return
         */
        boolean updateByParam(LiveAuthorizationConfigEditParam liveAuthorizationConfigEditParam);

        /**
         * 检查用户权限
         * @param uid
         * @return
         */
        boolean checkAuthorization(String uid);
}