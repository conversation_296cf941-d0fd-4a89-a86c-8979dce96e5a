package cn.getech.ehm.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 新闻分类信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("knowledge_news_category")
public class NewsCategory extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *  租户ID。
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 标签名。
     */
    @TableField("name")
    private String name;

    /**
     * 父分类ID。
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 是否是叶子节点。0-否，1-是
     */
    @TableField("is_leaf")
    private Boolean isLeaf;

    /**
     * 排序序号。
     */
    @TableField("sort_number")
    private Integer sortNumber;

    /**
     * 记录状态。0-正常，1-删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;


}
