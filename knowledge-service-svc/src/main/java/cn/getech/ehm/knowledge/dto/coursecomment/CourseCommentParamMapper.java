package cn.getech.ehm.knowledge.dto.coursecomment;

import cn.getech.ehm.knowledge.entity.CourseComment;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程评论信息 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-10
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  CourseCommentParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param courseCommentAddParam
     * @return
     */
    CourseComment addParam2Entity(CourseCommentAddParam courseCommentAddParam);

    /**
     * 实体转换为Dto
     * @param courseComment
     * @return
     */
    CourseCommentDto entity2Dto(CourseComment courseComment);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<CourseCommentDto> pageEntity2Dto(PageResult<CourseComment> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<CourseComment> dtoList2Entity(List<CourseCommentDto> rows);

}
