package cn.getech.ehm.knowledge.dto.coursecomment;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <pre>
 * 课程评论信息 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseComment新增", description = "课程评论信息新增参数")
public class CourseCommentAddParam extends ApiParam {

    @NotEmpty(message = "评论内容不能为空")
    @ApiModelProperty(value = "评论内容。")
    private String content;

    @NotEmpty(message = "课程id不能为空")
    @ApiModelProperty(value = "课程id。")
    private String courseId;

}