package cn.getech.ehm.knowledge.dto.courseaccessible;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 非公开课程可访问表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseAccessible新增", description = "非公开课程可访问表新增参数")
public class CourseAccessibleAddParam extends ApiParam {

    @ApiModelProperty(value = "描述")
    private String remark;
}