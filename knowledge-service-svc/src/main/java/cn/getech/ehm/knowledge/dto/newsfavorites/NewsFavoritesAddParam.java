package cn.getech.ehm.knowledge.dto.newsfavorites;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 新闻资讯收藏 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsFavorites新增", description = "新闻资讯收藏新增参数")
public class NewsFavoritesAddParam extends ApiParam {

    @ApiModelProperty(value = "描述")
    private String remark;
}