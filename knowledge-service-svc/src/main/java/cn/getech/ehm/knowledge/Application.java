package cn.getech.ehm.knowledge;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages={"cn.getech"})
@MapperScan("cn.getech.ehm.knowledge.mapper")
@EnableConfigurationProperties
@ComponentScan(basePackages={"cn.getech",  "com.alibaba.nacos"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Bean
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }
}
