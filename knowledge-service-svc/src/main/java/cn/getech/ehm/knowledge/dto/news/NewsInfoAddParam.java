package cn.getech.ehm.knowledge.dto.news;

import cn.getech.poros.framework.common.annotation.Excel;
import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

import javax.validation.constraints.NotBlank;

/**
 * <pre>
 * 新闻资讯信息表 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NewsInfo新增", description = "新闻资讯信息表新增参数")
public class NewsInfoAddParam extends ApiParam {

    @ApiModelProperty(value = " 图文标题 ")
    @NotBlank
    private String title;

    @ApiModelProperty(value = " 分类ID ")
    @NotBlank
    private String categoryId;

    @ApiModelProperty(value = " 图文内容 ")
    @NotBlank
    private String content;
}