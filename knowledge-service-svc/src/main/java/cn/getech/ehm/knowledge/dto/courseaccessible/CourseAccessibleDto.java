package cn.getech.ehm.knowledge.dto.courseaccessible;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 非公开课程可访问表 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-24
 */
@Data
@ApiModel(value = "CourseAccessibleDto", description = "非公开课程可访问表返回数据模型")
public class CourseAccessibleDto{

    @ApiModelProperty(value = "全局ID")
    @Excel(name="全局ID",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "描述")
    @Excel(name="描述",cellType = Excel.ColumnType.STRING )
    private String remark;

}