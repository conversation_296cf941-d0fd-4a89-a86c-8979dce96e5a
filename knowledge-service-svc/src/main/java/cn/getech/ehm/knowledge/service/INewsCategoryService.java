package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.news.NewsCategoryAddParam;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryDto;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryEditParam;
import cn.getech.ehm.knowledge.dto.news.NewsCategoryQueryParam;
import cn.getech.ehm.knowledge.entity.NewsCategory;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻分类信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface INewsCategoryService extends IBaseService<NewsCategory> {

        /**
         * 分页查询，返回Dto
         *
         * @param newsCategoryQueryParam
         * @return
         */
        PageResult<NewsCategoryDto> pageDto(NewsCategoryQueryParam newsCategoryQueryParam);

        /**
         * 保存
         * @param newsCategoryAddParam
         * @return
         */
        boolean saveByParam(NewsCategoryAddParam newsCategoryAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        NewsCategoryDto getDtoById(String id);

        /**
         * 根据id删除记录
         * @param id
         * @return
         */
        boolean removeById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<NewsCategoryDto> rows);

        /**
         * 更新
         * @param newsCategoryEditParam
         */
        boolean updateByParam(NewsCategoryEditParam newsCategoryEditParam);

        /**
         * 批量更新分类菜单信息
         * @param menuParamList
         * @return
         */
        boolean batchUpdateCategoryMenu(List<CategoryMenuParam> menuParamList);

        /**
         * 获取分类菜单接口
         * @return
         */
        List<CategoryMenuDto> fetchCategoryMenuList();

        /**
         * 根据父级分类id查询菜单列表
         * @param parentId
         * @return
         */
        List<String> fetchItemIdListByParentId(String parentId);

        /**
         * 获取子类菜单列表
         * @return
         */
        List<ChildrenCategoryDto> fetchChildrenCategoryList();
}