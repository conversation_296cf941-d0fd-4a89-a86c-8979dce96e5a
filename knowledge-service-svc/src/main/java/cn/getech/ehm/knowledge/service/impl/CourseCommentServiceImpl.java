package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.knowledge.entity.CourseComment;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentAddParam;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentDto;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentParamMapper;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentQueryParam;
import cn.getech.ehm.knowledge.mapper.CourseCommentMapper;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.dto.common.SecStaffDto;
import cn.getech.ehm.knowledge.service.ICourseCommentService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.service.ICourseInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.*;


/**
 * <pre>
 * 课程评论信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Slf4j
@Service
public class CourseCommentServiceImpl extends BaseServiceImpl<CourseCommentMapper, CourseComment> implements ICourseCommentService {

    @Autowired
    private CourseCommentParamMapper courseCommentParamMapper;

    @Autowired
    private PorosSecStaffClient porosSecStaffClient;

    @Autowired
    private ICourseInfoService courseInfoService;

    @Override
    public PageResult<CourseCommentDto> pageDto(CourseCommentQueryParam courseCommentQueryParam) {
        Wrapper<CourseComment> wrapper = getPageSearchWrapper(courseCommentQueryParam);
        PageResult<CourseCommentDto> result = courseCommentParamMapper.pageEntity2Dto(page(courseCommentQueryParam, wrapper));
        StringBuilder stringBuilder = new StringBuilder();
        Map<String, SecStaffDto> userMap = new HashMap();
        for (CourseCommentDto courseCommentDto : result.getRecords()){
            if (!userMap.containsKey(courseCommentDto.getUid())){
                userMap.put(courseCommentDto.getUid(), new SecStaffDto());
            }
            stringBuilder.append(courseCommentDto.getUid());
            stringBuilder.append(",");
            courseCommentDto.setUserInfo(userMap.get(courseCommentDto.getUid()));
        }
        RestResponse<List<PorosSecStaffDto>> clientList = porosSecStaffClient.getList(stringBuilder.toString());

        for (PorosSecStaffDto staffDto : clientList.getData()){
            SecStaffDto userInfo = userMap.get(staffDto.getUid());
            BeanUtils.copyProperties(staffDto, userInfo);
        }
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CourseCommentAddParam courseCommentAddParam) {
        CourseComment courseComment = courseCommentParamMapper.addParam2Entity(courseCommentAddParam);
        courseComment.setUid(PorosContextHolder.getCurrentUser().getUid());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,courseComment);
        boolean success = save(courseComment);
        if (success){
            courseInfoService.addCommentCount(courseCommentAddParam.getCourseId(), 1);
        }
        return success;
    }

    @Override
    public CourseCommentDto getDtoById(String id) {
        return courseCommentParamMapper.entity2Dto((CourseComment) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CourseCommentDto> rows) {
        return saveBatch(courseCommentParamMapper.dtoList2Entity(rows));
    }

    @Override
    public boolean removeById(String id) {
        CourseCommentDto commentDto = getDtoById(id);
        boolean remove = super.removeById(id);
        if (remove){
            courseInfoService.addCommentCount(commentDto.getCourseId(), -1);
        }
        return remove;
    }

    private Wrapper<CourseComment> getPageSearchWrapper(CourseCommentQueryParam courseCommentQueryParam) {
        LambdaQueryWrapper<CourseComment> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(courseCommentQueryParam.getContent()),
                CourseComment::getContent, courseCommentQueryParam.getContent());
        wrapper.eq(StringUtils.isNotBlank(courseCommentQueryParam.getCourseId()),
                CourseComment::getCourseId, courseCommentQueryParam.getCourseId());
        wrapper.gt(null != courseCommentQueryParam.getSort(),
                CourseComment::getSort, courseCommentQueryParam.getSort());
        if(BaseEntity.class.isAssignableFrom(CourseComment.class)){
            wrapper.orderByDesc(CourseComment::getUpdateTime,CourseComment::getCreateTime);
        }
        return wrapper;
    }
}
