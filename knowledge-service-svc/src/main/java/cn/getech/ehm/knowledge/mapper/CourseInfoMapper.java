package cn.getech.ehm.knowledge.mapper;

import cn.getech.ehm.knowledge.entity.CourseInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 课程信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Repository
public interface CourseInfoMapper extends BaseMapper<CourseInfo> {

    /**
     * 点赞数增加
     * @param id
     * @return
     */
    boolean addLikeCount(String id);

    /**
     * 点赞数减少
     * @param id
     * @return
     */
    boolean deleteLikeCount(String id);

    /**
     * 学习数增加
     * @param id
     * @return
     */
    boolean addLearningCount(String id);

    /**
     * 增加评论数
     * @param id
     * @return
     */
    boolean addCommentCount(@Param("id") String id, @Param("count") Integer count);


    /**
     * 查询课程的总播放数量
     * @return
     */
    Integer sumCoursePlayCount();
}
