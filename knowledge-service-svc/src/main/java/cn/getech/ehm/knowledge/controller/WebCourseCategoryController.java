package cn.getech.ehm.knowledge.controller;


import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuParam;
import cn.getech.ehm.knowledge.dto.common.ChildrenCategoryDto;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryAddParam;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryDto;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryEditParam;
import cn.getech.ehm.knowledge.dto.course.CourseCategoryQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.knowledge.service.ICourseCategoryService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 课程分类服务接口控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/courseCategory")
@Api(tags = "web: 课程分类服务接口")
public class WebCourseCategoryController {

    @Autowired
    private ICourseCategoryService courseCategoryService;

    /**
     * 分页获取课程分类服务接口列表
     */
    @ApiOperation("分页获取课程分类服务接口列表")
    @GetMapping("/list")
    //@Permission("course:category:list")
    public RestResponse<PageResult<CourseCategoryDto>> pageList(@Valid CourseCategoryQueryParam courseCategoryQueryParam){
        return RestResponse.ok(courseCategoryService.pageDto(courseCategoryQueryParam));
    }

    /**
     * 获取课程分类菜单服务接口列表
     */
    @ApiOperation("获取课程分类菜单接口")
    @GetMapping("/categoryMenuList")
    //@Permission("course:category:list")
    public RestResponse<List<CategoryMenuDto>> categoryMenuList(){
        return RestResponse.ok(courseCategoryService.fetchCategoryMenuList());
    }

    /**
     * 获取子分类菜下拉列表
     */
    @ApiOperation("获取子分类菜下拉列表接口")
    @GetMapping("/childrenCategoryList")
    //@Permission("course:category:list")
    public RestResponse<List<ChildrenCategoryDto>> childrenCategoryList(){
        return RestResponse.ok(courseCategoryService.fetchChildrenCategoryList());
    }

    /**
     * 新增课程分类服务接口
     */
    @ApiOperation("新增课程分类服务接口")
    @AuditLog(title = "课程分类服务接口",desc = "新增课程分类服务接口",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("course:category:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CourseCategoryAddParam courseCategoryAddParam) {
        return RestResponse.ok(courseCategoryService.saveByParam(courseCategoryAddParam));
    }

    /**
     * 修改课程分类服务接口
     */
    @ApiOperation(value="修改课程分类服务接口")
    @AuditLog(title = "课程分类服务接口",desc = "修改课程分类服务接口",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("course:category:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CourseCategoryEditParam courseCategoryEditParam) {
        return RestResponse.ok(courseCategoryService.updateByParam(courseCategoryEditParam));
    }

    /**
     * 修改课程分类服务接口
     */
    @ApiOperation(value="批量修改课程分类菜单服务接口")
    @AuditLog(title = "课程分类服务接口",desc = "修改课程分类服务接口",businessType = BusinessType.UPDATE)
    @PutMapping("batchUpdateCategoryMenu")
    //@Permission("course:category:update")
    public RestResponse<Boolean> batchUpdateCategoryMenu(@RequestBody List<CategoryMenuParam> menuList) {
        return RestResponse.ok(courseCategoryService.batchUpdateCategoryMenu(menuList));
    }

    /**
     * 根据id删除课程分类服务接口
     */
    @ApiOperation(value="根据id删除课程分类服务接口")
    @AuditLog(title = "课程分类服务接口",desc = "课程分类服务接口",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("course:category:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(courseCategoryService.removeById(id));
    }

    /**
     * 根据id获取课程分类服务接口
     */
    @ApiOperation(value = "根据id获取课程分类服务接口")
    @GetMapping(value = "/{id}")
    //@Permission("course:category:list")
    public RestResponse<CourseCategoryDto> get(@PathVariable String id) {
        return RestResponse.ok(courseCategoryService.getDtoById(id));
    }

}
