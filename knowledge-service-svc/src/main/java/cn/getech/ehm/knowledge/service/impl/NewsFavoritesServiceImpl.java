package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.entity.NewsFavorites;
import cn.getech.ehm.knowledge.mapper.NewsFavoritesMapper;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import cn.getech.ehm.knowledge.service.INewsFavoritesService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesQueryParam;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesAddParam;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesEditParam;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesParamMapper;
import cn.getech.ehm.knowledge.dto.newsfavorites.NewsFavoritesDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.UserAnalysisClient;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 * 新闻资讯收藏 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-26
 */
@Slf4j
@Service
public class NewsFavoritesServiceImpl extends BaseServiceImpl<NewsFavoritesMapper, NewsFavorites> implements INewsFavoritesService {

    @Autowired
    private NewsFavoritesParamMapper newsFavoritesParamMapper;
    @Autowired
    private NewsFavoritesMapper newsFavoritesMapper;

    @Autowired
    private UserAnalysisClient userAnalysisClient;

    @Override
    public PageResult<NewsFavoritesDto> pageDto(NewsFavoritesQueryParam newsFavoritesQueryParam) {
        Wrapper<NewsFavorites> wrapper = getPageSearchWrapper(newsFavoritesQueryParam);
        PageResult<NewsFavoritesDto> result = newsFavoritesParamMapper.pageEntity2Dto(page(newsFavoritesQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(NewsFavoritesAddParam newsFavoritesAddParam) {
        NewsFavorites newsFavorites = newsFavoritesParamMapper.addParam2Entity(newsFavoritesAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsFavorites);
        return save(newsFavorites);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(NewsFavoritesEditParam newsFavoritesEditParam) {
        NewsFavorites newsFavorites = newsFavoritesParamMapper.editParam2Entity(newsFavoritesEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,newsFavorites);
        return updateById(newsFavorites);
    }

    @Override
    public void updateFavorites(){
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<NewsFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(NewsFavorites::getUid, currentUser.getUid());
        Integer favorites = this.baseMapper.selectCount(queryWrapper);
        UpdateFavoritesParam updateFavoritesParam = new UpdateFavoritesParam();
        updateFavoritesParam.setFavorites(favorites);
        updateFavoritesParam.setKnowledgeType(KnowledgeType.NEWS.getCode());
        userAnalysisClient.updateFavorites(updateFavoritesParam);
    }

    @Override
    public NewsFavoritesDto getDtoById(String id) {
        return newsFavoritesParamMapper.entity2Dto((NewsFavorites) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<NewsFavoritesDto> rows) {
        return saveBatch(newsFavoritesParamMapper.dtoList2Entity(rows));
    }

    @Override
    public boolean favorites(String newsId) {
        String uid = PorosContextHolder.getCurrentUser().getUid();
        LambdaQueryWrapper<NewsFavorites> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(NewsFavorites::getNewsId, newsId);
        wrapper.eq(NewsFavorites::getUid, uid);
        NewsFavorites newsFavorites = newsFavoritesMapper.selectOne(wrapper);
        if (null == newsFavorites){
            newsFavorites = new NewsFavorites();
            newsFavorites.setNewsId(newsId);
            newsFavorites.setUid(uid);
            boolean saveSuccess = save(newsFavorites);
            updateFavorites();
            return saveSuccess;
        }
        boolean remove = removeById(newsFavorites.getId());
        updateFavorites();
        return !remove;
    }

    @Override
    public boolean isFavorite(String courseId) {
        if (StringUtils.isBlank(courseId)){
            return false;
        }
        LambdaQueryWrapper<NewsFavorites> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(NewsFavorites::getNewsId, courseId);
        wrapper.eq(NewsFavorites::getUid, PorosContextHolder.getCurrentUser().getUid());
        Integer selectCount = newsFavoritesMapper.selectCount(wrapper);
        return selectCount > 0;
    }

    private Wrapper<NewsFavorites> getPageSearchWrapper(NewsFavoritesQueryParam newsFavoritesQueryParam) {
        LambdaQueryWrapper<NewsFavorites> wrapper = Wrappers.lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(NewsFavorites.class)){
            wrapper.orderByDesc(NewsFavorites::getUpdateTime,NewsFavorites::getCreateTime);
        }
        return wrapper;
    }
}
