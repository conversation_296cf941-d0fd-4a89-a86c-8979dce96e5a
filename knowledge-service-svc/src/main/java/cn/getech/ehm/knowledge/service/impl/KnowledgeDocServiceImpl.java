package cn.getech.ehm.knowledge.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.knowledge.entity.KnowledgeDoc;
import cn.getech.ehm.knowledge.entity.KnowledgeDocFavorites;
import cn.getech.ehm.knowledge.dto.knowledgedoc.*;
import cn.getech.ehm.knowledge.mapper.CategoryKnowledgeDocMapper;
import cn.getech.ehm.knowledge.mapper.KnowledgeDocMapper;
import cn.getech.ehm.knowledge.service.IKnowledgeDocAccessibleService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.dto.common.KnowledgeCategoryDto;
import cn.getech.ehm.knowledge.service.IKnowledgeDocFavoritesService;
import cn.getech.ehm.knowledge.service.IKnowledgeDocService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <pre>
 * 知识学习库 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class KnowledgeDocServiceImpl extends BaseServiceImpl<KnowledgeDocMapper, KnowledgeDoc> implements IKnowledgeDocService {

    @Autowired
    private KnowledgeDocParamMapper knowledgeDocParamMapper;
    @Autowired
    private CategoryKnowledgeDocMapper categoryKnowledgeDocMapper;
    @Autowired
    private IKnowledgeDocFavoritesService knowledgeDocFavoritesService;
    @Autowired
    private IKnowledgeDocAccessibleService docAccessibleService;

    @Override
    public PageResult<KnowledgeDocDto> pageDto(KnowledgeDocQueryParam knowledgeDocQueryParam) {
        List<CategoryMenuDto> allCategoryKnowledgeDocDtos = categoryKnowledgeDocMapper.findAllCategoryKnowledgeDoc(0);
        Wrapper<KnowledgeDoc> wrapper = getPageSearchWrapper(knowledgeDocQueryParam);
        PageResult<KnowledgeDocDto> result = knowledgeDocParamMapper.pageEntity2Dto(page(knowledgeDocQueryParam, wrapper));
        if ( result.getRecords().size()!=0 && allCategoryKnowledgeDocDtos.size() != 0 ){
            Map<String, String> map = new HashMap();
            for (CategoryMenuDto dto : allCategoryKnowledgeDocDtos){
                generateCategoryStr(map, dto, "");
            }
            List<String> docIds = result.getRecords().stream().map(KnowledgeDocDto::getId).distinct().collect(Collectors.toList());
            Map<String, List<String>> roleIdMap = docAccessibleService.getDocAccessMap(docIds);
            result.getRecords().forEach(dto -> {
                dto.setCategoryName(map.get(dto.getCategoryId()));
                dto.setRoleIds(roleIdMap.get(dto.getId()));
            });
        }
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<KnowledgeDocDto> appPageList(KnowledgeDocQueryParam knowledgeDocQueryParam) {
        //app过滤权限
        knowledgeDocQueryParam.setIsClient(StaticValue.ONE);
        Wrapper<KnowledgeDoc> wrapper = getPageSearchWrapper(knowledgeDocQueryParam);
        PageResult<KnowledgeDocDto> result = knowledgeDocParamMapper.pageEntity2Dto(page(knowledgeDocQueryParam, wrapper));

        Map<String, KnowledgeDocDto> map = new HashMap();
        List<String> knowledgeDocIdList = new ArrayList();
        for (KnowledgeDocDto docDto : result.getRecords()){
            docDto.setIsFavorite(false);
            map.put(docDto.getId(), docDto);
            knowledgeDocIdList.add(docDto.getId());
        }

        // 查找出哪些为收藏知识文档
        if (CollectionUtils.isNotEmpty(knowledgeDocIdList)){
            List<String> likeKnowledgeDocIdList = knowledgeDocFavoritesService.getCurrUserFavoritesKnowledge(knowledgeDocIdList);
            if (CollectionUtils.isNotEmpty(likeKnowledgeDocIdList)){
                for (String likeCourseId : likeKnowledgeDocIdList){
                    map.get(likeCourseId).setIsFavorite(true);
                }
            }
        }
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<KnowledgeDocDto> pageCustomDto(KnowledgeDocQueryParam knowledgeDocQueryParam) {
        List<CategoryMenuDto> allCategoryKnowledgeDocDtos = categoryKnowledgeDocMapper.findAllCategoryKnowledgeDoc(0);
        knowledgeDocQueryParam.setIsClient(StaticValue.ONE);
        Wrapper<KnowledgeDoc> wrapper = getPageSearchWrapper(knowledgeDocQueryParam);
        PageResult<KnowledgeDocDto> result = knowledgeDocParamMapper.pageEntity2Dto(page(knowledgeDocQueryParam, wrapper));


        if ( result.getRecords().size()!=0 && allCategoryKnowledgeDocDtos.size() != 0 ){
            Map<String, String> map = new HashMap();
            for (CategoryMenuDto dto : allCategoryKnowledgeDocDtos){
                generateCategoryStr(map, dto, "");
            }
            result.getRecords().forEach(dto -> dto.setCategoryName(map.get(dto.getCategoryId())));
        }


        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @Override
    public PageResult<KnowledgeDocDto> appAllFavoritesPageList(KnowledgeDocQueryParam knowledgeDocQueryParam) {
        List<String> currUserFavoritesKnowledge = knowledgeDocFavoritesService.getCurrUserFavoritesKnowledge();
        PageResult<KnowledgeDocDto> result = new PageResult();
        if (currUserFavoritesKnowledge.size() != 0){
            LambdaQueryWrapper<KnowledgeDoc> wrapper = Wrappers.lambdaQuery();
            wrapper.in(KnowledgeDoc::getId,currUserFavoritesKnowledge);
            result = knowledgeDocParamMapper.pageEntity2Dto(page(knowledgeDocQueryParam, wrapper));
            result.getRecords().forEach( dto ->{
                dto.setIsFavorite(true);
            });
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(KnowledgeDocAddParam studyKnowledgeDocAddParam) {
        KnowledgeDoc studyKnowledgeDoc = knowledgeDocParamMapper.addParam2Entity(studyKnowledgeDocAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,studyKnowledgeDoc);
        return save(studyKnowledgeDoc);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(KnowledgeDocEditParam studyKnowledgeDocEditParam) {
        KnowledgeDoc studyKnowledgeDoc = knowledgeDocParamMapper.editParam2Entity(studyKnowledgeDocEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,studyKnowledgeDoc);
        return updateById(studyKnowledgeDoc);
    }

    @Override
    public boolean removeByIds(String[] ids) {
        return removeByIds(Arrays.stream(ids).collect(Collectors.toList()));
    }

    @Override
    public List<KnowledgeCategoryDto> listKnowledgeCategoryDto() {
        // 此查询大概在10条以内
        List<CategoryMenuDto> allCategoryKnowledgeDoc = categoryKnowledgeDocMapper.findAllCategoryKnowledgeDoc(0);
        List<KnowledgeCategoryDto> knowledgeCategoryDtos = new LinkedList<>();

        allCategoryKnowledgeDoc.forEach(dto -> {
            LambdaQueryWrapper<KnowledgeDoc> wrapper = Wrappers.lambdaQuery();
            // 筛选公开 和 非公开的指定的课程
            List<String> docList = docAccessibleService.listCurrentDocIds();
            if(CollectionUtils.isNotEmpty(docList)){
                wrapper.and( w -> w.eq(KnowledgeDoc::getIsPublic,1)
                        .or()
                        .in(KnowledgeDoc::getId, docList));
            }else{
                wrapper.eq(KnowledgeDoc::getIsPublic,1);
            }
            KnowledgeCategoryDto knowledgeCategoryDto = new KnowledgeCategoryDto();
            KnowledgeDocQueryParam knowledgeDocQueryParam = new KnowledgeDocQueryParam();
            knowledgeDocQueryParam.setCategoryId(dto.getId());
            Integer count = genrateCategoryChildrenWrapperAndReturnChildrenCount(knowledgeDocQueryParam, wrapper,dto);

            knowledgeCategoryDto.setCategoryId(dto.getId());
            knowledgeCategoryDto.setType(KnowledgeType.DOC.getCode());
            knowledgeCategoryDto.setTotal(count(wrapper));
            knowledgeCategoryDto.setChildren(count);
            knowledgeCategoryDto.setName(dto.getName());
            knowledgeCategoryDtos.add(knowledgeCategoryDto);
        });

        return knowledgeCategoryDtos;
    }

    @Override
    public boolean favorites(String kownledgeDocId) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<KnowledgeDocFavorites> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeDocFavorites::getKnowledgeDocId,kownledgeDocId);
        queryWrapper.eq(KnowledgeDocFavorites::getUid,currentUser.getUid());
        KnowledgeDocFavorites knowledgeDocFavorites = (KnowledgeDocFavorites) knowledgeDocFavoritesService.getOne(queryWrapper);
        if (knowledgeDocFavorites != null) {
            boolean remove = knowledgeDocFavoritesService.remove(queryWrapper);
            knowledgeDocFavoritesService.updateFavorites();
            return !remove;
        }
        knowledgeDocFavorites = new KnowledgeDocFavorites();
        knowledgeDocFavorites.setKnowledgeDocId(kownledgeDocId);
        knowledgeDocFavorites.setUid(currentUser.getUid());
        boolean saveSuccess = knowledgeDocFavoritesService.save(knowledgeDocFavorites);
        knowledgeDocFavoritesService.updateFavorites();
        return saveSuccess;
    }


    @Override
    public KnowledgeDocDto getDtoById(String id) {
        return knowledgeDocParamMapper.entity2Dto((KnowledgeDoc) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<KnowledgeDocDto> rows) {
        return saveBatch(knowledgeDocParamMapper.dtoList2Entity(rows));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean batchEmpower(DocEmpowerParam param){
        LambdaUpdateWrapper<KnowledgeDoc> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(KnowledgeDoc::getId, param.getKnowledgeDocIds());
        wrapper.set(KnowledgeDoc::getIsPublic, param.getIsPublic());
        Boolean flag = this.update(wrapper);

        docAccessibleService.deleteByDocIds(param.getKnowledgeDocIds());
        if(CollectionUtils.isNotEmpty(param.getRoleIds())){
            docAccessibleService.saveDtoBatch(param.getKnowledgeDocIds(), param.getRoleIds());
        }
        return flag;
    }

    private Wrapper<KnowledgeDoc> getPageSearchWrapper(KnowledgeDocQueryParam queryParam) {
        LambdaQueryWrapper<KnowledgeDoc> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(queryParam.getCategoryId())){
            List<CategoryMenuDto> menuList = categoryKnowledgeDocMapper.getCategoryChildren(0, queryParam.getCategoryId());
            List<String> idList = new LinkedList<>();
            idList.add(queryParam.getCategoryId());
            initIdList(menuList,idList);
            wrapper.in(CollectionUtils.isNotEmpty(idList), KnowledgeDoc::getCategoryId, idList);
        }

        wrapper.like(StringUtils.isNotBlank(queryParam.getKeyword()), KnowledgeDoc::getName, queryParam.getKeyword());

        wrapper.like(StringUtils.isNotBlank(queryParam.getName()),
                     KnowledgeDoc::getName, queryParam.getName());

        if( queryParam.getIsClient() == StaticValue.ONE){
            // 筛选公开 和 非公开的指定的课程
            List<String> docList = docAccessibleService.listCurrentDocIds();
            if(CollectionUtils.isNotEmpty(docList)){
                wrapper.and( w -> w.eq(KnowledgeDoc::getIsPublic,1)
                        .or()
                        .in(KnowledgeDoc::getId, docList));
            }else{
                wrapper.eq(KnowledgeDoc::getIsPublic,1);
            }
        }

        if(BaseEntity.class.isAssignableFrom(KnowledgeDoc.class)){
            wrapper.orderByDesc(KnowledgeDoc::getUpdateTime, KnowledgeDoc::getCreateTime);
        }
        return wrapper;
    }

    /**
     * 递归遍历 List<CategoryMenuDto>
     * @param menuParamList
     * @param categoryList
     */
    private void initIdList(List<CategoryMenuDto> menuParamList, List<String> categoryList){
        for (CategoryMenuDto menuDto : menuParamList){
            categoryList.add(menuDto.getId());
            if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
                initIdList(menuDto.getChildren(), categoryList);
            }
        }
    }

    /**
     * 组装QueryWrapper 和 返回 子分类的数量（最后一列）
     * @param knowledgeDocQueryParam
     * @param wrapper
     * @param dto
     * @return
     */
    private Integer genrateCategoryChildrenWrapperAndReturnChildrenCount(KnowledgeDocQueryParam knowledgeDocQueryParam, LambdaQueryWrapper<KnowledgeDoc> wrapper, CategoryMenuDto dto) {

        if (StringUtils.isNotEmpty(knowledgeDocQueryParam.getCategoryId()) && dto != null){
            List<String> ids = new LinkedList<>();
            // 整理数量
            if (CollectionUtils.isNotEmpty(dto.getChildren())){
                for (CategoryMenuDto e : dto.getChildren()){
                    initChildrenMenuNameList(e, dto.getName(), ids);
                }
                ids.add(dto.getId());
                wrapper.in(CollectionUtils.isNotEmpty(ids), KnowledgeDoc::getCategoryId, ids);
                return ids.size() -1 ;
            } else {
                ids.add(dto.getId());
                wrapper.in(CollectionUtils.isNotEmpty(ids), KnowledgeDoc::getCategoryId, ids);
                return 0;
            }
        }
        return 0;
    }


    /**
     * 生成 Map 的分类列表
     * 例如： A - B - C
     * 只生成为叶子节点的列
     * @return
     */
    private void generateCategoryStr(Map<String, String> map, CategoryMenuDto treeBean, String categoryStr){
        StringBuffer categoryStrBuffer = new StringBuffer().append(categoryStr);
        categoryStrBuffer.append("-"+ treeBean.getName());
        map.put(treeBean.getId(), categoryStrBuffer.toString().substring(1, categoryStrBuffer.length()));
        if (CollectionUtils.isNotEmpty(treeBean.getChildren())) {
            for (CategoryMenuDto doc : treeBean.getChildren()) {
                generateCategoryStr(map, doc, categoryStrBuffer.toString());
            }
        }
    }


    /**
     * 根据菜单列表处理子类菜单名
     * 例如： A - B - C
     * 只生成为叶子节点的列
     * @return
     */
    private void initChildrenMenuNameList(CategoryMenuDto menuDto, String parentName,List<String> result){
        String name = StringUtils.isNotEmpty(parentName) ? parentName + "-" + menuDto.getName() : menuDto.getName();
        if (CollectionUtils.isNotEmpty(menuDto.getChildren())){
            for (CategoryMenuDto dto : menuDto.getChildren()){
                initChildrenMenuNameList(dto, name, result);
            }
            result.add(menuDto.getId());
        } else {
            result.add(menuDto.getId());
        }
    }
}
