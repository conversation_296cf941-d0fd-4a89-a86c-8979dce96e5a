package cn.getech.ehm.knowledge.mapper;

import cn.getech.ehm.knowledge.dto.common.CategoryMenuDto;
import cn.getech.ehm.knowledge.entity.CourseCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 课程分类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Repository
public interface CourseCategoryMapper extends BaseMapper<CourseCategory> {

    List<CategoryMenuDto> fetchCategoryMenuList();

    List<CategoryMenuDto> fetchItemMenuByParentId(String parentId);

}
