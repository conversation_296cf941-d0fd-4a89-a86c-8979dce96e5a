package cn.getech.ehm.knowledge.service;

import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentAddParam;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentDto;
import cn.getech.ehm.knowledge.dto.coursecomment.CourseCommentQueryParam;
import cn.getech.ehm.knowledge.entity.CourseComment;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 课程评论信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface ICourseCommentService extends IBaseService<CourseComment> {

        /**
         * 分页查询，返回Dto
         *
         * @param courseCommentQueryParam
         * @return
         */
        PageResult<CourseCommentDto> pageDto(CourseCommentQueryParam courseCommentQueryParam);

        /**
         * 保存
         * @param courseCommentAddParam
         * @return
         */
        boolean saveByParam(CourseCommentAddParam courseCommentAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        CourseCommentDto getDtoById(String id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<CourseCommentDto> rows);

        /**
         * 删除记录
         * @param id
         * @return
         */
        boolean removeById(String id);

}