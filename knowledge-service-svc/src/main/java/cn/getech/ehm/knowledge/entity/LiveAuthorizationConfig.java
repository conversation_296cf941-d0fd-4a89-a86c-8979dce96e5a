package cn.getech.ehm.knowledge.entity;

import cn.getech.ehm.common.enums.LiveType;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * <p>
 * 直播权限配置
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_authorization_config")
public class LiveAuthorizationConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 频道ID
     */
    @TableField("channel_id")
    private Long channelId;

    /**
     * 直播类型
     * 0:公开
     * 1:授权观看
     */
    private LiveType type;

    /**
     * 授权用户uids
     */
    @TableField(value="uids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] uids = new String[]{};

}
