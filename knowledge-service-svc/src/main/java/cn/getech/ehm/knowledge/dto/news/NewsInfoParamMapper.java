package cn.getech.ehm.knowledge.dto.news;

import cn.getech.ehm.knowledge.entity.NewsInfo;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.knowledge.dto.NewsInfoDto;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 新闻资讯信息表 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-05
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  NewsInfoParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param newsInfoAddParam
     * @return
     */
    NewsInfo addParam2Entity(NewsInfoAddParam newsInfoAddParam);

    /**
     * 编辑参数转换为实体
     * @param newsInfoEditParam
     * @return
     */
    NewsInfo editParam2Entity(NewsInfoEditParam newsInfoEditParam);

    /**
     * 实体转换为Dto
     * @param newsInfo
     * @return
     */
    NewsInfoDto entity2Dto(NewsInfo newsInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<NewsInfoDto> pageEntity2Dto(PageResult<NewsInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<NewsInfo> dtoList2Entity(List<NewsInfoDto> rows);

}
