<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.knowledge.mapper.CourseInfoMapper">

    <update id="addLikeCount">
		update knowledge_course_info set like_count=like_count + 1 where id=#{id}
    </update>

    <update id="deleteLikeCount">
		update knowledge_course_info set like_count=like_count - 1 where id=#{id}
    </update>

    <update id="addLearningCount">
		update knowledge_course_info set learning_count=learning_count + 1 where id=#{id}
    </update>

    <update id="addCommentCount">
        UPDATE knowledge_course_info SET comment_count = comment_count + #{count}
        WHERE id = #{id}
    </update>

    <select id="sumCoursePlayCount" resultType="Integer">
        select sum(learning_count) from knowledge_course_info where deleted = 0
    </select>

</mapper>
