<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.knowledge.mapper.NewsInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.knowledge.entity.NewsInfo">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="title" property="title" />
        <result column="category_id" property="categoryId" />
        <result column="summary" property="summary" />
        <result column="content" property="content" />
        <result column="publish_time" property="publishTime" />
        <result column="sort_number" property="sortNumber" />
        <result column="scan_count" property="scanCount" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        update_by,
        create_time,
        update_time,
        tenant_id, title, category_id, summary, content, publish_time, sort_number, scan_count, deleted
    </sql>

    <select id="fetchUserFavoritesList" resultType="cn.getech.ehm.knowledge.dto.NewsInfoDto">
        SELECT ni.* FROM knowledge_news_info ni
        LEFT JOIN knowledge_news_favorites nf
        ON ni.id = nf.news_id
        WHERE ni.deleted = 0 AND nf.deleted = 0
        AND nf.uid = #{uid}
    </select>

</mapper>
