<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.knowledge.mapper.NewsCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.knowledge.entity.NewsCategory">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="update_by" property="updateBy" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="is_leaf" property="isLeaf" />
        <result column="sort_number" property="sortNumber" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        name, parent_id, is_leaf, sort_number, deleted
    </sql>

    <resultMap id="recursionItemMenuMap" type="cn.getech.ehm.knowledge.dto.common.CategoryMenuDto">
        <id column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="id" property="id"/>
        <collection property="children" ofType="cn.getech.ehm.knowledge.dto.common.CategoryMenuDto"
                    column="{deleted=deleted,parentId=id}" select="fetchItemMenuByParentId"/>
    </resultMap>

    <select id="fetchCategoryMenuList" resultMap="recursionItemMenuMap">
        SELECT * FROM knowledge_news_category
        WHERE parent_id = ''
        AND deleted = 0
        ORDER BY `sort_number` ASC
    </select>

    <select id="fetchItemMenuByParentId" resultMap="recursionItemMenuMap">
        SELECT * FROM knowledge_news_category
        WHERE parent_id = #{parentId}
        AND deleted = 0
        ORDER BY `sort_number` ASC
    </select>

</mapper>
