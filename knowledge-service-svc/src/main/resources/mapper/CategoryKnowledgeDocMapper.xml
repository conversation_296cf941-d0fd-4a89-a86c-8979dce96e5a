<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.knowledge.mapper.CategoryKnowledgeDocMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.knowledge.entity.CategoryKnowledgeDoc">
        <result column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="update_by" property="updateBy" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="sort_number" property="sortNumber" />
        <result column="deleted" property="deleted" />
        <result column="is_leaf" property="isLeaf" />
    </resultMap>

    <resultMap id="CategoryTree" type="cn.getech.ehm.knowledge.dto.common.CategoryMenuDto">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="is_leaf" property="isLeaf" />
        <collection property="children" ofType="cn.getech.ehm.knowledge.dto.common.CategoryMenuDto" column="{parentId=id,deleted=deleted}" select="getCategoryChildren"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        name, parent_id, sort_number, deleted, is_leaf
    </sql>

    <sql id="select_tree_list">
        id,
        name,
        parent_id,
        is_leaf,
        deleted,
        update_by,
        create_by,
        update_time,
        create_time
    </sql>

    <select id="findAllCategoryKnowledgeDoc" resultMap="CategoryTree">
        SELECT
            <include refid="select_tree_list" />
        FROM
            knowledge_category_knowledge_doc
        WHERE
        (parent_id IS NULL OR parent_id = '')
        <if test="deleted != null">
            AND
            deleted = #{deleted}
        </if>
        ORDER BY name ASC
    </select>



    <select id="getCategoryChildren" resultMap="CategoryTree">
        SELECT
            <include refid="select_tree_list" />
        FROM
            knowledge_category_knowledge_doc
        WHERE
            parent_id= #{parentId}
            <if test="deleted != null">
                AND
                deleted = #{deleted}
            </if>
        ORDER BY name ASC
    </select>


</mapper>
