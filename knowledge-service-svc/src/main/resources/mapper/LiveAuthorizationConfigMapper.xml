<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.knowledge.mapper.LiveAuthorizationConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.knowledge.entity.LiveAuthorizationConfig">
        <result column="id" property="id" />
        <result column="channel_id" property="channelId" />
        <result column="type" property="type" />
        <result column="uids" property="uids" typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        channel_id, type, uids
    </sql>

</mapper>
