feign_error         = remote connection failed
name_exists         = name already exists
node_name_exists    = the name under the parent node already exists
obj_has_datasource  = the object is bound to a data source
objs_has_datasource = the object {0} is bound to a data source
obj_has_children    = the object has child nodes
objs_has_children   = the object {0} has child nodes
has_parameter       = the object has parameters
value_exists        = value already exists
code_exists         = code already exists
codes_exists        = codes {0} already exists
code_build          = code generation failed
process_error       = call process failed
find_info_error     = getting information failure
referenced          = object is referenced    
objects_referenced  = object {0} is referenced    
type_error          = wrong object type 
check_error         = verification failed
has_bind            = the data source is already bound
identifier_exists   = identifier already exists
file_gen_error      = failed to generate file
not_find_person     = {0} has no maintenance personnel specified
update_error        = update failed
insert_error        = insert failed
update_user_error   = update user failed
user_error          = failed to find user information
data_null_error     = data is empty


interval_valid      = interval value cannot be blank
date_valid          = date value cannot be blank
month_valid         = month value cannot be blank
week_valid          = week value cannot be blank
uid_registered      = uid registered
mobile_registered   = mobile number registered
email_registered    = email registered
part_num_error     = the number of spare parts of this model{0} is insufficient

validator.constraints.null.message=can not be null
validator.constraints.length.message=the length is illegal