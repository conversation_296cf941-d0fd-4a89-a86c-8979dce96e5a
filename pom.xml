<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>poros-parent</artifactId>
        <groupId>cn.getech.poros</groupId>
        <version>geek-0.9.5</version>
    </parent>
    <groupId>cn.getech.ehm</groupId>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>workbench-service</artifactId>
    <version>2.2-tbea-SNAPSHOT</version>

    <properties>
        <poros.framework.version>geek-0.9.5</poros.framework.version>
        <ehm-common.version>2.1.3-SNAPSHOT</ehm-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.getech.ehm</groupId>
                <artifactId>ehm-common</artifactId>
                <version>${ehm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-common</artifactId>
                <version>geek-1.0.1</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-codegen</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-gateway</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://10.74.20.125:8081/repository/mcloud-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://10.74.20.125:8081/repository/mcloud-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


<modules>  <module>workbench-service-api</module>
    <module>workbench-service-svc</module>
  </modules>
</project>
