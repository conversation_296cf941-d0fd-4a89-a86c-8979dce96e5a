<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>poros-parent</artifactId>
        <groupId>cn.getech.poros</groupId>
        <version>geek-0.9.5</version>
    </parent>
    <groupId>cn.getech.ehm.scada</groupId>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>scada-service</artifactId>
    <version>2.2-tbea-SNAPSHOT</version>

    <properties>
        <poros.framework.version>geek-0.9.5</poros.framework.version>
        <ehm-common.version>2.1.3-tbea-SNAPSHOT</ehm-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-common</artifactId>
                <version>geek-1.0.1</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-codegen</artifactId>
                <version>${poros.framework.version}</version>
<!--                <version>1.0.1-SNAPSHOT</version>-->
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-gateway</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.ehm</groupId>
                <artifactId>ehm-common</artifactId>
                <version>${ehm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>2.0.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>scada-service-api</module>
        <module>scada-service-svc</module>
    </modules>
</project>
