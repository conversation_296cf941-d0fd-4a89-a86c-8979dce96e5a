```shell script
# 忽略启动的相关配置文件
git update-index --assume-unchanged equipment-service-svc/src/main/resources/bootstrap.yml
git update-index --assume-unchanged equipment-service-svc/src/main/resources/application.yml
git update-index --assume-unchanged equipment-service-svc/src/main/resources/application-dev.yml
# 忽略测试的启动配置文件
git update-index --assume-unchanged equipment-service-svc/src/test/resources/bootstrap.yml
git update-index --assume-unchanged equipment-service-svc/src/test/resources/application.yml
```
测试提交