<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="description">
    <title>没有权限</title>
    <link rel="stylesheet" th:href="@{/webjars/bootstrap/4.4.1/css/bootstrap.min.css}">
    <style>
        .container {
            margin-top: 50px;
            text-align: center;
        }
    </style>
</head>
<body>
<main class="container" role="main">
    <div class="jumbotron">
        <img class="image" th:src="@{/forbidden.png}">
        <h1 class="text-danger">没有权限</h1>
        <p class="lead">
            <svg class="bi bi-alert-circle" fill="currentColor" height="1em" viewBox="0 0 20 20" width="1em"
                 xmlns="http://www.w3.org/2000/svg">
                <path clip-rule="evenodd" d="M10 17a7 7 0 100-14 7 7 0 000 14zm0 1a8 8 0 100-16 8 8 0 000 16z"
                      fill-rule="evenodd"></path>
                <path d="M9.002 13a1 1 0 112 0 1 1 0 01-2 0zM9.1 6.995a.905.905 0 111.8 0l-.35 3.507a.553.553 0 01-1.1 0L9.1 6.995z"></path>
            </svg>
            <span>抱歉,您没有观看该直播的权限,请联系管理员开通权限</span>
        </p>
    </div>
</main>
<script th:src="@{/js.cookie-2.2.1.min.js}"></script>
<script type="application/javascript">
    $(function () {
        const cookies = Cookies.getAll();
        console.log(cookies);
    })
</script>
</body>
</html>