<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.UserActionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.UserAction">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="uid" property="uid"/>
        <result column="login_count" property="loginCount"/>
        <result column="solve_count" property="solveCount"/>
        <result column="favorites" property="favorites"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        uid, login_count, solve_count, favorites, tenant_id, deleted
    </sql>

</mapper>
