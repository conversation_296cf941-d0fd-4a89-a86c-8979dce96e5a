<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.SystemLearningGrandtotalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.SystemLearningGrandtotal">
        <id column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="course_count" property="courseCount"/>
        <result column="play_count" property="playCount"/>
        <result column="engineer_count" property="engineerCount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        remark,
        create_by,
        update_by,
        create_time,
        update_time,
        id, course_count, play_count, engineer_count, tenant_id, deleted
    </sql>

</mapper>
