<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.AppConfigIconMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.AppConfigIcon">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="ios_icon2" property="iosIcon2"/>
        <result column="ios_icon3" property="iosIcon3"/>
        <result column="data_path" property="dataPath"/>
        <result column="sort_number" property="sortNumber"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        name, icon, ios_icon2, ios_icon3, data_path, sort_number, tenant_id, deleted
    </sql>

    <select id="fetchAppConfigIconList" resultType="cn.getech.ehm.system.dto.appconfig.AppIconDto">
        SELECT name, icon, ios_icon2, ios_icon3, data_path
        FROM system_app_config_icon
        WHERE deleted = 0
        ORDER BY sort_number ASC
    </select>

    <select id="getMaxSortNumber" resultType="java.lang.Integer">
        SELECT MAX(sort_number)
        FROM system_app_config_icon
        WHERE deleted = 0
    </select>
    <select id="getAllData" resultType="cn.getech.ehm.system.entity.AppConfigIcon">
        SELECT * FROM system_app_config_icon WHERE tenant_id = 'geek'
    </select>
    <insert id="insertField" parameterType="java.util.List">
        INSERT INTO system_app_config_icon ( id, name,icon, ios_icon2,ios_icon3,data_path,sort_number,
        tenant_id, create_by, create_time, update_by, update_time )
        <foreach collection="entities" open="VALUES" close=";" index="index" item="entity" separator=",">
            (REPLACE(uuid(),'-',''), #{entity.name},#{entity.icon}, #{entity.iosIcon2},#{entity.iosIcon3},
            #{entity.dataPath},#{entity.sortNumber},#{entity.tenantId},
            #{entity.createBy},now(), #{entity.updateBy},now())
        </foreach>
    </insert>
</mapper>
