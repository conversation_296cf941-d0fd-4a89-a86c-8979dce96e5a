<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.UserAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.UserAnalysis">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="uid" property="uid"/>
        <result column="user_name" property="userName"/>
        <result column="phone" property="phone"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="course_favorites" property="courseFavorites"/>
        <result column="news_favorites" property="newsFavorites"/>
        <result column="doc_favorites" property="docFavorites"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        uid, user_name, phone, customer_id, customer_name, course_favorites, news_favorites, doc_favorites, deleted
    </sql>

    <select id="fetchUserAnalysisList" resultType="cn.getech.ehm.system.dto.useranalysis.UserAnalysisDto">
        SELECT
        an.uid, an.user_name, an.phone, an.customer_name, an.course_favorites, an.news_favorites, an.create_time,
        an.doc_favorites, SUM(ac.login_count) AS login_count, SUM(ac.solve_count) AS solve_count
        FROM system_user_analysis an LEFT JOIN system_user_action ac ON an.uid = ac.uid
        <where>
            <if test="queryParam.customerId != null and queryParam.customerId != ''">
                AND an.customer_id = #{queryParam.customerId}
            </if>
            <if test="queryParam.uid != null and queryParam.uid != ''">
                AND an.uid = #{queryParam.uid}
            </if>
            <if test="queryParam.startDate != null">
                AND ac.create_time &gt;= #{queryParam.startDate}
            </if>
            <if test="queryParam.endDate != null">
                AND ac.create_time &lt;= #{queryParam.endDate}
            </if>
            <if test="queryParam.keyword != null and queryParam.keyword != ''">
                AND (
                an.user_name LIKE CONCAT('%', #{queryParam.keyword}, '%')
                OR an.phone LIKE CONCAT('%', #{queryParam.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY an.uid, an.user_name, an.phone, an.customer_name, an.course_favorites,
        an.news_favorites, an.doc_favorites, an.create_time
        ORDER BY solve_count DESC, an.create_time DESC
    </select>

    <select id="getMonthLoginCountMapByUid" resultType="cn.getech.ehm.system.dto.customer.LoginCountDto">
        SELECT an.user_name,an.phone,date_format(ac.create_time,'%Y-%m') as createTime,SUM( ac.login_count ) AS
        login_count
        FROM system_user_analysis an LEFT JOIN system_user_action ac ON an.uid = ac.uid
        <where>
            <if test="queryParam.customerId != null and queryParam.customerId != ''">
                AND an.customer_id = #{queryParam.customerId}
            </if>
            <if test="queryParam.uid != null and queryParam.uid != ''">
                AND an.uid = #{queryParam.uid}
            </if>
            <if test="queryParam.startDate != null">
                AND ac.create_time &gt;= #{queryParam.startDate}
            </if>
            <if test="queryParam.endDate != null">
                AND ac.create_time &lt;= #{queryParam.endDate}
            </if>
        </where>
        GROUP BY an.user_name,an.phone,createTime
        ORDER BY createTime
    </select>

</mapper>
