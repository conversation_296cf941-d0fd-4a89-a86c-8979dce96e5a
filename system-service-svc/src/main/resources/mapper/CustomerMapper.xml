<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.CustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.Customer">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="company" property="company"/>
        <result column="area" property="area"/>
        <result column="city" property="city" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="address" property="address"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="maintenance" property="maintenance"/>
        <result column="uid" property="uid"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        code, name, company, area, city, address, contact_person, contact_phone, maintenance, uid, tenant_id, deleted
    </sql>
    <select id="getMapByEquipmentIds" resultType="cn.getech.ehm.system.dto.EquipmentCustomerDto">
        SELECT cus.id, info.equipment_id equipmentId, cus.name, cus.code
        FROM customer_equipment info
        LEFT JOIN customer cus ON info.customer_id = cus.id
        WHERE
        info.equipment_id IN
        <foreach collection="equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <insert id="saveImportBatch" parameterType="java.util.List">
        INSERT INTO customer(id,code,name,company,area,city,address,lng_lat,contact_person,
        contact_phone,maintenance,tenant_id, create_by, create_time, update_by, update_time)
        VALUES
        <foreach collection="params" index="index" item="param" separator=",">
            (#{param.id},#{param.code},#{param.name},#{param.company},#{param.area},
            #{param.city},#{param.address},#{param.lngLat},#{param.contactPerson},#{param.contactPhone},
            #{param.maintenance},#{param.tenantId},#{param.createBy},now(),#{param.updateBy},now())
        </foreach>
        ON DUPLICATE KEY UPDATE update_time = now()
    </insert>
    <update id="updateCityBatch" parameterType="java.util.List">
        UPDATE customer
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="city =case" suffix="end,">
                <foreach collection="params" item="param" index="index">
                    <if test="param.city!=null">
                        when code=#{param.code} then #{param.city}
                    </if>
                </foreach>
            </trim>
            <trim prefix="address =case" suffix="end,">
                <foreach collection="params" item="param" index="index">
                    <if test="param.address!=null">
                        when code=#{param.code} then #{param.address}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lng_lat =case" suffix="end,">
                <foreach collection="params" item="param" index="index">
                    <if test="param.lngLat!=null">
                        when code=#{param.code} then #{param.lngLat}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="params" separator="or" item="param" index="index">
            code=#{param.code}
        </foreach>
    </update>

    <select id="customerSignCount" resultType="Integer">
        SELECT count(1) FROM customer
        <where>
            uid IS NOT NULL
            AND trim(uid) != ''
        </where>
    </select>

    <select id="orderByCustomerName" resultType="cn.getech.ehm.system.dto.EquipmentCustomerDto">
        SELECT
        c.id, c.`name`, e.equipment_id
        FROM customer_equipment e LEFT JOIN customer c ON e.customer_id = c.id
        WHERE e.equipment_id IN (
        <foreach collection="param.equipmentIdList" item="id" separator=",">
            #{id}
        </foreach>
        )
        AND c.deleted = 0
        ORDER BY c.`name` ${param.sort}
    </select>

</mapper>
