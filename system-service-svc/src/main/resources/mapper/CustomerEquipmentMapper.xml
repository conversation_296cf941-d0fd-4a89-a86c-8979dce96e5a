<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.CustomerEquipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.CustomerEquipment">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="customer_id" property="customerId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        customer_id, equipment_id, tenant_id, deleted
    </sql>

    <select id="getCurrentInfoIdsByCustomerName" resultType="string">
        select equipment_id
        from customer_equipment as ce,
             customer as c
        where (c.name like '%${customerName}%' or c.code like '%${customerName}%')
          and ce.customer_id = c.id
    </select>

    <select id="getEquipmentIdsByCustomerNames" resultType="string">
        select equipment_id
        from customer_equipment as ce,
             customer as c
        where c.name like '%${customerNames}%'
          and ce.customer_id = c.id
    </select>

    <select id="getEquipmentIdsByTwoCustomerNames" resultType="string">
        select equipment_id
        from customer_equipment as ce,
             customer as c
        where c.name like '%${customerName1}%'
          and c.name like '%${customerName2}%'
          and ce.customer_id = c.id
    </select>

</mapper>
