<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.NotificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.Notification">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="recipients" property="recipients" jdbcType="VARCHAR" typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="source" property="source"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
</mapper>
