<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.AppVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.AppVersion">
        <id column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="version" property="version"/>
        <result column="mast_update" property="mastUpdate"/>
        <result column="publish" property="publish"/>
        <result column="version_desc" property="versionDesc"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="attach_name" property="attachName"/>
        <result column="attach_id" property="attachId"/>
        <result column="version_code" property="versionCode"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        id, version, mast_update, publish, version_desc, download_url,
        attach_name, attach_id, version_code, tenant_id, deleted
    </sql>

</mapper>
