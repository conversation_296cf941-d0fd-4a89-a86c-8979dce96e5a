<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.CustomerSalesmanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.CustomerSalesman">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="uid" property="uid"/>
        <result column="status" property="status"/>
        <result column="customer_id" property="customerId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        update_by,
        create_time,
        update_time,
        uid, status, customer_id, tenant_id, deleted
    </sql>

</mapper>
