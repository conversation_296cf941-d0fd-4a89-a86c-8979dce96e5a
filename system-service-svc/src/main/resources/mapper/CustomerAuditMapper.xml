<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.CustomerAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.CustomerAudit">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="customer_id" property="customerId"/>
        <result column="apply_time" property="applyTime"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        name, phone, company, apply_time, status, tenant_id, deleted
    </sql>
    <select id="pageList" resultType="cn.getech.ehm.system.dto.audit.CustomerAuditDto">
        SELECT audit.id,audit.uid,audit.name,audit.email,audit.mobile,audit.customer_id customerId,
        cus.name customerName,audit.apply_time applyTime,audit.status,audit.company,audit.remark
        FROM customer_audit audit
        LEFT JOIN customer cus ON audit.customer_id = cus.id
        <where>
            <if test="null != param.status">
                AND audit.status = #{param.status}
            </if>
            <if test="null != param.keyword and '' != param.keyword">
                AND (
                audit.name LIKE concat('%', #{param.keyword} , '%')
                OR audit.mobile LIKE concat('%', #{param.keyword} , '%')
                OR cus.name LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.customerId and '' != param.customerId">
                AND audit.customer_id = #{param.customerId}
            </if>
            <if test="null != param.usedUids">
                AND audit.uid NOT IN
                <foreach collection="param.usedUids" index="index" item="uid" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
            </if>
        </where>
        ORDER BY audit.update_time DESC
    </select>
    <select id="getList" resultType="cn.getech.ehm.system.dto.audit.CustomerAuditDto">
        SELECT audit.id,audit.uid,audit.name,audit.email,audit.mobile,audit.customer_id customerId,
        cus.name customerName,audit.apply_time applyTime,audit.status,audit.remark
        FROM customer_audit audit
        LEFT JOIN customer cus ON audit.customer_id = cus.id
        <where>
            <if test="null != param.status">
                AND audit.status = #{param.status}
            </if>
            <if test="null != param.keyword and '' != param.keyword">
                AND (
                audit.name LIKE concat('%', #{param.keyword} , '%')
                OR audit.mobile LIKE concat('%', #{param.keyword} , '%')
                OR cus.name LIKE concat('%', #{param.keyword} , '%')
                )
            </if>
            <if test="null != param.customerId and '' != param.customerId">
                AND audit.customer_id = #{param.customerId}
            </if>
        </where>
        ORDER BY audit.update_time DESC
    </select>
</mapper>
