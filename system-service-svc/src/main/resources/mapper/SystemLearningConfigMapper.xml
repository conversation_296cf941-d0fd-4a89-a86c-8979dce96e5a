<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.SystemLearningConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.SystemLearningConfig">
        <id column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="learning_time" property="learningTime"/>
        <result column="online_upCount" property="onlineUpCount"/>
        <result column="online_downCount" property="onlineDownCount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <resultMap id="LearningCountResultMap" type="cn.getech.ehm.system.dto.LearningConfigPersonCountDto">
        <result column="online_up_count" property="onlineUpCount" javaType="Integer" jdbcType="INTEGER"/>
        <result column="online_down_count" property="onlineDownCount" javaType="Integer" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        remark,
        create_by,
        update_by,
        create_time,
        update_time,
        id, learning_time, onlineUpCount, onlineDownCount, tenant_id, deleted
    </sql>


    <select id="sumOnlinePersonCount" resultMap="LearningCountResultMap">
        select sum(online_up_count)   as online_up_count,
               sum(online_down_count) as online_down_count
        from system_learning_config
        where #{endTime} >= learning_time
          and learning_time >= #{startTime}
          and deleted = 0
    </select>

</mapper>
