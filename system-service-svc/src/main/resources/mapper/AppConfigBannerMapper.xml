<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.AppConfigBannerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.AppConfigBanner">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="name" property="name"/>
        <result column="img_url" property="imgUrl"/>
        <result column="href_type" property="hrefType"/>
        <result column="news_id" property="newsId"/>
        <result column="news_title" property="newsTitle"/>
        <result column="news_url" property="newsUrl"/>
        <result column="publish_time" property="publishTime"/>
        <result column="sort_number" property="sortNumber"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        name, img_url, href_type, news_id, news_title, news_url, publish_time,
        sort_number, tenant_id, deleted, remark
    </sql>

    <select id="fetchAppBannerList" resultType="cn.getech.ehm.system.dto.appconfig.AppBannerDto">
        SELECT name, img_url, href_type, news_id, news_url
        FROM system_app_config_banner
        WHERE deleted = 0
        ORDER BY sort_number ASC
    </select>

</mapper>
