<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.system.mapper.AppConfigDataTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.system.entity.AppConfigDataType">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="name" property="name"/>
        <result column="path" property="path"/>
        <result column="icon" property="icon"/>
        <result column="module_type" property="moduleType"/>
        <result column="sort_number" property="sortNumber"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        update_by,
        create_by,
        update_time,
        create_time,
        name, path, icon, module_type, sort_number, deleted
    </sql>

    <select id="fetchAppDataTypeList" resultType="cn.getech.ehm.system.dto.AppDataTypeDto">
        SELECT name, path, icon, module_type, sort_number
        FROM system_app_config_data_type
        WHERE deleted = 0
        ORDER BY sort_number ASC
    </select>
    <select id="getAllData" resultType="cn.getech.ehm.system.entity.AppConfigDataType">
        SELECT * FROM system_app_config_data_type WHERE tenant_id = 'geek'
    </select>
    <insert id="insertField" parameterType="java.util.List">
        INSERT INTO system_app_config_data_type ( id, name,path,icon, module_type,sort_number,deleted,
        tenant_id, create_by, create_time, update_by, update_time )
        <foreach collection="entities" open="VALUES" close=";" index="index" item="entity" separator=",">
            (REPLACE(uuid(),'-',''), #{entity.name},#{entity.path},#{entity.icon}, #{entity.moduleType},
            #{entity.sortNumber},#{entity.deleted},#{entity.tenantId},
            #{entity.createBy},now(), #{entity.updateBy},now())
        </foreach>
    </insert>
</mapper>
