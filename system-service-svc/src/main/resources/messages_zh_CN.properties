feign_error         = \u8FDC\u7A0B\u8FDE\u63A5\u5931\u8D25
name_exists         = \u540D\u79F0\u5DF2\u5B58\u5728
node_name_exists    = \u7236\u8282\u70B9\u4E0B\u540D\u79F0\u5DF2\u5B58\u5728
obj_has_datasource  = \u5BF9\u8C61\u5DF2\u7ED1\u5B9A\u6570\u636E\u6E90
objs_has_datasource = \u5BF9\u8C61{0}\u5DF2\u7ED1\u5B9A\u6570\u636E\u6E90
obj_has_children    = \u5BF9\u8C61\u62E5\u6709\u5B50\u8282\u70B9
objs_has_children   = \u5BF9\u8C61{0}\u62E5\u6709\u5B50\u8282\u70B9
has_parameter       = \u5BF9\u8C61\u62E5\u6709\u53C2\u6570
value_exists        = \u503C\u5DF2\u5B58\u5728
code_exists         = \u7F16\u7801\u5DF2\u5B58\u5728
codes_exists        = \u7F16\u7801{0}\u5DF2\u5B58\u5728
code_build          = \u7F16\u7801\u751F\u6210\u5931\u8D25
process_error       = \u8C03\u7528\u6D41\u7A0B\u5931\u8D25
find_info_error     = \u83B7\u53D6\u4FE1\u606F\u5931\u8D25
referenced          = \u5BF9\u8C61\u88AB\u5F15\u7528
objects_referenced  = \u5BF9\u8C61{0}\u88AB\u5F15\u7528
type_error          = \u5BF9\u8C61\u7C7B\u578B\u9519\u8BEF
check_error         = \u6821\u9A8C\u5931\u8D25
has_bind            = \u8BE5\u6570\u636E\u6E90\u5DF2\u88AB\u7ED1\u5B9A
identifier_exists   = \u6807\u8BC6\u7B26\u5DF2\u5B58\u5728
file_gen_error      = \u751F\u6210\u6587\u4EF6\u5931\u8D25
not_find_person     = {0}\u672A\u6307\u5B9A\u7EF4\u62A4\u4EBA\u5458
update_error        = \u66F4\u65B0\u5931\u8D25
insert_error        = \u65B0\u589E\u5931\u8D25
update_user_error   = \u66F4\u65B0\u7528\u6237\u5931\u8D25
user_error          = \u67E5\u627E\u7528\u6237\u4FE1\u606F\u5931\u8D25
data_null_error     = \u6570\u636E\u4E3A\u7A7A


interval_valid      = \u95F4\u9694\u503C\u4E0D\u80FD\u4E3A\u7A7A
date_valid          = \u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
month_valid         = \u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A
week_valid          = \u661F\u671F\u4E0D\u80FD\u4E3A\u7A7A
uid_registered      = uid\u5DF2\u6CE8\u518C
mobile_registered   = \u624B\u673A\u53F7\u5DF2\u6CE8\u518C
email_registered    = \u90AE\u7BB1\u5DF2\u6CE8\u518C

validator.constraints.null.message=\u4E0D\u80FD\u4E3A\u7A7A
validator.constraints.length.message=\u957F\u5EA6\u4E0D\u6B63\u786E

version_error       = \u7248\u672C\u7F16\u7801\u8FC7\u4F4E
version_code_error  = \u7248\u672C\u8FC7\u4F4E
version_released    = \u7248\u672C\u5DF2\u53D1\u5E03
engineer_count_error= \u542F\u7528\u5DE5\u7A0B\u5E08\u6570\u91CF\u5DF2\u8FBE\u4E0A\u9650
info_referenced     = \u8BBE\u5907\u5DF2\u5206\u914D\u7ED9{0}