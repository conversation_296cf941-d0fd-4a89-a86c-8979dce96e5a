package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.notification.NotificationAddParam;
import cn.getech.ehm.system.dto.notification.NotificationDto;
import cn.getech.ehm.system.dto.notification.NotificationQueryParam;
import cn.getech.ehm.system.entity.Notification;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 消息通知
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface INotificationService extends IBaseService<Notification> {

    /**
     * 分页查询，返回Dto
     *
     * @param notificationQueryParam
     * @return
     */
    PageResult<NotificationDto> pageDto(NotificationQueryParam notificationQueryParam);

    /**
     * 保存
     *
     * @param notificationAddParam
     * @return
     */
    boolean saveByParam(NotificationAddParam notificationAddParam);

    List<Notification> latestMailNotification();
}