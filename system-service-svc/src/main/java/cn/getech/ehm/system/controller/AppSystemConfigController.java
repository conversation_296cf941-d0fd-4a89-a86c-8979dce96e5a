package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.service.ISystemConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * <p>
 * 系统配置信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@RestController
@RequestMapping("/systemConfig")
@Api(tags = "app接口: app启动和登录页面配置")
public class AppSystemConfigController {

    @Autowired
    private ISystemConfigService systemConfigService;

    /**
     * app启动页配置
     */
    @ApiOperation(value = "app启动页配置")
    @GetMapping(value = "appStartImg")
    //@Permission("system:config:list")
    public RestResponse<String> appStartImg() {
        String startImg = "app_start_img";
        String startImgDefault = "app_start_img_default";
        Map<String, SystemConfigDto> configDtoMap = systemConfigService.mapByNames(startImg + "," + startImgDefault);
        if (CollectionUtils.isEmpty(configDtoMap)) {
            return RestResponse.ok("");
        }
        if (configDtoMap.containsKey(startImg)) {
            SystemConfigDto systemConfigDto = configDtoMap.get(startImg);
            if (StringUtils.isNotBlank(systemConfigDto.getCfgValue())) {
                return RestResponse.ok(systemConfigDto.getCfgValue());
            }
        }
        return RestResponse.ok(configDtoMap.get(startImgDefault).getCfgValue());
    }

    /**
     * app登录页配置
     */
    @ApiOperation(value = "app登录页配置", notes = "返回图片url")
    @GetMapping(value = "appLoginImg")
    //@Permission("system:config:list")
    public RestResponse<String> appLoginImg() {
        String loginImg = "app_login_img";
        String loginImgDefault = "app_login_img_default";
        Map<String, SystemConfigDto> configDtoMap = systemConfigService.mapByNames(loginImg + "," + loginImgDefault);
        if (CollectionUtils.isEmpty(configDtoMap)) {
            return RestResponse.ok("");
        }
        if (configDtoMap.containsKey(loginImg)) {
            SystemConfigDto systemConfigDto = configDtoMap.get(loginImg);
            if (StringUtils.isNotBlank(systemConfigDto.getCfgValue())) {
                return RestResponse.ok(systemConfigDto.getCfgValue());
            }
        }
        return RestResponse.ok(configDtoMap.get(loginImgDefault).getCfgValue());
    }
}
