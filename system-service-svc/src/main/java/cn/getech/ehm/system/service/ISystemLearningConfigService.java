package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.learningconfig.*;
import cn.getech.ehm.system.entity.SystemLearningConfig;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.LearningConfigPersonCountDto;
import cn.getech.ehm.system.dto.TimeQueryParam;

import java.util.List;

/**
 * <pre>
 * 培训认证表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public interface ISystemLearningConfigService extends IBaseService<SystemLearningConfig> {

    /**
     * 分页查询，返回Dto
     *
     * @param systemLearningConfigQueryParam
     * @return
     */
    PageResult<SystemLearningConfigDto> pageDto(SystemLearningConfigQueryParam systemLearningConfigQueryParam);

    /**
     * 保存
     *
     * @param systemLearningAddParam
     * @return
     */
    boolean saveByParam(SystemLearningAddParam systemLearningAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    SystemLearningConfigDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<SystemLearningConfigDto> rows);

    /**
     * 更新
     *
     * @param systemLearningConfigEditParam
     */
    boolean updateByParam(SystemLearningConfigEditParam systemLearningConfigEditParam);

    /**
     * 查询培训认证
     *
     * @return
     */
    SystemLearningDto listLearningConfig();

    /**
     * 统计培训认证的人数
     *
     * @param timeQueryParam
     * @return
     */
    LearningConfigPersonCountDto onLinePersonCount(TimeQueryParam timeQueryParam);

    /**
     * 查询大屏培训认证的信息
     *
     * @return
     */
    SystemLearningGrandtotalDto getScreenLearningConfigInfo();
}