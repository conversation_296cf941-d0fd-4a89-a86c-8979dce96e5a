package cn.getech.ehm.system.notify;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-12-15 15:16:14
 **/
@Slf4j
@Service
public class SmsService {

    @Value("${aliyun.access-key.id}")
    private String accessKeyId;

    @Value("${aliyun.access-key.secret}")
    private String accessKeySecret;

    @Value("${aliyun.region-id}")
    private String regionId;

    @Value("${aliyun.sms.sys.domain}")
    private String sysDomain;

    @Value("${aliyun.sms.sys.version}")
    private String sysVersion;

    @Value("${aliyun.sms.default-sign}")
    private String defaultSign;

    /**
     * 发送短信
     *
     * @param template
     * @param params
     * @param mobiles
     */
    public boolean sendSms(@Nullable String sign, String template, Map<String, String> params, String... mobiles) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(sysDomain);
        request.setSysVersion(sysVersion);
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", String.join(",", mobiles));
        if (null == sign) {
            sign = defaultSign;
        }
        request.putQueryParameter("SignName", sign);
        request.putQueryParameter("TemplateCode", template);
        request.putQueryParameter("TemplateParam", JSON.toJSONString(params));
        try {
            CommonResponse response = client.getCommonResponse(request);
            log.info("发送短信响应:" + response.getData());
            return true;
        } catch (ServerException e) {
            log.error("发送短信失败", e.getErrMsg());
            return false;
        } catch (ClientException e) {
            log.error("发送短信失败", e.getErrMsg());
            return false;
        }
    }

}
