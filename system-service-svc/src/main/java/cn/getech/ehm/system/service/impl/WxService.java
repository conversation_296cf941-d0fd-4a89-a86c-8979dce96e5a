package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.config.WxCpConfiguration;
import cn.getech.ehm.task.enums.TaskIotPushType;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.messagebuilder.TextBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class WxService {
    @Value("${wechat.cp.default.corpId:ww248985dac72abf43}")
    private String corpId;

    @Value("${wechat.cp.default.agentId:1000011}")
    private Integer agentId;

    @Value("${wechat.cp.default.jumpUrl:http://premaint.ehm.heroixinu.com/}")
    private String jumpUrl;

    public String getUserIdByCode(String corpId, Integer agentId, String code) {
        log.info("corpId:{},agentId:{},code:{}", corpId, agentId, code);
        final val wxCpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOAuth2Service oauth2Service = wxCpService.getOauth2Service();
        WxCpOauth2UserInfo userInfo = null;
        try {
            userInfo = oauth2Service.getUserInfo(code);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        log.info("userinfo:{}", JSON.toJSONString(userInfo));
        if (ObjectUtil.isNotEmpty(userInfo)) {
            String userId = userInfo.getUserId();
            return userId;
        } else {
            log.info("userinfo为空");
            return "";
        }
    }

    @SneakyThrows
    public void sendMsg(String title, String content, String url, String[] toUid) {
        final val wxCpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpMessageService messageService = wxCpService.getMessageService();
        WxCpMessage build = WxCpMessage.TEXTCARD().title(title).description(content).url(jumpUrl + url).toUser(String.join("|", toUid)).build();
        messageService.send(build);
    }
}
