package cn.getech.ehm.system.notify.enmu;

/**
 * <AUTHOR>
 * @date 2021-04-08 16:42:10
 **/
public enum DeviceType {

    ANDROID(1, "安卓"),
    IOS(2, "iOS"),
    ALL(0, "所有");

    int code;
    String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    DeviceType(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
