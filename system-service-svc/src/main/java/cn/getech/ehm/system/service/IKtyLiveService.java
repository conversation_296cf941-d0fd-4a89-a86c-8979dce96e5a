package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.ketianyun.KeTianVideoQueryParam;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunLiveInfoDto;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunVideoDto;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunVideoResponse;

import java.util.Map;

/**
 * <pre>
 * 科天直播
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
public interface IKtyLiveService {

    /**
     * 设置token
     *
     * @param token
     * @return
     */
    Boolean setToken(String token);

    /**
     * 登录后重定向
     *
     * @param token
     * @param url
     * @return
     */
    String authLogin(String token, String url);

    /**
     * 获取签名
     *
     * @param object
     * @return
     */
    String getSign(Object object);

    /**
     * 获取直播云属性
     *
     * @param paramMap
     * @return
     */
    String getCloudLiveSign(Map<String, String> paramMap);

    /**
     * 获取当前直播信息
     *
     * @return
     */
    KeTianYunLiveInfoDto getCurrLiveInfo();

    /**
     * 查询直播在线人数
     *
     * @return
     */
    Integer getLiveOnlineCount();

    /**
     * 获取直播状态
     *
     * @param stream
     * @return
     */
    String getLiveStatus(String stream);

    /**
     * 获取WriteToken
     */
    String getWriteToken();

    KeTianYunVideoResponse getKeTianYunVideoList(KeTianVideoQueryParam keTianVideoQueryParam);

    /**
     * 获取科天单条视频信息
     *
     * @param vid
     * @return
     */
    KeTianYunVideoDto getKeTianYunVideoInfo(String vid);
}

