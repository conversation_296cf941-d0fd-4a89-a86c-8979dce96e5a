package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.appdatatype.*;
import cn.getech.ehm.system.dto.appicon.AppConfigMoveParam;
import cn.getech.ehm.system.entity.AppConfigDataType;
import cn.getech.ehm.system.mapper.AppConfigDataTypeMapper;
import cn.getech.ehm.system.service.IAppConfigDataTypeService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.AppDataTypeDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <pre>
 * 分类信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Slf4j
@Service
public class AppConfigDataTypeServiceImpl extends BaseServiceImpl<AppConfigDataTypeMapper, AppConfigDataType> implements IAppConfigDataTypeService {

    @Autowired
    private AppConfigDataTypeParamMapper appConfigDataTypeParamMapper;
    @Autowired
    private AppConfigDataTypeMapper appConfigDataTypeMapper;

    @Override
    public PageResult<AppConfigDataTypeDto> pageDto(AppConfigDataTypeQueryParam appConfigDataTypeQueryParam) {
        Wrapper<AppConfigDataType> wrapper = getPageSearchWrapper(appConfigDataTypeQueryParam);
        PageResult<AppConfigDataTypeDto> result = appConfigDataTypeParamMapper.pageEntity2Dto(page(appConfigDataTypeQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(AppConfigDataTypeAddParam appConfigDataTypeAddParam) {
        AppConfigDataType appConfigDataType = appConfigDataTypeParamMapper.addParam2Entity(appConfigDataTypeAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigDataType);
        return save(appConfigDataType);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(AppConfigDataTypeEditParam appConfigDataTypeEditParam) {
        AppConfigDataType appConfigDataType = appConfigDataTypeParamMapper.editParam2Entity(appConfigDataTypeEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigDataType);
        return updateById(appConfigDataType);
    }


    @Override
    public AppConfigDataTypeDto getDtoById(String id) {
        return appConfigDataTypeParamMapper.entity2Dto((AppConfigDataType) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<AppConfigDataTypeDto> rows) {
        return saveBatch(appConfigDataTypeParamMapper.dtoList2Entity(rows));
    }

    @Override
    public boolean moving(AppConfigMoveParam moveParam) {
        LambdaQueryWrapper<AppConfigDataType> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByAsc(AppConfigDataType::getSortNumber);
        List<AppConfigDataType> dataTypeList = appConfigDataTypeMapper.selectList(wrapper);
        Map<Integer, AppConfigDataType> sortMap = new HashMap();
        int sourceSort = getSourceSort(dataTypeList, moveParam.getId(), sortMap);
        int targetSort = sourceSort + moveParam.getMovingNumber();
        if (swapSortNumber(sortMap, sourceSort, targetSort)) {
            for (AppConfigDataType dataType : dataTypeList) {
                updateById(dataType);
            }
            return true;
        }
        return false;
    }

    @Override
    public List<AppDataTypeDto> fetchAppConfigDataTypeList() {
        return appConfigDataTypeMapper.fetchAppDataTypeList();
    }

    private Wrapper<AppConfigDataType> getPageSearchWrapper(AppConfigDataTypeQueryParam queryParam) {
        LambdaQueryWrapper<AppConfigDataType> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), AppConfigDataType::getName, queryParam.getName());
        wrapper.orderByAsc(AppConfigDataType::getSortNumber);
        return wrapper;
    }

    /**
     * 获取移动目标的序号
     *
     * @param iconList
     * @param movingId
     * @param sortMap
     * @return
     */
    private int getSourceSort(List<AppConfigDataType> iconList, String movingId, Map<Integer, AppConfigDataType> sortMap) {
        int sourceSort = -100;
        for (int i = 0; i < iconList.size(); i++) {
            AppConfigDataType dataType = iconList.get(i);
            dataType.setSortNumber(i + 1);
            sortMap.put(dataType.getSortNumber(), dataType);
            if (dataType.getId().equals(movingId)) {
                sourceSort = i + 1;
            }
        }
        return sourceSort;
    }

    /**
     * 交换序号
     *
     * @param indexMap
     * @param sourceSort
     * @param targetSort
     * @return true-交换成功，false-交换失败
     */
    private boolean swapSortNumber(Map<Integer, AppConfigDataType> indexMap, int sourceSort, int targetSort) {
        if (indexMap.containsKey(sourceSort) && indexMap.containsKey(targetSort)) {
            indexMap.get(sourceSort).setSortNumber(targetSort);
            indexMap.get(targetSort).setSortNumber(sourceSort);
            return true;
        }
        return false;
    }

    @Override
    public Boolean initialization(String tenantId) {
        List<AppConfigDataType> entities = appConfigDataTypeMapper.getAllData();
        for(AppConfigDataType entity : entities){
            entity.setTenantId(tenantId);
        }
        if(CollectionUtils.isNotEmpty(entities)) {
            appConfigDataTypeMapper.insertField(entities);
        }
        return true;
    }

}
