package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.audit.*;
import cn.getech.ehm.system.dto.poros.secstaff.KjsMetaData;
import cn.getech.ehm.system.dto.poros.secstaff.SecStaffRemark;
import cn.getech.ehm.system.entity.CustomerAudit;
import cn.getech.ehm.system.mapper.CustomerAuditMapper;
import cn.getech.ehm.system.service.ICustomerAuditService;
import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.SecStaffAddParam;
import cn.getech.poros.permission.dto.UserCheckDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.CustomerDto;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.system.dto.notify.EmailNotify;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import cn.getech.ehm.system.dto.notify.SMSNotify;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <pre>
 * 审核用户 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CustomerAuditServiceImpl extends BaseServiceImpl<CustomerAuditMapper, CustomerAudit> implements ICustomerAuditService {

    @Value("${fixed.role.audit.code:web-admin_register-user}")
    private String auditRoleCode;
    @Autowired
    private CustomerAuditMapper customerAuditMapper;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private NotifyClient notifyClient;

    @Value("${aliyun.sms.default-sign:康吉森}")
    private String defaultSign;

    @Value("${aliyun.sms.templates.registry.success:SMS_207530360}")
    private String registrySuccessTemplate;

    @Value("${aliyun.sms.templates.registry.fail:SMS_207755274}")
    private String registryFailTemplate;

    @Override
    public PageResult<CustomerAuditDto> pageDto(CustomerAuditQueryParam customerAuditQueryParam) {
        Page<CustomerAuditQueryParam> page = new Page<>(customerAuditQueryParam.getPageNo(), customerAuditQueryParam.getLimit());
        IPage<CustomerAuditDto> result = customerAuditMapper.pageList(page, customerAuditQueryParam);

        return Optional.ofNullable(PageResult.<CustomerAuditDto>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public List<CustomerAuditDto> getList(CustomerAuditQueryParam customerAuditQueryParam) {
        return customerAuditMapper.getList(customerAuditQueryParam);
    }

    @Override
    public List<CustomerAuditExcel> getExportList() {
        CustomerAuditQueryParam customerAuditQueryParam = new CustomerAuditQueryParam();
        customerAuditQueryParam.setStatus(StaticValue.ONE);
        List<CustomerAuditDto> customerAuditDtos = customerAuditMapper.getList(customerAuditQueryParam);
        return CopyDataUtil.copyList(customerAuditDtos, CustomerAuditExcel.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    public RestResponse<Boolean> saveByParam(CustomerAuditAddParam customerAuditAddParam) {
        //校验中台用户是否已存在
        UserCheckDto userCheckDto = new UserCheckDto();
        userCheckDto.setUid(customerAuditAddParam.getUid());
        userCheckDto.setMobile(customerAuditAddParam.getMobile());
        userCheckDto.setEmail(customerAuditAddParam.getEmail());
        log.info("中台校验审核数据->" + JSONObject.toJSON(userCheckDto));
        RestResponse<Boolean> restResponse = porosSecStaffClient.uniqueCheck(userCheckDto);
        log.info("中台返回审核数据->" + JSONObject.toJSON(userCheckDto));
        //校验失败 ， 返回中台提示语
        if (!restResponse.isOk()) {
            return restResponse;
        }
        if (uniqueCheck(customerAuditAddParam.getUid(), null, null)) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("uid_registered", null, LocaleContextHolder.getLocale())));
        }
        if (uniqueCheck(null, customerAuditAddParam.getMobile(), null)) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("mobile_registered", null, LocaleContextHolder.getLocale())));
        }
        if (uniqueCheck(null, null, customerAuditAddParam.getEmail())) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("email_registered", null, LocaleContextHolder.getLocale())));
        }


        CustomerAudit customerAudit = CopyDataUtil.copyObject(customerAuditAddParam, CustomerAudit.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customerAudit);
        customerAudit.setApplyTime(new Date());
        customerAudit.setStatus(StaticValue.ZERO);
        customerAudit.setCreateBy("system");
        customerAudit.setUpdateBy("system");
        return RestResponse.ok(save(customerAudit));
    }

    /**
     * 校验审核表里面是否已存在
     *
     * @param uid
     * @param mobile
     * @param email
     * @return
     */
    private Boolean uniqueCheck(String uid, String mobile, String email) {
        LambdaQueryWrapper<CustomerAudit> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(uid)) {
            wrapper.eq(CustomerAudit::getUid, uid);
        }
        if (StringUtils.isNotBlank(mobile)) {
            wrapper.eq(CustomerAudit::getMobile, mobile);
        }
        if (StringUtils.isNotBlank(email)) {
            wrapper.eq(CustomerAudit::getEmail, email);
        }
        //未审核的不可以使用
        wrapper.eq(CustomerAudit::getStatus, StaticValue.ZERO);
        Integer count = customerAuditMapper.selectCount(wrapper);
        return count > StaticValue.ZERO;
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public RestResponse<Boolean> updateByParam(CustomerAuditEditParam customerAuditEditParam) {
        CustomerAudit customerAudit = customerAuditMapper.selectById(customerAuditEditParam.getId());
        customerAudit.setStatus(customerAuditEditParam.getStatus());
        customerAudit.setRemark(customerAuditEditParam.getRemark());
        customerAudit.setCustomerId(customerAuditEditParam.getCustomerId());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customerAudit);
        if (customerAuditEditParam.getStatus().intValue() == StaticValue.ONE) {
            CustomerDto dto = customerService.getDtoById(customerAuditEditParam.getCustomerId());

            //审核通过新增中台用户
            SecStaffAddParam secStaffAddParam = new SecStaffAddParam();
            secStaffAddParam.setName(customerAudit.getName());
            secStaffAddParam.setUid(customerAudit.getUid());
            secStaffAddParam.setMobile(customerAudit.getMobile());
            secStaffAddParam.setEmail(customerAudit.getEmail());
            secStaffAddParam.setPassword(StringUtils.isNotBlank(customerAudit.getPassword()) ? customerAudit.getPassword() : null);
            List<String> roleCodeList = new ArrayList<>();
            roleCodeList.add(auditRoleCode);
            secStaffAddParam.setRoleCodeList(roleCodeList);

            // 增加客户名称到remark里
            KjsMetaData kjsMetaData = new KjsMetaData();
            kjsMetaData.setCompanyName(dto != null && StringUtils.isNotBlank(dto.getName().trim()) ? dto.getName() : "");
            SecStaffRemark secStaffRemark = new SecStaffRemark();
            secStaffRemark.setKjs(kjsMetaData);

            secStaffAddParam.setRemark(JSONObject.toJSONString(secStaffRemark));

            log.info("审核通过新增中台用户->" + JSON.toJSONString(secStaffAddParam));
            RestResponse<Boolean> restResponse = porosSecStaffClient.add(secStaffAddParam);
            if (!restResponse.isOk()) {
                return restResponse;
            }
        }
        switch (customerAudit.getStatus()) {
            case 1:
                // 审核成功发送推送
                try {
                    String title = "注册审核通知";
                    String content = "您已成功注册康吉森工业互联网平台，您的账号为" + customerAudit.getUid() + "，密码为" + customerAudit.getPassword() + "，请登录平台或APP后及时修改密码。";
                    EmailNotify emailNotify = EmailNotify.builder().title(title).content(content).emails(new String[]{customerAudit.getEmail()}).build();
                    Map<String, String> smsParams = new HashMap<>(3);
                    smsParams.put("uid", customerAudit.getUid());
                    smsParams.put("password", customerAudit.getPassword());
                    SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(registrySuccessTemplate).params(smsParams).mobiles(new String[]{customerAudit.getMobile()}).build();
                    notifyClient.sendNotify(NotifyParam.builder().notifyTypes(EnumSet.of(NotifyType.EMAIL, NotifyType.SMS)).emailNotify(emailNotify).smsNotify(smsNotify).build());
                } catch (Exception e) {
                    log.error("注册审核通知发送异常", e.getLocalizedMessage());
                }
                break;
            case 2:
                // 审核不通过发送推送
                try {
                    String title = "注册审核通知";
                    String content = "您的注册申请未通过，原因:" + customerAuditEditParam.getRemark() + "，请重新注册！";
                    EmailNotify emailNotify = EmailNotify.builder().title(title).content(content).emails(new String[]{customerAudit.getEmail()}).build();
                    Map<String, String> smsParams = new HashMap<>(1);
                    smsParams.put("reson", customerAudit.getRemark());
                    SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(registryFailTemplate).params(smsParams).mobiles(new String[]{customerAudit.getMobile()}).build();
                    notifyClient.sendNotify(NotifyParam.builder().notifyTypes(EnumSet.of(NotifyType.EMAIL, NotifyType.SMS)).emailNotify(emailNotify).smsNotify(smsNotify).build());
                } catch (Exception e) {
                    log.error("注册审核通知发送异常", e.getLocalizedMessage());
                }
                break;
            default:
                log.error("不支持的审核状态");
        }
        //审核过清空密码
        customerAudit.setPassword("");
        return RestResponse.ok(updateById(customerAudit));
    }

    @Override
    public Boolean checkIsAudit() {
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        String uid = userInfo.getUid();
        LambdaQueryWrapper<CustomerAudit> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerAudit::getUid, uid);
        wrapper.eq(CustomerAudit::getStatus, StaticValue.ONE);
        Integer count = customerAuditMapper.selectCount(wrapper);
        return count > StaticValue.ZERO;
    }

    @Override
    public Boolean checkIsVisitor() {
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        String uid = userInfo.getUid();
        LambdaQueryWrapper<CustomerAudit> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerAudit::getUid, uid);
        Integer count = customerAuditMapper.selectCount(wrapper);
        return count > StaticValue.ZERO;
    }
}
