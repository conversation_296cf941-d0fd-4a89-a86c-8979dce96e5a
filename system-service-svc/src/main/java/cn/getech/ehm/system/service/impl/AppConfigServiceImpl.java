package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.TransferOrderFirstDto;
import cn.getech.ehm.system.dto.appconfig.AppConfigDto;
import cn.getech.ehm.system.service.*;
import cn.getech.ehm.system.utils.ketianyun.LiveProperty;
import cn.getech.ehm.task.client.TaskClient;
import cn.getech.ehm.task.dto.AppFirstPageDto;
import cn.getech.poros.framework.common.api.RestResponse;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.AppDataTypeDto;
import cn.getech.ehm.system.dto.DataTypeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * app配置信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Slf4j
@Service
public class AppConfigServiceImpl implements IAppConfigService {

    @Autowired
    private IAppConfigIconService appConfigIconService;

    @Autowired
    private IAppConfigDataTypeService appConfigDataTypeService;

    @Autowired
    private IAppConfigBannerService appConfigBannerService;

    @Autowired
    private TaskClient taskClient;

    @Autowired
    private LiveProperty liveProperty;

    @Autowired
    private PartClient partClient;


    @Override
    public AppConfigDto getAppConfig() {
        AppConfigDto appConfigDto = new AppConfigDto();
        appConfigDto.setBannerList(appConfigBannerService.fetchAppConfigBannerList());
        appConfigDto.setIconList(appConfigIconService.fetchAppConfigIconList());
        return appConfigDto;
    }


    /*public DataTypeDto dataTypeListOld() {
        DataTypeDto dataTypeDto = new DataTypeDto();
        List<AppDataTypeDto> appDataTypeDtos = appConfigDataTypeService.fetchAppConfigDataTypeList();
        // 首页只需要两条信息 暂时写死
        RestResponse<AppFirstPageDto> firstPage = taskClient.getFirstPage(2);
        RestResponse<List<AppContractRecordDto>> listRestResponse = orderRecordClient.orderList(2);
        RestResponse<TransferOrderFirstDto> firstPage1 = partClient.getFirstPage(2);
        appDataTypeDtos.forEach(dto -> {
            if ("1".equals(dto.getModuleType())){
                if (firstPage.getData() != null && firstPage.getData().getWarns() !=null && firstPage.getData().getWarns().size() != 0){
                    dto.setData(firstPage.getData().getWarns());
                }else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setWarnsDataType(dto);
            } else if ("2".equals(dto.getModuleType())){
                if (firstPage.getData() != null && firstPage.getData().getRepairs() !=null && firstPage.getData().getRepairs().size() != 0 ){
                    dto.setData(firstPage.getData().getRepairs());
                }else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setRepairsDataType(dto);
            } else if ("3".equals(dto.getModuleType())){
                if (listRestResponse.getData() != null && listRestResponse.getData() !=null && listRestResponse.getData().size() != 0 ){
                    dto.setData(listRestResponse.getData());
                }else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setOrderRecordDataType(dto);
            } else if ("4".equals(dto.getModuleType())){
                KeTianYunLiveInfoDto currLiveInfo = keTianLiveService.getCurrLiveInfo();

                //设置直播跳转地址
                currLiveInfo.setLiveUrl(liveProperty.getOauthRedirectUrl());

                List<KeTianYunLiveInfoDto> liveInfoDtos = new ArrayList<>();
                liveInfoDtos.add(currLiveInfo);
                dto.setData(liveInfoDtos);
                dataTypeDto.setKeTianLiveDataType(dto);
            }
        });


        return dataTypeDto;
    }*/

    @Override
    public DataTypeDto dataTypeList() {
        DataTypeDto dataTypeDto = new DataTypeDto();
        List<AppDataTypeDto> appDataTypeDtos = appConfigDataTypeService.fetchAppConfigDataTypeList();
        // 首页只需要两条信息 暂时写死
        RestResponse<AppFirstPageDto> firstTaskPage = taskClient.getFirstPage(2);
        
        RestResponse<List<TransferOrderFirstDto>> firstPartPage = partClient.getFirstPage(2);
        appDataTypeDtos.forEach(dto -> {
            if ("1".equals(dto.getModuleType())) {
                if (firstTaskPage.getData() != null && firstTaskPage.getData().getMaintTasks() != null && firstTaskPage.getData().getMaintTasks().size() != 0) {
                    dto.setData(firstTaskPage.getData().getMaintTasks());
                } else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setMaintTaskType(dto);
            } else if ("2".equals(dto.getModuleType())) {
                if (firstPartPage.getData() != null && firstPartPage.getData().size() != 0) {
                    dto.setData(firstPartPage.getData());
                } else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setPartType(dto);
            } else if ("3".equals(dto.getModuleType())) {
                if (firstTaskPage.getData() != null && firstTaskPage.getData().getWarns() != null && firstTaskPage.getData().getWarns().size() != 0) {
                    dto.setData(firstTaskPage.getData().getWarns());
                } else {
                    dto.setData(new ArrayList());
                }
                dataTypeDto.setWarnsDataType(dto);
            }
        });

        return dataTypeDto;
    }

}
