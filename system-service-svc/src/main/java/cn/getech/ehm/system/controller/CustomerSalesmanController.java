package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.service.ICustomerSalesmanService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 客户销售人员控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-07
 */
@RestController
@RequestMapping("/customerSalesman")
@Api(tags = "客户销售人员服务接口")
public class CustomerSalesmanController {

    @Autowired
    private ICustomerSalesmanService customerSalesmanService;

}
