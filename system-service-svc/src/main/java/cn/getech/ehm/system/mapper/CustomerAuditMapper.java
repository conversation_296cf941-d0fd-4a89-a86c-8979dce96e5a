package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.dto.audit.CustomerAuditDto;
import cn.getech.ehm.system.dto.audit.CustomerAuditQueryParam;
import cn.getech.ehm.system.entity.CustomerAudit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 审核用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Repository
public interface CustomerAuditMapper extends BaseMapper<CustomerAudit> {
    /**
     * 分页查询
     *
     * @param page
     * @param customerAuditQueryParam
     * @return
     */
    IPage<CustomerAuditDto> pageList(Page<CustomerAuditQueryParam> page,
                                     @Param("param") CustomerAuditQueryParam customerAuditQueryParam);

    /**
     * 查询全部数据
     *
     * @param customerAuditQueryParam
     * @return
     */
    List<CustomerAuditDto> getList(@Param("param") CustomerAuditQueryParam customerAuditQueryParam);
}
