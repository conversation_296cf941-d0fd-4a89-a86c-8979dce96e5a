package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.system.dto.appsearch.AppSearchHistoryDto;
import cn.getech.ehm.system.dto.appsearch.AppSearchHistoryParamMapper;
import cn.getech.ehm.system.entity.AppSearchHistory;
import cn.getech.ehm.system.mapper.AppSearchHistoryMapper;
import cn.getech.ehm.system.service.IAppSearchHistoryService;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 系统app搜索记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-28
 */
@Slf4j
@Service
public class AppSearchHistoryServiceImpl extends BaseServiceImpl<AppSearchHistoryMapper, AppSearchHistory> implements IAppSearchHistoryService {

    @Autowired
    private AppSearchHistoryParamMapper appSearchHistoryParamMapper;

    @Autowired
    private AppSearchHistoryMapper appSearchHistoryMapper;


    @Override
    public Boolean saveSearchHistory(AppSearchHistoryDto appSearchHistoryDto) {
        if (StringUtils.isEmpty(appSearchHistoryDto.getKeyword()) || StringUtils.isBlank(appSearchHistoryDto.getKeyword())) {
            return true;
        }

        LambdaQueryWrapper<AppSearchHistory> queryWrapper = Wrappers.<AppSearchHistory>lambdaQuery();
        queryWrapper.eq(AppSearchHistory::getUid, appSearchHistoryDto.getUid());
        queryWrapper.eq(AppSearchHistory::getKeyword, appSearchHistoryDto.getKeyword());

        List<AppSearchHistory> list = list(queryWrapper);

        if (list == null || list.size() == 0) {
            return save(appSearchHistoryParamMapper.dto2Entity(appSearchHistoryDto));
        } else {
            LambdaUpdateWrapper<AppSearchHistory> updateWrapper = Wrappers.<AppSearchHistory>lambdaUpdate();

            updateWrapper.ge(AppSearchHistory::getId, list.get(0).getId());
            updateWrapper.set(AppSearchHistory::getUpdateTime, new Date());
            return update(updateWrapper);
        }
    }

    @Override
    public List<AppSearchHistoryDto> searchHistory() {
        LambdaQueryWrapper<AppSearchHistory> queryWrapper = Wrappers.lambdaQuery();
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        String uid = null == user ? "" : user.getUid();
        queryWrapper.eq(AppSearchHistory::getUid, uid);
        queryWrapper.orderBy(true, false, AppSearchHistory::getUpdateTime);
        List<AppSearchHistory> list = list(queryWrapper);
        List<AppSearchHistoryDto> resultList = null;
        if (list != null && list.size() > 0) {
            resultList = CopyDataUtil.copyList(list, AppSearchHistoryDto.class);
        }
        return Optional.ofNullable(resultList).orElse(new ArrayList<AppSearchHistoryDto>());
    }

    @Override
    public Boolean deleteByUid() {
        LambdaQueryWrapper<AppSearchHistory> queryWrapper = Wrappers.lambdaQuery();
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        String uid = null == user ? "" : user.getUid();
        queryWrapper.eq(AppSearchHistory::getUid, uid);
        return remove(queryWrapper);
    }


}
