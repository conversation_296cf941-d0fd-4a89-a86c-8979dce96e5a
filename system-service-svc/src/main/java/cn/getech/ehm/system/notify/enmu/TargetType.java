package cn.getech.ehm.system.notify.enmu;

/**
 * <AUTHOR>
 * @date 2021-04-08 16:42:10
 **/
public enum TargetType {

    DEVICE(1, "设备ID"),
    ACCOUNT(2, "账号UID"),
    ALIAS(3, "别名"),
    TAG(4, "标签"),
    ALL(0, "所有");

    int code;
    String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    TargetType(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
