package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.appsearch.AppSearchHistoryDto;
import cn.getech.ehm.system.entity.AppSearchHistory;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <p>
 * 系统app搜索记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-28
 */
public interface IAppSearchHistoryService extends IBaseService<AppSearchHistory> {

    /**
     * 保存搜索历史记录
     *
     * @param appSearchHistoryDto
     * @return
     */
    Boolean saveSearchHistory(AppSearchHistoryDto appSearchHistoryDto);

    /**
     * 查询历史记录
     *
     * @return
     */
    List<AppSearchHistoryDto> searchHistory();

    /**
     * 根据uid 删除
     *
     * @return
     */
    Boolean deleteByUid();
}