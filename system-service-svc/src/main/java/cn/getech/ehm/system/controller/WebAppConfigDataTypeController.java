package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeAddParam;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeDto;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeEditParam;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeQueryParam;
import cn.getech.ehm.system.dto.appicon.AppConfigMoveParam;
import cn.getech.ehm.system.service.IAppConfigDataTypeService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * app应用icon配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@RestController
@RequestMapping("/appConfigDataType")
@Api(tags = "web接口：app应用数据类型配置服务接口")
public class WebAppConfigDataTypeController {

    @Autowired
    private IAppConfigDataTypeService appConfigDataTypeService;

    /**
     * 分页获取app应用icon配置列表
     */
    @ApiOperation("分页获取app应用数据类型配置列表")
    @GetMapping("/list")
    //@Permission("app:config:data:type:list")
    public RestResponse<PageResult<AppConfigDataTypeDto>> pageList(@Valid AppConfigDataTypeQueryParam appConfigDataTypeQueryParam) {
        return RestResponse.ok(appConfigDataTypeService.pageDto(appConfigDataTypeQueryParam));
    }

    /**
     * 新增app应用icon配置
     */
    @ApiOperation("新增app应用数据类型配置")
    @AuditLog(title = "app应用数据类型配置", desc = "新增app应用数据类型配置", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("app:config:data:type:update")
    public RestResponse<Boolean> add(@RequestBody @Valid AppConfigDataTypeAddParam appConfigDataTypeAddParam) {
        return RestResponse.ok(appConfigDataTypeService.saveByParam(appConfigDataTypeAddParam));
    }

    /**
     * 修改app应用icon配置
     */
    @ApiOperation(value = "修改app应用数据类型配置")
    @AuditLog(title = "app应用数据类型配置", desc = "修改app应用数据类型配置", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("app:config:data:type:update")
    public RestResponse<Boolean> update(@RequestBody @Valid AppConfigDataTypeEditParam appConfigDataTypeEditParam) {
        return RestResponse.ok(appConfigDataTypeService.updateByParam(appConfigDataTypeEditParam));
    }

    /**
     * 上移下移icon配置
     */
    @ApiOperation(value = "上移下移")
    @AuditLog(title = "app应用数据类型配置", desc = "上移下移icon配置", businessType = BusinessType.UPDATE)
    @PutMapping("moving")
    //@Permission("app:config:icon:update")
    public RestResponse<Boolean> moving(@RequestBody @Valid AppConfigMoveParam appConfigMoveParam) {
        return RestResponse.ok(appConfigDataTypeService.moving(appConfigMoveParam));
    }

    /**
     * 根据id删除app应用icon配置
     */
    @ApiOperation(value = "根据id删除app应用数据类型配置")
    @AuditLog(title = "app应用数据类型配置", desc = "app应用数据类型配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("app:config:data:type:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(appConfigDataTypeService.removeById(id));
    }

    /**
     * 根据id获取app应用icon配置
     */
    @ApiOperation(value = "根据id获取app应用数据类型配置")
    @GetMapping(value = "/{id}")
    //@Permission("app:config:data:type:list")
    public RestResponse<AppConfigDataTypeDto> get(@PathVariable String id) {
        return RestResponse.ok(appConfigDataTypeService.getDtoById(id));
    }

    @ApiOperation("初始化表单配置")
    @GetMapping("/initialization")
    public RestResponse<Boolean> initialization(@RequestParam String tenantId){
        return RestResponse.ok(appConfigDataTypeService.initialization(tenantId));
    }

}
