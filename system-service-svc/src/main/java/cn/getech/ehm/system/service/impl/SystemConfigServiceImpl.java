package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.systemconfig.SystemConfigAddParam;
import cn.getech.ehm.system.dto.systemconfig.SystemConfigEditParam;
import cn.getech.ehm.system.dto.systemconfig.SystemConfigParamMapper;
import cn.getech.ehm.system.dto.systemconfig.SystemConfigQueryParam;
import cn.getech.ehm.system.entity.SystemConfig;
import cn.getech.ehm.system.mapper.SystemConfigMapper;
import cn.getech.ehm.system.service.ISystemConfigService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <pre>
 * 系统配置信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Slf4j
@Service
public class SystemConfigServiceImpl extends BaseServiceImpl<SystemConfigMapper, SystemConfig> implements ISystemConfigService {

    @Autowired
    private SystemConfigParamMapper systemConfigParamMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public PageResult<SystemConfigDto> pageDto(SystemConfigQueryParam systemConfigQueryParam) {
        Wrapper<SystemConfig> wrapper = getPageSearchWrapper(systemConfigQueryParam);
        PageResult<SystemConfigDto> result = systemConfigParamMapper.pageEntity2Dto(page(systemConfigQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(SystemConfigAddParam addParam) {
        LambdaQueryWrapper<SystemConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemConfig::getSystemName, addParam.getSystemName());
        wrapper.eq(SystemConfig::getCfgName, addParam.getCfgName());
        SystemConfig config = (SystemConfig) this.baseMapper.selectOne(wrapper);
        if (null == config) {
            SystemConfig systemConfig = systemConfigParamMapper.addParam2Entity(addParam);
            Assert.notNull(ResultCode.PARAM_VALID_ERROR, systemConfig);
            return save(systemConfig);
        }
        config.setCfgValue(addParam.getCfgValue());
        return updateById(config);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(SystemConfigEditParam systemConfigEditParam) {
        SystemConfig systemConfig = systemConfigParamMapper.editParam2Entity(systemConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, systemConfig);
        return updateById(systemConfig);
    }


    @Override
    public SystemConfigDto getDtoById(String id) {
        return systemConfigParamMapper.entity2Dto((SystemConfig) this.getById(id));
    }

    @Override
    public PageResult<SystemConfigDto> listByName(String name) {
        SystemConfigQueryParam queryParam = new SystemConfigQueryParam();
        queryParam.setCfgName(name);
        return pageDto(queryParam);
    }

    @Override
    public Map<String, SystemConfigDto> mapByNames(String names) {
        Map<String, SystemConfigDto> configMap = new HashMap();
        LambdaQueryWrapper<SystemConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SystemConfig::getCfgName, names.split(","));
        List<SystemConfig> configList = systemConfigMapper.selectList(wrapper);
        for (SystemConfig config : configList) {
            SystemConfigDto systemConfigDto = new SystemConfigDto();
            BeanUtils.copyProperties(config, systemConfigDto);
            configMap.put(config.getCfgName(), systemConfigDto);
        }
        return configMap;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<SystemConfigDto> rows) {
        return saveBatch(systemConfigParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<SystemConfig> getPageSearchWrapper(SystemConfigQueryParam systemConfigQueryParam) {
        LambdaQueryWrapper<SystemConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(systemConfigQueryParam.getCfgName()),
                SystemConfig::getCfgName, systemConfigQueryParam.getCfgName());
        wrapper.orderByAsc(SystemConfig::getSortNumber);
        if (BaseEntity.class.isAssignableFrom(SystemConfig.class)) {
            wrapper.orderByDesc(SystemConfig::getUpdateTime, SystemConfig::getCreateTime);
        }
        return wrapper;
    }
}
