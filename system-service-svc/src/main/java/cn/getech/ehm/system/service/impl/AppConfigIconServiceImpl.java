package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.appconfig.AppIconDto;
import cn.getech.ehm.system.dto.appicon.*;
import cn.getech.ehm.system.entity.AppConfigIcon;
import cn.getech.ehm.system.mapper.AppConfigIconMapper;
import cn.getech.ehm.system.service.IAppConfigIconService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <pre>
 * 分类信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Slf4j
@Service
public class AppConfigIconServiceImpl extends BaseServiceImpl<AppConfigIconMapper, AppConfigIcon> implements IAppConfigIconService {

    @Autowired
    private AppConfigIconParamMapper appConfigIconParamMapper;
    @Autowired
    private AppConfigIconMapper appConfigIconMapper;

    @Override
    public PageResult<AppConfigIconDto> pageDto(AppConfigIconQueryParam appConfigIconQueryParam) {
        Wrapper<AppConfigIcon> wrapper = getPageSearchWrapper(appConfigIconQueryParam);
        PageResult<AppConfigIconDto> result = appConfigIconParamMapper.pageEntity2Dto(page(appConfigIconQueryParam, wrapper));
        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(AppConfigIconAddParam appConfigIconAddParam) {
        Integer maxSortNumber = appConfigIconMapper.getMaxSortNumber();
        AppConfigIcon appConfigIcon = appConfigIconParamMapper.addParam2Entity(appConfigIconAddParam);
        appConfigIcon.setSortNumber(null == maxSortNumber ? 1 : maxSortNumber + 1);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigIcon);
        return save(appConfigIcon);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(AppConfigIconEditParam appConfigIconEditParam) {
        AppConfigIcon appConfigIcon = appConfigIconParamMapper.editParam2Entity(appConfigIconEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigIcon);
        return updateById(appConfigIcon);
    }

    @Override
    public boolean moving(AppConfigMoveParam moveParam) {
        LambdaQueryWrapper<AppConfigIcon> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByAsc(AppConfigIcon::getSortNumber);
        List<AppConfigIcon> iconList = appConfigIconMapper.selectList(wrapper);
        Map<Integer, AppConfigIcon> sortMap = new HashMap();
        int sourceSort = getSourceSort(iconList, moveParam.getId(), sortMap);
        int targetSort = sourceSort + moveParam.getMovingNumber();
        if (swapSortNumber(sortMap, sourceSort, targetSort)) {
            for (AppConfigIcon configIcon : iconList) {
                updateById(configIcon);
            }
            return true;
        }
        return false;
    }

    @Override
    public AppConfigIconDto getDtoById(String id) {
        return appConfigIconParamMapper.entity2Dto((AppConfigIcon) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<AppConfigIconDto> rows) {
        return saveBatch(appConfigIconParamMapper.dtoList2Entity(rows));
    }

    @Override
    public List<AppIconDto> fetchAppConfigIconList() {
        return appConfigIconMapper.fetchAppConfigIconList();
    }

    /**
     * 获取移动目标的序号
     *
     * @param iconList
     * @param movingId
     * @param sortMap
     * @return
     */
    private int getSourceSort(List<AppConfigIcon> iconList, String movingId, Map<Integer, AppConfigIcon> sortMap) {
        int sourceSort = -100;
        for (int i = 0; i < iconList.size(); i++) {
            AppConfigIcon configIcon = iconList.get(i);
            configIcon.setSortNumber(i + 1);
            sortMap.put(configIcon.getSortNumber(), configIcon);
            if (configIcon.getId().equals(movingId)) {
                sourceSort = i + 1;
            }
        }
        return sourceSort;
    }

    /**
     * 交换序号
     *
     * @param indexMap
     * @param sourceSort
     * @param targetSort
     * @return true-交换成功，false-交换失败
     */
    private boolean swapSortNumber(Map<Integer, AppConfigIcon> indexMap, int sourceSort, int targetSort) {
        if (indexMap.containsKey(sourceSort) && indexMap.containsKey(targetSort)) {
            indexMap.get(sourceSort).setSortNumber(targetSort);
            indexMap.get(targetSort).setSortNumber(sourceSort);
            return true;
        }
        return false;
    }

    private Wrapper<AppConfigIcon> getPageSearchWrapper(AppConfigIconQueryParam appConfigIconQueryParam) {
        LambdaQueryWrapper<AppConfigIcon> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(appConfigIconQueryParam.getName()),
                AppConfigIcon::getName, appConfigIconQueryParam.getName());
        wrapper.orderByAsc(AppConfigIcon::getSortNumber);
        return wrapper;
    }

    @Override
    public Boolean initialization(String tenantId) {
        List<AppConfigIcon> entities = appConfigIconMapper.getAllData();
        for(AppConfigIcon entity : entities){
            entity.setTenantId(tenantId);
        }
        if(CollectionUtils.isNotEmpty(entities)) {
            appConfigIconMapper.insertField(entities);
        }
        return true;
    }
}
