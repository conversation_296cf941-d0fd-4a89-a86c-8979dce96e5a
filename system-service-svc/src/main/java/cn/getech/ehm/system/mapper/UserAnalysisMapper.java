package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.dto.customer.LoginCountDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisQueryParam;
import cn.getech.ehm.system.entity.UserAnalysis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 用户分析信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface UserAnalysisMapper extends BaseMapper<UserAnalysis> {

    Page<UserAnalysisDto> fetchUserAnalysisList(Page page, UserAnalysisQueryParam queryParam);

    /**
     * 按工程师，按月查询登录次数
     *
     * @param queryParam
     * @return
     */
    List<LoginCountDto> getMonthLoginCountMapByUid(@Param("queryParam") UserAnalysisQueryParam queryParam);
}
