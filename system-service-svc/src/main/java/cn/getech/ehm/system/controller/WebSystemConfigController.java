package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.systemconfig.SystemConfigAddParam;
import cn.getech.ehm.system.dto.systemconfig.WebAppLoginImgAddParam;
import cn.getech.ehm.system.dto.systemconfig.WebAppStartImgAddParam;
import cn.getech.ehm.system.service.ISystemConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;


/**
 * <p>
 * 系统配置信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@RestController
@RequestMapping("/webSystemConfig")
@Api(tags = "web接口: app启动和登录页面配置")
public class WebSystemConfigController {

    @Autowired
    private ISystemConfigService systemConfigService;

    /**
     * app启动页配置
     */
    @ApiOperation(value = "app启动页配置")
    @PostMapping(value = "appStartImg")
    //@Permission("system:config:list")
    public RestResponse<Boolean> appStartImg(@RequestBody @Valid WebAppStartImgAddParam addParam) {
        SystemConfigAddParam systemConfigAddParam = new SystemConfigAddParam();
        systemConfigAddParam.setSystemName("app");
        systemConfigAddParam.setCfgName("app_start_img");
        systemConfigAddParam.setCfgValue(addParam.getImgUrl());
        systemConfigAddParam.setCfgDesc("app启动页配置图片url和新闻");
        return RestResponse.ok(systemConfigService.saveByParam(systemConfigAddParam));
    }

    /**
     * app登录页配置
     */
    @ApiOperation(value = "app登录页配置")
    @PostMapping(value = "appLoginImg")
    //@Permission("system:config:list")
    public RestResponse<Boolean> appLoginImg(@RequestBody @Valid WebAppLoginImgAddParam addParam) {
        SystemConfigAddParam systemConfigAddParam = new SystemConfigAddParam();
        systemConfigAddParam.setSystemName("app");
        systemConfigAddParam.setCfgName("app_login_img");
        systemConfigAddParam.setCfgValue(addParam.getImgUrl());
        systemConfigAddParam.setCfgDesc("app登录页配置图片url");
        return RestResponse.ok(systemConfigService.saveByParam(systemConfigAddParam));
    }

    /**
     * app启动页配置
     */
    @ApiOperation(value = "查询app启动页配置")
    @GetMapping(value = "appStartImg")
    //@Permission("system:config:list")
    public RestResponse<String> appStartImg() {
        String startImg = "app_start_img";
        String startImgDefault = "app_start_img_default";
        Map<String, SystemConfigDto> configDtoMap = systemConfigService.mapByNames(startImg + "," + startImgDefault);
        if (CollectionUtils.isEmpty(configDtoMap)) {
            return RestResponse.ok("");
        }
        if (configDtoMap.containsKey(startImg)) {
            SystemConfigDto systemConfigDto = configDtoMap.get(startImg);
            if (StringUtils.isNotBlank(systemConfigDto.getCfgValue())) {
                return RestResponse.ok(systemConfigDto.getCfgValue());
            }
        }
        return RestResponse.ok(configDtoMap.get(startImgDefault).getCfgValue());
    }

    /**
     * app登录页配置
     */
    @ApiOperation(value = "查询app登录页配置", notes = "返回图片url")
    @GetMapping(value = "appLoginImg")
    //@Permission("system:config:list")
    public RestResponse<String> appLoginImg() {
        String loginImg = "app_login_img";
        String loginImgDefault = "app_login_img_default";
        Map<String, SystemConfigDto> configDtoMap = systemConfigService.mapByNames(loginImg + "," + loginImgDefault);
        if (CollectionUtils.isEmpty(configDtoMap)) {
            return RestResponse.ok("");
        }
        if (configDtoMap.containsKey(loginImg)) {
            SystemConfigDto systemConfigDto = configDtoMap.get(loginImg);
            if (StringUtils.isNotBlank(systemConfigDto.getCfgValue())) {
                return RestResponse.ok(systemConfigDto.getCfgValue());
            }
        }
        return RestResponse.ok(configDtoMap.get(loginImgDefault).getCfgValue());
    }

}
