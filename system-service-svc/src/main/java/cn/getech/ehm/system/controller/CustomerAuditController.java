package cn.getech.ehm.system.controller;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.audit.*;
import cn.getech.ehm.system.service.ICustomerAuditService;
import cn.getech.ehm.system.service.ICustomerEngineerService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 审核用户控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/customerAudit")
@Api(tags = "审核用户服务接口")
public class CustomerAuditController {

    @Autowired
    private ICustomerAuditService customerAuditService;
    @Autowired
    private ICustomerEngineerService customerEngineerService;

    /**
     * 分页获取审核用户列表
     */
    @ApiOperation("分页获取审核用户列表")
    @GetMapping("/list")
    //@Permission("customer:audit:list")
    public RestResponse<PageResult<CustomerAuditDto>> pageList(@Valid CustomerAuditQueryParam customerAuditQueryParam) {
        return RestResponse.ok(customerAuditService.pageDto(customerAuditQueryParam));
    }

    /**
     * 获取已审核用户
     */
    @ApiOperation("获取已审核用户")
    @GetMapping("/getList")
    //@Permission("customer:audit:list")
    public RestResponse<PageResult<CustomerAuditDto>> getList(@Valid CustomerAuditQueryParam customerAuditQueryParam) {
        customerAuditQueryParam.setStatus(StaticValue.ONE);
        //过滤已配置的客户
        List<String> usedUids = customerEngineerService.getAllUid();
        customerAuditQueryParam.setUsedUids(usedUids);
        return RestResponse.ok(customerAuditService.pageDto(customerAuditQueryParam));
    }

    /**
     * 导出已审核用户
     */
    @ApiOperation(value = "导出已审核用户")
    @AuditLog(title = "审核用户", desc = "导出已审核用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void excelExport(HttpServletResponse response) {
        List<CustomerAuditExcel> customerAuditExcels = customerAuditService.getExportList();
        ExcelUtils<CustomerAuditExcel> util = new ExcelUtils<>(CustomerAuditExcel.class);

        util.exportExcel(customerAuditExcels, "审核用户", response);
    }

    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody CustomerAuditAddParam customerAuditAddParam) {
        return customerAuditService.saveByParam(customerAuditAddParam);
    }

    /**
     * 审核
     */
    @ApiOperation(value = "审核用户")
    @AuditLog(title = "审核用户", desc = "审核用户", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("customer:audit:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CustomerAuditEditParam customerAuditEditParam) {
        return customerAuditService.updateByParam(customerAuditEditParam);
    }

    /**
     * 判断当前用户是否为已审核用户
     */
    @ApiOperation("判断当前用户是否为已审核用户")
    @GetMapping("/checkIsAudit")
    //@Permission("customer:audit:list")
    public RestResponse<Boolean> checkIsAudit() {
        return RestResponse.ok(customerAuditService.checkIsAudit());
    }

    /**
     * 判断当前用户是否是游客
     */
    @ApiOperation("判断当前用户是否是游客")
    @GetMapping("/checkIsVisitor")
    //@Permission("customer:audit:list")
    public RestResponse<Boolean> checkIsVisitor() {
        return RestResponse.ok(customerAuditService.checkIsVisitor());
    }
}
