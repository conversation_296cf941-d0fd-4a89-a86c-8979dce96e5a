package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.appconfig.AppIconDto;
import cn.getech.ehm.system.dto.appicon.*;
import cn.getech.ehm.system.entity.AppConfigIcon;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 分类信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
public interface IAppConfigIconService extends IBaseService<AppConfigIcon> {

    /**
     * 分页查询，返回Dto
     *
     * @param appConfigIconQueryParam
     * @return
     */
    PageResult<AppConfigIconDto> pageDto(AppConfigIconQueryParam appConfigIconQueryParam);

    /**
     * 保存
     *
     * @param appConfigIconAddParam
     * @return
     */
    boolean saveByParam(AppConfigIconAddParam appConfigIconAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    AppConfigIconDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<AppConfigIconDto> rows);

    /**
     * 更新
     *
     * @param appConfigIconEditParam
     */
    boolean updateByParam(AppConfigIconEditParam appConfigIconEditParam);

    /**
     * 上移下移
     *
     * @param moveParam
     * @return
     */
    boolean moving(AppConfigMoveParam moveParam);

    /**
     * 获取app应用icon配置列表
     *
     * @return
     */
    List<AppIconDto> fetchAppConfigIconList();

    /**
     * 初始化
     * @return
     */
    Boolean initialization(String tenantId);
}