package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.useranalysis.*;
import cn.getech.ehm.system.entity.UserAction;
import cn.getech.ehm.system.mapper.UserActionMapper;
import cn.getech.ehm.system.service.IUserActionService;
import cn.getech.ehm.system.service.IUserAnalysisService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <pre>
 * 用户行为信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Slf4j
@Service
public class UserActionServiceImpl extends BaseServiceImpl<UserActionMapper, UserAction> implements IUserActionService {

    @Autowired
    private UserActionParamMapper userActionParamMapper;
    @Autowired
    private UserActionMapper userActionMapper;
    @Autowired
    private IUserAnalysisService userAnalysisService;

    @Override
    public PageResult<UserActionDto> pageDto(UserActionQueryParam userActionQueryParam) {
        Wrapper<UserAction> wrapper = getPageSearchWrapper(userActionQueryParam);
        PageResult<UserActionDto> result = userActionParamMapper.pageEntity2Dto(page(userActionQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UserActionAddParam userActionAddParam) {
        userActionAddParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        UserAction userAction = userActionParamMapper.addParam2Entity(userActionAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, userAction);
        boolean saveSuccess = save(userAction);
        if (saveSuccess) {
            UserAnalysisAddParam analysisAddParam = new UserAnalysisAddParam();
            analysisAddParam.setUid(userActionAddParam.getUid());
            userAnalysisService.saveByParam(analysisAddParam);
        }
        return saveSuccess;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UserActionEditParam userActionEditParam) {
        UserAction userAction = userActionParamMapper.editParam2Entity(userActionEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, userAction);
        return updateById(userAction);
    }


    @Override
    public UserActionDto getDtoById(Long id) {
        return userActionParamMapper.entity2Dto((UserAction) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UserActionDto> rows) {
        return saveBatch(userActionParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UserAction> getPageSearchWrapper(UserActionQueryParam queryParam) {
        LambdaQueryWrapper<UserAction> wrapper = Wrappers.lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(UserAction.class)) {
            wrapper.orderByDesc(UserAction::getUpdateTime, UserAction::getCreateTime);
        }
        return wrapper;
    }
}
