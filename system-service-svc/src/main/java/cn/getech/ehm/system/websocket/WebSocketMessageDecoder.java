package cn.getech.ehm.system.websocket;

import cn.getech.ehm.system.dto.WebSocketData;
import com.alibaba.fastjson.JSON;
import javax.websocket.Decoder;
import javax.websocket.EndpointConfig;

public class WebSocketMessageDecoder implements Decoder.Text<WebSocketData> {

    @Override
    public WebSocketData decode(String jsonMessage) {
        return JSON.parseObject(jsonMessage, WebSocketData.class);
    }

    @Override
    public boolean willDecode(String jsonMessage) {
        return JSON.isValidObject(jsonMessage);
    }

    @Override
    public void init(EndpointConfig ec) {
        System.out.println("WebSocketMessageDecoder -init method called");
    }

    @Override
    public void destroy() {
        System.out.println("WebSocketMessageDecoder - destroy method called");
    }

}
