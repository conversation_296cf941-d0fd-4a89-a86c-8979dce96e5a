package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.system.dto.app.SearchItemDto;
import cn.getech.ehm.system.dto.app.SearchParamDto;
import cn.getech.ehm.system.dto.appsearch.AppSearchHistoryDto;
import cn.getech.ehm.system.service.IAppIndexService;
import cn.getech.ehm.system.service.IAppSearchHistoryService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.param.PageParam;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.client.KnowledgeClient;
import cn.getech.ehm.knowledge.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * app首页信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@Slf4j
@Service
public class AppIndexServiceImpl implements IAppIndexService {

    @Autowired
    private IAppSearchHistoryService appSearchHistoryService;

    @Autowired
    private EquipmentClient equipmentClient;

    @Autowired
    private KnowledgeClient knowledgeClient;

    @Override
    public List<SearchItemDto> searchItemList(SearchParamDto searchParamDto) {
        AppSearchHistoryDto appSearchHistoryDto = new AppSearchHistoryDto();
        appSearchHistoryDto.setKeyword(searchParamDto.getKeyword());
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        String uid = null == user ? "" : user.getUid();
        appSearchHistoryDto.setUid(uid);

        appSearchHistoryService.saveSearchHistory(appSearchHistoryDto);

        KnowledgeDto knowledgeDto = searchKnowledge(searchParamDto);
        List<EquipmentListDto> equipmentList = searchEquipment(searchParamDto);

        List<SearchItemDto> itemDtoList = new ArrayList();
        for (int i = 0; i < 10; i++) {
            if (null != knowledgeDto) {
                addSearchDocsItem(itemDtoList, knowledgeDto.getKnowledgeDocDtos(), i);
                addSearchNewsItem(itemDtoList, knowledgeDto.getNewsInfoDtos(), i);
                addSearchCourseItem(itemDtoList, knowledgeDto.getCourseInfoDtos(), i);
            }
            addSearchEquipmentItem(itemDtoList, equipmentList, i);

            if (itemDtoList.size() >= 10) {
                break;
            }
        }
        return itemDtoList;
    }

    private KnowledgeDto searchKnowledge(SearchParamDto searchParamDto) {
        KnowledgeDto knowledgeDto = null;
        if (null == searchParamDto.getType() || searchParamDto.getType() <= 3 || searchParamDto.getType() == -1) {
            KnowledgeQueryParam knowledgeQueryParam = new KnowledgeQueryParam();
            knowledgeQueryParam.setKeyword(searchParamDto.getKeyword());

            try {
                RestResponse<KnowledgeDto> knowledgeResponse = knowledgeClient.listKnowledge(knowledgeQueryParam);
                if (knowledgeResponse.isOk()) {
                    knowledgeDto = knowledgeResponse.getData();
                }
            } catch (Exception e) {
                log.error("搜索知识库信息失败！");
            }
        }
        return knowledgeDto;
    }

    private List<EquipmentListDto> searchEquipment(SearchParamDto searchParamDto) {
        if (null == searchParamDto.getType() || searchParamDto.getType() == 5 || searchParamDto.getType() == -1) {
            PageParam pageParam = new PageParam();
            pageParam.setKeyword(searchParamDto.getKeyword());
            try {
                RestResponse<List<EquipmentListDto>> equipmentResponse = equipmentClient.search(pageParam);
                if (equipmentResponse.isOk()) {
                    return equipmentResponse.getData();
                }
            } catch (Exception e) {
                log.error("搜索设备信息失败");
            }
        }
        return new ArrayList();
    }

    private void addSearchDocsItem(List<SearchItemDto> itemDtoList, List<KnowledgeDocDto> dtoList, int index) {
        if (CollectionUtils.isEmpty(dtoList) || dtoList.size() <= index) {
            return;
        }
        KnowledgeDocDto knowledgeDocDto = dtoList.get(index);
        itemDtoList.add(initSearchItemDto(knowledgeDocDto.getAttachId(), knowledgeDocDto.getName(), KnowledgeType.DOC.getCode()));
    }

    private void addSearchNewsItem(List<SearchItemDto> itemDtoList, List<NewsInfoDto> dtoList, int index) {
        if (CollectionUtils.isEmpty(dtoList) || dtoList.size() <= index) {
            return;
        }
        NewsInfoDto newsInfoDto = dtoList.get(index);
        itemDtoList.add(initSearchItemDto(newsInfoDto.getId(), newsInfoDto.getTitle(), KnowledgeType.NEWS.getCode()));
    }

    private void addSearchCourseItem(List<SearchItemDto> itemDtoList, List<CourseInfoDto> dtoList, int index) {
        if (CollectionUtils.isEmpty(dtoList) || dtoList.size() <= index) {
            return;
        }
        CourseInfoDto courseInfoDto = dtoList.get(index);
        itemDtoList.add(initSearchItemDto(courseInfoDto.getId(), courseInfoDto.getTitle(), KnowledgeType.COURSE.getCode()));
    }

    private void addSearchEquipmentItem(List<SearchItemDto> itemDtoList, List<EquipmentListDto> dtoList, int index) {
        if (CollectionUtils.isEmpty(dtoList) || dtoList.size() <= index) {
            return;
        }
        EquipmentListDto listDto = dtoList.get(index);
        itemDtoList.add(initSearchItemDto(listDto.getEquipmentId(), listDto.getEquipmentName(), 5));
    }

    private SearchItemDto initSearchItemDto(String id, String name, int type) {
        SearchItemDto itemDto = new SearchItemDto();
        itemDto.setId(id);
        itemDto.setName(name);
        itemDto.setType(type);
        return itemDto;
    }

}
