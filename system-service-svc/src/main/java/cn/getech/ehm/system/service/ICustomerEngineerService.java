package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.customer.CustomerEngineerAddParam;
import cn.getech.ehm.system.dto.customer.CustomerEngineerEditParam;
import cn.getech.ehm.system.dto.customer.CustomerEngineerQueryParam;
import cn.getech.ehm.system.entity.CustomerEngineer;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.CustomerEngineerDto;

import java.util.List;

/**
 * <pre>
 * 客户工程师 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICustomerEngineerService extends IBaseService<CustomerEngineer> {

    /**
     * 分页查询，返回Dto
     *
     * @param customerEngineerQueryParam
     * @return
     */
    PageResult<CustomerEngineerDto> pageDto(CustomerEngineerQueryParam customerEngineerQueryParam);

    /**
     * 保存
     *
     * @param customerEngineerAddParam
     * @return
     */
    boolean saveByParam(CustomerEngineerAddParam customerEngineerAddParam);

    /**
     * 同步iot
     *
     * @param customerId
     * @param newUid
     * @param customerName
     */
    void buildIot(String customerId, String newUid, String customerName);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    CustomerEngineerDto getDtoById(String id);

    /**
     * 根据客户id批量启用/禁用
     *
     * @param customerId
     * @return
     */
    void updateStatusByCustomId(String customerId, Integer status, String customerUid);

    /**
     * 启用/禁用
     *
     * @param id
     * @return
     */
    Boolean updateStatus(String id, Integer status);

    /**
     * 更新
     *
     * @param customerEngineerEditParam
     */
    boolean updateByParam(CustomerEngineerEditParam customerEngineerEditParam);

    /**
     * 获取当前登录用户的客户id
     *
     * @return
     */
    String getCurrentCustomerId(String uid);

    /**
     * 获取客户下工程师数量
     *
     * @param customerId
     * @return
     */
    Integer engineerCount(String customerId);

    /**
     * 获取所有工程师数量
     *
     * @return
     */
    Integer engineerCount();

    /**
     * 根据客户id工程师uid集合
     *
     * @param customerId
     * @return
     */
    List<CustomerEngineer> getEngineerUidsByCustomerId(String customerId);

    /**
     * 获取已配置的uid
     *
     * @return
     */
    List<String> getAllUid();
}