package cn.getech.ehm.system.controller;


import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.appversion.AppVersionDto;
import cn.getech.ehm.system.service.IAppVersionService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * app版本信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
@RestController
@RequestMapping("/appVersion")
@Api(tags = "app接口：app版本信息服务接口")
public class AppVersionController {

    @Autowired
    private IAppVersionService appVersionService;

    /**
     * 获取最新app版本信息
     */
    @ApiOperation(value = "获取最新app版本信息")
    @GetMapping(value = "/latest")
    //@Permission("app:version:list")
    public RestResponse<AppVersionDto> latestVersion() {
        AppVersionDto appVersionDto = appVersionService.latestVersion(StaticValue.ZERO);
        return RestResponse.ok(null != appVersionDto ? appVersionDto : new AppVersionDto());
    }


}
