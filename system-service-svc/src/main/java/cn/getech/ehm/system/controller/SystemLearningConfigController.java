package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.learningconfig.SystemLearningAddParam;
import cn.getech.ehm.system.dto.learningconfig.SystemLearningDto;
import cn.getech.ehm.system.dto.learningconfig.SystemLearningGrandtotalDto;
import cn.getech.ehm.system.service.ISystemLearningConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.LearningConfigPersonCountDto;
import cn.getech.ehm.system.dto.TimeQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 培训认证控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@RestController
@RequestMapping("/systemLearningConfig")
@Api(tags = "培训认证服务接口")
public class SystemLearningConfigController {

    @Autowired
    private ISystemLearningConfigService systemLearningConfigService;


    /**
     * 查询培训认证信息
     */
    @ApiOperation("查询培训认证信息")
    @AuditLog(title = "培训认证", desc = "查询培训认证信息", businessType = BusinessType.INSERT)
    @GetMapping("/list")
    //@Permission("system:learning:config:update")
    public RestResponse<SystemLearningDto> list() {
        return RestResponse.ok(systemLearningConfigService.listLearningConfig());
    }

    /**
     * 配置培训认证
     */
    @ApiOperation("配置培训认证")
    @AuditLog(title = "培训认证", desc = "配置培训认证", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("system:learning:config:update")
    public RestResponse<Boolean> add(@RequestBody SystemLearningAddParam systemLearningAddParam) {
        return RestResponse.ok(systemLearningConfigService.saveByParam(systemLearningAddParam));
    }

    /**
     * 获取大屏 培训认证的培训人数
     *
     * @param timeQueryParam
     * @return
     */
    @ApiOperation(value = "获取大屏 培训认证的培训人数")
    @PostMapping("/onLinePersonCount")
    @AuditLog(title = "大屏", desc = "获取大屏 培训认证的培训人数", businessType = BusinessType.QUERY)
    public RestResponse<LearningConfigPersonCountDto> onLinePersonCount(@RequestBody TimeQueryParam timeQueryParam) {
        return RestResponse.ok(systemLearningConfigService.onLinePersonCount(timeQueryParam));
    }

    @ApiOperation(value = "获取大屏培训认证的累计信息")
    @PostMapping("/getScreenLearningConfigInfo")
    @AuditLog(title = "大屏", desc = "获取大屏培训认证的累计信息", businessType = BusinessType.QUERY)
    public RestResponse<SystemLearningGrandtotalDto> getScreenLearningConfigInfo() {
        return RestResponse.ok(systemLearningConfigService.getScreenLearningConfigInfo());
    }

}
