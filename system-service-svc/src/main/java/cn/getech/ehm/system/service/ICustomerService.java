package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.*;
import cn.getech.ehm.system.dto.customer.*;
import cn.getech.ehm.system.entity.Customer;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 客户列表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICustomerService extends IBaseService<Customer> {

    /**
     * 分页查询，返回Dto
     *
     * @param customerQueryParam
     * @return
     */
    PageResult<CustomerDto> pageDto(CustomerQueryParam customerQueryParam);

    /**
     * 获取全部客户
     *
     * @param customerQueryDto
     * @return
     */
    List<CustomerDto> list(CustomerQueryDto customerQueryDto);

    /**
     * 获取签约客户数
     *
     * @return
     */
    Integer listSignCustomerCount();

    /**
     * 获取客户数
     *
     * @return
     */
    Integer listCustomerCount();

    /**
     * 根据设备id集合获取map
     *
     * @param equipmentIds
     * @return
     */
    Map<String, EquipmentCustomerDto> getMapByEquipmentIds(String[] equipmentIds);

    /**
     * 根据设备id获取所属客户和名下工程师uid集合
     *
     * @param equipmentId
     * @return
     */
    List<String> getUidsByEquipmentId(String equipmentId);

    /**
     * 根据uid获取客户
     *
     * @param uid
     * @return
     */
    CustomerDto getDtoByUid(String uid);

    /**
     * 保存
     *
     * @param customerAddParam
     * @return
     */
    boolean saveByParam(CustomerAddParam customerAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    CustomerDto getDtoById(String id);

    /**
     * 根据id禁用/启用客户
     *
     * @param id
     * @return
     */
    Boolean updateStatusById(String id, Integer status);

    /**
     * 导入
     *
     * @param excels
     */
    boolean importExcel(List<CustomerExcel> excels);

    /**
     * 导入批量修改城市
     *
     * @param excels
     */
    boolean updateCityBatch(List<CustomerCityExcel> excels);

    /**
     * 更新
     *
     * @param customerEditParam
     */
    boolean updateByParam(CustomerEditParam customerEditParam);

    /**
     * 导出
     *
     * @return
     */
    List<CustomerExcel> exportExcel(String keyword, Integer area);

    /**
     * 校验uid是否已经绑定
     *
     * @param uid
     * @return
     */
    Boolean checkUid(String uid);

    /**
     * 批量添加客户
     *
     * @param customerImportDtos
     * @return
     */
    Boolean addBatchCustomer(List<CustomerImportDto> customerImportDtos);

    /**
     * 获取当前登录用户的客户id
     *
     * @return
     */
    String getCurrentCustomerId();

    /**
     * 获取当前登录用户的客户信息
     *
     * @return
     */
    CustomerDto getCurrentCustomer(String customerId);

    /**
     * 根据customerId获取客户信息
     *
     * @param customerId
     * @return
     */
    CustomerDto buildDto(String customerId);

    /**
     * 获取当前登录用户的设备id集合
     *
     * @return
     */
    List<String> getCurrentInfoIds();

    /**
     * 根据客户id获取的设备id集合
     *
     * @return
     */
    List<String> getInfoIdsByCustomerId(String customerId);

    /**
     * 根据客户名称获取的设备id集合
     *
     * @param customerName
     * @return
     */
    List<String> getCurrentInfoIdsByCustomerName(String customerName);

    /**
     * 根据两个客户名称获取的设备id集合
     *
     * @param customerName1
     * @param customerName2
     * @return
     */
    List<String> getEquipmentIdsByTwoCustomerNames(String customerName1, String customerName2);

    /**
     * 根据客户名称列表获取的设备id集合
     *
     * @param customerNames
     * @return
     */
    List<String> getEquipmentIdsByCustomerNames(String customerNames);

    /**
     * 获取当前登录用户工程师数量
     */
    Integer engineerCount();

    /**
     * 根据id获取大屏客户
     *
     * @param customerId
     * @return
     */
    CustomerDto getScreenDto(String customerId);

    /**
     * 获取所有客户和客户关联的设备Id
     *
     * @return
     */
    List<CustomerInfoEquipmentDto> getAllCustomerEqui();

    /**
     * 获取当前登录人的用户名称
     *
     * @return
     */
    String getCurrentCustomerName();

    /**
     * 根据客户编码获取客户id
     *
     * @param code
     * @return
     */
    CustomerDto getCustomerByCode(String code);

    /**
     * 根据客户id获取组态
     *
     * @param id
     * @return
     */
    CustomerStudioDto getStudioByCustomerId(String id);

    /**
     * 编辑客户组态
     *
     * @param studioEditParam
     * @return
     */
    Boolean updateCustomerStudio(StudioEditParam studioEditParam);

    /**
     * 校验设备是否被客户使用
     *
     * @param equipmentIds
     * @return
     */
    List<String> checkEquipmentUsed(String[] equipmentIds);

    /**
     * 根据用户名排序
     *
     * @param orderParam
     * @return
     */
    PageResult<EquipmentCustomerDto> orderByCustomerName(CustomerNameOrderParam orderParam);
}