package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.entity.CustomerEquipment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 客户设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Repository
public interface CustomerEquipmentMapper extends BaseMapper<CustomerEquipment> {
    /**
     * 根据客户名或者客户编码查询设备Ids
     *
     * @param customerName
     * @return
     */
    List<String> getCurrentInfoIdsByCustomerName(String customerName);

    /**
     * 根据设备名列表获取设备id集合
     *
     * @param customerNames
     * @return
     */
    List<String> getEquipmentIdsByCustomerNames(String customerNames);

    List<String> getEquipmentIdsByTwoCustomerNames(String customerName1, String customerName2);
}
