package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.equipment.dto.InfoSearchDto;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentAddParam;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentDto;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentParamMapper;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentQueryParam;
import cn.getech.ehm.system.entity.Customer;
import cn.getech.ehm.system.entity.CustomerEquipment;
import cn.getech.ehm.system.mapper.CustomerEquipmentMapper;
import cn.getech.ehm.system.service.ICustomerEquipmentService;
import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.ehm.system.utils.IotUtils;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.CustomerDto;
import cn.getech.ehm.system.dto.EquipmentDetailDto;
import cn.getech.ehm.system.dto.EquipmentEditDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <pre>
 * 客户设备 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CustomerEquipmentServiceImpl extends BaseServiceImpl<CustomerEquipmentMapper, CustomerEquipment> implements ICustomerEquipmentService {

    @Value("${iot.kjs.TRICON:6430ba3461a26a9e79e988dcc1b9de17}")
    private String TRICON;
    @Value("${iot.kjs.TSXPLUS:74ab491e521f897c0d98098409e5301b}")
    private String TSXPLUS;

    @Autowired
    private CustomerEquipmentParamMapper customerEquipmentParamMapper;
    @Autowired
    private CustomerEquipmentMapper customerEquipmentMapper;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Override
    public PageResult<CustomerEquipmentDto> pageDto(CustomerEquipmentQueryParam customerEquipmentQueryParam) {

        //前端查询过滤设备
        if (StringUtils.isNotBlank(customerEquipmentQueryParam.getKeyword())) {
            EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
            param.setKeyword(customerEquipmentQueryParam.getKeyword());
            RestResponse<List<String>> listRestResponse = equipmentClient.getEquipmentIdsByParam(param);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用equipment-service出错");
            } else {
                List<String> equipmentIds = listRestResponse.getData();
                if (CollectionUtils.isEmpty(equipmentIds)) {
                    return new PageResult<>();
                } else {
                    customerEquipmentQueryParam.setEquipmentIds(equipmentIds);
                }
            }
        }


        Wrapper<CustomerEquipment> wrapper = getPageSearchWrapper(customerEquipmentQueryParam);
        PageResult<CustomerEquipmentDto> result = customerEquipmentParamMapper.pageEntity2Dto(page(customerEquipmentQueryParam, wrapper));
        List<CustomerEquipmentDto> record = result.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            String[] equipmentIds = record.stream().map(CustomerEquipmentDto::getEquipmentId).distinct()
                    .collect(Collectors.toList()).stream().toArray(String[]::new);
            RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(equipmentIds);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用equipment-service出错");
            } else {
                Map<String, EquipmentListDto> stringListMap = listRestResponse.getData();
                record.stream().forEach(dto -> {
                    EquipmentListDto equipment = stringListMap.get(dto.getEquipmentId());
                    if (null != equipment) {
                        dto.setEquipmentName(equipment.getEquipmentName());
                        dto.setEquipmentCode(equipment.getEquipmentCode());
                        dto.setEquipmentCategoryId(equipment.getCategoryId());
                        dto.setEquipmentCategoryName(equipment.getCategoryName());
                        dto.setEquipmentSpecification(equipment.getSpecification());
                        dto.setEquipmentProductionTime(equipment.getProductionTime());
                    }
                });
            }
        }
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(CustomerEquipmentAddParam customerEquipmentAddParam) {
        List<CustomerEquipment> customerEquipments = new ArrayList<>(customerEquipmentAddParam.getEquipmentIds().size());
        List<String> equipmentIds = customerEquipmentAddParam.getEquipmentIds();
        String[] equipmentIdStrs = equipmentIds.toArray(new String[customerEquipmentAddParam.getEquipmentIds().size()]);
        RestResponse<Map<String, EquipmentListDto>> restResponse = equipmentClient.getListByIds(equipmentIdStrs);
        if (restResponse.isOk()) {
            Map<String, EquipmentListDto> map = restResponse.getData();

            List<EquipmentDetailDto> deviceInfoList = new ArrayList<>(equipmentIds.size());
            for (String equipmentId : equipmentIds) {
                CustomerEquipment customerEquipment = new CustomerEquipment();
                customerEquipment.setCustomerId(customerEquipmentAddParam.getCustomerId());
                customerEquipment.setEquipmentId(equipmentId);
                customerEquipments.add(customerEquipment);

                /*EquipmentListDto equipmentListDto = map.get(equipmentId);
                if (null != equipmentListDto) {
                    Integer platform = equipmentListDto.getPlatform();
                    String deviceTypeId = null;
                    if (platform == StaticValue.ZERO) {
                        deviceTypeId = TRICON;
                    } else if (platform == StaticValue.ONE) {
                        deviceTypeId = TSXPLUS;
                    }
                    //目前只跟iot对接前两个
                    if (StringUtils.isNotBlank(deviceTypeId)) {
                        EquipmentDetailDto equipmentDetailDto = new EquipmentDetailDto();
                        equipmentDetailDto.setDeviceId(equipmentListDto.getIotEquipmentId());
                        String deviceName = equipmentListDto.getEquipmentName() + "(" + equipmentListDto.getEquipmentCode() + ")";
                        equipmentDetailDto.setDeviceName(deviceName);
                        equipmentDetailDto.setDeviceTypeId(deviceTypeId);
                        equipmentDetailDto.setOutDeviceId(equipmentListDto.getEquipmentId());
                        deviceInfoList.add(equipmentDetailDto);
                    }
                }*/
            }
            //同步iot && 修改 设备所属客户
            if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                CustomerDto customerDto = customerService.getDtoById(customerEquipmentAddParam.getCustomerId());
                EquipmentEditDto equipmentEditDto = new EquipmentEditDto();
                equipmentEditDto.setDataAreaId(customerDto.getIotAreaId());
                equipmentEditDto.setDeviceInfoList(deviceInfoList);

                //同步更新设备表
                /*List<IotResDto> iotResDtos = IotUtils.addDeviceBatch(equipmentEditDto);
                List<IotEquipmentDto> iotEquipmentDtos = CopyDataUtil.copyList(iotResDtos, IotEquipmentDto.class);
                if(CollectionUtils.isNotEmpty(iotEquipmentDtos)){
                    log.info("选择的设备有部分从来没有同步过iot平台，需要绑定iot设备id");
                    RestResponse<Boolean> infoResponse =  equipmentClient.updateDeviceId(iotEquipmentDtos);
                    if(!infoResponse.isOk()){
                        log.info("同步iot设备id失败 ->" + JSONObject.toJSON(infoResponse.getMsg()));
                        throw new GlobalServiceException(GlobalResultMessage.of("同步设备信息失败"));
                    }
                }*/
            }
        } else {
            log.error("获取设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }

        return saveBatch(customerEquipments);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean deleteByCustomerId(String customerId, String equipmentId) {
        //同步解绑iot
        /*RestResponse<Map<String, EquipmentListDto>> restResponse = equipmentClient.getListByIds(new String[]{equipmentId});
        if (restResponse.isOk()) {
            Map<String, EquipmentListDto> map = restResponse.getData();
            EquipmentListDto equipmentListDto = map.get(equipmentId);
            if (null != equipmentListDto) {
                if (StringUtils.isNotBlank(equipmentListDto.getIotEquipmentId())) {
                    EquipmentDetailDto equipmentDetailDto = new EquipmentDetailDto();
                    equipmentDetailDto.setDeviceId(equipmentListDto.getIotEquipmentId());
                    String deviceName = equipmentListDto.getEquipmentName() + "(" + equipmentListDto.getEquipmentCode() + ")";
                    equipmentDetailDto.setDeviceName(deviceName);
                    IotUtils.unbindDevice(equipmentDetailDto);
                }
            } else {
                throw new GlobalServiceException(GlobalResultMessage.of("未获取到设备信息"));
            }
        }*/


        LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEquipment::getCustomerId, customerId);
        wrapper.eq(CustomerEquipment::getEquipmentId, equipmentId);
        return customerEquipmentMapper.delete(wrapper) > StaticValue.ZERO;
    }

    @Override
    public Boolean addBatchRel(Map<String, List<String>> customerInfoMap) {
        List<CustomerEquipment> customerEquipments = new ArrayList<>();
        for (Map.Entry<String, List<String>> entity : customerInfoMap.entrySet()) {
            String customerId = entity.getKey();
            List<String> equipmentIds = entity.getValue();
            for (String equipmentId : equipmentIds) {
                CustomerEquipment customerEquipment = new CustomerEquipment();
                customerEquipment.setCustomerId(customerId);
                customerEquipment.setEquipmentId(equipmentId);
                customerEquipments.add(customerEquipment);
            }
        }
        return saveBatch(customerEquipments);
    }

    @Override
    public List<String> getCurrentInfoIds(String customerId) {
        if (StringUtils.isNotBlank(customerId)) {
            LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(CustomerEquipment::getCustomerId, customerId);
            List<CustomerEquipment> customerEquipments = customerEquipmentMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(customerEquipments)) {
                List<String> equipmentIds = customerEquipments.stream().map(CustomerEquipment::getEquipmentId).distinct().collect(Collectors.toList());
                return equipmentIds;
            }
        }
        return null;
    }

    @Override
    public List<String> getCurrentInfoIdsByCustomerName(String customerName) {
        if (StringUtils.isNotBlank(customerName)) {
            List<String> equipmentIds = customerEquipmentMapper.getCurrentInfoIdsByCustomerName(customerName);
            return equipmentIds;
        }
        return null;
    }

    @Override
    public List<String> getEquipmentIdsByCustomerNames(String customerNames) {
        if (StringUtils.isNotBlank(customerNames)) {
            List<String> equipmentIds = customerEquipmentMapper.getEquipmentIdsByCustomerNames(customerNames);
            return equipmentIds;
        }
        return null;
    }

    @Override
    public List<String> getEquipmentIdsByTwoCustomerNames(String customerName1, String customerName2) {
        if (StringUtils.isNotBlank(customerName1) && StringUtils.isNotBlank(customerName2)) {
            List<String> equipmentIds = customerEquipmentMapper.getEquipmentIdsByTwoCustomerNames(customerName1, customerName2);
            return equipmentIds;
        }
        return null;
    }

    @Override
    public List<String> getUsedInfoIds() {
        LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.select(CustomerEquipment::getId, CustomerEquipment::getEquipmentId);
        List<CustomerEquipment> customerEquipments = customerEquipmentMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customerEquipments)) {
            return customerEquipments.stream().map(CustomerEquipment::getEquipmentId).distinct().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Boolean checkInfoUsed(String equipmentId) {
        LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEquipment::getEquipmentId, equipmentId);
        wrapper.select(CustomerEquipment::getCustomerId);
        List<CustomerEquipment> customerEquipments = customerEquipmentMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customerEquipments)) {
            String customerId = customerEquipments.get(0).getCustomerId();
            Customer customer = (Customer) customerService.getById(customerId);
            if (null != customer) {
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("info_referenced", new Object[]{customer.getName()}, LocaleContextHolder.getLocale())));
            }

        }
        return true;
    }

    @Override
    public CustomerDto getCustomerIdByInfoId(String equipmentId) {
        LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEquipment::getEquipmentId, equipmentId);
        wrapper.select(CustomerEquipment::getCustomerId);
        CustomerEquipment customerEquipment = customerEquipmentMapper.selectOne(wrapper);
        if (null != customerEquipment) {
            String customerId = customerEquipment.getCustomerId();
            CustomerDto customerDto = customerService.getDtoById(customerId);
            return customerDto;
        }
        return null;
    }

    @Override
    public List<String> searchEquipmentListByUserName(String name) {
        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.like(Customer::getName, name);
        List<Customer> customerList = customerService.getBaseMapper().selectList(wrapper);
        if (CollectionUtils.isEmpty(customerList)) {
            return Collections.emptyList();
        }

        List<String> customerIdList = customerList.stream().map(Customer::getId).collect(Collectors.toList());
        LambdaQueryWrapper<CustomerEquipment> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(CustomerEquipment::getCustomerId, customerIdList);
        List<CustomerEquipment> equipmentList = customerEquipmentMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(equipmentList)) {
            return Collections.emptyList();
        }
        return equipmentList.stream().map(CustomerEquipment::getEquipmentId).collect(Collectors.toList());
    }

    @Override
    public List<CustomerEquipmentDto> listByCustomerIds(List<String> customerIds) {
        LambdaQueryWrapper<CustomerEquipment> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(CustomerEquipment::getCustomerId, CustomerEquipment::getEquipmentId);
        queryWrapper.in(customerIds.size() != 0, CustomerEquipment::getCustomerId, customerIds);
        return customerEquipmentParamMapper.entityList2Dto(list(queryWrapper));
    }

    @Override
    public String[] checkCustomerEquipmentUsed(String[] equipmentIds) {
        LambdaQueryWrapper<CustomerEquipment> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CustomerEquipment::getEquipmentId, equipmentIds);
        queryWrapper.select(CustomerEquipment::getEquipmentId);
        List<CustomerEquipment> list = list(queryWrapper);
        return list.stream().map(e -> e.getEquipmentId()).distinct().toArray(String[]::new);
    }


    private Wrapper<CustomerEquipment> getPageSearchWrapper(CustomerEquipmentQueryParam customerEquipmentQueryParam) {
        LambdaQueryWrapper<CustomerEquipment> wrapper = Wrappers.<CustomerEquipment>lambdaQuery();
        if (CollectionUtils.isNotEmpty(customerEquipmentQueryParam.getEquipmentIds())) {
            wrapper.in(CustomerEquipment::getEquipmentId, customerEquipmentQueryParam.getEquipmentIds());
        }
        wrapper.eq(CustomerEquipment::getCustomerId, customerEquipmentQueryParam.getCustomerId());
        if (BaseEntity.class.isAssignableFrom(CustomerEquipment.class)) {
            wrapper.orderByDesc(CustomerEquipment::getUpdateTime, CustomerEquipment::getCreateTime);
        }
        return wrapper;
    }
}
