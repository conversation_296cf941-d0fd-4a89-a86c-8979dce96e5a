package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.learningconfig.SystemLearningGrandtotalAddParam;
import cn.getech.ehm.system.dto.learningconfig.SystemLearningGrandtotalDto;
import cn.getech.ehm.system.dto.learningconfig.SystemLearningGrandtotalEditParam;
import cn.getech.ehm.system.dto.learningconfig.SystemLearningGrandtotalQueryParam;
import cn.getech.ehm.system.entity.SystemLearningGrandtotal;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 培训认证累计表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public interface ISystemLearningGrandtotalService extends IBaseService<SystemLearningGrandtotal> {

    /**
     * 分页查询，返回Dto
     *
     * @param systemLearningGrandtotalQueryParam
     * @return
     */
    PageResult<SystemLearningGrandtotalDto> pageDto(SystemLearningGrandtotalQueryParam systemLearningGrandtotalQueryParam);

    /**
     * 保存
     *
     * @param systemLearningGrandtotalAddParam
     * @return
     */
    boolean saveByParam(SystemLearningGrandtotalAddParam systemLearningGrandtotalAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    SystemLearningGrandtotalDto getDtoById(Long id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<SystemLearningGrandtotalDto> rows);

    /**
     * 更新
     *
     * @param systemLearningGrandtotalEditParam
     */
    boolean updateByParam(SystemLearningGrandtotalEditParam systemLearningGrandtotalEditParam);

    /**
     * 获取 培训认证累计信息
     *
     * @return
     */
    SystemLearningGrandtotalDto getDto();
}