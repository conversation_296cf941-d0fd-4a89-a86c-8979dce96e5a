package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.app.SearchItemDto;
import cn.getech.ehm.system.dto.app.SearchParamDto;
import cn.getech.ehm.system.dto.appsearch.AppSearchHistoryDto;
import cn.getech.ehm.system.service.IAppIndexService;
import cn.getech.ehm.system.service.IAppSearchHistoryService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * app首页控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@RestController
@RequestMapping("/app/index")
@Api(tags = "app接口：app首页")
public class AppIndexController {

    @Autowired
    private IAppIndexService appIndexService;

    @Autowired
    private IAppSearchHistoryService appSearchHistoryService;

    /**
     * app首页搜索接口
     */
    @ApiOperation(value = "app首页搜索接口", notes = "知识库搜索，传 1 2 3 任意值")
    @GetMapping("/search")
    public RestResponse<List<SearchItemDto>> searchItemList(SearchParamDto searchParamDto) {
        return RestResponse.ok(appIndexService.searchItemList(searchParamDto));
    }

    /**
     * app首页历史记录保存
     *
     * @param keyword
     * @return
     */
    @ApiOperation(value = "app首页历史记录保存")
    @PostMapping("/saveSearchHistory")
    public RestResponse<Boolean> saveSearchHistory(@RequestBody String keyword) {
        AppSearchHistoryDto appSearchHistoryDto = new AppSearchHistoryDto();
        appSearchHistoryDto.setKeyword(keyword);
        appSearchHistoryDto.setUid(PorosContextHolder.getCurrentUser().getUid());
        return RestResponse.ok(appSearchHistoryService.saveSearchHistory(appSearchHistoryDto));
    }

    /**
     * app 首页搜索历史记录
     */
    @ApiOperation(value = "app首页搜索历史记录")
    @GetMapping("/searchHistory")
    public RestResponse<List<AppSearchHistoryDto>> searchHistory() {
        return RestResponse.ok(appSearchHistoryService.searchHistory());
    }

    /**
     * 删除app 首页搜索历史记录
     */
    @ApiOperation(value = "删除app首页搜索历史记录")
    @DeleteMapping("/searchHistory")
    public RestResponse<Boolean> delete() {
        return RestResponse.ok(appSearchHistoryService.deleteByUid());
    }
}
