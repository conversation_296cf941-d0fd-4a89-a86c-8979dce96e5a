package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.systemconfig.SystemConfigAddParam;
import cn.getech.ehm.system.dto.systemconfig.SystemConfigEditParam;
import cn.getech.ehm.system.dto.systemconfig.SystemConfigQueryParam;
import cn.getech.ehm.system.entity.SystemConfig;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.SystemConfigDto;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 系统配置信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
public interface ISystemConfigService extends IBaseService<SystemConfig> {

    /**
     * 分页查询，返回Dto
     *
     * @param systemConfigQueryParam
     * @return
     */
    PageResult<SystemConfigDto> pageDto(SystemConfigQueryParam systemConfigQueryParam);

    /**
     * 保存
     *
     * @param systemConfigAddParam
     * @return
     */
    boolean saveByParam(SystemConfigAddParam systemConfigAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    SystemConfigDto getDtoById(String id);

    /**
     * 根据name查询，转dto
     *
     * @param name
     * @return
     */
    PageResult<SystemConfigDto> listByName(String name);

    /**
     * 根据names查询，转dto(多个名称以逗号分隔）
     *
     * @param names
     * @return
     */
    Map<String, SystemConfigDto> mapByNames(String names);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<SystemConfigDto> rows);

    /**
     * 更新
     *
     * @param systemConfigEditParam
     */
    boolean updateByParam(SystemConfigEditParam systemConfigEditParam);

}