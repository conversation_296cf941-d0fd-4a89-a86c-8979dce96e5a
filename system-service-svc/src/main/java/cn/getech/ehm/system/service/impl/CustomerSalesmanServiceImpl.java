package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.customer.*;
import cn.getech.ehm.system.entity.CustomerSalesman;
import cn.getech.ehm.system.mapper.CustomerSalesmanMapper;
import cn.getech.ehm.system.service.ICustomerSalesmanService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <pre>
 * 客户销售人员 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-07
 */
@Slf4j
@Service
public class CustomerSalesmanServiceImpl extends BaseServiceImpl<CustomerSalesmanMapper, CustomerSalesman> implements ICustomerSalesmanService {

    @Autowired
    private CustomerSalesmanParamMapper customerSalesmanParamMapper;


    @Override
    public PageResult<CustomerSalesmanDto> pageDto(CustomerSalesmanQueryParam customerSalesmanQueryParam) {
        Wrapper<CustomerSalesman> wrapper = getPageSearchWrapper(customerSalesmanQueryParam);
        PageResult<CustomerSalesmanDto> result = customerSalesmanParamMapper.pageEntity2Dto(page(customerSalesmanQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(CustomerSalesmanAddParam customerSalesmanAddParam) {
        CustomerSalesman customerSalesman = customerSalesmanParamMapper.addParam2Entity(customerSalesmanAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customerSalesman);
        return save(customerSalesman);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CustomerSalesmanEditParam customerSalesmanEditParam) {
        CustomerSalesman customerSalesman = customerSalesmanParamMapper.editParam2Entity(customerSalesmanEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customerSalesman);
        return updateById(customerSalesman);
    }

    @Override
    public boolean removeByIds(String[] ids) {
        return removeByIds(Stream.of(ids).collect(Collectors.toList()));
    }

    @Override
    public List<String> listSalesman(String customerId) {
        List<String> uids = new ArrayList<>();
        LambdaQueryWrapper<CustomerSalesman> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerSalesman::getCustomerId, customerId);
        List<CustomerSalesman> list = list(queryWrapper);
        list.forEach(sales -> {
            uids.add(sales.getUid());
        });
        return uids;
    }


    @Override
    public CustomerSalesmanDto getDtoById(String id) {
        return customerSalesmanParamMapper.entity2Dto((CustomerSalesman) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<CustomerSalesmanDto> rows) {
        return saveBatch(customerSalesmanParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<CustomerSalesman> getPageSearchWrapper(CustomerSalesmanQueryParam customerSalesmanQueryParam) {
        LambdaQueryWrapper<CustomerSalesman> wrapper = Wrappers.<CustomerSalesman>lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(CustomerSalesman.class)) {
            wrapper.orderByDesc(CustomerSalesman::getUpdateTime, CustomerSalesman::getCreateTime);
        }
        return wrapper;
    }
}
