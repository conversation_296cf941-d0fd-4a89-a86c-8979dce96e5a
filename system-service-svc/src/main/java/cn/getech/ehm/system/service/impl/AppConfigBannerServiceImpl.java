package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.appbanner.*;
import cn.getech.ehm.system.dto.appconfig.AppBannerDto;
import cn.getech.ehm.system.entity.AppConfigBanner;
import cn.getech.ehm.system.mapper.AppConfigBannerMapper;
import cn.getech.ehm.system.service.IAppConfigBannerService;
import cn.getech.ehm.system.service.ISystemConfigService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <pre>
 * 轮播图 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Slf4j
@Service
public class AppConfigBannerServiceImpl extends BaseServiceImpl<AppConfigBannerMapper, AppConfigBanner> implements IAppConfigBannerService {

    @Autowired
    private AppConfigBannerParamMapper appConfigBannerParamMapper;
    @Autowired
    private AppConfigBannerMapper appConfigBannerMapper;
    @Autowired
    private ISystemConfigService systemConfigService;

    @Override
    public PageResult<AppConfigBannerDto> pageDto(AppConfigBannerQueryParam appConfigBannerQueryParam) {
        Wrapper<AppConfigBanner> wrapper = getPageSearchWrapper(appConfigBannerQueryParam);
        PageResult<AppConfigBannerDto> result = appConfigBannerParamMapper.pageEntity2Dto(page(appConfigBannerQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(AppConfigBannerAddParam appConfigBannerAddParam) {
        AppConfigBanner appConfigBanner = appConfigBannerParamMapper.addParam2Entity(appConfigBannerAddParam);
        appConfigBanner.setPublishTime(DateUtils.getNowDate());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigBanner);
        initNewsUrl(appConfigBanner);
        return save(appConfigBanner);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(AppConfigBannerEditParam appConfigBannerEditParam) {
        AppConfigBanner appConfigBanner = appConfigBannerParamMapper.editParam2Entity(appConfigBannerEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appConfigBanner);
        initNewsUrl(appConfigBanner);
        return updateById(appConfigBanner);
    }


    @Override
    public AppConfigBannerDto getDtoById(String id) {
        return appConfigBannerParamMapper.entity2Dto((AppConfigBanner) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<AppConfigBannerDto> rows) {
        return saveBatch(appConfigBannerParamMapper.dtoList2Entity(rows));
    }

    @Override
    public List<AppBannerDto> fetchAppConfigBannerList() {
        return appConfigBannerMapper.fetchAppBannerList();
    }

    private void initNewsUrl(AppConfigBanner appConfigBanner) {
        String newsDetail = "news_detail_url";
        Map<String, SystemConfigDto> configDtoMap = systemConfigService.mapByNames(newsDetail);
        if (CollectionUtils.isNotEmpty(configDtoMap)) {
            appConfigBanner.setNewsUrl(configDtoMap.get(newsDetail).getCfgValue() + appConfigBanner.getNewsId());
        }
    }

    private Wrapper<AppConfigBanner> getPageSearchWrapper(AppConfigBannerQueryParam queryParam) {
        LambdaQueryWrapper<AppConfigBanner> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), AppConfigBanner::getName, queryParam.getName());
        wrapper.like(StringUtils.isNotBlank(queryParam.getNewsTitle()), AppConfigBanner::getNewsTitle, queryParam.getNewsTitle());
        if (BaseEntity.class.isAssignableFrom(AppConfigBanner.class)) {
            wrapper.orderByDesc(AppConfigBanner::getUpdateTime, AppConfigBanner::getCreateTime);
        }
        return wrapper;
    }
}
