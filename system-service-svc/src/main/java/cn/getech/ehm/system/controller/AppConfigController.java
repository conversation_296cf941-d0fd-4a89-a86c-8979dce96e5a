package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.dto.appconfig.AppConfigDto;
import cn.getech.ehm.system.service.IAppConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.DataTypeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * app应用配置控制器
 * <AUTHOR>
 * @since 2020-11-04
 */
@RestController
@RequestMapping("/app/appConfig")
@Api(tags = "app接口：app应用配置服务接口")
public class AppConfigController {

    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 获取app应用配置信息
     */
    @ApiOperation("获取app应用配置信息接口")
    @GetMapping
    //@Permission("app:config:icon:list")
    public RestResponse<AppConfigDto> appConfig() {
        return RestResponse.ok(appConfigService.getAppConfig());
    }

    /**
     * 获取app应用icon配置列表
     */
    @ApiOperation(value = "获取app应用数据类型配置列表", notes = "对应 WarnFirstPageDto RepairFirstPageDto AppContractRecordDto KeTianYunLiveInfoDto")
    @GetMapping("/dataTypeList")
    //@Permission("app:config:data:type:list")
    public RestResponse<DataTypeDto> dataTypeList() {
        return RestResponse.ok(appConfigService.dataTypeList());
    }
}
