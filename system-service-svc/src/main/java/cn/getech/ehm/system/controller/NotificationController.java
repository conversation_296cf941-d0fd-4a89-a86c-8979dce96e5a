package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.config.WebSocketServer;
import cn.getech.ehm.system.dto.WebSocketData;
import cn.getech.ehm.system.dto.notification.NotificationDto;
import cn.getech.ehm.system.dto.notification.NotificationQueryParam;
import cn.getech.ehm.system.notify.NotifyService;
import cn.getech.ehm.system.service.INotificationService;
import cn.getech.ehm.system.service.impl.SystemCustomServiceImpl;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020-12-15 14:42:37
 **/
@RestController
@RequestMapping("/notification")
@Api(tags = "消息通知历史记录")
public class NotificationController {

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private INotificationService customizeSendService;
    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private SystemCustomServiceImpl systemCustomService;

    @PostMapping("/send")
    @ResponseBody
    public RestResponse<String> sentNotify(@RequestBody NotifyParam notifyParam) {
        notifyService.sendNotify(notifyParam);
        return RestResponse.ok("推送成功");
    }

    @GetMapping("/sendWebSocketTest")
    @ResponseBody
    public RestResponse<String> sendWebSocketTest() {
        for (int i = 0; i < 100; i++) {
            WebSocketData<Object> webSocketData = WebSocketData.builder().key("dataKey" + i).data(i).build();
            webSocketServer.sendMessage("scada", webSocketData);
        }

        return RestResponse.ok("推送成功");
    }

    /**
     * 推送历史记录
     */
    @ApiOperation("推送历史记录")
    @PostMapping("/list")
    //@Permission("customer:audit:list")
    public RestResponse<PageResult<NotificationDto>> pageList(@RequestBody @Valid NotificationQueryParam notificationQueryParam) {
        return RestResponse.ok(customizeSendService.pageDto(notificationQueryParam));
    }

    @PostMapping("/pushData")
    public RestResponse<String> pushData(@RequestBody WebSocketData webSocketData) {
        notifyService.pushData(webSocketData);
        return RestResponse.ok("websocket推送成功");
    }

    @GetMapping("/markTodo")
    public RestResponse markTodo(String serviceId) {
        systemCustomService.markTodo(serviceId);
        return RestResponse.ok();
    }

}
