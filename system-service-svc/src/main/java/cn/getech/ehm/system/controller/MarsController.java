package cn.getech.ehm.system.controller;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.system.dto.mars.TokenResponse;
import cn.getech.ehm.system.properties.MarsProperty;
import cn.getech.poros.framework.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Mars Controller
 *
 * <AUTHOR>
 * @date 2021-01-13 10:40:09
 **/
@Slf4j
@Controller
@RequestMapping("/mars")
public class MarsController {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private MarsProperty marsProperty;

    /**
     * mars授权跳转
     *
     * @return
     */
    @GetMapping("/oauth/redirect")
    public String marsOauthLogin() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        Map<String, String> body = new HashMap<>(2);
        body.put("username", marsProperty.getUsername());
        body.put("password", marsProperty.getPassword());
        HttpEntity<Map> httpEntity = new HttpEntity<>(body, headers);
        String authUrl = marsProperty.getApiHost() + marsProperty.getApiPrefix() + marsProperty.getAuthApi();
        ResponseEntity<TokenResponse> tokenRestResponse = restTemplate.exchange(authUrl, HttpMethod.POST, httpEntity, TokenResponse.class);
        if (tokenRestResponse.getStatusCode().isError()) {
            log.error("获取mars系统token失败,错误信息:" + tokenRestResponse.getBody().getMessage());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        String token = tokenRestResponse.getBody().getToken();
        String redirectUrl = marsProperty.getApiHost() + marsProperty.getIndexPath() + "?token=" + token;
        log.info("Mars授权跳转url:" + redirectUrl);
        return "redirect:" + redirectUrl;
    }

}
