package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.appicon.*;
import cn.getech.ehm.system.service.IAppConfigIconService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * app应用icon配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@RestController
@RequestMapping("/web/appConfigIcon")
@Api(tags = "web接口：app应用icon配置服务接口")
public class WebAppConfigIconController {

    @Autowired
    private IAppConfigIconService appConfigIconService;

    /**
     * 分页获取app应用icon配置列表
     */
    @ApiOperation("分页获取app应用icon配置列表")
    @GetMapping("/list")
    //@Permission("app:config:icon:list")
    public RestResponse<PageResult<AppConfigIconDto>> pageList(@Valid AppConfigIconQueryParam appConfigIconQueryParam) {
        return RestResponse.ok(appConfigIconService.pageDto(appConfigIconQueryParam));
    }

    /**
     * 新增app应用icon配置
     */
    @ApiOperation("新增app应用icon配置")
    @AuditLog(title = "app应用icon配置", desc = "新增app应用icon配置", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("app:config:icon:update")
    public RestResponse<Boolean> add(@RequestBody @Valid AppConfigIconAddParam appConfigIconAddParam) {
        return RestResponse.ok(appConfigIconService.saveByParam(appConfigIconAddParam));
    }

    /**
     * 修改app应用icon配置
     */
    @ApiOperation(value = "修改app应用icon配置")
    @AuditLog(title = "app应用icon配置", desc = "修改app应用icon配置", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("app:config:icon:update")
    public RestResponse<Boolean> update(@RequestBody @Valid AppConfigIconEditParam appConfigIconEditParam) {
        return RestResponse.ok(appConfigIconService.updateByParam(appConfigIconEditParam));
    }

    /**
     * 上移下移icon配置
     */
    @ApiOperation(value = "上移下移icon配置")
    @AuditLog(title = "app应用icon配置", desc = "上移下移icon配置", businessType = BusinessType.UPDATE)
    @PutMapping("moving")
    //@Permission("app:config:icon:update")
    public RestResponse<Boolean> moving(@RequestBody @Valid AppConfigMoveParam appConfigMoveParam) {
        return RestResponse.ok(appConfigIconService.moving(appConfigMoveParam));
    }

    /**
     * 根据id删除app应用icon配置
     */
    @ApiOperation(value = "根据id删除app应用icon配置")
    @AuditLog(title = "app应用icon配置", desc = "app应用icon配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("app:config:icon:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(appConfigIconService.removeById(id));
    }

    /**
     * 根据id获取app应用icon配置
     */
    @ApiOperation(value = "根据id获取app应用icon配置")
    @GetMapping(value = "/{id}")
    //@Permission("app:config:icon:list")
    public RestResponse<AppConfigIconDto> get(@PathVariable String id) {
        return RestResponse.ok(appConfigIconService.getDtoById(id));
    }
    /**
     * 初始化表单配置
     */
    @ApiOperation("初始化表单配置")
    @GetMapping("/initialization")
    public RestResponse<Boolean> initialization(@RequestParam String tenantId){
        return RestResponse.ok(appConfigIconService.initialization(tenantId));
    }
}
