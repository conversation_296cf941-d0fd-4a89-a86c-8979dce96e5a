package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.customer.CustomerSalesmanAddParam;
import cn.getech.ehm.system.dto.customer.CustomerSalesmanDto;
import cn.getech.ehm.system.dto.customer.CustomerSalesmanEditParam;
import cn.getech.ehm.system.dto.customer.CustomerSalesmanQueryParam;
import cn.getech.ehm.system.entity.CustomerSalesman;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 客户销售人员 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-07
 */
public interface ICustomerSalesmanService extends IBaseService<CustomerSalesman> {

    /**
     * 分页查询，返回Dto
     *
     * @param customerSalesmanQueryParam
     * @return
     */
    PageResult<CustomerSalesmanDto> pageDto(CustomerSalesmanQueryParam customerSalesmanQueryParam);

    /**
     * 保存
     *
     * @param customerSalesmanAddParam
     * @return
     */
    boolean saveByParam(CustomerSalesmanAddParam customerSalesmanAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    CustomerSalesmanDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<CustomerSalesmanDto> rows);

    /**
     * 更新
     *
     * @param customerSalesmanEditParam
     */
    boolean updateByParam(CustomerSalesmanEditParam customerSalesmanEditParam);

    /**
     * 批量删除
     *
     * @param id
     * @return
     */
    boolean removeByIds(String[] id);

    /**
     * 根据客户Id查找销售人员UID
     *
     * @param customerId
     * @return
     */
    List<String> listSalesman(String customerId);
}