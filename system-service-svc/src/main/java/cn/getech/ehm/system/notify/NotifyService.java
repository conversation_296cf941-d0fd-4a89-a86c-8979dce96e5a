package cn.getech.ehm.system.notify;

import cn.getech.ehm.system.config.WebSocketServer;
import cn.getech.ehm.system.dto.WebSocketData;
import cn.getech.ehm.system.dto.notification.NotificationAddParam;
import cn.getech.ehm.system.dto.notify.*;
import cn.getech.ehm.system.notify.enmu.DeviceType;
import cn.getech.ehm.system.notify.enmu.PushType;
import cn.getech.ehm.system.notify.enmu.TargetType;
import cn.getech.ehm.system.service.INotificationService;
import cn.getech.ehm.system.service.TbeaNotifyService;
import cn.getech.ehm.system.service.impl.SystemCustomServiceImpl;
import cn.getech.ehm.system.service.impl.WxService;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date 2020-12-18 13:50:10
 **/
@Slf4j
@Service
public class NotifyService {

    @Autowired
    private MailService mailService;

    @Autowired
    private PushService pushService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private INotificationService notificationService;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private WxService wxService;
    @Autowired
    private SystemCustomServiceImpl systemCustomService;

    /**
     * 发送通知
     *
     * @param notifyParam
     */
    public void sendNotify(NotifyParam notifyParam) {
        EnumSet<NotifyType> notifyTypeEnumSet = notifyParam.getNotifyTypes();
        if (notifyTypeEnumSet.contains(NotifyType.SYSTEM)) {
            this.storeNotification(notifyParam.getSystemNotify().getTitle(), notifyParam.getSystemNotify().getContent(), NotifyType.SYSTEM, notifyParam.getNotifySource(), notifyParam.getSystemNotify().getNames());
            for (String uid : notifyParam.getSystemNotify().getUids()) {
                String topic = (StringUtils.isNotBlank(notifyParam.getSystemNotify().getTopicName()) ? notifyParam.getSystemNotify().getTopicName() : "notice") + StringPool.DASH + uid;
                //log.info("topic = " + topic);
                SystemNotice systemNotice = SystemNotice.builder()
                        .type(notifyParam.getSystemNotify().getType())
                        .title(notifyParam.getSystemNotify().getTitle())
                        .content(notifyParam.getSystemNotify().getContent())
                        .time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build();
                WebSocketData webSocketData = WebSocketData.builder().topic(topic).key(notifyParam.getSystemNotify().getType()).data(systemNotice).build();
                this.pushData(webSocketData);
            }
        }
        if (notifyTypeEnumSet.contains(NotifyType.SMS)) {
            try {
                smsService.sendSms(notifyParam.getSmsNotify().getSign(), notifyParam.getSmsNotify().getTemplate(), notifyParam.getSmsNotify().getParams(), notifyParam.getSmsNotify().getMobiles());
                log.debug("发送短信通知成功,接收号码:%s,参数:%s", notifyParam.getSmsNotify().getMobiles(), notifyParam.getSmsNotify().getParams());
            } catch (Exception e) {
                log.error("发送短信通知异常:", e.getLocalizedMessage());
            }
            this.storeNotification("短信通知", notifyParam.getSmsNotify().getSign() + notifyParam.getSmsNotify().getParams().values(), NotifyType.SMS, notifyParam.getNotifySource(), notifyParam.getSmsNotify().getMobiles());
        }
        if (notifyTypeEnumSet.contains(NotifyType.APP)) {
            try {
                pushService.push(PushType.NOTICE, DeviceType.ANDROID, TargetType.ACCOUNT, notifyParam.getPushNotify().getTitle(), notifyParam.getPushNotify().getContent(), notifyParam.getPushNotify().getUids());
            } catch (Exception e) {
                log.error("发送APP推送异常:", e.getLocalizedMessage());
            }
            this.storeNotification(notifyParam.getPushNotify().getTitle(), notifyParam.getPushNotify().getContent(), NotifyType.APP, notifyParam.getNotifySource(), notifyParam.getPushNotify().getNames());
        }
        if (notifyTypeEnumSet.contains(NotifyType.EMAIL)) {
            try {
                mailService.sendEmail(notifyParam.getEmailNotify().getTitle(), notifyParam.getEmailNotify().getContent(), notifyParam.getEmailNotify().getEmails());
                log.info(String.format("发送邮件通知成功,接收邮箱:%s,内容:%s", StringUtils.join(notifyParam.getEmailNotify().getEmails()), notifyParam.getEmailNotify().getContent()));
            } catch (Exception e) {
                log.error("发送邮件通知异常:", e.getMessage());
            }
            this.storeNotification(notifyParam.getEmailNotify().getTitle(), notifyParam.getEmailNotify().getContent(), NotifyType.EMAIL, notifyParam.getNotifySource(), notifyParam.getEmailNotify().getEmails());
        }
        if (notifyTypeEnumSet.contains(NotifyType.WXCP)) {
            try {
                wxService.sendMsg(notifyParam.getWxCpNotify().getTitle(), notifyParam.getWxCpNotify().getContent(), notifyParam.getWxCpNotify().getUrl(), notifyParam.getWxCpNotify().getUids());
                log.info(String.format("发送企业微信消息成功,接收uid:%s,内容:%s", StringUtils.join(notifyParam.getWxCpNotify().getUids()), notifyParam.getWxCpNotify().getContent()));
            } catch (Exception e) {
                log.error("发送企业微信消息异常:", e.getMessage());
            }
            this.storeNotification(notifyParam.getWxCpNotify().getTitle(), notifyParam.getWxCpNotify().getContent(), NotifyType.WXCP, notifyParam.getNotifySource(), notifyParam.getWxCpNotify().getUids());
        }
        if (notifyTypeEnumSet.contains(NotifyType.EJIA)) {
            try {
                TbeaEjiaNotify tbeaEjiaNotify = notifyParam.getTbeaEjiaNotify();
                systemCustomService.generateTodo(tbeaEjiaNotify.getTitle(), tbeaEjiaNotify.getContent(), tbeaEjiaNotify.getUids(), tbeaEjiaNotify.getUrl(), tbeaEjiaNotify.getServiceId());
                log.info(String.format("发送特变E+消息成功,接收uid:%s,内容:%s", StringUtils.join(notifyParam.getTbeaEjiaNotify().getUids()), notifyParam.getTbeaEjiaNotify().getContent()));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("发送特变E+消息消息异常:", e.getMessage());
            }
            this.storeNotification(notifyParam.getTbeaEjiaNotify().getTitle(), notifyParam.getTbeaEjiaNotify().getContent(), NotifyType.EJIA, notifyParam.getNotifySource(), notifyParam.getTbeaEjiaNotify().getUids());
        }
    }

    private void storeNotification(String title, String content, NotifyType type, NotifySource source, String[] recipients) {
        NotificationAddParam notificationAddParam = new NotificationAddParam();
        notificationAddParam.setTitle(title);
        notificationAddParam.setContent(content);
        notificationAddParam.setRecipients(recipients);
        notificationAddParam.setType(type);
        notificationAddParam.setSource(source);
        notificationService.saveByParam(notificationAddParam);
    }

    public void pushData(WebSocketData webSocketData) {
        webSocketServer.sendMessage(webSocketData.getTopic(), webSocketData);
    }

}
