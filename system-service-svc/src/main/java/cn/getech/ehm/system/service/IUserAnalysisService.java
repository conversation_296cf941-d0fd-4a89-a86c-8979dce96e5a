package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.customer.LoginCountDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisAddParam;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisEditParam;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisQueryParam;
import cn.getech.ehm.system.entity.UserAnalysis;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;

import java.util.List;

/**
 * <pre>
 * 用户分析信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface IUserAnalysisService extends IBaseService<UserAnalysis> {

    /**
     * 分页查询，返回Dto
     *
     * @param userAnalysisQueryParam
     * @return
     */
    PageResult<UserAnalysisDto> pageDto(UserAnalysisQueryParam userAnalysisQueryParam);

    /**
     * 更新收藏数
     *
     * @param addParam
     * @return
     */
    boolean updateFavorites(UpdateFavoritesParam addParam);

    /**
     * 保存
     *
     * @param userAnalysisAddParam
     * @return
     */
    boolean saveByParam(UserAnalysisAddParam userAnalysisAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    UserAnalysisDto getDtoById(Long id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<UserAnalysisDto> rows);

    /**
     * 更新
     *
     * @param userAnalysisEditParam
     */
    boolean updateByParam(UserAnalysisEditParam userAnalysisEditParam);

    /**
     * 按工程师，按月查询登录次数
     *
     * @param queryParam
     * @return
     */
    List<LoginCountDto> getMonthLoginCountMapByUid(UserAnalysisQueryParam queryParam);
}