package cn.getech.ehm.system.notify;

import cn.getech.ehm.system.notify.enmu.DeviceType;
import cn.getech.ehm.system.notify.enmu.PushType;
import cn.getech.ehm.system.notify.enmu.TargetType;

/**
 * <AUTHOR>
 * @date 2021-04-08 16:27:37
 **/
public interface PushService {

    /**
     * 推送
     * @param pushType
     * @param deviceType
     * @param targetType
     * @param title
     * @param content
     * @param targets
     */
    void push(PushType pushType, DeviceType deviceType, TargetType targetType, String title, String content, String ...targets);

}
