package cn.getech.ehm.system.controller;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.system.dto.audit.CustomerAuditAddParam;
import cn.getech.ehm.system.dto.notification.NotificationDto;
import cn.getech.ehm.system.entity.Notification;
import cn.getech.ehm.system.service.ICustomerAuditService;
import cn.getech.ehm.system.service.INotificationService;
import cn.getech.ehm.system.utils.KeTianYunUtils;
import cn.getech.ehm.system.utils.MD5Utils;
import cn.getech.ehm.system.utils.ketianyun.LiveProperty;
import cn.getech.ehm.system.utils.ketianyun.OauthProperty;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.exception.ServiceException;
import cn.hutool.core.util.URLUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.OAuth2Definition;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.client.LiveConfigClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 开放API
 *
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Controller
@RequestMapping("/public")
public class PublicController {

    private static final String BASIC_AUTHORIZATION_HEADER = "Authorization";

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private LiveProperty liveProperty;
    @Autowired
    private OauthProperty oauthProperty;
    @Autowired
    private LiveConfigClient liveConfigClient;
    @Autowired
    private ICustomerAuditService customerAuditService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Autowired
    private INotificationService iNotificationService;

    @Value("${app.config.wx-login:false}")
    private boolean wxLogin;

    /**
     * @param id
     * @param ts
     * @param sign
     * @param url  http://kangjisenlive.ketianyun.com/auth/2008087/ftg0n8nkow/custom
     * @return
     */
    @GetMapping("/live/oauth/login")
    public String liveOauthLogin(@RequestParam String id, @RequestParam long ts, @RequestParam String sign, @RequestParam String url) throws UnsupportedEncodingException {
        if (KeTianYunUtils.checkSign(sign, liveProperty.getOauthSecretKey(), id, ts)) {
            String state = URLUtil.toUrlForHttp(url).getPath().split("/")[3];
            String fromUrl = URLEncoder.encode(String.format(oauthProperty.getOauthRedirectUrl(), liveProperty.getOauthCallbackUrl(), state), "utf-8");
            String redirectUrl = String.format(oauthProperty.getOauthLoginUrl(), fromUrl);
            log.info("直播观看授权登录url:" + redirectUrl);
            return "redirect:" + redirectUrl;
        } else {
            log.error("签名校验失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
    }

    /**
     * 授权登录成功回调
     *
     * @param accessToken
     * @param accessTokenType
     * @param state
     * @return
     * @throws UnsupportedEncodingException
     */
    @GetMapping("/live/oauth/callback")
    public String liveOauthCallback(@RequestParam String accessToken, @RequestParam String accessTokenType, @RequestParam(required = false) String state) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(accessToken) || StringUtils.isEmpty(accessTokenType) || StringUtils.isEmpty(state)) {
            log.error("缺少参数");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
        log.info("直播回调state:" + state);
        HttpHeaders headers = new HttpHeaders();
        headers.add(BASIC_AUTHORIZATION_HEADER, accessTokenType + " " + accessToken);
        HttpEntity<Map> httpEntity = new HttpEntity<>(null, headers);
        String userInfoUrl = String.format(oauthProperty.getUserInfoUrl(), accessToken);
        ResponseEntity<RestResponse> userinfoRestResponse = restTemplate.exchange(userInfoUrl, HttpMethod.GET, httpEntity, RestResponse.class);
        RestResponse<Map<String, String>> userInfoDtoRestResponse = userinfoRestResponse.getBody();
        if (userInfoDtoRestResponse.isSuccess()) {
            String userid = userInfoDtoRestResponse.getData().get("platformUserId");
            RestResponse<Boolean> checkAuthorizationRestResponse = liveConfigClient.checkAuthorization(userid);
            log.info("check authority:" + checkAuthorizationRestResponse);
            if (checkAuthorizationRestResponse.isOk()) {
                boolean authorization = checkAuthorizationRestResponse.getData();
                if (authorization) {
                    String nickname = userInfoDtoRestResponse.getData().get("name");
                    log.info("userInfo:" + userInfoDtoRestResponse);
                    long ts = System.currentTimeMillis();
                    String sign = MD5Utils.md5Hex(liveProperty.getOauthSecretKey() + liveProperty.getChannelId() + liveProperty.getOauthSecretKey() + ts + liveProperty.getOauthSecretKey() + userid, "utf-8");
                    String redirectUrl = String.format(liveProperty.getLiveOauthUrl(), state) + "?ts=" + ts + "&sign=" + sign + "&userid=" + userid + "&nickname=" + URLEncoder.encode(Base64.getEncoder().encodeToString(nickname.getBytes()), "utf-8") + "&avatar=" + oauthProperty.getDefaultUserAvatar();
                    log.info("直播观看redirect url:" + redirectUrl);
                    return "redirect:" + redirectUrl;
                } else {
                    log.info("[" + userid + "]没有观看直播权限");
                    return "forbidden";
                }
            }
        }
        log.error("获取用户信息失败，错误信息:" + userInfoDtoRestResponse.getMsg());
        return "error";
    }

    /**
     * 获取wx login配置
     *
     * @return
     */
    @GetMapping("/app/config/wxLogin")
    @ResponseBody
    public RestResponse<Boolean> wxLogin() {
        return RestResponse.ok(wxLogin);
    }

    @PostMapping("/audit/add")
    public RestResponse<Boolean> add(@RequestBody CustomerAuditAddParam customerAuditAddParam) {
        return customerAuditService.saveByParam(customerAuditAddParam);
    }

    @ApiOperation("获取邮件通知列表")
    @GetMapping("/mailNotifications")
    @ResponseBody
    public RestResponse<List<Notification>> latestMailNotification() {
        return RestResponse.ok(iNotificationService.latestMailNotification());
    }

}
