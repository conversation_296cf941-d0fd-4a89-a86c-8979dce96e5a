package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.base.util.AddressLngLatUtil;
import cn.getech.ehm.common.enums.AreaType;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.*;
import cn.getech.ehm.system.dto.customer.*;
import cn.getech.ehm.system.dto.poros.secstaff.KjsMetaData;
import cn.getech.ehm.system.dto.poros.secstaff.SecStaffRemark;
import cn.getech.ehm.system.entity.Customer;
import cn.getech.ehm.system.entity.CustomerEngineer;
import cn.getech.ehm.system.entity.CustomerSalesman;
import cn.getech.ehm.system.mapper.CustomerMapper;
import cn.getech.ehm.system.service.ICustomerEngineerService;
import cn.getech.ehm.system.service.ICustomerEquipmentService;
import cn.getech.ehm.system.service.ICustomerSalesmanService;
import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.ehm.system.utils.IotUtils;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.SecStaffBaseUpdateParam;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <pre>
 * 客户列表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CustomerServiceImpl extends BaseServiceImpl<CustomerMapper, Customer> implements ICustomerService {

    @Value("${iot.kjs.preUrl:http://iot.cloud.consen.net}")
    private String iotUrl;
    @Autowired
    private CustomerParamMapper customerParamMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private ICustomerEngineerService customerEngineerService;
    @Autowired
    private ICustomerEquipmentService customerEquipmentService;
    @Autowired
    private ICustomerSalesmanService customerSalesmanService;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;


    @Override
    public PageResult<CustomerDto> pageDto(CustomerQueryParam customerQueryParam) {
        Wrapper<Customer> wrapper = getPageSearchWrapper(customerQueryParam);
        PageResult<CustomerDto> result = customerParamMapper.pageEntity2Dto(page(customerQueryParam, wrapper));
        List<CustomerDto> customerDtos = result.getRecords();
        if (CollectionUtils.isNotEmpty(customerDtos)) {
            customerDtos.stream().forEach(dto -> {
                dto.setAreaValue(AreaType.getNameByValue(dto.getArea()));
                dto.setAllAddress(buildAddress(dto.getCity(), dto.getAddress()));
            });
        }

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    //将省市区与详细地址拼接
    private String buildAddress(String[] city, String address) {
        StringBuffer sb = new StringBuffer();
        if (null != city && city.length > StaticValue.ZERO) {
            for (String str : city) {
                sb.append(str);
            }
        }
        if (StringUtils.isNotBlank(address)) {
            sb.append(address);
        }
        return sb.toString();
    }

    @Override
    public List<CustomerDto> list(CustomerQueryDto customerQueryDto) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.<Customer>lambdaQuery();
        if (StringUtils.isNotBlank(customerQueryDto.getKeyword())) {
            wrapper.and(w -> w.like(Customer::getName, customerQueryDto.getKeyword())
                    .or()
                    .like(Customer::getCode, customerQueryDto.getKeyword())
                    .or()
                    .like(Customer::getCompany, customerQueryDto.getKeyword()));
        }
        if (null != customerQueryDto.getIsHaveUid() && customerQueryDto.getIsHaveUid() == StaticValue.ONE) {
            wrapper.ne(Customer::getUid, "");
            wrapper.isNotNull(Customer::getUid);
        }
        wrapper.select(Customer::getCode, Customer::getId, Customer::getName, Customer::getCity,
                Customer::getAddress, Customer::getUid, Customer::getLngLat);
        if (BaseEntity.class.isAssignableFrom(Customer.class)) {
            wrapper.orderByDesc(Customer::getUpdateTime, Customer::getCreateTime);
        }
        List<Customer> customers = customerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customers)) {
            return CopyDataUtil.copyList(customers, CustomerDto.class);
        }
        return new ArrayList<>(StaticValue.ONE);
    }


    @Override
    public Integer listSignCustomerCount() {
        return customerMapper.customerSignCount();
    }

    @Override
    public Integer listCustomerCount() {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.<Customer>lambdaQuery();
        wrapper.eq(Customer::getDeleted, DeletedType.NO.getValue());
        return customerMapper.selectCount(wrapper);
    }

    @Override
    public Map<String, EquipmentCustomerDto> getMapByEquipmentIds(String[] equipmentIds) {
        Map<String, EquipmentCustomerDto> map = new HashMap<>();
        if (null != equipmentIds && equipmentIds.length > StaticValue.ZERO) {
            List<EquipmentCustomerDto> customers = customerMapper.getMapByEquipmentIds(equipmentIds);
            if (CollectionUtils.isNotEmpty(customers)) {
                for (EquipmentCustomerDto customer : customers) {
                    if (!map.containsKey(customer.getEquipmentId())) {
                        map.put(customer.getEquipmentId(), customer);
                    }
                }
            }
        }
        return map;
    }

    @Override
    public List<String> getUidsByEquipmentId(String equipmentId) {
        List<String> uids = new ArrayList<>();
        CustomerDto customerDto = customerEquipmentService.getCustomerIdByInfoId(equipmentId);
        if (null != customerDto) {
            if (StringUtils.isNotBlank(customerDto.getUid())) {
                uids.add(customerDto.getUid());
            }
            List<CustomerEngineer> customerEngineers = customerEngineerService.getEngineerUidsByCustomerId(customerDto.getId());
            List<String> engineerUids = customerEngineers.stream().map(CustomerEngineer::getUid).distinct().collect(Collectors.toList());
            uids.addAll(engineerUids);
        }
        return uids;
    }

    @Override
    public CustomerDto getDtoByUid(String uid) {
        String customerId = getCustomerIdByUid(uid);
        return buildDto(customerId);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(CustomerAddParam customerAddParam) {
        Integer count = checkIsHave(customerAddParam.getCode(), null, null);
        if (count > StaticValue.ZERO) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        count = checkIsHave(null, customerAddParam.getName(), null);
        if (count > StaticValue.ZERO) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");
        Customer customer = CopyDataUtil.copyObject(customerAddParam, Customer.class);
        String allAddress = buildAddress(customerAddParam.getCity(), customerAddParam.getAddress());
        if (StringUtils.isNotBlank(allAddress)) {
            String lngLat = AddressLngLatUtil.getCoordinate(allAddress);
            customer.setLngLat(lngLat);
        }

        customer.setId(uuid);
        customer.setMaxEngineerCount(null != customerAddParam.getMaxEngineerCount() ?
                customerAddParam.getMaxEngineerCount() : StaticValue.ZERO);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customer);
        if (StringUtils.isNotBlank(customerAddParam.getUid())) {
            //获取iot分组id
            AreaEditDto areaEditDto = new AreaEditDto();
            areaEditDto.setAreaName(customerAddParam.getName());
            areaEditDto.setUid(customerAddParam.getUid());
            IotResDto iotResDto = IotUtils.addArea(areaEditDto);
            customer.setIotAreaId(iotResDto.getDataAreaId());

            // 修改用户元信息 客户名
            updateUserCustomerMetaData(customer.getUid(), customer.getName());
        }

        // 批量保存销售人员的列表
        if (null != customerAddParam.getSalesmanUids() && customerAddParam.getSalesmanUids().length > 0) {
            List<CustomerSalesman> salesmanList = new ArrayList<>();
            for (String uid : customerAddParam.getSalesmanUids()) {
                CustomerSalesman salesman = new CustomerSalesman();
                salesman.setCustomerId(uuid);
                salesman.setUid(uid);
                salesmanList.add(salesman);
            }
            customerSalesmanService.saveBatch(salesmanList);
        }

        return save(customer);
    }

    /**
     * 校验名称/编码是否已有
     *
     * @param code
     * @param name
     * @param id
     */
    private Integer checkIsHave(String code, String name, String id) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(id)) {
            wrapper.ne(Customer::getId, id);
        }
        if (StringUtils.isNotBlank(code)) {
            wrapper.eq(Customer::getCode, code);
        }
        if (StringUtils.isNotBlank(name)) {
            wrapper.eq(Customer::getName, name);
        }
        wrapper.select(Customer::getId);
        return customerMapper.selectCount(wrapper);
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateByParam(CustomerEditParam customerEditParam) {
        Integer count = checkIsHave(customerEditParam.getCode(), null, customerEditParam.getId());
        if (count > StaticValue.ZERO) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_exists", null, LocaleContextHolder.getLocale())));
        }
        count = checkIsHave(null, customerEditParam.getName(), customerEditParam.getId());
        if (count > StaticValue.ZERO) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("name_exists", null, LocaleContextHolder.getLocale())));
        }
        Customer customer = customerParamMapper.editParam2Entity(customerEditParam);
        customer.setMaxEngineerCount(null != customerEditParam.getMaxEngineerCount() ?
                customerEditParam.getMaxEngineerCount() : StaticValue.ZERO);
        //区域为国外，经纬度置为空
        if (customer.getArea() == AreaType.OVERSEA.getValue()) {
            customer.setLngLat("");
        } else {
            String allAddress = buildAddress(customerEditParam.getCity(), customerEditParam.getAddress());
            if (StringUtils.isNotBlank(allAddress)) {
                String lngLat = AddressLngLatUtil.getCoordinate(allAddress);
                customer.setLngLat(lngLat);
            }
        }
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, customer);
        if (StringUtils.isNotBlank(customerEditParam.getUid())) {
            Customer oldCustomer = customerMapper.selectById(customerEditParam.getId());

            if (StringUtils.isBlank(oldCustomer.getUid())) {
                //历史记录没有绑定过uid，同步iot新增分组
                AreaEditDto areaEditDto = new AreaEditDto();
                areaEditDto.setAreaName(customerEditParam.getName());
                areaEditDto.setUid(customerEditParam.getUid());
                IotResDto iotResDto = IotUtils.addArea(areaEditDto);
                customer.setIotAreaId(iotResDto.getDataAreaId());

                // 修改用户元信息 客户名
                updateUserCustomerMetaData(customer.getUid(), customer.getName());

            } else if (!customer.getName().equals(oldCustomer.getName())) {
                //客户名称修改
                customerEngineerService.buildIot(customerEditParam.getId(), null, customerEditParam.getName());

                // 修改用户元信息 客户名 新的管理人员uid
                updateUserCustomerMetaData(customer.getUid(), customer.getName());
            }


        }

        LambdaQueryWrapper<CustomerSalesman> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNoneBlank(customerEditParam.getId()), CustomerSalesman::getCustomerId, customerEditParam.getId());
        customerSalesmanService.remove(queryWrapper);
        // 批量保存销售人员的列表
        if (customerEditParam.getSalesmanUids() != null) {
            List<CustomerSalesman> salesmanList = new ArrayList<>();
            for (String uid : customerEditParam.getSalesmanUids()) {
                CustomerSalesman salesman = new CustomerSalesman();
                salesman.setCustomerId(customerEditParam.getId());
                salesman.setUid(uid);
                salesmanList.add(salesman);
            }

            if (salesmanList.size() != 0) {
                customerSalesmanService.saveBatch(salesmanList);
            }
        }

        return updateById(customer);
    }


    @Override
    public CustomerDto getDtoById(String id) {
        Customer customer = customerMapper.selectById(id);
        CustomerDto customerDto = CopyDataUtil.copyObject(customer, CustomerDto.class);
        LambdaQueryWrapper<CustomerSalesman> wrapper = Wrappers.lambdaQuery();
        wrapper.select(CustomerSalesman::getUid);
        wrapper.eq(StringUtils.isNotBlank(id), CustomerSalesman::getCustomerId, id);
        List<CustomerSalesman> customerSalesmans = customerSalesmanService.list(wrapper);
        if (CollectionUtils.isNotEmpty(customerSalesmans)) {
            List<String> customerSalesmanuids = new ArrayList<>();
            customerSalesmans.forEach(cs -> {
                customerSalesmanuids.add(cs.getUid());
            });
            customerDto.setSalesmanUids(Arrays.copyOf(customerSalesmanuids.toArray(), customerSalesmanuids.size(), String[].class));
        }
        customerDto.setMaxEngineerCount(null != customerDto.getMaxEngineerCount() ?
                customerDto.getMaxEngineerCount() : StaticValue.ZERO);
        return customerDto;
    }

    @Override
    public CustomerDto getCurrentCustomer(String customerId) {
        if (StringUtils.isBlank(customerId)) {
            customerId = getCurrentCustomerId();
        }
        return buildDto(customerId);
    }

    @Override
    public CustomerDto buildDto(String customerId) {
        if (StringUtils.isBlank(customerId)) {
            return null;
        }
        Customer customer = customerMapper.selectById(customerId);
        CustomerDto customerDto = CopyDataUtil.copyObject(customer, CustomerDto.class);
        if (null != customerDto) {
            Integer curEngineerCount = customerEngineerService.engineerCount(customerId);
            customerDto.setCurEngineerCount(curEngineerCount);
            customerDto.setMaxEngineerCount(null != customerDto.getMaxEngineerCount() ?
                    customerDto.getMaxEngineerCount() : StaticValue.ZERO);
        }
        return customerDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateStatusById(String id, Integer status) {
        Customer customer = customerMapper.selectById(id);
        customer.setStatus(status);
        customerEngineerService.updateStatusByCustomId(id, status, customer.getUid());
        return customerMapper.updateById(customer) > StaticValue.ZERO;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean importExcel(List<CustomerExcel> excels) {
        List<CustomerImportDto> customerImportDtos = new ArrayList<>();
        List<String> codes = new ArrayList<>();
        for (CustomerExcel excel : excels) {
            if (codes.contains(excel.getCode())) {
                continue;
            }
            codes.add(excel.getCode());
            CustomerImportDto customerImportDto = new CustomerImportDto();
            customerImportDto.setId(UUID.randomUUID().toString().replace("-", ""));
            customerImportDto.setName(excel.getName());
            customerImportDto.setCode(excel.getCode());
            customerImportDto.setCompany(excel.getCompany());
            customerImportDto.setArea(null != excel.getArea() ? Integer.valueOf(excel.getArea()) : 0);
            customerImportDto.setAddress(excel.getAddress());
            customerImportDto.setContactPerson(excel.getContactPerson());
            customerImportDto.setContactPhone(excel.getContactPhone());
            customerImportDto.setMaintenance(null != excel.getMaintenance() ? Integer.valueOf(excel.getMaintenance()) : 1);
            if (StringUtils.isNotBlank(excel.getCityStr())) {
                String cityStr = excel.getCityStr().replace(StringPool.SLASH, StringPool.COMMA);
                customerImportDto.setCity(cityStr);
                String[] city = cityStr.split(StringPool.COMMA);
                String allAddress = buildAddress(city, excel.getAddress());
                if (StringUtils.isNotBlank(allAddress)) {
                    String lngLat = AddressLngLatUtil.getCoordinate(allAddress);
                    customerImportDto.setLngLat(lngLat);
                }
            }
            UserBaseInfo user = PorosContextHolder.getCurrentUser();
            customerImportDto.setCreateBy(user.getUid());
            customerImportDto.setUpdateBy(user.getUid());
            customerImportDto.setTenantId(user.getTenantId());
            customerImportDtos.add(customerImportDto);
        }

        return customerMapper.saveImportBatch(customerImportDtos);
    }

    @Override
    public boolean updateCityBatch(List<CustomerCityExcel> excels) {
        List<CustomerImportDto> customerImportDtos = new ArrayList<>();
        for (CustomerCityExcel excel : excels) {
            CustomerImportDto customerImportDto = new CustomerImportDto();
            customerImportDto.setName(excel.getName());
            customerImportDto.setCode(excel.getCode());
            customerImportDto.setAddress(excel.getAddress());
            if (StringUtils.isNotBlank(excel.getCityStr())) {
                String cityStr = excel.getCityStr().replace(StringPool.SLASH, StringPool.COMMA);
                customerImportDto.setCity(cityStr);
                String[] city = cityStr.split(StringPool.COMMA);
                String allAddress = buildAddress(city, excel.getAddress());
                if (StringUtils.isNotBlank(allAddress)) {
                    String lngLat = AddressLngLatUtil.getCoordinate(allAddress);
                    customerImportDto.setLngLat(lngLat);
                }
            }
            customerImportDtos.add(customerImportDto);
        }

        return customerMapper.updateCityBatch(customerImportDtos);
    }

    @Override
    public List<CustomerExcel> exportExcel(String keyword, Integer area) {
        CustomerQueryParam customerQueryParam = new CustomerQueryParam();
        customerQueryParam.setKeyword(keyword);
        customerQueryParam.setArea(area);
        Wrapper<Customer> wrapper = getPageSearchWrapper(customerQueryParam);
        List<Customer> customers = customerMapper.selectList(wrapper);
        List<CustomerExcel> excels = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customers)) {
            for (Customer customer : customers) {
                CustomerExcel customerExcel = CopyDataUtil.copyObject(customer, CustomerExcel.class);
                customerExcel.setArea(null != customer.getArea() ? customer.getArea().toString() : "0");
                customerExcel.setMaintenance(null != customer.getMaintenance() ? customer.getMaintenance().toString() : "0");
                String[] city = customer.getCity();
                if (null != city && city.length > StaticValue.ZERO) {
                    String cityStr = StringUtils.join(city, StringPool.SLASH);
                    customerExcel.setCityStr(cityStr);
                }
                excels.add(customerExcel);
            }
        }
        return excels;
    }

    @Override
    public Boolean checkUid(String uid) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getUid, uid);
        Integer count = customerMapper.selectCount(wrapper);
        return count > StaticValue.ZERO;
    }

    @Override
    public Boolean addBatchCustomer(List<CustomerImportDto> customerImportDtos) {
        if (CollectionUtils.isNotEmpty(customerImportDtos)) {
            for (CustomerImportDto customerImportDto : customerImportDtos) {
                String[] city = null;
                if (StringUtils.isNotBlank(customerImportDto.getCity())) {
                    city = customerImportDto.getCity().split(",");
                }
                String allAddress = buildAddress(city, customerImportDto.getAddress());
                if (StringUtils.isNotBlank(allAddress)) {
                    String lngLat = AddressLngLatUtil.getCoordinate(allAddress);
                    customerImportDto.setLngLat(lngLat);
                }
            }
        }
        return customerMapper.saveImportBatch(customerImportDtos);
    }

    @Override
    public String getCurrentCustomerId() {
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        String uid = userInfo.getUid();
        log.info("userInfo.getUid():" + uid);
        return getCustomerIdByUid(uid);
    }

    @Override
    public PageResult<EquipmentCustomerDto> orderByCustomerName(CustomerNameOrderParam orderParam) {
        Page<EquipmentCustomerDto> page = new Page(orderParam.getPageNo(), orderParam.getLimit());
        IPage<EquipmentCustomerDto> pageList = customerMapper.orderByCustomerName(page, orderParam);
        return new PageResult(pageList.getRecords(), pageList.getTotal());
    }

    /**
     * 根据uid获取客户名称
     *
     * @return
     */
    private String getCustomerNameByUid(String uid) {
        String customerName = null;
        //先判断当前登录人是否为客户
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getUid, uid);
        List<Customer> customers = customerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customers)) {
            customerName = customers.get(0).getName();

        } else {
            //再判断当前登录人是否为工程师
//            customerId = customerEngineerService.getCurrentCustomerId(uid);
        }
        return customerName;
    }

    /**
     * 根据uid获取客户id
     *
     * @return
     */
    private String getCustomerIdByUid(String uid) {
        String customerId;
        //先判断当前登录人是否为客户
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getUid, uid);
        List<Customer> customers = customerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customers)) {
            customerId = customers.get(0).getId();
            log.info("Customer customerId=" + customerId);
        } else {
            //再判断当前登录人是否为工程师
            customerId = customerEngineerService.getCurrentCustomerId(uid);
            log.info("customerEngineer customerId=" + customerId);
        }
        log.info("return customerId=" + customerId);
        return customerId;
    }

    @Override
    public List<String> getCurrentInfoIds() {
        String customerId = this.getCurrentCustomerId();
        return customerEquipmentService.getCurrentInfoIds(customerId);
    }

    @Override
    public List<String> getInfoIdsByCustomerId(String customerId) {
        return customerEquipmentService.getCurrentInfoIds(customerId);
    }

    @Override
    public List<String> getCurrentInfoIdsByCustomerName(String customerName) {
        return customerEquipmentService.getCurrentInfoIdsByCustomerName(customerName);
    }

    @Override
    public List<String> getEquipmentIdsByCustomerNames(String customerNames) {
        return customerEquipmentService.getEquipmentIdsByCustomerNames(customerNames);
    }

    @Override
    public List<String> getEquipmentIdsByTwoCustomerNames(String customerName1, String customerName2) {
        return customerEquipmentService.getEquipmentIdsByTwoCustomerNames(customerName1, customerName2);
    }

    @Override
    public Integer engineerCount() {
        String customerId = this.getCurrentCustomerId();
        return customerEngineerService.engineerCount(customerId);
    }

    @Override
    public CustomerDto getScreenDto(String customerId) {
        Customer customer = customerMapper.selectById(customerId);
        CustomerDto customerDto = CopyDataUtil.copyObject(customer, CustomerDto.class);
        return customerDto;
    }

    @Override
    public List<CustomerInfoEquipmentDto> getAllCustomerEqui() {
        LambdaQueryWrapper<Customer> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Customer::getDeleted, DeletedType.NO.getValue());
        queryWrapper.select(Customer::getId, Customer::getName);
        List<Customer> customers = customerMapper.selectList(queryWrapper);
        List<String> customerIds = new ArrayList<>();

        List<CustomerInfoEquipmentDto> dtoList = new ArrayList<>();
        Map<String, CustomerInfoEquipmentDto> map = new HashMap<>();
        customers.forEach(customer -> {
            CustomerInfoEquipmentDto customerInfoEquipmentDto = new CustomerInfoEquipmentDto();
            customerIds.add(customer.getId());
            customerInfoEquipmentDto.setCustomerId(customer.getId());
            customerInfoEquipmentDto.setName(customer.getName());
            dtoList.add(customerInfoEquipmentDto);
            map.put(customer.getId(), customerInfoEquipmentDto);
        });


        List<CustomerEquipmentDto> customerEquipmentDtos = customerEquipmentService.listByCustomerIds(customerIds);

        customerEquipmentDtos.forEach(dto -> {
            CustomerInfoEquipmentDto customerInfoEquipmentDto = map.get(dto.getCustomerId());
            customerInfoEquipmentDto.getEquipmentIds().add(dto.getEquipmentId());
        });
        List<CustomerInfoEquipmentDto> resultDtoList = new ArrayList<>();
        dtoList.forEach(dto -> {
            if (dto.getEquipmentIds().size() != 0) {
                resultDtoList.add(dto);
            }
        });
        return resultDtoList;
    }

    @Override
    public String getCurrentCustomerName() {
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        String uid = userInfo.getUid();
        return getCustomerNameByUid(uid);
    }

    private Wrapper<Customer> getPageSearchWrapper(CustomerQueryParam customerQueryParam) {
        LambdaQueryWrapper<Customer> wrapper = Wrappers.<Customer>lambdaQuery();
        if (StringUtils.isNotBlank(customerQueryParam.getKeyword())) {
            wrapper.and(w -> w.like(Customer::getName, customerQueryParam.getKeyword())
                    .or()
                    .like(Customer::getCode, customerQueryParam.getKeyword())
                    .or()
                    .like(Customer::getCompany, customerQueryParam.getKeyword()));
        }
        if (null != customerQueryParam.getArea()) {
            wrapper.eq(Customer::getArea, customerQueryParam.getArea());
        }
        if (null != customerQueryParam.getStatus()) {
            if (customerQueryParam.getStatus() == StaticValue.ONE) {
                wrapper.isNotNull(Customer::getUid);
                wrapper.ne(Customer::getUid, "");
            } else if (customerQueryParam.getStatus() == StaticValue.TWO) {
                wrapper.and(w -> w.isNull(Customer::getUid)
                        .or()
                        .eq(Customer::getUid, ""));
            }
        }
        wrapper.eq(Customer::getDeleted, DeletedType.NO.getValue());
        if (BaseEntity.class.isAssignableFrom(Customer.class)) {
            wrapper.orderByDesc(Customer::getUpdateTime, Customer::getCreateTime);
        }
        return wrapper;
    }

    @Override
    public CustomerDto getCustomerByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getCode, code);
        Customer customer = (Customer) this.baseMapper.selectOne(wrapper);
        return CopyDataUtil.copyObject(customer, CustomerDto.class);
    }

    @Override
    public CustomerStudioDto getStudioByCustomerId(String id) {
        CustomerStudioDto customerStudioDto = new CustomerStudioDto();
        LambdaQueryWrapper<Customer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Customer::getId, id);
        wrapper.select(Customer::getId, Customer::getStudioId);
        Customer customer = customerMapper.selectOne(wrapper);
        if (null != customer && StringUtils.isNotBlank(customer.getStudioId())) {
            customerStudioDto = IotUtils.getStudioById(customer.getStudioId());
            if (null != customerStudioDto && StringUtils.isNotBlank(customerStudioDto.getStudioId())) {
                String pcStudioUrl = iotUrl + "/interface/interface_preview?id=" + customerStudioDto.getStudioId();
                customerStudioDto.setStudioUrl(pcStudioUrl);
            }

        }
        return customerStudioDto;
    }

    @Override
    public Boolean updateCustomerStudio(StudioEditParam studioEditParam) {

        LambdaUpdateWrapper<Customer> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(Customer::getStudioId, studioEditParam.getStudioId());
        wrapper.eq(Customer::getId, studioEditParam.getCustomerId());
        return update(wrapper);
    }

    /**
     * 修改用户组织的元信息
     *
     * @param uid
     * @param companyName
     * @return
     */
    private boolean updateUserCustomerMetaData(String uid, String companyName) {
        SecStaffBaseUpdateParam secStaffUpdateParam = new SecStaffBaseUpdateParam();
        secStaffUpdateParam.setUid(uid);

        // 增加客户名称到remark里
        KjsMetaData kjsMetaData = new KjsMetaData();
        kjsMetaData.setCompanyName(companyName);
        SecStaffRemark secStaffRemark = new SecStaffRemark();
        secStaffRemark.setKjs(kjsMetaData);

        secStaffUpdateParam.setRemark(JSONObject.toJSONString(secStaffRemark));
        RestResponse<Boolean> restResponse = porosSecStaffClient.updateBaseByUid(secStaffUpdateParam);
        if(restResponse.isOk()){
            return true;
        } else {
            log.error("中台修改用户失败->" + JSONObject.toJSONString(restResponse));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Override
    public List<String> checkEquipmentUsed(String[] equipmentIds) {
        String[] strings = customerEquipmentService.checkCustomerEquipmentUsed(equipmentIds);
        if (null != strings && strings.length != 0) {
            return Stream.of(strings).collect(Collectors.toList());
        }
        return null;
    }
}
