package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.entity.AppConfigDataType;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.getech.ehm.system.dto.AppDataTypeDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * <p>
 * 分类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Repository
public interface AppConfigDataTypeMapper extends BaseMapper<AppConfigDataType> {

    List<AppDataTypeDto> fetchAppDataTypeList();

    /**
     * 获取geek下所有数据
     * @return
     */
    @SqlParser(filter = true)
    List<AppConfigDataType> getAllData();

    /**
     * 插入数据
     * @param entities
     * @return
     */
    @SqlParser(filter = true)
    Boolean insertField(@Param("entities") List<AppConfigDataType> entities);
}
