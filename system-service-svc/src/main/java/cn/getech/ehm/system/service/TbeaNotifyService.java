package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.tbea.*;
import com.dtflys.forest.annotation.*;
import org.springframework.stereotype.Component;

import java.util.List;

@BaseRequest(contentType = "application/json")
@Component
public interface TbeaNotifyService {

    /**
     * 获取token
     */
    @Request(value = "https://ejia.tbea.com/gateway/oauth2/token/getAccessToken", type = "POST")
    NotifyResDto<TokenResDto> getAccessToken(@Body TokenRequestDto dto);

    /**
     * 根据手机号获取用户信息
     */
    @Request(value = "https://ejia.tbea.com/gateway/openimport/open/person/get?accessToken=${accessToken}", type = "POST")
    NotifyResDto<List<PersonResDto>> getPerson(@DataVariable("accessToken") String accessToken, @Body PersonRequestDto dto);

    /**
     * 获取token
     */
    @Request(value = "https://ejia.tbea.com/gateway/newtodo/extapp/open/generatetodo?accessToken=${accessToken}", type = "POST")
    NotifyResDto<String> generateTodo(@DataVariable("accessToken") String accessToken, @Body GenerateTodoDto dto);

    //标记已读状态
    @Request(value = "https://ejia.tbea.com/gateway/newtodo/extapp/open/action?accessToken=${accessToken}", type = "POST")
    NotifyResDto markTodo(@DataVariable("accessToken") String accessToken, @Body MarkTodoRequestDto dto);

}

