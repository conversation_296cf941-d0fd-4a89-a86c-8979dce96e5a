package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.ehm.system.utils.ketianyun.ChatProperty;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.CustomerDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Slf4j
@Controller
@RequestMapping("/ktyChat")
@Api(tags = "科天云课程聊天模块")
public class WebKeTianChatController {

    @Autowired
    private ChatProperty chatProperty;

    @Autowired
    private ICustomerService iCustomerService;

    @Autowired
    private PorosSecStaffClient porosSecStaffClient;

    @GetMapping("/client/redirect")
    public String liveManageRedirect() throws UnsupportedEncodingException {
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        String userId = userBaseInfo.getUid();
        String nickname = URLEncoder.encode(userBaseInfo.getName(), "utf-8");
        String customerCompany = "", customerEmail = "", customerPhone = "";
        CustomerDto customerDto = iCustomerService.getDtoByUid(userId);
        log.info("customer:" + customerDto);
        if (null == customerDto) {
            RestResponse<PorosSecStaffDto> restResponse = porosSecStaffClient.getByUid(userId, null);
            log.info("staff:" + restResponse);
            if (restResponse.isOk()) {
                PorosSecStaffDto porosSecStaffDto = restResponse.getData();
                customerEmail = URLEncoder.encode(porosSecStaffDto.getEmail(), "utf-8");
                customerPhone = porosSecStaffDto.getMobile();
            } else {
                log.error("获取用户信息失败,", restResponse.getMsg());
            }
        } else {
            customerCompany = URLEncoder.encode(customerDto.getName(), "utf-8");
        }
        String redirectUrl = String.format(chatProperty.getVisitorUrl(), userId, nickname);
        if (StringUtils.isNotBlank(customerCompany)) {
            redirectUrl += "&customerCompany=" + customerCompany;
        }
        if (StringUtils.isNotBlank(customerEmail)) {
            redirectUrl += "&customerEmail=" + customerEmail;
        }
        if (StringUtils.isNotBlank(customerCompany)) {
            redirectUrl += "&customerPhone=" + customerPhone;
        }
        log.info("在线客服redirect:" + redirectUrl);
        return "redirect:" + redirectUrl;
    }

}
