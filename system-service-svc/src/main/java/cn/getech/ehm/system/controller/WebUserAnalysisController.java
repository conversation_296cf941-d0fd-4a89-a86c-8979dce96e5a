package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.useranalysis.UserActionAddParam;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisQueryParam;
import cn.getech.ehm.system.service.IUserActionService;
import cn.getech.ehm.system.service.IUserAnalysisService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 用户分析信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@RestController
@RequestMapping("/userAnalysis")
@Api(tags = "用户分析信息服务接口")
public class WebUserAnalysisController {

    @Autowired
    private IUserAnalysisService userAnalysisService;
    @Autowired
    private IUserActionService userActionService;

    /**
     * 分页获取用户分析信息列表
     */
    @ApiOperation("分页获取用户分析信息列表")
    @PostMapping("/list")
    //@Permission("user:analysis:list")
    public RestResponse<PageResult<UserAnalysisDto>> pageList(@RequestBody @Valid UserAnalysisQueryParam userAnalysisQueryParam) {
        return RestResponse.ok(userAnalysisService.pageDto(userAnalysisQueryParam));
    }

    /**
     * 更新用户收藏数
     */
    @ApiOperation("更新用户收藏数")
    @AuditLog(title = "更新用户收藏数", desc = "更新用户收藏数", businessType = BusinessType.UPDATE)
    @PostMapping("/updateFavorites")
    //@Permission("user:analysis:update")
    public RestResponse<Boolean> updateFavorites(@RequestBody @Valid UpdateFavoritesParam updateParam) {
        return RestResponse.ok(userAnalysisService.updateFavorites(updateParam));
    }

    /**
     * 添加解决问题数
     */
    @ApiOperation("添加解决问题数")
    @AuditLog(title = "添加解决问题数", desc = "添加解决问题数", businessType = BusinessType.UPDATE)
    @PostMapping("/addSolveCount")
    //@Permission("user:analysis:update")
    public RestResponse<Boolean> addSolveCount() {
        UserActionAddParam actionAddParam = new UserActionAddParam();
        actionAddParam.setSolveCount(1);
        return RestResponse.ok(userActionService.saveByParam(actionAddParam));
    }

    /**
     * 导出用户分析信息列表
     */
    @ApiOperation(value = "导出用户分析信息列表")
    @AuditLog(title = "用户分析信息", desc = "导出用户分析信息列表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    // @Permission("user:analysis:export")
    public void excelExport(@Valid UserAnalysisQueryParam userAnalysisQueryParam, HttpServletResponse response) {
        PageResult<UserAnalysisDto> pageResult = userAnalysisService.pageDto(userAnalysisQueryParam);
        ExcelUtils<UserAnalysisDto> util = new ExcelUtils<>(UserAnalysisDto.class);

        util.exportExcel(pageResult.getRecords(), "用户分析信息", response);
    }

    /**
     * Excel导入用户分析信息
     */
    @ApiOperation(value = "Excel导入用户分析信息")
    @AuditLog(title = "用户分析信息", desc = "Excel导入用户分析信息", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("user:analysis:import")
    public RestResponse<Boolean> excelImport(@RequestPart("file") MultipartFile file) {
        ExcelUtils<UserAnalysisDto> util = new ExcelUtils<>(UserAnalysisDto.class);
        List<UserAnalysisDto> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)) {
            return RestResponse.failed();
        }
        return RestResponse.ok(userAnalysisService.saveDtoBatch(rows));
    }

}
