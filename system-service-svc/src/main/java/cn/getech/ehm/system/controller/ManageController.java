package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.service.impl.WxService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "运营接口", value = "运营接口")
@RestController
@RequestMapping("/manage")
@Slf4j
public class ManageController {
    @Autowired
    WxService wxService;

    @GetMapping("/send/wxcp/msg")
    @ApiOperation("测试发送企业微信消息")
    public RestResponse sendWxCpMsg(String title, String content, String url, String[] uids) {
        wxService.sendMsg(title, content, url, uids);
        return RestResponse.ok();
    }
}
