package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.useranalysis.UserActionAddParam;
import cn.getech.ehm.system.dto.useranalysis.UserActionDto;
import cn.getech.ehm.system.dto.useranalysis.UserActionEditParam;
import cn.getech.ehm.system.dto.useranalysis.UserActionQueryParam;
import cn.getech.ehm.system.entity.UserAction;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 用户行为信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
public interface IUserActionService extends IBaseService<UserAction> {

    /**
     * 分页查询，返回Dto
     *
     * @param userActionQueryParam
     * @return
     */
    PageResult<UserActionDto> pageDto(UserActionQueryParam userActionQueryParam);

    /**
     * 保存
     *
     * @param userActionAddParam
     * @return
     */
    boolean saveByParam(UserActionAddParam userActionAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    UserActionDto getDtoById(Long id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<UserActionDto> rows);

    /**
     * 更新
     *
     * @param userActionEditParam
     */
    boolean updateByParam(UserActionEditParam userActionEditParam);
}