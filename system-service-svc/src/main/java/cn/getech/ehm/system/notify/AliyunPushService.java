package cn.getech.ehm.system.notify;

import cn.getech.ehm.system.notify.enmu.DeviceType;
import cn.getech.ehm.system.notify.enmu.PushType;
import cn.getech.ehm.system.notify.enmu.TargetType;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.push.model.v20160801.PushRequest;
import com.aliyuncs.push.model.v20160801.PushResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-04-08 16:30:41
 **/
@Slf4j
@Service
public class AliyunPushService implements PushService {

    @Value("${aliyun.region-id:cn-hangzhou}")
    private String regionId;

    @Value("${aliyun.access-key.id:}")
    private String accessKeyId;

    @Value("${aliyun.access-key.secret:}")
    private String accessKeySecret;

    @Value("${aliyun.push.app-key:333412562}")
    private Long appKey;

    @Override
    public void push(PushType pushType, DeviceType deviceType, TargetType targetType, String title, String content, String ...targets) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        PushRequest request = new PushRequest();
        request.setAppKey(appKey);
        request.setPushType(pushType.name());
        request.setDeviceType(deviceType.name());
        request.setTarget(targetType.name());
        request.setTargetValue(String.join(",", targets));
        request.setBody(content);
        request.setTitle(title);
        request.setAndroidPopupActivity("com.getech.ehm.activity.PushPopupActivity");
        request.setAndroidPopupTitle(title);
        request.setAndroidPopupBody(content);
        request.setAndroidNotificationChannel("1");
        request.setStoreOffline(true);
        try {
            PushResponse response = client.getAcsResponse(request);
            log.info(String.format("阿里云推送成功, requestId: %s, messageId: %s", response.getRequestId(), response.getMessageId()));
        } catch (ClientException e) {
            log.warn(String.format("阿里云推送异常, 错误消息: %s", e.getErrMsg()));
        }
    }
}
