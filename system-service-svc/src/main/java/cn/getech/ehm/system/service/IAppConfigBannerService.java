package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.appbanner.AppConfigBannerAddParam;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerDto;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerEditParam;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerQueryParam;
import cn.getech.ehm.system.dto.appconfig.AppBannerDto;
import cn.getech.ehm.system.entity.AppConfigBanner;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 轮播图 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
public interface IAppConfigBannerService extends IBaseService<AppConfigBanner> {

    /**
     * 分页查询，返回Dto
     *
     * @param appConfigBannerQueryParam
     * @return
     */
    PageResult<AppConfigBannerDto> pageDto(AppConfigBannerQueryParam appConfigBannerQueryParam);

    /**
     * 保存
     *
     * @param appConfigBannerAddParam
     * @return
     */
    boolean saveByParam(AppConfigBannerAddParam appConfigBannerAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    AppConfigBannerDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<AppConfigBannerDto> rows);

    /**
     * 更新
     *
     * @param appConfigBannerEditParam
     */
    boolean updateByParam(AppConfigBannerEditParam appConfigBannerEditParam);

    /**
     * 获取app应用banner列表
     *
     * @return
     */
    List<AppBannerDto> fetchAppConfigBannerList();
}