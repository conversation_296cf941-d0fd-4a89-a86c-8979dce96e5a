package cn.getech.ehm.system.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 培训认证表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("system_learning_config")
public class SystemLearningConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 学习时间
     */
    @TableField("learning_time")
    private Date learningTime;

    /**
     * 在线培训人数
     */
    @TableField("online_up_count")
    private Integer onlineUpCount;

    /**
     * 线下培训人数
     */
    @TableField("online_down_count")
    private Integer onlineDownCount;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 0：未删除  1: 已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;


}
