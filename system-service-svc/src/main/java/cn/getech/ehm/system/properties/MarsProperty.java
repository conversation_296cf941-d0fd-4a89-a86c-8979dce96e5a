package cn.getech.ehm.system.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * Mars配置文件
 *
 * <AUTHOR>
 * @date 2021-01-13 10:15:43
 **/
@RefreshScope
@ConfigurationProperties(prefix = "mars")
public class MarsProperty {

    String apiHost;

    String apiPrefix = "/api/v1";

    String authApi = "/auth";

    String username = "admin";

    String password = "admin";

    String indexPath = "/#/index";

    public String getApiHost() {
        return apiHost;
    }

    public void setApiHost(String apiHost) {
        this.apiHost = apiHost;
    }

    public String getApiPrefix() {
        return apiPrefix;
    }

    public void setApiPrefix(String apiPrefix) {
        this.apiPrefix = apiPrefix;
    }

    public String getAuthApi() {
        return authApi;
    }

    public void setAuthApi(String authApi) {
        this.authApi = authApi;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getIndexPath() {
        return indexPath;
    }

    public void setIndexPath(String indexPath) {
        this.indexPath = indexPath;
    }
}
