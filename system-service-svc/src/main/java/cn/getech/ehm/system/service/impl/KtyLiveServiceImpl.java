package cn.getech.ehm.system.service.impl;


import cn.getech.ehm.system.dto.ketianyun.*;
import cn.getech.ehm.system.service.IKtyLiveService;
import cn.getech.ehm.system.utils.KeTianYunUtils;
import cn.getech.ehm.system.utils.ketianyun.LiveProperty;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.KeTianVideoInfoQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.codec.digest.DigestUtils.md5Hex;

@Slf4j
@Service
public class KtyLiveServiceImpl implements IKtyLiveService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private LiveProperty liveProperty;

    @Override
    public Boolean setToken(String token) {

        long ts = System.currentTimeMillis();

        Map<String, String> parmaMap = new HashMap<>();
        parmaMap.put("appId", liveProperty.getAppId());
        parmaMap.put("timestamp", String.valueOf(ts));
        parmaMap.put("token", token);
        String cloudLiveSign = this.getCloudLiveSign(parmaMap);

        MultiValueMap<String, String> param = new LinkedMultiValueMap();

        param.add("appId", liveProperty.getAppId());
        param.add("timestamp", String.valueOf(ts));
        param.add("token", token);
        param.add("sign", cloudLiveSign);


        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity httpEntity = new HttpEntity(param, httpHeaders);
        KeTianYunResponse response = restTemplate.postForObject(liveProperty.getSetTokenUrl(), httpEntity, KeTianYunResponse.class);
        return response.getCode() == 200;
    }

    @Override
    public String authLogin(String token, String url) {
        return String.format(liveProperty.getAuthLoginUrl(), token, url);
    }

    @Override
    public String getSign(Object object) {

        // 创建参数表 （创建接口需要传递的所有参数表）
        Map<String, String> paramMap = new HashMap<String, String>();

        if (object != null) {
            if (object instanceof KeTianVideoQueryParam) {
                KeTianVideoQueryParam keTianVideoQueryParam = (KeTianVideoQueryParam) object;
                getValueByParamMap(keTianVideoQueryParam, paramMap);
            } else if (object instanceof KeTianVideoInfoQueryParam) {
                KeTianVideoInfoQueryParam keTianVideoInfoQueryParam = (KeTianVideoInfoQueryParam) object;
                getValueByParamMap(keTianVideoInfoQueryParam, paramMap);
            }

        }

        paramMap.put("userid", liveProperty.getUserId());
        //对参数名进行字典排序
        String[] keyArray = paramMap.keySet().toArray(new String[0]);
        Arrays.sort(keyArray);

        //拼接有序的参数串
        StringBuilder stringBuilder = new StringBuilder();
        for (String key : keyArray) {
            stringBuilder.append(key + "=").append(paramMap.get(key) + "&");
        }
        // 去掉最后一个 &
        String substring = stringBuilder.substring(0, stringBuilder.length() - 1);
        stringBuilder = new StringBuilder(substring);
        stringBuilder.append(liveProperty.getSecretKey());

        String signSource = stringBuilder.toString();

        // sha1 加密
        return KeTianYunUtils.sha1Encode(signSource).toUpperCase();
    }

    private <T> void getValueByParamMap(T t, Map<String, String> paramMap) {
        Class classz = t.getClass();
        Field[] fields = classz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            fields[i].setAccessible(true);
            try {
                Object o = fields[i].get(t);
                if (o != null) {
                    if (o instanceof String) {
                        paramMap.put(fields[i].getName(), (String) o);
                    } else {
                        paramMap.put(fields[i].getName(), String.valueOf(o));
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("获取不到对应的方法/属性", e);
            }
        }
    }

    @Override
    public String getCloudLiveSign(Map<String, String> paramMap) {
        //对参数名进行字典排序
        String[] keyArray = paramMap.keySet().toArray(new String[0]);
        Arrays.sort(keyArray);

        //拼接有序的参数串
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(liveProperty.getAppSecret());
        for (String key : keyArray) {
            stringBuilder.append(key).append(paramMap.get(key));
        }

        stringBuilder.append(liveProperty.getAppSecret());
        String signSource = stringBuilder.toString();
        return md5Hex(signSource).toUpperCase();
    }

    @Override
    public KeTianYunLiveInfoDto getCurrLiveInfo() {
        long ts = System.currentTimeMillis();

        Map<String, String> paramMap = new HashMap<>(2);
        paramMap.put("appId", liveProperty.getAppId());
        paramMap.put("timestamp", Long.toString(ts));
        String cloudLiveSign = getCloudLiveSign(paramMap);

        String liveInfoUrl = String.format(liveProperty.getLiveChannelInfoUrl(), ts, cloudLiveSign);
        KeTianYunLiveInfoResponse response = restTemplate.getForObject(liveInfoUrl, KeTianYunLiveInfoResponse.class);
        if (response.getCode() != 200) {
            // TODO
            return null;
        }

        response.getData().setLiveStatus(getLiveStatus(response.getData().getStream()).equals(LiveProperty.LiveStatus.ONLINE_STATUS.getCode()) ? 1 : 0);
        if (response.getData().getLiveStatus() == 1) {
            response.getData().setOnLineCount(getLiveOnlineCount());
        }

        return response.getData();

    }

    @Override
    public Integer getLiveOnlineCount() {
        Long ts = System.currentTimeMillis();

        Map<String, String> paramMap = new HashMap<>(2);
        paramMap.put("appId", liveProperty.getAppId());
        paramMap.put("timestamp", Long.toString(ts));
        paramMap.put("userId", liveProperty.getUserId());
        String cloudLiveSign = getCloudLiveSign(paramMap);
        String liveCountUrl = String.format(liveProperty.getLiveOnlineCountUrl(), ts, cloudLiveSign);
        // 获取直播人数
        KeTianYunLiveOnlineCountResponse onlineCountResponse = restTemplate.getForObject(liveCountUrl, KeTianYunLiveOnlineCountResponse.class);

        if (onlineCountResponse.getResult() != null && !onlineCountResponse.getResult().isEmpty()) {
            return onlineCountResponse.getResult().get(0).getCount();
        }
        return 0;
    }

    @Override
    public String getLiveStatus(String stream) {
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(String.format(liveProperty.getLiveChannelStatusUrl(), stream), String.class);
        if (responseEntity.getStatusCode().isError()) {
            log.error("获取直播状态失败");
            return "error";
        }
        log.info("直播状态:" + responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Override
    public String getWriteToken() {
        return liveProperty.getWriteToken();
    }

    @Override
    public KeTianYunVideoResponse getKeTianYunVideoList(KeTianVideoQueryParam keTianVideoQueryParam) {
        String sign = getSign(keTianVideoQueryParam);

        MultiValueMap<String, String> param = new LinkedMultiValueMap();

        param.add("userid", liveProperty.getUserId());
        param.add("ptime", keTianVideoQueryParam.getPtime());
        param.add("sign", sign);
        param.add("numPerPage", String.valueOf(keTianVideoQueryParam.getNumPerPage()));
        param.add("pageNum", String.valueOf(keTianVideoQueryParam.getPageNum()));

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity httpEntity = new HttpEntity(param, httpHeaders);

        KeTianYunVideoResponse keTianYunVideoResponse = restTemplate.postForObject(liveProperty.getVideoSearchUrl(), httpEntity, KeTianYunVideoResponse.class);
        Assert.fail(ResultCode.INTERNAL_SERVER_ERROR, keTianYunVideoResponse.getCode() != 200);
        keTianYunVideoResponse.setNumPerPage(keTianVideoQueryParam.getNumPerPage());
        keTianYunVideoResponse.setPageNum(keTianVideoQueryParam.getPageNum());
        return keTianYunVideoResponse;
    }

    @Override
    public KeTianYunVideoDto getKeTianYunVideoInfo(String vid) {
        KeTianVideoInfoQueryParam keTianVideoInfoQueryParam = new KeTianVideoInfoQueryParam();

        keTianVideoInfoQueryParam.setPtime(String.valueOf(System.currentTimeMillis()));
        keTianVideoInfoQueryParam.setVid(vid);

        String sign = getSign(keTianVideoInfoQueryParam);

        MultiValueMap<String, String> param = new LinkedMultiValueMap();

        param.add("userid", liveProperty.getUserId());
        param.add("ptime", keTianVideoInfoQueryParam.getPtime());
        param.add("sign", sign);
        param.add("vid", keTianVideoInfoQueryParam.getVid());

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity httpEntity = new HttpEntity(param, httpHeaders);

        KeTianYunVideoInfoResponse infoResponse = restTemplate.postForObject(liveProperty.getVideoInfoUrl(), httpEntity, KeTianYunVideoInfoResponse.class);
        Assert.fail(ResultCode.INTERNAL_SERVER_ERROR, infoResponse.getCode() != 200);
        return infoResponse.getData().get(0);
    }

}
