package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.enums.TimeType;
import cn.getech.ehm.system.dto.learningconfig.*;
import cn.getech.ehm.system.dto.screen.ScreenLearningConfigDto;
import cn.getech.ehm.system.entity.SystemLearningConfig;
import cn.getech.ehm.system.entity.SystemLearningGrandtotal;
import cn.getech.ehm.system.mapper.SystemLearningConfigMapper;
import cn.getech.ehm.system.service.ICustomerEngineerService;
import cn.getech.ehm.system.service.ISystemLearningConfigService;
import cn.getech.ehm.system.service.ISystemLearningGrandtotalService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.client.KnowledgeClient;
import cn.getech.ehm.system.dto.LearningConfigPersonCountDto;
import cn.getech.ehm.system.dto.TimeQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;


/**
 * <pre>
 * 培训认证表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Slf4j
@Service
public class SystemLearningConfigServiceImpl extends BaseServiceImpl<SystemLearningConfigMapper, SystemLearningConfig> implements ISystemLearningConfigService {

    @Autowired
    private SystemLearningConfigParamMapper systemLearningConfigParamMapper;

    @Autowired
    private SystemLearningGrandtotalParamMapper systemLearningGrandtotalParamMapper;

    @Autowired
    private SystemLearningConfigMapper systemLearningConfigMapper;

    @Autowired
    private ICustomerEngineerService customerEngineerService;

    @Autowired
    private KnowledgeClient knowledgeClient;

    @Autowired
    private ISystemLearningGrandtotalService systemLearningGrandtotalService;

    @Override
    public PageResult<SystemLearningConfigDto> pageDto(SystemLearningConfigQueryParam systemLearningConfigQueryParam) {
        Wrapper<SystemLearningConfig> wrapper = getPageSearchWrapper(systemLearningConfigQueryParam);
        PageResult<SystemLearningConfigDto> result = systemLearningConfigParamMapper.pageEntity2Dto(page(systemLearningConfigQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByParam(SystemLearningAddParam systemLearningAddParam) {

        SystemLearningGrandtotal learningGrandtotal = systemLearningGrandtotalParamMapper.addParam2Entity(systemLearningAddParam.getGrandTotal());

        systemLearningGrandtotalService.remove(Wrappers.emptyWrapper());
        systemLearningGrandtotalService.save(learningGrandtotal);

        List<SystemLearningConfig> systemLearningConfigs = systemLearningConfigParamMapper.addParamList2Entity(systemLearningAddParam.getLearningconfigs());
//        Assert.notNull(ResultCode.PARAM_VALID_ERROR,systemLearningConfigs);
        remove(Wrappers.emptyWrapper());
        saveBatch(systemLearningConfigs);
        return true;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(SystemLearningConfigEditParam systemLearningConfigEditParam) {
        SystemLearningConfig systemLearningConfig = systemLearningConfigParamMapper.editParam2Entity(systemLearningConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, systemLearningConfig);
        return updateById(systemLearningConfig);
    }

    @Override
    public SystemLearningDto listLearningConfig() {
        SystemLearningDto dto = new SystemLearningDto();

        Wrapper<SystemLearningConfig> searchWrapper = getSearchWrapper();
        dto.setLearningconfigDtos(systemLearningConfigParamMapper.entityList2Dto(systemLearningConfigMapper.selectList(searchWrapper)));

        dto.setGrandTotaldto(systemLearningGrandtotalService.getDto());
        return dto;
    }

    @Override
    public LearningConfigPersonCountDto onLinePersonCount(TimeQueryParam timeQueryParam) {

        Date startTime = DateUtil.beginOfYear(new Date());
        Date endTime = DateUtil.endOfYear(new Date());


        if (TimeType.MONTH.getValue().equals(timeQueryParam.getTime())) {
            Calendar c1 = Calendar.getInstance();
            c1.set(Calendar.YEAR, timeQueryParam.getYear());
            c1.set(Calendar.MONTH, timeQueryParam.getMonth() - 1);
            c1.set(Calendar.DAY_OF_MONTH, 1);
            startTime = DateUtil.beginOfMonth(c1).getTime();
            endTime = DateUtil.endOfMonth(c1).getTime();

        } else if (TimeType.YEAR.getValue().equals(timeQueryParam.getTime())) {
            Calendar c1 = Calendar.getInstance();
            c1.set(Calendar.YEAR, timeQueryParam.getYear());
            c1.set(Calendar.MONTH, 1);
            c1.set(Calendar.DAY_OF_MONTH, 1);
            startTime = DateUtil.beginOfYear(c1).getTime();
            endTime = DateUtil.endOfYear(c1).getTime();
        }

        LearningConfigPersonCountDto countDto = systemLearningConfigMapper.sumOnlinePersonCount(startTime, endTime);

        return countDto;
    }

    @Override
    public SystemLearningGrandtotalDto getScreenLearningConfigInfo() {
        ScreenLearningConfigDto dto = new ScreenLearningConfigDto();
        dto.setEngineerCount(customerEngineerService.engineerCount());
        RestResponse<Integer> courseCountResponse = knowledgeClient.getCourseCount();
        if (courseCountResponse != null && courseCountResponse.getCode() == 0) {
            dto.setCourseCount(courseCountResponse.getData());
        }
        RestResponse<Integer> playCountResponse = knowledgeClient.getPlayCount();
        if (playCountResponse != null && playCountResponse.getCode() == 0) {
            dto.setPlayCount(playCountResponse.getData());
        }

        return systemLearningGrandtotalService.getDto();
    }


    @Override
    public SystemLearningConfigDto getDtoById(String id) {
        return systemLearningConfigParamMapper.entity2Dto((SystemLearningConfig) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<SystemLearningConfigDto> rows) {
        return saveBatch(systemLearningConfigParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<SystemLearningConfig> getPageSearchWrapper(SystemLearningConfigQueryParam systemLearningConfigQueryParam) {
        LambdaQueryWrapper<SystemLearningConfig> wrapper = Wrappers.<SystemLearningConfig>lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(SystemLearningConfig.class)) {
            wrapper.orderByDesc(SystemLearningConfig::getUpdateTime, SystemLearningConfig::getCreateTime);
        }
        return wrapper;
    }

    private Wrapper<SystemLearningConfig> getSearchWrapper() {
        LambdaQueryWrapper<SystemLearningConfig> wrapper = Wrappers.<SystemLearningConfig>lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(SystemLearningConfig.class)) {
            wrapper.orderByAsc(SystemLearningConfig::getLearningTime);
        }
        return wrapper;
    }
}
