package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.appbanner.AppConfigBannerAddParam;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerDto;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerEditParam;
import cn.getech.ehm.system.dto.appbanner.AppConfigBannerQueryParam;
import cn.getech.ehm.system.service.IAppConfigBannerService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * app应用banner配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@RestController
@RequestMapping("/appConfigBanner")
@Api(tags = "web接口：app应用轮播图配置服务接口")
public class WebAppConfigBannerController {

    @Autowired
    private IAppConfigBannerService appConfigBannerService;

    /**
     * 分页获取app应用banner配置列表
     */
    @ApiOperation("分页获取app应用轮播图配置列表")
    @GetMapping("/list")
    //@Permission("app:config:banner:list")
    public RestResponse<PageResult<AppConfigBannerDto>> pageList(@Valid AppConfigBannerQueryParam appConfigBannerQueryParam) {
        return RestResponse.ok(appConfigBannerService.pageDto(appConfigBannerQueryParam));
    }

    /**
     * 新增app应用banner配置
     */
    @ApiOperation("新增app应用banner配置")
    @AuditLog(title = "app应用banner配置", desc = "新增app应用轮播图配置", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("app:config:banner:update")
    public RestResponse<Boolean> add(@RequestBody @Valid AppConfigBannerAddParam appConfigBannerAddParam) {
        return RestResponse.ok(appConfigBannerService.saveByParam(appConfigBannerAddParam));
    }

    /**
     * 修改app应用banner配置
     */
    @ApiOperation(value = "修改app应用banner配置")
    @AuditLog(title = "app应用banner配置", desc = "修改app应用轮播图配置", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("app:config:banner:update")
    public RestResponse<Boolean> update(@RequestBody @Valid AppConfigBannerEditParam appConfigBannerEditParam) {
        return RestResponse.ok(appConfigBannerService.updateByParam(appConfigBannerEditParam));
    }

    /**
     * 根据id删除app应用banner配置
     */
    @ApiOperation(value = "根据id删除app应用banner配置")
    @AuditLog(title = "app应用banner配置", desc = "app应用轮播图配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("app:config:banner:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(appConfigBannerService.removeById(id));
    }

    /**
     * 根据id获取app应用banner配置
     */
    @ApiOperation(value = "根据id获取app应用轮播图配置")
    @GetMapping(value = "/{id}")
    //@Permission("app:config:banner:list")
    public RestResponse<AppConfigBannerDto> get(@PathVariable String id) {
        return RestResponse.ok(appConfigBannerService.getDtoById(id));
    }

}
