package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.system.dto.learningconfig.*;
import cn.getech.ehm.system.entity.SystemLearningGrandtotal;
import cn.getech.ehm.system.mapper.SystemLearningGrandtotalMapper;
import cn.getech.ehm.system.service.ISystemLearningGrandtotalService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <pre>
 * 培训认证累计表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Slf4j
@Service
public class SystemLearningGrandtotalServiceImpl extends BaseServiceImpl<SystemLearningGrandtotalMapper, SystemLearningGrandtotal> implements ISystemLearningGrandtotalService {

    @Autowired
    private SystemLearningGrandtotalParamMapper systemLearningGrandtotalParamMapper;

    @Override
    public PageResult<SystemLearningGrandtotalDto> pageDto(SystemLearningGrandtotalQueryParam systemLearningGrandtotalQueryParam) {
        Wrapper<SystemLearningGrandtotal> wrapper = getPageSearchWrapper(systemLearningGrandtotalQueryParam);
        PageResult<SystemLearningGrandtotalDto> result = systemLearningGrandtotalParamMapper.pageEntity2Dto(page(systemLearningGrandtotalQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(SystemLearningGrandtotalAddParam systemLearningGrandtotalAddParam) {
        SystemLearningGrandtotal systemLearningGrandtotal = systemLearningGrandtotalParamMapper.addParam2Entity(systemLearningGrandtotalAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, systemLearningGrandtotal);
        return save(systemLearningGrandtotal);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(SystemLearningGrandtotalEditParam systemLearningGrandtotalEditParam) {
        SystemLearningGrandtotal systemLearningGrandtotal = systemLearningGrandtotalParamMapper.editParam2Entity(systemLearningGrandtotalEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, systemLearningGrandtotal);
        return updateById(systemLearningGrandtotal);
    }

    @Override
    public SystemLearningGrandtotalDto getDto() {

        List<SystemLearningGrandtotal> list = list(Wrappers.emptyWrapper());

        if (list != null && list.size() != 0) {
            return systemLearningGrandtotalParamMapper.entity2Dto(list.get(0));
        }
        return null;
    }


    @Override
    public SystemLearningGrandtotalDto getDtoById(Long id) {
        return systemLearningGrandtotalParamMapper.entity2Dto((SystemLearningGrandtotal) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<SystemLearningGrandtotalDto> rows) {
        return saveBatch(systemLearningGrandtotalParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<SystemLearningGrandtotal> getPageSearchWrapper(SystemLearningGrandtotalQueryParam systemLearningGrandtotalQueryParam) {
        LambdaQueryWrapper<SystemLearningGrandtotal> wrapper = Wrappers.<SystemLearningGrandtotal>lambdaQuery();
        if (BaseEntity.class.isAssignableFrom(SystemLearningGrandtotal.class)) {
            wrapper.orderByDesc(SystemLearningGrandtotal::getUpdateTime, SystemLearningGrandtotal::getCreateTime);
        }
        return wrapper;
    }
}
