package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.appversion.*;
import cn.getech.ehm.system.entity.AppVersion;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * app版本信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
public interface IAppVersionService extends IBaseService<AppVersion> {

    /**
     * 分页查询，返回Dto
     *
     * @param appVersionQueryParam
     * @return
     */
    PageResult<AppVersionDto> pageDto(AppVersionQueryParam appVersionQueryParam);

    /**
     * 保存
     *
     * @param appVersionAddParam
     * @return
     */
    boolean saveByParam(AppVersionAddParam appVersionAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    AppVersionDto getDtoById(String id);

    /**
     * 获取最新版本二维码信息
     *
     * @return
     */
    AppVersionQrCodeDto latestVersion();

    /**
     * 获取最新版本url
     *
     * @return
     */
    AppVersionDto latestVersion(Integer versionCode);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<AppVersionDto> rows);

    /**
     * 更新
     *
     * @param appVersionEditParam
     */
    boolean updateByParam(AppVersionEditParam appVersionEditParam);

    /**
     * 发布或取消发布
     *
     * @param id
     * @return
     */
    boolean publish(String id);
}