package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.appversion.*;
import cn.getech.ehm.system.service.IAppVersionService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * app版本信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
@RestController
@RequestMapping("/appVersion")
@Api(tags = "web接口：app版本信息服务接口")
public class WebAppVersionController {

    @Autowired
    private IAppVersionService appVersionService;

    /**
     * 分页获取app版本信息列表
     */
    @ApiOperation("分页获取app版本信息列表")
    @GetMapping("/list")
    //@Permission("app:version:list")
    public RestResponse<PageResult<AppVersionDto>> pageList(@Valid AppVersionQueryParam appVersionQueryParam) {
        return RestResponse.ok(appVersionService.pageDto(appVersionQueryParam));
    }

    /**
     * 新增app版本信息
     */
    @ApiOperation("新增app版本信息")
    @AuditLog(title = "app版本信息", desc = "新增app版本信息", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("app:version:update")
    public RestResponse<Boolean> add(@RequestBody @Valid AppVersionAddParam appVersionAddParam) {
        return RestResponse.ok(appVersionService.saveByParam(appVersionAddParam));
    }

    /**
     * 修改app版本信息
     */
    @ApiOperation(value = "修改app版本信息")
    @AuditLog(title = "app版本信息", desc = "修改app版本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("app:version:update")
    public RestResponse<Boolean> update(@RequestBody @Valid AppVersionEditParam appVersionEditParam) {
        return RestResponse.ok(appVersionService.updateByParam(appVersionEditParam));
    }

    /**
     * 根据id删除app版本信息
     */
    @ApiOperation(value = "根据id删除app版本信息")
    @AuditLog(title = "app版本信息", desc = "app版本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    //@Permission("app:version:delete")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(appVersionService.removeById(id));
    }

    /**
     * 根据id获取app版本信息
     */
    @ApiOperation(value = "根据id获取app版本信息")
    @GetMapping(value = "/{id}")
    //@Permission("app:version:list")
    public RestResponse<AppVersionDto> get(@PathVariable String id) {
        return RestResponse.ok(appVersionService.getDtoById(id));
    }

    /**
     * 发布或取消发布
     */
    @ApiOperation(value="发布或取消发布")
    @AuditLog(title = "发布或取消发布",desc = "发布或取消发布,true-发布，false-不发布",businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    //@Permission("app:version:update")
    public RestResponse<Boolean> publish(@PathVariable  String id) {
        return RestResponse.ok(appVersionService.publish(id));
    }

    /**
     * 获取最新app版本二维码信息
     */
    @ApiOperation(value = "获取最新app版本二维码信息")
    @GetMapping(value = "/latestVersion")
    //@Permission("app:version:list")
    public RestResponse<AppVersionQrCodeDto> latestVersion() {
        return RestResponse.ok(appVersionService.latestVersion());
    }


}
