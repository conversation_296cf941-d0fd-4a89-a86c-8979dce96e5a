package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.system.dto.notification.NotificationAddParam;
import cn.getech.ehm.system.dto.notification.NotificationDto;
import cn.getech.ehm.system.dto.notification.NotificationQueryParam;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.system.entity.Notification;
import cn.getech.ehm.system.mapper.NotificationMapper;
import cn.getech.ehm.system.service.INotificationService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 消息通知
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class NotificationServiceImpl extends BaseServiceImpl<NotificationMapper, Notification> implements INotificationService {

    @Override
    public PageResult<NotificationDto> pageDto(NotificationQueryParam notificationQueryParam) {
        LambdaQueryWrapper<Notification> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(notificationQueryParam.getKeyword())) {
            wrapper.and(w -> w.like(Notification::getRecipients, notificationQueryParam.getKeyword())
                    .or()
                    .like(Notification::getTitle, notificationQueryParam.getKeyword())
                    .or()
                    .like(Notification::getContent, notificationQueryParam.getKeyword()));
        }
        if (null != notificationQueryParam.getBeginTime() && null != notificationQueryParam.getEndTime()) {
            wrapper.between(Notification::getCreateTime, notificationQueryParam.getBeginTime(), notificationQueryParam.getEndTime());
        }
        wrapper.select(Notification::getId, Notification::getTitle, Notification::getContent, Notification::getRecipients,
                Notification::getType, Notification::getCreateBy, Notification::getCreateTime);
        wrapper.orderByDesc(Notification::getCreateTime);
        PageResult<Notification> result = page(notificationQueryParam, wrapper);
        List<NotificationDto> customizeSendDtos = CopyDataUtil.copyList(result.getRecords(), NotificationDto.class);

        return Optional.ofNullable(PageResult.<NotificationDto>builder()
                .records(customizeSendDtos)
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }


    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(NotificationAddParam notificationAddParam) {
        Notification notification = CopyDataUtil.copyObject(notificationAddParam, Notification.class);
        return save(notification);
    }

    @Override
    public List<Notification> latestMailNotification() {
        LambdaQueryWrapper<Notification> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(Notification::getType, NotifyType.EMAIL);
        lambdaQueryWrapper.gt(Notification::getCreateTime, new Date(System.currentTimeMillis()-10000));
        return this.list(lambdaQueryWrapper);
    }
}
