package cn.getech.ehm.system.notify;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.concurrent.ExecutorService;

/**
 * 邮件测试服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MailService {

    @Value("${spring.mail.username}")
    private String username;

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private ExecutorService executorService;

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param text
     */
    public void sendEmail(String subject, String text,String... to) {
        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(username);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text, true);
            executorService.execute(() -> mailSender.send(message));
            log.info("邮件发送成功");
        } catch (MessagingException e) {
            log.error("邮件发送失败", e);
        }
    }

}
