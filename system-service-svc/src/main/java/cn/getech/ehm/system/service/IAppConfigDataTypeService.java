package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeAddParam;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeDto;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeEditParam;
import cn.getech.ehm.system.dto.appdatatype.AppConfigDataTypeQueryParam;
import cn.getech.ehm.system.dto.appicon.AppConfigMoveParam;
import cn.getech.ehm.system.entity.AppConfigDataType;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.AppDataTypeDto;

import java.util.List;

/**
 * <pre>
 * 分类信息表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
public interface IAppConfigDataTypeService extends IBaseService<AppConfigDataType> {

    /**
     * 分页查询，返回Dto
     *
     * @param appConfigDataTypeQueryParam
     * @return
     */
    PageResult<AppConfigDataTypeDto> pageDto(AppConfigDataTypeQueryParam appConfigDataTypeQueryParam);

    /**
     * 保存
     *
     * @param appConfigDataTypeAddParam
     * @return
     */
    boolean saveByParam(AppConfigDataTypeAddParam appConfigDataTypeAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    AppConfigDataTypeDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<AppConfigDataTypeDto> rows);

    /**
     * 更新
     *
     * @param appConfigDataTypeEditParam
     */
    boolean updateByParam(AppConfigDataTypeEditParam appConfigDataTypeEditParam);

    /**
     * 上移下移
     *
     * @param moveParam
     * @return
     */
    boolean moving(AppConfigMoveParam moveParam);

    /**
     * 获取app应用配置数据类型列表
     *
     * @return
     */
    List<AppDataTypeDto> fetchAppConfigDataTypeList();

    /**
     * 初始化
     * @return
     */
    Boolean initialization(String tenantId);
}