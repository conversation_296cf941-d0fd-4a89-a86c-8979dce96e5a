package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.dto.useranalysis.UserActionAddParam;
import cn.getech.ehm.system.service.IUserActionService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 登录回调控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@RestController
@RequestMapping("/login")
@Api(tags = "登录回调服务接口")
public class LoginCallbackController {

    @Autowired
    private IUserActionService userActionService;

    /**
     * 统计登录次数信息
     */
    @ApiOperation("统计登录次数信息")
    @AuditLog(title = "统计登录次数信息", desc = "统计登录次数信息", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("user:action:update")
    public RestResponse<Boolean> add() {
        UserActionAddParam userActionAddParam = new UserActionAddParam();
        userActionAddParam.setLoginCount(1);
        return RestResponse.ok(userActionService.saveByParam(userActionAddParam));
    }

}
