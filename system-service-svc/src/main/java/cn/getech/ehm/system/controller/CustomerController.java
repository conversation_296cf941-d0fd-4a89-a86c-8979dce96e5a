package cn.getech.ehm.system.controller;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.*;
import cn.getech.ehm.system.dto.customer.*;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisDto;
import cn.getech.ehm.system.dto.useranalysis.UserAnalysisQueryParam;
import cn.getech.ehm.system.service.*;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import cn.getech.poros.framework.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.*;

/**
 * <p>
 * 客户控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/customer")
@Api(tags = "客户服务接口")
public class CustomerController {

    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ICustomerEngineerService customerEngineerService;

    @Autowired
    private ICustomerEquipmentService customerEquipmentService;

    @Autowired
    private ICustomerSalesmanService customerSalesmanService;
    @Autowired
    private IUserAnalysisService userAnalysisService;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    /**
     * 分页获取客户列表
     */
    @ApiOperation("分页获取客户列表")
    @GetMapping("/list")
    //@Permission("customer:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<PageResult<CustomerDto>> pageList(@Valid CustomerQueryParam customerQueryParam) {
        return RestResponse.ok(customerService.pageDto(customerQueryParam));
    }

    /**
     * 获取已签约客户列表
     */
    @ApiOperation("获取已签约客户列表")
    @GetMapping("/allList")
    //@Permission("customer:list")
    public RestResponse<List<CustomerDto>> allList(@Valid CustomerQueryDto customerQueryDto) {
        customerQueryDto.setIsHaveUid(StaticValue.ONE);
        return RestResponse.ok(customerService.list(customerQueryDto));
    }

    /**
     * 获取所有客户列表
     */
    @ApiOperation("获取所有客户列表")
    @GetMapping("/all")
    //@Permission("customer:list")
    public RestResponse<List<CustomerDto>> all(@Valid CustomerQueryDto customerQueryDto) {
        customerQueryDto.setIsHaveUid(StaticValue.ZERO);
        return RestResponse.ok(customerService.list(customerQueryDto));
    }

    @ApiOperation("获取已签约客户的所有设备Ids（筛选掉没有设备的用户）")
    @GetMapping("/getAllCustomerEqui")
    //@Permission("customer:list")
    public RestResponse<List<CustomerInfoEquipmentDto>> getAllCustomerEqui() {
        return RestResponse.ok(customerService.getAllCustomerEqui());
    }

    @ApiOperation("获取客户总数和已签约客户总数")
    @GetMapping("/customerCount")
    public RestResponse<List<Integer>> customerCount() {
        List<Integer> list = new ArrayList<>();

        list.add(customerService.listCustomerCount());
        list.add(customerService.listSignCustomerCount());

        return RestResponse.ok(list);
    }

    /**
     * 根据设备id集合获取客户map
     */
    @ApiOperation("根据设备id集合获取客户map")
    @PostMapping("/getMapByEquipmentIds")
    //@Permission("customer:list")
    public RestResponse<Map<String, EquipmentCustomerDto>> getMapByEquipmentIds(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(customerService.getMapByEquipmentIds(equipmentIds));
    }

    /**
     * 根据设备id集合获取客户和工程师uid集合
     */
    @ApiOperation("根据设备id集合获取客户和工程师uid集合")
    @GetMapping("/getUidsByEquipmentId")
    //@Permission("customer:list")
    public RestResponse<List<String>> getUidsByEquipmentId(@RequestParam("equipmentId") String equipmentId) {
        return RestResponse.ok(customerService.getUidsByEquipmentId(equipmentId));
    }

    /**
     * 根据uid获取客户
     */
    @ApiOperation("根据uid获取客户")
    @GetMapping("/getDtoByUid")
    //@Permission("customer:list")
    public RestResponse<CustomerDto> getDtoByUid(@RequestParam("uid") String uid) {
        return RestResponse.ok(customerService.getDtoByUid(uid));
    }

    /**
     * 新增客户
     */
    @ApiOperation("新增客户")
    @AuditLog(title = "客户", desc = "新增客户", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("customer:update")
    public RestResponse<Boolean> add(@RequestBody @Valid CustomerAddParam customerAddParam) {
        return RestResponse.ok(customerService.saveByParam(customerAddParam));
    }

    /**
     * 批量添加客户
     */
    @ApiOperation("批量添加客户")
    @AuditLog(title = "客户", desc = "批量添加客户", businessType = BusinessType.INSERT)
    @PostMapping("/addBatchCustomer")
    RestResponse<Boolean> addBatchCustomer(@RequestBody List<CustomerImportDto> customerImportDtos) {

        return RestResponse.ok(customerService.addBatchCustomer(customerImportDtos));
    }

    /**
     * 修改客户
     */
    @ApiOperation(value = "修改客户")
    @AuditLog(title = "客户", desc = "修改客户", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("customer:update")
    public RestResponse<Boolean> update(@RequestBody @Valid CustomerEditParam customerEditParam) {
        return RestResponse.ok(customerService.updateByParam(customerEditParam));
    }

    /**
     * 根据id禁用/启用客户
     */
    @ApiOperation(value = "根据id禁用/启用客户")
    @AuditLog(title = "客户", desc = "客户", businessType = BusinessType.UPDATE)
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "客户id", dataType = "String", required = true),
            @ApiImplicitParam(name = "status", value = "0禁用1启用", dataType = "int", required = true)})*/
    @GetMapping("/updateStatusById")
    //@Permission("customer:delete")
    public RestResponse<Boolean> updateStatusById(@RequestParam("id") @NotEmpty String id, @RequestParam("status") Integer status) {
        return RestResponse.ok(customerService.updateStatusById(id, status));
    }

    /**
     * 根据id获取客户
     */
    @ApiOperation(value = "根据id获取客户")
    @GetMapping(value = "/{id}")
    //@Permission("customer:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<CustomerDto> get(@PathVariable String id) {
        return RestResponse.ok(customerService.getDtoById(id));
    }

    /**
     * 获取当前登录人所属客户信息
     * 如果传参，则取传入客户信息
     */
    @ApiOperation(value = "获取当前登录人所属客户信息")
    @AuditLog(title = "客户", desc = "获取当前登录人所属客户信息", businessType = BusinessType.QUERY)
//    @ApiImplicitParam(name="customerId",value="客户id",paramType="String")
    @GetMapping("/getCurrentCustomer")
    public RestResponse<CustomerDto> getCurrentCustomer(@RequestParam(name = "customerId", required = false) String customerId) {
        return RestResponse.ok(customerService.getCurrentCustomer(customerId));
    }

    /**
     * 校验uid是否已经绑定
     */
    @ApiOperation(value = "校验uid是否已经绑定")
    @GetMapping(value = "/checkUid")
    //@Permission("customer:list")
    public RestResponse<Boolean> checkUid(@RequestParam("uid") String uid) {
        return RestResponse.ok(customerService.checkUid(uid));
    }

    /**
     * 导出客户列表
     */
    @ApiOperation(value = "导出客户列表")
    @AuditLog(title = "客户", desc = "导出客户列表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    // @Permission("customer:export")
    public void excelExport(@Valid CustomerQueryParam customerQueryParam, HttpServletResponse response) {
        List<CustomerExcel> excels = customerService.exportExcel(customerQueryParam.getKeyword(), customerQueryParam.getArea());
        ExcelUtils<CustomerExcel> util = new ExcelUtils<>(CustomerExcel.class);

        util.exportExcel(excels, "客户列表", response);
    }

    /**
     * Excel导入客户
     */
    @ApiOperation(value = "Excel导入客户")
    @AuditLog(title = "客户", desc = "Excel导入客户", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("customer:import")
    public RestResponse<Boolean> excelImport(@RequestPart("file") MultipartFile file) {
        ExcelUtils<CustomerExcel> util = new ExcelUtils<>(CustomerExcel.class);
        List<CustomerExcel> excels = util.importExcel(file);
        if (CollectionUtils.isEmpty(excels)) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("data_null_error", null, LocaleContextHolder.getLocale())));
        }
        return RestResponse.ok(customerService.importExcel(excels));
    }

    /**
     * 导入修改客户城市
     */
    @ApiOperation(value = "导入修改客户城市")
    @AuditLog(title = "客户", desc = "导入修改客户城市", businessType = BusinessType.INSERT)
    @PostMapping("/importCity")
    //@Permission("customer:import")
    public RestResponse<Boolean> importCity(@RequestPart("file") MultipartFile file) {
        ExcelUtils<CustomerCityExcel> util = new ExcelUtils<>(CustomerCityExcel.class);
        List<CustomerCityExcel> excels = util.importExcel(file);
        if (CollectionUtils.isEmpty(excels)) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("data_null_error", null, LocaleContextHolder.getLocale())));
        }

        return RestResponse.ok(customerService.updateCityBatch(excels));
    }


    /**
     * 分页获取工程师列表
     */
    @ApiOperation("分页获取工程师列表")
    @GetMapping("/engineerList")
    //@Permission("customer:list")
    public RestResponse<PageResult<CustomerEngineerDto>> engineerList(@Valid CustomerEngineerQueryParam customerEngineerQueryParam) {
        //客户平台需要过滤
        if (null != customerEngineerQueryParam && customerEngineerQueryParam.getIsClient().intValue() == StaticValue.ONE) {
            String customerId = customerService.getCurrentCustomerId();
            if (StringUtils.isBlank(customerId)) {
                return RestResponse.ok(null);
            } else {
                customerEngineerQueryParam.setCustomerId(customerId);
            }
        }

        return RestResponse.ok(customerEngineerService.pageDto(customerEngineerQueryParam));
    }

    /**
     * 查询客户名下的工程师平台使用信息
     */
    @ApiOperation("查询客户名下的工程师平台使用信息")
    @PostMapping("/getEngineerList")
    public RestResponse<List<CustomerEngineerDto>> getEngineerList(@RequestBody CustomerEngineerDto customerEngineerDto) {
        CustomerEngineerQueryParam customerEngineerQueryParam = new CustomerEngineerQueryParam();
        customerEngineerQueryParam.setIsClient(StaticValue.ONE);
        customerEngineerQueryParam.setLimit(1000);
        customerEngineerQueryParam.setCustomerId(customerEngineerDto.getCustomerId());

        List<CustomerEngineerDto> engineerlist = customerEngineerService.pageDto(customerEngineerQueryParam).getRecords();

        //查询登录次数和解决问题数
        for (CustomerEngineerDto dto : engineerlist) {
            UserAnalysisQueryParam userAnalysisQueryParam = new UserAnalysisQueryParam();
            userAnalysisQueryParam.setCustomerId(customerEngineerDto.getCustomerId());
            userAnalysisQueryParam.setStartDate(customerEngineerDto.getStartDate());
            userAnalysisQueryParam.setEndDate(customerEngineerDto.getEndDate());
            userAnalysisQueryParam.setUid(dto.getUid());
            userAnalysisQueryParam.setLimit(1000);

            //查询登录总次数，解决问题总次数
            List<UserAnalysisDto> userlist = userAnalysisService.pageDto(userAnalysisQueryParam).getRecords();
            if (null != userlist && userlist.size() > 0) {
                dto.setLoginCount(userlist.get(0).getLoginCount());
                dto.setSolveCount(userlist.get(0).getSolveCount());
                dto.setUserName(userlist.get(0).getUserName());
                dto.setUserMobile(userlist.get(0).getPhone());

                //按工程师，按月查询登录次数
                List<LoginCountDto> loginCountDtoList = userAnalysisService.getMonthLoginCountMapByUid(userAnalysisQueryParam);
                Map<String, Integer> loginCountMap = null;
                List<Map<String, Integer>> dataList = new ArrayList<>();

                Calendar c1 = Calendar.getInstance();
                Calendar c2 = Calendar.getInstance();
                c1.setTime(customerEngineerDto.getStartDate());
                c2.setTime(customerEngineerDto.getEndDate());

                while (c1.compareTo(c2) <= 0) {
                    loginCountMap = new LinkedHashMap<>();
                    if ((c1.get(Calendar.MONTH) + 1) < 10) {
                        loginCountMap.put(c1.get(Calendar.YEAR) + "-0" + (c1.get(Calendar.MONTH) + 1), 0);
                    } else {
                        loginCountMap.put(c1.get(Calendar.YEAR) + "-" + (c1.get(Calendar.MONTH) + 1), 0);
                    }
                    dataList.add(loginCountMap);
                    c1.add(Calendar.MONTH, 1);
                }

                for (int j = 0; j < dataList.size(); j++) {
                    Map<String, Integer> dataMap = dataList.get(j);
                    //获取迭代器
                    Set<Map.Entry<String, Integer>> set = dataMap.entrySet();
                    Iterator iterator = (Iterator) set.iterator();
                    Map.Entry entry = null;
                    while (iterator.hasNext()) {
                        entry = (Map.Entry) iterator.next();
                        for (LoginCountDto loginDto : loginCountDtoList) {
                            if (loginDto.getCreateTime().equals(entry.getKey())) {
                                dataMap.put(loginDto.getCreateTime(), loginDto.getLoginCount());
                            }
                        }
                    }
                }

                dto.setDataList(dataList);
            } else {
                dto.setLoginCount(0);
                dto.setSolveCount(0);
                dto.setDataList(new ArrayList<>());
            }
        }

        return RestResponse.ok(engineerlist);
    }

    /**
     * 新增工程师
     */
    @ApiOperation("新增工程师")
    @AuditLog(title = "工程师", desc = "新增工程师", businessType = BusinessType.INSERT)
    @PostMapping("/addEngineer")
    //@Permission("customer:update")
    public RestResponse<Boolean> addEngineer(@RequestBody @Valid CustomerEngineerAddParam customerEngineerAddParam) {
        //客户平台需要过滤
        if (null != customerEngineerAddParam && customerEngineerAddParam.getIsClient().intValue() == StaticValue.ONE) {
            String customerId = customerService.getCurrentCustomerId();
            if (StringUtils.isBlank(customerId)) {
                return RestResponse.ok(null);
            } else {
                customerEngineerAddParam.setCustomerId(customerId);
            }

        }
        return RestResponse.ok(customerEngineerService.saveByParam(customerEngineerAddParam));
    }

    /**
     * 修改工程师
     */
    @ApiOperation(value = "修改工程师")
    @AuditLog(title = "工程师", desc = "修改工程师", businessType = BusinessType.UPDATE)
    @PutMapping("updateEngineer")
    //@Permission("customer:update")
    public RestResponse<Boolean> updateEngineer(@RequestBody @Valid CustomerEngineerEditParam customerEngineerEditParam) {
        return RestResponse.ok(customerEngineerService.updateByParam(customerEngineerEditParam));
    }

    /**
     * 根据id禁用/启用工程师
     */
    @ApiOperation(value = "根据id禁用/启用工程师")
    @AuditLog(title = "客户", desc = "客户", businessType = BusinessType.UPDATE)
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "工程师id", dataType = "String", required = true),
            @ApiImplicitParam(name = "status", value = "0禁用1启用", dataType = "int", required = true)})*/
    @GetMapping("/updateEngineerStatus")
    //@Permission("customer:delete")
    public RestResponse<Boolean> updateEngineerStatus(@RequestParam("id") @NotEmpty String id, @RequestParam("status") Integer status) {
        return RestResponse.ok(customerEngineerService.updateStatus(id, status));
    }

    /**
     * 根据id获取工程师
     */
    @ApiOperation(value = "根据id获取工程师")
    @GetMapping(value = "/getEngineer")
    //@Permission("customer:list")
    public RestResponse<CustomerEngineerDto> getEngineer(@RequestParam("id") String id) {
        return RestResponse.ok(customerEngineerService.getDtoById(id));
    }

    /**
     * 分页获取设备列表
     */
    @ApiOperation("分页获取设备列表")
    @GetMapping("/equipmentList")
    //@Permission("customer:list")
    public RestResponse<PageResult<CustomerEquipmentDto>> equipmentList(@Valid CustomerEquipmentQueryParam customerEquipmentQueryParam) {
        return RestResponse.ok(customerEquipmentService.pageDto(customerEquipmentQueryParam));
    }

    /**
     * 分配设备
     */
    @ApiOperation("分配设备")
    @AuditLog(title = "设备", desc = "分配设备", businessType = BusinessType.INSERT)
    @PostMapping("/addEquipment")
    //@Permission("customer:update")
    public RestResponse<Boolean> addEquipment(@RequestBody @Valid CustomerEquipmentAddParam customerEquipmentAddParam) {
        return RestResponse.ok(customerEquipmentService.saveByParam(customerEquipmentAddParam));
    }

    /**
     * 校验设备是否被客户使用
     */
    @ApiOperation(value = "校验设备是否被客户使用")
    @AuditLog(title = "客户设备", desc = "校验设备是否被客户使用", businessType = BusinessType.DELETE)
    @GetMapping("/checkInfoUsed")
    public RestResponse<Boolean> checkInfoUsed(@RequestParam @NotBlank String equipmentId) {
        return RestResponse.ok(customerEquipmentService.checkInfoUsed(equipmentId));
    }

    /**
     * 取消分配设备
     */
    @ApiOperation(value = "取消分配设备")
    @AuditLog(title = "设备", desc = "取消分配设备", businessType = BusinessType.DELETE)
    @GetMapping("/deleteEquipment")
    //@Permission("customer:delete")
    public RestResponse<Boolean> deleteEquipment(@RequestParam @NotEmpty String customerId, @RequestParam @NotEmpty String equipmentId) {
        return RestResponse.ok(customerEquipmentService.deleteByCustomerId(customerId, equipmentId));
    }

    /**
     * 导入添加客户设备关联表
     */
    @ApiOperation("导入添加客户设备关联表")
    @AuditLog(title = "客户", desc = "导入添加客户设备关联表", businessType = BusinessType.INSERT)
    @PostMapping("/addBatchRel")
    RestResponse<Boolean> addBatchRel(@RequestBody Map<String, List<String>> customerInfoMap) {
        return RestResponse.ok(customerEquipmentService.addBatchRel(customerInfoMap));
    }

    /**
     * 获取当前登录人的用户id
     */
    @ApiOperation(value = "获取当前登录人的用户id")
    @AuditLog(title = "客户", desc = "获取当前登录人的用户id", businessType = BusinessType.QUERY)
    @GetMapping("/getCurrentCustomerId")
    public RestResponse<String> getCurrentCustomerId() {
        return RestResponse.ok(customerService.getCurrentCustomerId());
    }

    /**
     * 获取当前登录人的用户姓名
     */
    @ApiOperation(value = "获取当前登录人的用户")
    @AuditLog(title = "客户", desc = "获取当前登录人的用户姓名", businessType = BusinessType.QUERY)
    @GetMapping("/getCurrentCustomerName")
    public RestResponse<String> getCurrentCustomerName() {
        return RestResponse.ok(customerService.getCurrentCustomerName());
    }

    /**
     * 获取当前登录人的设备id集合
     */
    @ApiOperation(value = "获取当前登录人的设备id集合")
    @AuditLog(title = "客户", desc = "获取当前登录人的设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getCurrentInfoIds")
    public RestResponse<List<String>> getCurrentInfoIds() {
        return RestResponse.ok(customerService.getCurrentInfoIds());
    }

    /**
     * 根据客户id获取的设备id集合
     */
    @ApiOperation(value = "根据客户id获取的设备id集合")
    @AuditLog(title = "客户", desc = "根据客户id获取的设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getInfoIdsByCustomerId")
    public RestResponse<List<String>> getInfoIdsByCustomerId(@RequestParam("customerId") String customerId) {
        return RestResponse.ok(customerService.getInfoIdsByCustomerId(customerId));
    }

    /**
     * 根据客户名称获取的设备id集合
     */
    @ApiOperation(value = "根据客户名称获取的设备id集合")
    @AuditLog(title = "客户", desc = "根据客户名称获取的设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getInfoIdsByCustomerName")
    public RestResponse<List<String>> getInfoIdsByCustomerName(@RequestParam("customerName") String customerName) {
        return RestResponse.ok(customerService.getCurrentInfoIdsByCustomerName(customerName));
    }

    /**
     * 根据两个客户名称获取设备id集合
     */
    @ApiOperation(value = "根据两个客户名称获取设备id集合")
    @AuditLog(title = "客户", desc = "根据两个客户名称获取设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getEquipmentIdsByTwoCustomerNames")
    public RestResponse<List<String>> getEquipmentIdsByTwoCustomerNames(@RequestParam("customerName1") String customerName1, @RequestParam("customerName2") String customerName2) {
        return RestResponse.ok(customerService.getEquipmentIdsByTwoCustomerNames(customerName1, customerName2));
    }

    /**
     * 根据客户名称列表获取设备id集合
     */
    @ApiOperation(value = "根据客户名称列表获取设备id集合")
    @AuditLog(title = "客户", desc = "根据客户名称列表获取设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getEquipmentIdsByCustomerNames")
    public RestResponse<List<String>> getEquipmentIdsByCustomerNames(@RequestParam("customerNames") String customerNames) {
        return RestResponse.ok(customerService.getEquipmentIdsByCustomerNames(customerNames));
    }

    /**
     * 获取已分配设备id集合
     */
    @ApiOperation(value = "获取已分配设备id集合")
    @AuditLog(title = "客户", desc = "获取已分配设备id集合", businessType = BusinessType.QUERY)
    @GetMapping("/getUsedInfoIds")
    public RestResponse<List<String>> getUsedInfoIds() {
        return RestResponse.ok(customerEquipmentService.getUsedInfoIds());
    }

    /**
     * 获取当前登录用户工程师数量
     */
    @ApiOperation("获取当前登录用户工程师数量")
    @GetMapping("/engineerCount")
    //@Permission("maint:plan:list")
    public RestResponse<Integer> engineerCount() {
        return RestResponse.ok(customerService.engineerCount());
    }

    /**
     * 根据设备id获取的客户
     */
    @ApiOperation(value = "根据设备id获取的客户")
    @AuditLog(title = "客户", desc = "根据设备id获取的客户", businessType = BusinessType.QUERY)
    @GetMapping("/getCustomerIdByInfoId")
    public RestResponse<CustomerDto> getCustomerIdByInfoId(@RequestParam("equipmentId") String equipmentId) {
        return RestResponse.ok(customerEquipmentService.getCustomerIdByInfoId(equipmentId));
    }

    /**
     * 根据id获取大屏客户
     */
    @ApiOperation(value = "根据id获取大屏客户")
    @GetMapping(value = "/getScreenDto")
    //@Permission("customer:list")
    public RestResponse<CustomerDto> getScreenDto(@RequestParam("customerId") String customerId) {
        return RestResponse.ok(customerService.getScreenDto(customerId));
    }

    /**
     * 根据客户名称搜索设备列表
     */
    @ApiOperation(value = "根据客户名称搜索设备列表")
    @AuditLog(title = "搜索设备id列表", desc = "根据客户名称搜索设备id列表", businessType = BusinessType.QUERY)
    @GetMapping("/searchEquipmentListByUserName")
    public RestResponse<List<String>> searchEquipmentListByUserName(@RequestParam("name") String name) {
        return RestResponse.ok(customerEquipmentService.searchEquipmentListByUserName(name));
    }

    /**
     * 根据客户名称搜索销售人员
     */
    @ApiOperation(value = "根据客户名称搜索销售人员")
    @AuditLog(title = "销售人员", desc = "根据客户名称搜索销售人员", businessType = BusinessType.QUERY)
    @GetMapping("/listSalesman")
    public RestResponse<List<String>> listSalesman(@RequestParam("customerId") String customerId) {
        return RestResponse.ok(customerSalesmanService.listSalesman(customerId));
    }

    /**
     * 根据客户编号获取客户信息
     */
    @ApiOperation(value = "根据客户编号获取客户信息")
    @AuditLog(title = "根据客户编号获取客户信息", desc = "根据客户编号获取客户信息", businessType = BusinessType.QUERY)
    @GetMapping("/getCustomer/{code}")
    public RestResponse<CustomerDto> getCustomerByCode(@PathVariable("code") String code) {
        return RestResponse.ok(customerService.getCustomerByCode(code));
    }

    /**
     * 根据id获取客户组态信息
     */
    @ApiOperation(value = "根据id获取客户组态信息")
    @GetMapping(value = "/getStudioByCustomerId")
    //@Permission("equipment:list")
    public RestResponse<CustomerStudioDto> getStudioByCustomerId(@RequestParam(required = false) String id) {
        //如果不传客户id，取当前登录用户客户id
        if (StringUtils.isBlank(id)) {
            id = customerService.getCurrentCustomerId();
        }
        return RestResponse.ok(customerService.getStudioByCustomerId(id));
    }

    /**
     * 编辑客户组态
     */
    @ApiOperation("编辑客户组态")
    @PostMapping("/updateCustomerStudio")
    @AuditLog(title = "客户", desc = "编辑客户组态", businessType = BusinessType.UPDATE)
    public RestResponse<Boolean> updateCustomerStudio(@RequestBody StudioEditParam studioEditParam) {
        return RestResponse.ok(customerService.updateCustomerStudio(studioEditParam));
    }

    /**
     * 校验设备 是否被客户使用
     */
    @ApiOperation("校验设备 是否被客户使用")
    @PostMapping("/checkEquipmentUsed")
    @AuditLog(title = "校验设备", desc = "校验设备 是否被客户使用", businessType = BusinessType.QUERY)
    public RestResponse<List<String>> checkEquipmentUsed(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(customerService.checkEquipmentUsed(equipmentIds));
    }

    /**
     * 根据用户名进行排序
     */
    @ApiOperation("根据用户名进行排序")
    @AuditLog(title = "根据用户名进行排序", desc = "根据用户名进行排序", businessType = BusinessType.QUERY)
    @PostMapping("/orderByCustomerName")
    public RestResponse<PageResult<EquipmentCustomerDto>> orderByCustomerName(@RequestBody CustomerNameOrderParam orderParam) {
        return RestResponse.ok(customerService.orderByCustomerName(orderParam));
    }
}
