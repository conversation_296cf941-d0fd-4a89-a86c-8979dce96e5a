package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.enums.KnowledgeType;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.customer.LoginCountDto;
import cn.getech.ehm.system.dto.useranalysis.*;
import cn.getech.ehm.system.entity.UserAnalysis;
import cn.getech.ehm.system.mapper.UserAnalysisMapper;
import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.ehm.system.service.IUserActionService;
import cn.getech.ehm.system.service.IUserAnalysisService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.CustomerDto;
import cn.getech.ehm.system.dto.UpdateFavoritesParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <pre>
 * 用户分析信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Slf4j
@Service
public class UserAnalysisServiceImpl extends BaseServiceImpl<UserAnalysisMapper, UserAnalysis> implements IUserAnalysisService {

    @Autowired
    private UserAnalysisParamMapper userAnalysisParamMapper;
    @Autowired
    private UserAnalysisMapper userAnalysisMapper;

    @Autowired
    private ICustomerService customerService;
    @Autowired
    private IUserActionService userActionService;

    @Override
    public PageResult<UserAnalysisDto> pageDto(UserAnalysisQueryParam queryParam) {
        Page pageParam = new Page(queryParam.getPageNo(), queryParam.getLimit());
        Page<UserAnalysisDto> result = userAnalysisMapper.fetchUserAnalysisList(pageParam, queryParam);
        for (UserAnalysisDto userAnalysisDto : result.getRecords()) {
            if (null == userAnalysisDto.getLoginCount()) {
                userAnalysisDto.setLoginCount(StaticValue.ZERO);
                userAnalysisDto.setSolveCount(StaticValue.ZERO);
            }
        }
        return null != result ? new PageResult(result.getRecords(), result.getTotal()) : new PageResult();
    }


    @Override
    public boolean updateFavorites(UpdateFavoritesParam addParam) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        LambdaQueryWrapper<UserAnalysis> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAnalysis::getUid, currentUser.getUid());
        UserAnalysis userAnalysis = (UserAnalysis) this.baseMapper.selectOne(wrapper);
        if (null == userAnalysis) {
            return saveByParam(initUserAnalysisAddParam(addParam, currentUser.getUid()));
        } else {

        }
        initUserAnalysisFavorites(addParam, userAnalysis);
        return updateById(userAnalysis);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UserAnalysisAddParam addParam) {
        UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        log.info("currentUser: {}", currentUser);
        if (StringUtils.isBlank(addParam.getUid())) {
            addParam.setUid(currentUser.getUid());
        }
        LambdaQueryWrapper<UserAnalysis> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAnalysis::getUid, addParam.getUid());
        UserAnalysis analysis = (UserAnalysis) this.baseMapper.selectOne(wrapper);
        CustomerDto customerDto = customerService.getDtoByUid(addParam.getUid());
        if (null == customerDto) {
            return false;
        }
        if (null != analysis) {
            initUserAnalysis(analysis, customerDto, currentUser);
            return updateById(analysis);
        }

        UserAnalysis userAnalysis = userAnalysisParamMapper.addParam2Entity(addParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, userAnalysis);
        initUserAnalysis(userAnalysis, customerDto, currentUser);
        return save(userAnalysis);
    }

    private void initUserAnalysis(UserAnalysis userAnalysis, CustomerDto customerDto, UserBaseInfo currentUser) {
        userAnalysis.setUserName(currentUser.getName());
        userAnalysis.setPhone(currentUser.getMobile());
        userAnalysis.setCustomerId(customerDto.getId());
        userAnalysis.setCustomerName(customerDto.getName());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UserAnalysisEditParam userAnalysisEditParam) {
        UserAnalysis userAnalysis = userAnalysisParamMapper.editParam2Entity(userAnalysisEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, userAnalysis);
        return updateById(userAnalysis);
    }

    @Override
    public List<LoginCountDto> getMonthLoginCountMapByUid(UserAnalysisQueryParam queryParam) {
        return userAnalysisMapper.getMonthLoginCountMapByUid(queryParam);
    }

    @Override
    public UserAnalysisDto getDtoById(Long id) {
        return userAnalysisParamMapper.entity2Dto((UserAnalysis) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UserAnalysisDto> rows) {
        return saveBatch(userAnalysisParamMapper.dtoList2Entity(rows));
    }

    private UserAnalysisAddParam initUserAnalysisAddParam(UpdateFavoritesParam addParam, String uid) {
        UserAnalysisAddParam userAnalysisAddParam = new UserAnalysisAddParam();
        userAnalysisAddParam.setUid(uid);
        if (KnowledgeType.COURSE.getCode() == addParam.getKnowledgeType()) {
            userAnalysisAddParam.setCourseFavorites(addParam.getFavorites());
        } else if (KnowledgeType.NEWS.getCode() == addParam.getKnowledgeType()) {
            userAnalysisAddParam.setNewsFavorites(addParam.getFavorites());
        } else if (KnowledgeType.DOC.getCode() == addParam.getKnowledgeType()) {
            userAnalysisAddParam.setDocFavorites(addParam.getFavorites());
        }
        return userAnalysisAddParam;
    }

    private void initUserAnalysisFavorites(UpdateFavoritesParam addParam, UserAnalysis userAnalysis) {
        if (KnowledgeType.COURSE.getCode() == addParam.getKnowledgeType()) {
            userAnalysis.setCourseFavorites(addParam.getFavorites());
        } else if (KnowledgeType.NEWS.getCode() == addParam.getKnowledgeType()) {
            userAnalysis.setNewsFavorites(addParam.getFavorites());
        } else if (KnowledgeType.DOC.getCode() == addParam.getKnowledgeType()) {
            userAnalysis.setDocFavorites(addParam.getFavorites());
        }
    }

    private Wrapper<UserAnalysis> getPageSearchWrapper(UserAnalysisQueryParam queryParam) {
        LambdaQueryWrapper<UserAnalysis> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getKeyword()), UserAnalysis::getUserName, queryParam.getKeyword());
        wrapper.like(StringUtils.isNotBlank(queryParam.getKeyword()), UserAnalysis::getPhone, queryParam.getKeyword());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getCustomerId()), UserAnalysis::getCustomerId, queryParam.getCustomerId());
        if (BaseEntity.class.isAssignableFrom(UserAnalysis.class)) {
            wrapper.orderByDesc(UserAnalysis::getUpdateTime, UserAnalysis::getCreateTime);
        }
        return wrapper;
    }
}
