package cn.getech.ehm.system.websocket;

import cn.getech.ehm.system.dto.WebSocketData;
import com.alibaba.fastjson.JSON;
import javax.websocket.EncodeException;
import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

public class WebSocketMessageEncoder implements Encoder.Text<WebSocketData> {

    @Override
    public String encode(WebSocketData message) throws EncodeException {
        return JSON.toJSONString(message);
    }

    @Override
    public void init(EndpointConfig ec) {
        //System.out.println("WebSocketMessageEncoder - init method called");
    }

    @Override
    public void destroy() {
        //System.out.println("WebSocketMessageEncoder - destroy method called");
    }

}
