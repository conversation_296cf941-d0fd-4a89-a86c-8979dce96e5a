package cn.getech.ehm.system.controller;


import cn.getech.ehm.system.service.ISystemConfigService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import cn.getech.ehm.system.dto.SystemConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 系统配置信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@RestController
@RequestMapping("/systemConfig")
@Api(tags = "系统配置信息服务接口")
public class SystemConfigController {

    @Autowired
    private ISystemConfigService systemConfigService;

    /**
     * 根据配置名称获取系统配置信息列表
     */
    @ApiOperation(value = "根据配置名称获取系统配置信息列表")
    @GetMapping(value = "list/{name}")
    //@Permission("system:config:list")
    public RestResponse<PageResult<SystemConfigDto>> list(@PathVariable String name) {
        return RestResponse.ok(systemConfigService.listByName(name));
    }

    /**
     * 根据多个名称获取系统配置信息
     */
    @ApiOperation(value = "根据多个名称获取系统配置信息")
    @GetMapping(value = "map/{names}")
    //@Permission("system:config:list")
    public RestResponse<Map<String, SystemConfigDto>> map(@PathVariable String names) {
        return RestResponse.ok(systemConfigService.mapByNames(names));
    }

}
