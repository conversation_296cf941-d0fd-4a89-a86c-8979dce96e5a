package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.DataTypeDto;
import cn.getech.ehm.system.dto.appconfig.AppConfigDto;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * app配置信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Service
public interface IAppConfigService {

    /**
     * 获取轮播图配置和轻应用配置
     *
     * @return
     */
    AppConfigDto getAppConfig();

    /**
     * 获取数据类型配置
     *
     * @return
     */
    DataTypeDto dataTypeList();

}
