package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.entity.SystemLearningConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.getech.ehm.system.dto.LearningConfigPersonCountDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 培训认证表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public interface SystemLearningConfigMapper extends BaseMapper<SystemLearningConfig> {


    /**
     * @param startTime
     * @param endTime
     * @return
     */
    LearningConfigPersonCountDto sumOnlinePersonCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
