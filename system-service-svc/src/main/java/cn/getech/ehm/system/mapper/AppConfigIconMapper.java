package cn.getech.ehm.system.mapper;

import cn.getech.ehm.system.dto.appconfig.AppIconDto;
import cn.getech.ehm.system.entity.AppConfigIcon;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
public interface AppConfigIconMapper extends BaseMapper<AppConfigIcon> {

    List<AppIconDto> fetchAppConfigIconList();

    Integer getMaxSortNumber();

    /**
     * 获取geek下所有数据
     * @return
     */
    @SqlParser(filter = true)
    List<AppConfigIcon> getAllData();

    /**
     * 插入数据
     * @param entities
     * @return
     */
    @SqlParser(filter = true)
    Boolean insertField(@Param("entities") List<AppConfigIcon> entities);
}
