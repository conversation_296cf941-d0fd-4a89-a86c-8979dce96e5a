package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.service.impl.WxService;
import cn.getech.poros.framework.common.api.RestResponse;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/wx/login/{corpId}/{agentId}")
public class WxController {
    private final WxService wxService;

//    @GetMapping("/userId")
//    public RestResponse<String> getUserIdWithCode(@PathVariable String corpId,
//                                                  @PathVariable Integer agentId,
//                                                  @RequestParam String code) {
//        return RestResponse.ok(wxService.getUserIdByCode(corpId, agentId, code));
//    }

    @PostMapping("/userId")
    public RestResponse<String> getUserIdWithCodePost(@PathVariable String corpId,
                                                      @PathVariable Integer agentId,
                                                      @RequestBody JSONObject request) {
        String code = request.getString("code");
        return RestResponse.ok(wxService.getUserIdByCode(corpId, agentId, code));
    }
}
