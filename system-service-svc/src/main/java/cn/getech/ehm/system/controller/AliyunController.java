package cn.getech.ehm.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

/**
 * <PERSON>yun Controller
 *
 * <AUTHOR>
 * @date 2021-01-13 10:40:09
 **/
@Slf4j
@Controller
@RequestMapping("/aliyun")
public class AliyunController {

    @Value("${aliyun.iot.studio.url:http://iot.aliyun.com/studio}")
    private String url;

    @Value("${aliyun.iot.studio.token:123456}")
    private String token;

    private static String byte2Base64(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    /**
     * mars授权跳转
     *
     * @return
     */
    @GetMapping("/iot/redirect")
    public String marsOauthLogin() {
        String redirectUrl = getSignedUrl(token);
        return "redirect:" + redirectUrl;
    }

    private String getSignedUrl(String token) {
        Date date = new Date();
        Long time = date.getTime();
        String stringToSign = String.valueOf(time);
        String signature = HMACSHA256(stringToSign.getBytes(), token.getBytes());
        String SignedUrl = url + "?time=" + time + "&signature=" + signature;
        return SignedUrl;
    }

    /**
     * 利用Java原生的摘要实现SHA256加密
     *
     * @param data 加密后的报文
     * @param key
     * @return
     */
    private String HMACSHA256(byte[] data, byte[] key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            return URLEncoder.encode(byte2Base64(mac.doFinal(data)));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }
        return null;
    }

}
