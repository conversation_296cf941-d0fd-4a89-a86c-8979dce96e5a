package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.audit.*;
import cn.getech.ehm.system.entity.CustomerAudit;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 审核用户 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICustomerAuditService extends IBaseService<CustomerAudit> {

    /**
     * 分页查询，返回Dto
     *
     * @param customerAuditQueryParam
     * @return
     */
    PageResult<CustomerAuditDto> pageDto(CustomerAuditQueryParam customerAuditQueryParam);

    /**
     * 获取已审核的用户
     *
     * @param customerAuditQueryParam
     * @return
     */
    List<CustomerAuditDto> getList(CustomerAuditQueryParam customerAuditQueryParam);

    /**
     * 获取审核用户
     *
     * @return
     */
    List<CustomerAuditExcel> getExportList();

    /**
     * 保存
     *
     * @param customerAuditAddParam
     * @return
     */
    RestResponse<Boolean> saveByParam(CustomerAuditAddParam customerAuditAddParam);

    /**
     * 审核
     *
     * @param customerAuditEditParam
     */
    RestResponse<Boolean> updateByParam(CustomerAuditEditParam customerAuditEditParam);

    /**
     * 判断当前用户是否审核用户
     *
     * @return
     */
    Boolean checkIsAudit();

    /**
     * 判断当前用户是否是游客
     *
     * @return true-是，false-不是
     */
    Boolean checkIsVisitor();

}