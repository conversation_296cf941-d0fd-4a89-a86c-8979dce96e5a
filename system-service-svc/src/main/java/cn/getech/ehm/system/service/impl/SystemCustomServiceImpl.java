package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.system.dto.tbea.*;
import cn.getech.ehm.system.service.TbeaNotifyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class SystemCustomServiceImpl {
    @Autowired
    TbeaNotifyService notifyService;
    @Autowired
    RedisTemplate redisTemplate;
    private final static String tokenKey = "TBEA:ACCESSTOKEN";

    @Value("${tbea.notify.appId:500000276}")
    public String appId;
    @Value("${tbea.notify.secret:P0bt80EF62boPGIjzAeE}")
    public String secret;
    @Value("${tbea.notify.eId:2020042301}")
    public String eId;


    private String getToken() {
        String token;
        TokenRequestDto requestDto = TokenRequestDto.builder()
                .appId(appId)
                .secret(secret)
                .timestamp(new Date().getTime())
                .scope("app")
                .build();
        NotifyResDto<TokenResDto> resDto = notifyService.getAccessToken(requestDto);
        if (resDto != null && resDto.getSuccess() && resDto.getData() != null && resDto.getData().getAccessToken() != null) {
            token = resDto.getData().getAccessToken();
            //redisTemplate.opsForValue().set(tokenKey, resDto.getData().getAccessToken(), resDto.getData().getExpireIn());
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("获取token失败" + JSON.toJSONString(resDto)));
        }
        return token;
    }

    public void generateTodo(String title, String content, String[] uids, String url, String serviceId) {
        //this.markTodo(serviceId);
        List<GenerateTodoDetailDto> detailDtoList = Lists.newArrayList();
        for (String uid : uids) {
            detailDtoList.add(GenerateTodoDetailDto.builder().jobNo(uid).build());
        }
        GenerateTodoDto generatetodoDto = GenerateTodoDto.builder()
                .content(content)
                .title(title)
                .itemtitle(title)
                .headImg("http://ejia.tbea.com/space/c/photo/load?id=5a2f7ad750f8dd7810e79981")
                .eid(eId)
                .appId(appId)
                .url(url)
                .sourceId(serviceId)
                .todoType("0")
                .params(detailDtoList)
                .build();
        NotifyResDto<String> resDto = notifyService.generateTodo(this.getToken(), generatetodoDto);
    }

    public void markTodo(String serviceId) {
        MarkTodoRequestDto.ActiontypeDTO actiontypeDTO = new MarkTodoRequestDto.ActiontypeDTO();
        actiontypeDTO.setDeal(1);
        actiontypeDTO.setRead(1);
        MarkTodoRequestDto build = MarkTodoRequestDto.builder()
                .sourcetype(appId)
                .sourceitemid(serviceId)
                .eid(eId)
                .actiontype(actiontypeDTO)
                .build();
        NotifyResDto notifyResDto = notifyService.markTodo(this.getToken(), build);
    }

}
