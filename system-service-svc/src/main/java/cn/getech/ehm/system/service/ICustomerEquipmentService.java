package cn.getech.ehm.system.service;

import cn.getech.ehm.system.dto.customer.CustomerEquipmentAddParam;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentDto;
import cn.getech.ehm.system.dto.customer.CustomerEquipmentQueryParam;
import cn.getech.ehm.system.entity.CustomerEquipment;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.system.dto.CustomerDto;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 客户设备 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
public interface ICustomerEquipmentService extends IBaseService<CustomerEquipment> {

    /**
     * 分页查询，返回Dto
     *
     * @param customerEquipmentQueryParam
     * @return
     */
    PageResult<CustomerEquipmentDto> pageDto(CustomerEquipmentQueryParam customerEquipmentQueryParam);

    /**
     * 保存
     *
     * @param customerEquipmentAddParam
     * @return
     */
    boolean saveByParam(CustomerEquipmentAddParam customerEquipmentAddParam);

    /**
     * 删除
     *
     * @param customerId
     * @param equipmentId
     * @return
     */
    Boolean deleteByCustomerId(String customerId, String equipmentId);

    /**
     * 导入添加客户设备关联表
     *
     * @param customerInfoMap
     * @return
     */
    Boolean addBatchRel(Map<String, List<String>> customerInfoMap);

    /**
     * 获取当前登录用户的设备id集合
     *
     * @return
     */
    List<String> getCurrentInfoIds(String customerId);

    /**
     * 根据设备名获取设备id集合
     *
     * @return
     */
    List<String> getCurrentInfoIdsByCustomerName(String customerName);

    /**
     * 根据设备名列表获取设备id集合
     *
     * @param customerNames
     * @return
     */
    List<String> getEquipmentIdsByCustomerNames(String customerNames);

    /**
     * 获取已分配的设备id集合
     *
     * @return
     */
    List<String> getUsedInfoIds();

    /**
     * 校验设备是否被客户使用
     *
     * @param equipmentId
     * @return
     */
    Boolean checkInfoUsed(String equipmentId);

    /**
     * 根据设备id获取客户id
     *
     * @param equipmentId
     * @return
     */
    CustomerDto getCustomerIdByInfoId(String equipmentId);

    /**
     * 根据客户名称搜索设备id列表
     *
     * @param name
     * @return
     */
    List<String> searchEquipmentListByUserName(String name);

    /**
     * 根据客户Ids查询所有设备Ids
     *
     * @param customerIds
     * @return
     */
    List<CustomerEquipmentDto> listByCustomerIds(List<String> customerIds);

    /**
     * 根据两个客户名称获取的设备id集合
     *
     * @param customerName1
     * @param customerName2
     * @return
     */
    List<String> getEquipmentIdsByTwoCustomerNames(String customerName1, String customerName2);

    /**
     * 校验设备 是否有客户使用
     *
     * @param equipmentIds
     * @return
     */
    String[] checkCustomerEquipmentUsed(String[] equipmentIds);
}