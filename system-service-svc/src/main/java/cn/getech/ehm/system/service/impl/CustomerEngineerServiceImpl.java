package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.client.PorosPermissionClient;
import cn.getech.ehm.system.dto.customer.CustomerEngineerAddParam;
import cn.getech.ehm.system.dto.customer.CustomerEngineerEditParam;
import cn.getech.ehm.system.dto.customer.CustomerEngineerParamMapper;
import cn.getech.ehm.system.dto.customer.CustomerEngineerQueryParam;
import cn.getech.ehm.system.dto.poros.secstaff.KjsMetaData;
import cn.getech.ehm.system.dto.poros.secstaff.SecStaffRemark;
import cn.getech.ehm.system.entity.CustomerEngineer;
import cn.getech.ehm.system.mapper.CustomerEngineerMapper;
import cn.getech.ehm.system.service.ICustomerEngineerService;
import cn.getech.ehm.system.service.ICustomerService;
import cn.getech.ehm.system.utils.IotUtils;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.system.dto.AreaEditDto;
import cn.getech.ehm.system.dto.CustomerDto;
import cn.getech.ehm.system.dto.CustomerEngineerDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 * 客户工程师 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Slf4j
@Service
public class CustomerEngineerServiceImpl extends BaseServiceImpl<CustomerEngineerMapper, CustomerEngineer> implements ICustomerEngineerService {
    @Value("${fixed.role.engineer.code:web-consumer-center_engineer}")
    private String engineerRoleCode;

    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private PorosSecGrantClient porosSecGrantClient;
    @Autowired
    private PorosPermissionClient porosPermissionClient;
    @Autowired
    private CustomerEngineerParamMapper customerEngineerParamMapper;
    @Autowired
    private CustomerEngineerMapper customerEngineerMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Override
    public PageResult<CustomerEngineerDto> pageDto(CustomerEngineerQueryParam customerEngineerQueryParam) {
        if(StringUtils.isNotBlank(customerEngineerQueryParam.getKeyword()) || StringUtils.isNotBlank(customerEngineerQueryParam.getMobile()) || StringUtils.isNotBlank(customerEngineerQueryParam.getEmail())) {
            RestResponse<PageResult<PorosSecStaffDto>> restResponse = porosPermissionClient.getStaffList(customerEngineerQueryParam.getKeyword(), customerEngineerQueryParam.getEmail(), customerEngineerQueryParam.getMobile(), 1, 1000);
            if(restResponse.isOk()){
                List<PorosSecStaffDto> secStaffDtos = restResponse.getData().getRecords();
                if(CollectionUtils.isEmpty(secStaffDtos)){
                    return new PageResult<>();
                }
                customerEngineerQueryParam.setUids(secStaffDtos.stream().map(PorosSecStaffDto::getUid).distinct().collect(Collectors.toList()));
            }else{
                log.error("连接中台查询用户失败");
            }
        }
        Wrapper<CustomerEngineer> wrapper = getPageSearchWrapper(customerEngineerQueryParam);
        PageResult<CustomerEngineerDto> result = customerEngineerParamMapper.pageEntity2Dto(page(customerEngineerQueryParam, wrapper));
        List<CustomerEngineerDto> customerEngineerDtos = result.getRecords();
        if (CollectionUtils.isNotEmpty(customerEngineerDtos)) {
            String[] uids = customerEngineerDtos.stream().map(CustomerEngineerDto::getUid).distinct().toArray(String[]::new);
            RestResponse<List<PorosSecStaffDto>> restResponse = porosSecStaffClient.getListByPost(uids);
            if (restResponse.isOk()) {
                List<PorosSecStaffDto> secStaffDtos = restResponse.getData();

                if (CollectionUtils.isNotEmpty(secStaffDtos)) {
                    Map<String, PorosSecStaffDto> map = new HashMap<>(secStaffDtos.size());
                    for (PorosSecStaffDto secStaffDto : secStaffDtos) {
                        if (!map.containsKey(secStaffDto.getUid())) {
                            map.put(secStaffDto.getUid(), secStaffDto);
                        }
                    }
                    for (CustomerEngineerDto customerEngineerDto : customerEngineerDtos) {
                        PorosSecStaffDto porosSecStaffDto = map.get(customerEngineerDto.getUid());
                        if (null != porosSecStaffDto) {
                            customerEngineerDto.setUserName(porosSecStaffDto.getName());
                            customerEngineerDto.setUserMobile(porosSecStaffDto.getMobile());
                            customerEngineerDto.setUserEmail(porosSecStaffDto.getEmail());
                        }
                    }
                }
                result.setRecords(customerEngineerDtos);
            } else {
                log.info("中台获取用户信息失败");
            }
        }

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveByParam(CustomerEngineerAddParam customerEngineerAddParam) {
        // 获取客户名称
        CustomerDto dto = customerService.getDtoById(customerEngineerAddParam.getCustomerId());

        if (null != customerEngineerAddParam.getIsAuditUser() && customerEngineerAddParam.getIsAuditUser() == StaticValue.ONE) {
            RoleEditCodeParam roleEditCodeParam = new RoleEditCodeParam();
            roleEditCodeParam.setUid(customerEngineerAddParam.getUid());
            List<String> roleCodes = new ArrayList<>();
            roleCodes.add(engineerRoleCode);
            roleEditCodeParam.setCode(roleCodes);
            log.info("中台修改用户角色入参->" + JSONObject.toJSON(roleEditCodeParam));
            RestResponse<Boolean> restResponse = porosSecGrantClient.updateStaffRole(roleEditCodeParam);
            if (!restResponse.isOk()) {
                log.error("中台修改用户角色失败");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
            }
            log.info("中台修改用户角色结果->" + restResponse);

            log.info("中台修改用户remark");
            //修改工程师用户的客户所属
            SecStaffBaseUpdateParam secStaffUpdateParam = new SecStaffBaseUpdateParam();
            secStaffUpdateParam.setUid(customerEngineerAddParam.getUid());
            // 增加客户名称到remark里
            KjsMetaData kjsMetaData = new KjsMetaData();
            kjsMetaData.setCompanyName(dto != null && StringUtils.isNotBlank(dto.getName().trim()) ? dto.getName() : "");
            SecStaffRemark secStaffRemark = new SecStaffRemark();
            secStaffRemark.setKjs(kjsMetaData);
            secStaffUpdateParam.setRemark(JSONObject.toJSONString(secStaffRemark));
            RestResponse<Boolean> remarkResponse = porosSecStaffClient.updateBaseByUid(secStaffUpdateParam);
            if (!remarkResponse.isOk()) {
                log.error("中台修改用户失败->" + JSONObject.toJSONString(secStaffRemark));
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
            }
            log.info("中台修改用户remark结果->" + remarkResponse);

            CustomerEngineer customerEngineer = new CustomerEngineer();
            customerEngineer.setCustomerId(customerEngineerAddParam.getCustomerId());
            customerEngineer.setUid(customerEngineerAddParam.getUid());
            customerEngineer.setStatus(StaticValue.ONE);
            buildIot(customerEngineerAddParam.getCustomerId(), customerEngineerAddParam.getUid(), null);

            return save(customerEngineer);
        } else {
            SecStaffAddParam secStaffAddParam = new SecStaffAddParam();
            secStaffAddParam.setName(customerEngineerAddParam.getUserName());
            secStaffAddParam.setUid(customerEngineerAddParam.getUid());
            secStaffAddParam.setMobile(customerEngineerAddParam.getUserMobile());
            secStaffAddParam.setEmail(customerEngineerAddParam.getUserEmail());

            // 增加客户名称到remark里
            KjsMetaData kjsMetaData = new KjsMetaData();
            kjsMetaData.setCompanyName(dto != null && StringUtils.isNotBlank(dto.getName().trim()) ? dto.getName() : "");
            SecStaffRemark secStaffRemark = new SecStaffRemark();
            secStaffRemark.setKjs(kjsMetaData);
            secStaffAddParam.setRemark(JSONObject.toJSONString(secStaffRemark));

            List<String> roleCodeList = new ArrayList<>();
            roleCodeList.add(engineerRoleCode);
            secStaffAddParam.setRoleCodeList(roleCodeList);
            //绑定固定用户 客户工程师
            RestResponse<Boolean> restResponse = porosSecStaffClient.add(secStaffAddParam);
            if (restResponse.isOk()) {
                CustomerEngineer customerEngineer = new CustomerEngineer();
                customerEngineer.setCustomerId(customerEngineerAddParam.getCustomerId());
                customerEngineer.setUid(customerEngineerAddParam.getUid());
                customerEngineer.setStatus(StaticValue.ONE);

                //同步新增iot用户
                addIotUser(customerEngineerAddParam);

                buildIot(customerEngineerAddParam.getCustomerId(), customerEngineerAddParam.getUid(), null);

                return save(customerEngineer);
            } else {
                log.error("中台新增失败->" + JSONObject.toJSONString(restResponse));
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
            }
        }
    }

    private void addIotUser(CustomerEngineerAddParam customerEngineerAddParam) {
        AreaEditDto areaEditDto = new AreaEditDto();
        areaEditDto.setUid(customerEngineerAddParam.getUid());
        areaEditDto.setRealName(customerEngineerAddParam.getUserName());
        areaEditDto.setPhone(customerEngineerAddParam.getUserMobile());
        areaEditDto.setEmail(customerEngineerAddParam.getUserEmail());
        //todo 后期多租户需要自定义租户id
        Boolean flag = IotUtils.addUser(areaEditDto);
        if (!flag) {
            log.info("iot新增用户信息失败");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void buildIot(String customerId, String newUid, String customerName) {
        //同步iot
        List<String> uids = new ArrayList<>();
        CustomerDto customerDto = customerService.getDtoById(customerId);
        uids.add(customerDto.getUid());
        if (StringUtils.isNotBlank(newUid)) {
            uids.add(newUid);
        }

        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getCustomerId, customerId);
        wrapper.select(CustomerEngineer::getId, CustomerEngineer::getUid);
        List<CustomerEngineer> customerEngineers = customerEngineerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customerEngineers)) {
            List<String> engineerUids = customerEngineers.stream().map(CustomerEngineer::getUid).distinct().collect(Collectors.toList());
            uids.addAll(engineerUids);
        }

        AreaEditDto areaEditDto = new AreaEditDto();
        areaEditDto.setAreaName(null != customerName ? customerName : customerDto.getName());
        areaEditDto.setDataAreaId(customerDto.getIotAreaId());
        String uidsStr = StringUtils.join(uids, StringPool.COMMA);
        areaEditDto.setUids(uidsStr);
        Boolean flag = IotUtils.editArea(areaEditDto);
        if (!flag) {
            log.info("iot修改用户信息失败");
        }

    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(CustomerEngineerEditParam customerEngineerEditParam) {
        CustomerDto dto = customerService.getDtoById(customerEngineerEditParam.getCustomerId());

        SecStaffBaseUpdateParam secStaffUpdateParam = new SecStaffBaseUpdateParam();
        secStaffUpdateParam.setUid(customerEngineerEditParam.getUid());
        secStaffUpdateParam.setName(customerEngineerEditParam.getUserName());
        secStaffUpdateParam.setMobile(customerEngineerEditParam.getUserMobile());
        secStaffUpdateParam.setEmail(customerEngineerEditParam.getUserEmail());

        // 增加客户名称到remark里
        KjsMetaData kjsMetaData = new KjsMetaData();
        kjsMetaData.setCompanyName(dto != null && StringUtils.isNotBlank(dto.getName().trim()) ? dto.getName() : "");
        SecStaffRemark secStaffRemark = new SecStaffRemark();
        secStaffRemark.setKjs(kjsMetaData);
        secStaffUpdateParam.setRemark(JSONObject.toJSONString(secStaffRemark));

        RestResponse<Boolean> restResponse = porosSecStaffClient.updateBaseByUid(secStaffUpdateParam);
        if (restResponse.isOk()) {
            CustomerEngineer customerEngineer = new CustomerEngineer();
            customerEngineer.setId(customerEngineerEditParam.getId());
            customerEngineer.setStatus(customerEngineerEditParam.getStatus());
            return updateById(customerEngineer);
        } else {
            log.info("中台修改用户失败->" + JSONObject.toJSONString(restResponse));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @Override
    public String getCurrentCustomerId(String uid) {
        //判断当前登录人为工程师时候绑定的客户id
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getUid, uid);
        List<CustomerEngineer> customerEngineers = customerEngineerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customerEngineers)) {
            String customerId = customerEngineers.get(0).getCustomerId();
            return customerId;
        }
        return null;
    }

    @Override
    public CustomerEngineerDto getDtoById(String id) {
        CustomerEngineer customerEngineer = customerEngineerMapper.selectById(id);
        CustomerEngineerDto customerEngineerDto = CopyDataUtil.copyObject(customerEngineer, CustomerEngineerDto.class);
        if (null != customerEngineer) {
            RestResponse<PorosSecStaffDto> restResponse = porosSecStaffClient.getByUid(customerEngineer.getUid(), null);
            if (restResponse.isOk()) {
                PorosSecStaffDto porosSecStaffDto = restResponse.getData();
                customerEngineerDto.setUserName(porosSecStaffDto.getName());
                customerEngineerDto.setUserMobile(porosSecStaffDto.getMobile());
                customerEngineerDto.setUserEmail(porosSecStaffDto.getEmail());
            } else {
                log.info("中台未获取到用户信息");
            }
        }
        return customerEngineerDto;
    }

    @Override
    public void updateStatusByCustomId(String customId, Integer status, String customerUid) {
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getCustomerId, customId);
        List<CustomerEngineer> engineerList = customerEngineerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(engineerList)) {
            engineerList.stream().forEach(engineer -> engineer.setStatus(status));
            this.updateBatchById(engineerList);
            //跟随客户，批量启用/禁用中台用户
            List<String> uids = engineerList.stream().map(CustomerEngineer::getUid).distinct().collect(Collectors.toList());
            if (StringUtils.isNotBlank(customerUid)) {
                uids.add(customerUid);
            }
            if (CollectionUtils.isNotEmpty(uids)) {
                staffClientUpdate(uids, status);
            }
        }
    }

    @Override
    public Boolean updateStatus(String id, Integer status) {
        CustomerEngineer customerEngineer = customerEngineerMapper.selectById(id);
        //启用的时候判断是否工程师数量超过客户设置限制数量
        if (status == StaticValue.ONE) {
            CustomerDto customerDto = customerService.buildDto(customerEngineer.getCustomerId());
            if (null == customerDto) {
                log.error("未找到所属客户信息");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
            }
            Integer maxEngineerCount = customerDto.getMaxEngineerCount();
            Integer curEngineerCount = customerDto.getCurEngineerCount() + 1;
            if (maxEngineerCount != StaticValue.ZERO && curEngineerCount > maxEngineerCount) {
                log.error("启用工程师数量已达上限(" + maxEngineerCount + ")");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("engineer_count_error", null, LocaleContextHolder.getLocale())));
            }
        }

        customerEngineer.setStatus(status);
        List<String> uids = new ArrayList<>(StaticValue.ONE);
        uids.add(customerEngineer.getUid());
        //启用/禁用中台用户
        staffClientUpdate(uids, status);

        return customerEngineerMapper.updateById(customerEngineer) > StaticValue.ZERO;
    }

    @Override
    public Integer engineerCount(String customerId) {
        if (StringUtils.isBlank(customerId)) {
            return StaticValue.ZERO;
        }
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getCustomerId, customerId);
        wrapper.eq(CustomerEngineer::getStatus, StaticValue.ONE);
        return customerEngineerMapper.selectCount(wrapper);
    }

    @Override
    public Integer engineerCount() {
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getStatus, StaticValue.ONE);
        return customerEngineerMapper.selectCount(wrapper);
    }

    @Override
    public List<CustomerEngineer> getEngineerUidsByCustomerId(String customerId) {
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerEngineer::getCustomerId, customerId);
        wrapper.select(CustomerEngineer::getUid);
        return list(wrapper);
    }

    /**
     * 远程更新中台用户状态
     *
     * @param uids
     * @param status
     */
    private void staffClientUpdate(List<String> uids, Integer status) {
        SecStaffStatusBatchParam secStaffStatusBatchParam = new SecStaffStatusBatchParam();
        secStaffStatusBatchParam.setUids(uids);
        secStaffStatusBatchParam.setStatus(status);
        RestResponse<Boolean> restResponse = porosSecStaffClient.updateStatusBatch(secStaffStatusBatchParam);
        if (!restResponse.isOk() ) {
            log.error("修改用户状态失败," + restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("update_user_error", null, LocaleContextHolder.getLocale())));
        }
    }

    private Wrapper<CustomerEngineer> getPageSearchWrapper(CustomerEngineerQueryParam customerEngineerQueryParam) {
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.<CustomerEngineer>lambdaQuery();
        if (StringUtils.isNotBlank(customerEngineerQueryParam.getCustomerId())) {
            wrapper.eq(CustomerEngineer::getCustomerId, customerEngineerQueryParam.getCustomerId());
        }
        if(CollectionUtils.isNotEmpty(customerEngineerQueryParam.getUids())){
            wrapper.in(CustomerEngineer::getUid, customerEngineerQueryParam.getUids());
        }
        if (BaseEntity.class.isAssignableFrom(CustomerEngineer.class)) {
            wrapper.orderByDesc(CustomerEngineer::getUpdateTime, CustomerEngineer::getCreateTime);
        }
        return wrapper;
    }

    @Override
    public List<String> getAllUid() {
        LambdaQueryWrapper<CustomerEngineer> wrapper = Wrappers.lambdaQuery();
        wrapper.select(CustomerEngineer::getUid);
        List<CustomerEngineer> customerEngineers = customerEngineerMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(customerEngineers)) {
            return customerEngineers.stream().map(CustomerEngineer::getUid).distinct().collect(Collectors.toList());
        } else {
            return null;
        }
    }
}
