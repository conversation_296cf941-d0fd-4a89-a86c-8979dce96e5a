package cn.getech.ehm.system.controller;

import cn.getech.ehm.system.dto.ketianyun.KeTianVideoQueryParam;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunLiveInfoDto;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunVideoDto;
import cn.getech.ehm.system.dto.ketianyun.KeTianYunVideoResponse;
import cn.getech.ehm.system.service.IKtyLiveService;
import cn.getech.ehm.system.utils.MD5Utils;
import cn.getech.ehm.system.utils.ketianyun.LiveProperty;
import cn.getech.ehm.system.utils.ketianyun.OauthProperty;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import cn.getech.ehm.knowledge.client.LiveConfigClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.UUID;

/**
 * <p>
 * 课程点赞模块控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Slf4j
@Controller
@RequestMapping("/ktyLive")
@Api(tags = "科天云直播模块")
public class WebKeTianLiveController {

    @Autowired
    private IKtyLiveService keTianLiveService;

    @Autowired
    private LiveProperty liveProperty;

    @Autowired
    private OauthProperty oauthProperty;

    @Autowired
    private LiveConfigClient liveConfigClient;

    @GetMapping("/manage/redirect")
    public String liveManageRedirect() {
        String token = UUID.randomUUID().toString().replace("-", "");
        if (keTianLiveService.setToken(token)) {
            String redirectUrl = String.format(liveProperty.getLiveRedirectUrl(), token);
            log.info("直播讲师跳转:" + redirectUrl);
            return "redirect:" + redirectUrl;
        }
        return "";
    }

    @GetMapping("/live/app/redirect")
    public String liveAppRedirect(HttpServletRequest request) throws UnsupportedEncodingException {
        log.info("poros-current-user:" + request.getHeader("poros-current-user"));
        long ts = System.currentTimeMillis();
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        log.info("current user: " + userBaseInfo.getName());
        String userid = userBaseInfo.getUid();
        RestResponse<Boolean> restResponse = liveConfigClient.checkAuthorization(userid);
        if (restResponse.isOk()) {
            RestResponse<Boolean> checkAuthorizationRestResponse = liveConfigClient.checkAuthorization(userid);
            log.info("check authority:" + checkAuthorizationRestResponse);
            if (checkAuthorizationRestResponse.isOk()) {
                boolean authorization = checkAuthorizationRestResponse.getData();
                if (authorization) {
                    String sign = MD5Utils.md5Hex(liveProperty.getAppSecretKey() + userBaseInfo.getUid() + liveProperty.getAppSecretKey() + ts, "utf-8");
                    String nickname = URLEncoder.encode(Base64.getEncoder().encodeToString(userBaseInfo.getName().getBytes()), "utf-8");
                    String redirectUrl = liveProperty.getLiveWatchUrl() + "?ts=" + ts + "&sign=" + sign + "&userid=" + userBaseInfo.getUid() + "&nickname=" + nickname + "&avatar=" + oauthProperty.getDefaultUserAvatar();
                    log.info("APP观看直播跳转:" + redirectUrl);
                    return "redirect:" + redirectUrl;
                } else {
                    log.info("[" + userid + "]没有观看直播权限");
                    return "forbidden";
                }
            }
        }
        return "error";
    }

    @ApiOperation("获取科天云Sign")
    @PostMapping("/getKeTianYunSign")
    @ResponseBody
    public RestResponse<String> getKeTianYunSign(@RequestBody KeTianVideoQueryParam keTianVideoQueryParam) {
        return RestResponse.ok(keTianLiveService.getSign(keTianVideoQueryParam));
    }

    @ApiOperation("获取科天云直播信息")
    @PostMapping("/getCurrLiveInfo")
    @ResponseBody
    public RestResponse<KeTianYunLiveInfoDto> getCurrLiveInfo() {
        return RestResponse.ok(keTianLiveService.getCurrLiveInfo());
    }

    @ApiOperation("获取科天云直播状态")
    @PostMapping("/getLiveStatus")
    @ResponseBody
    public RestResponse<String> getLiveStatus(@RequestParam String stream) {
        return RestResponse.ok(keTianLiveService.getLiveStatus(stream));
    }

    @ApiOperation("获取科天云直播人数")
    @GetMapping("/getLiveOnlineCount")
    @ResponseBody
    public RestResponse<Integer> getLiveOnlineCount() {
        return RestResponse.ok(keTianLiveService.getLiveOnlineCount());
    }

    @ApiOperation("获取科天云视频列表")
    @PostMapping("/getKeTianYunVideoList")
    @ResponseBody
    public RestResponse<KeTianYunVideoResponse> getKeTianYunVideoList(@RequestBody KeTianVideoQueryParam keTianVideoQueryParam) {
        return RestResponse.ok(keTianLiveService.getKeTianYunVideoList(keTianVideoQueryParam));
    }

    @ApiOperation("获取科天云单条视频")
    @GetMapping("/getKeTianYunVideoInfo/{vid}")
    @ResponseBody
    public RestResponse<KeTianYunVideoDto> getKeTianYunVideoInfo(@PathVariable("vid") String vid) {
        return RestResponse.ok(keTianLiveService.getKeTianYunVideoInfo(vid));
    }

    @ApiOperation("获取科天写入的token")
    @ResponseBody
    @GetMapping("/getWriteToken")
    public RestResponse<String> getWriteToken() {
        return RestResponse.ok(keTianLiveService.getWriteToken());
    }
}
