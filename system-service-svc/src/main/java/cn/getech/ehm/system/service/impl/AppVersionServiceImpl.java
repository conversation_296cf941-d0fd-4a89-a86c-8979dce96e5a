package cn.getech.ehm.system.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.system.dto.appversion.*;
import cn.getech.ehm.system.entity.AppVersion;
import cn.getech.ehm.system.mapper.AppVersionMapper;
import cn.getech.ehm.system.service.IAppVersionService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.VersionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <pre>
 * app版本信息表 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
@Slf4j
@Service
public class AppVersionServiceImpl extends BaseServiceImpl<AppVersionMapper, AppVersion> implements IAppVersionService {

    @Autowired
    private AppVersionParamMapper appVersionParamMapper;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Override
    public PageResult<AppVersionDto> pageDto(AppVersionQueryParam appVersionQueryParam) {
        Wrapper<AppVersion> wrapper = getPageSearchWrapper(appVersionQueryParam);
        PageResult<AppVersionDto> result = appVersionParamMapper.pageEntity2Dto(page(appVersionQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(AppVersionAddParam addParam) {
        AppVersion latestVersion = getLatestVersion(StaticValue.ZERO, null);
        if (null != latestVersion && addParam.getVersionCode() <= latestVersion.getVersionCode()) {
            log.error(String.format("请填写大于%s的版本code", latestVersion.getVersionCode()));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("version_code_error", null, LocaleContextHolder.getLocale())));
        }
        if (null != latestVersion && VersionUtils.compareVersion(addParam.getVersion(), latestVersion.getVersion()) <= 0) {
            log.error(String.format("当前最新版本号为%s，请填写比该版本号大的版本号", latestVersion.getVersion()));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("version_error", null, LocaleContextHolder.getLocale())));
        }
        AppVersion appVersion = appVersionParamMapper.addParam2Entity(addParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appVersion);
        return save(appVersion);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(AppVersionEditParam editParam) {
        LambdaQueryWrapper<AppVersion> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AppVersion::getVersionCode, editParam.getVersionCode());
        AppVersion existAppVersion = (AppVersion) this.baseMapper.selectOne(wrapper);
        if (null != existAppVersion && !existAppVersion.getId().equals(editParam.getId())) {
            AppVersion latestVersion = getLatestVersion(StaticValue.ZERO, null);
            log.error(String.format("已有相同的版本号！请填写大于%s的版本code", latestVersion.getVersionCode()));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("version_error", null, LocaleContextHolder.getLocale())));
        }
        AppVersion version = (AppVersion) this.baseMapper.selectById(editParam.getId());
        if (version.getPublish()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("version_released", null, LocaleContextHolder.getLocale())));
        }
        AppVersion appVersion = appVersionParamMapper.editParam2Entity(editParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, appVersion);
        return updateById(appVersion);
    }

    @Override
    public boolean publish(String id) {
        AppVersion version = (AppVersion) this.baseMapper.selectById(id);
        if (null == version) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }
        version.setPublish(!version.getPublish());
        this.baseMapper.updateById(version);
        return version.getPublish();
    }

    @Override
    public AppVersionDto getDtoById(String id) {
        return appVersionParamMapper.entity2Dto((AppVersion) this.getById(id));
    }

    @Override
    public AppVersionQrCodeDto latestVersion() {
        AppVersion appVersion = getLatestVersion(StaticValue.ZERO, true);
        AppVersionQrCodeDto qrCodeDto = new AppVersionQrCodeDto();
        if (null != appVersion) {
            qrCodeDto.setAttachName(appVersion.getAttachName());
            qrCodeDto.setDownloadUrl(appVersion.getDownloadUrl());
        }
        return qrCodeDto;
    }

    @Override
    public AppVersionDto latestVersion(Integer versionCode) {
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, versionCode);
        AppVersion appVersion = getLatestVersion(versionCode, true);
        return CopyDataUtil.copyObject(appVersion, AppVersionDto.class);
    }

    private AppVersion getLatestVersion(Integer versionCode, Boolean publish) {
        LambdaQueryWrapper<AppVersion> wrapper = Wrappers.lambdaQuery();
        wrapper.gt(AppVersion::getVersionCode, versionCode);
        wrapper.eq(null != publish, AppVersion::getPublish, publish);
        wrapper.orderByDesc(AppVersion::getVersionCode).last("limit 1");
        AppVersion appVersion = (AppVersion) this.baseMapper.selectOne(wrapper);
        return appVersion;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<AppVersionDto> rows) {
        return saveBatch(appVersionParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<AppVersion> getPageSearchWrapper(AppVersionQueryParam queryParam) {
        LambdaQueryWrapper<AppVersion> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(queryParam.getVersion()), AppVersion::getVersion, queryParam.getVersion());
        if (BaseEntity.class.isAssignableFrom(AppVersion.class)) {
            wrapper.orderByDesc(AppVersion::getUpdateTime, AppVersion::getCreateTime);
        }
        return wrapper;
    }

}
