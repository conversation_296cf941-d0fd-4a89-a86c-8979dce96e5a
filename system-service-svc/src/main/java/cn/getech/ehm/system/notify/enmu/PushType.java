package cn.getech.ehm.system.notify.enmu;

/**
 * <AUTHOR>
 * @date 2021-04-08 16:42:10
 **/
public enum PushType {

    MESSAGE(1, "消息"),
    NOTICE(2, "通知");

    int code;
    String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    PushType(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
