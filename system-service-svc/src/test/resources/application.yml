spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      url: *********************************************************************************************************************************************
      username: root
      password:
      driver-class-name: com.mysql.cj.jdbc.Driver
      max-active: 50
      max-wait: 10000
      min-idle: 3
      initial-size: 5
  mvc:
    throw-exception-if-no-handler-found: true
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mail:
    host: smtp.qiye.aliyun.com
    port: 465
    username: <EMAIL>
    password: Abcd1234
    from: <EMAIL>
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
            fallback: true
          starttls:
            enable: true
            required: true
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.getech.ehm.system.entity

feign:
  sentinel:
    enabled: true
  httpclient:
    enabled: true
    # 最大连接数，默认：200
    max-connections: 1000
    # 最大路由，默认：50
    max-connections-per-route: 50
    # 连接超时，默认：2000/毫秒
    connection-timeout: 3000
    # 生存时间，默认：900L
    time-to-live: 900
    time-to-live-unit: seconds

poros:
  gateway:
    enabled: false
    gatewayUrl: http://kong.poros-platform:8001
  sername: kjs-service-system
  #系统标识，读取maven property
  syscode: '@syscode@'
  # swagger配置
  swagger:
    version: 1.0
    title: test
    #rest响应aop
    base-package: cn.getech.ehm.system
  rest-aop:
    base-packages: cn.getech.ehm.system
  permission:
    enabled: false

aliyun:
  access-key:
    id: LTAI4G9WGXfkHDe2c2DHv5gU
    secret: ******************************
  region-id: cn-hangzhou
  sms:
    sys:
      domain: dysmsapi.aliyuncs.com
      version: '2017-05-25'
    default-sign: 康吉森
    templates:
      alarm: SMS_206810149
  push:
    app-key: 333412562
  iot:
    studio:
      url: https://scada.gmaint.cn/page/505652
      token: aa20c1317b42b80066dbeee66146c7e7
#百度api AK码
baidu:
  ak: tFlFhngr3CMoj5Pc8zjAjC3UQUWr9s5a

mars:
  api-host: https://mars.gmaint.cn
  api-prefix: /api/v1
  auth-api: /auth
  index-path: /#/index
  username: admin
  password: getechehm2021