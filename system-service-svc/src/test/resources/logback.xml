<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="AuditLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    AUDIT_LOG‖%-5level‖%d{dd-MM-yyyy HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="LoginLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    LOGIN_LOG‖%-5level‖%d{dd-MM-yyyy HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="BizLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    %-5level %d{dd-MM-yyyy HH:mm:ss.SSS} %thread %msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="audit_log" level="info">
        <appender-ref ref="AuditLogAppender"/>
    </logger>

    <logger name="login_log" level="info">
        <appender-ref ref="LoginLogAppender"/>
    </logger>

    <root level="info">
        <appender-ref ref="BizLogAppender"/>
    </root>
</configuration>