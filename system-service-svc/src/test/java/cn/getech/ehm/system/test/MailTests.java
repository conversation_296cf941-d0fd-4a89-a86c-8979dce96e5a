package cn.getech.ehm.system.test;

import cn.getech.ehm.system.Application;
import cn.getech.ehm.system.notify.MailService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-11-18 14:38:01
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class MailTests {

    @Autowired
    private MailService mailService;

    @Test
    public void sendMailTest(){
        mailService.sendEmail("设备报警邮件", "设备发生报警，请立即处理","<EMAIL>");
    }

}
